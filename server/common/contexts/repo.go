package contexts

import "context"

type RepoContext struct {
	Uid      string
	Did      string
	RepoPath string
	RepoName string
	RepoURL  string
	Branch   string
	Language string
}

func GetRepoContext(ctx context.Context) *RepoContext {
	val := ctx.Value(gitRepoContextKey)
	if val != nil {
		return val.(*RepoContext)
	}
	return nil
}

func WithRepoContext(ctx context.Context, gitRepoContext *RepoContext) context.Context {
	return context.WithValue(ctx, gitRepoContextKey, gitRepoContext)
}

package contexts

import (
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestRepoContext(t *testing.T) {
	gitRepoContext := &RepoContext{
		Uid:      "testuid",
		Did:      "testdid",
		RepoURL:  "https://code.byted.org/ies/lvweb",
		RepoName: "ies/lvweb",
		Branch:   "master",
		RepoPath: "/tmp",
	}
	ctx := WithRepoContext(context.Background(), gitRepoContext)
	got := GetRepoContext(ctx)
	assert.NotNil(t, got)

	assert.Equal(t, gitRepoContext.Uid, got.Uid)
	assert.Equal(t, gitRepoContext.Did, got.Did)
	assert.Equal(t, gitRepoContext.RepoURL, got.RepoURL)
	assert.Equal(t, gitRepoContext.RepoName, got.RepoName)
	assert.Equal(t, gitRepoContext.Branch, got.Branch)
	assert.Equal(t, gitRepoContext.RepoPath, got.RepoPath)
}

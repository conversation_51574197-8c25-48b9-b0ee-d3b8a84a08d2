package group

import (
	"code.byted.org/ies/codin/common/contexts"
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBuildAsyncCtx(t *testing.T) {
	var ctx = contexts.WithLogID(context.Background())

	t.Logf("LogID: %v", contexts.GetLogID(ctx))

	repoCtx := &contexts.RepoContext{
		Uid: "123",
	}
	ctx = contexts.WithRepoContext(ctx, repoCtx)

	newCtx := BuildAsyncCtx(ctx)
	t.Logf("AsyncCtx: %v", contexts.GetLogID(newCtx))

	assert.Equal(t, contexts.GetLogID(ctx), contexts.GetLogID(newCtx))
	assert.Equal(t, repoCtx.Uid, contexts.GetRepoContext(newCtx).Uid)
}

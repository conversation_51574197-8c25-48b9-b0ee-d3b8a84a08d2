package utils

import (
	"fmt"
)

type ToolStatus string

const (
	ToolStatusSuccess ToolStatus = "[ToolRunSuccess]"
	ToolStatusError   ToolStatus = "[ToolRunError]"
)

type ToolResultOptions struct {
	ToolStatus    ToolStatus
	ToolOutput    string
	MaxLength     int
	ExtraReminder string
}

const defaultMaxToolOutputLength = 10_000
const separator = "\n"

func CreateToolResult(options ToolResultOptions) (string, error) {
	toolOutput := options.ToolOutput

	if options.MaxLength == 0 {
		options.MaxLength = defaultMaxToolOutputLength
	}

	truncateTips := ""
	if len(toolOutput) > options.MaxLength {
		truncateTips = fmt.Sprintf("The tool output exceeds %d characters, with %d characters truncated.", options.MaxLength, len(toolOutput)-options.MaxLength)
		toolOutput = toolOutput[:options.MaxLength]
	}

	reminder := string(options.ToolStatus) + separator + truncateTips

	if options.ExtraReminder != "" {
		reminder += separator + options.ExtraReminder
	}

	reminderTag := createSystemReminderTag(reminder)

	return reminderTag + separator + toolOutput, nil
}

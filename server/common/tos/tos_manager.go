package tos

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"strings"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/common/semantic/tos/config"
	"github.com/volcengine/ve-tos-golang-sdk/v2/tos"
)

type TosManager struct {
	bucketName *string
	client     *tos.ClientV2
	dir        string
}

func NewTosManager() *TosManager {
	m := &TosManager{}
	return m
}

func (m *TosManager) InitTosClientIfNeeded(config config.TosConfig) error {
	if m.client != nil {
		return nil
	}

	credential := tos.NewStaticCredentials(config.AccessKey, config.SecretKey)
	client, err := tos.NewClientV2(config.Endpoint,
		tos.WithCredentials(credential),
		tos.WithRegion(config.Region),
	)

	if err != nil {
		logs.Error("创建TOS客户端失败 config: %v", config)
		return fmt.Errorf("创建TOS客户端失败: %v", err)
	}
	m.client = client
	m.bucketName = &config.BucketName
	return nil
}

func (m *TosManager) SetDir(dir string) {
	if strings.HasSuffix(dir, "/") {
		m.dir = dir
	} else {
		m.dir = dir + "/"
	}
}

// 上传文件
func (m *TosManager) UploadFileToTos(ctx context.Context, fileData []byte, rootHash string) (string, error) {
	fileReader := bytes.NewReader(fileData)

	_, err := m.client.PutObjectV2(ctx, &tos.PutObjectV2Input{
		PutObjectBasicInput: tos.PutObjectBasicInput{
			Bucket: *m.bucketName,
			Key:    m.dir + rootHash,
		},
		Content: fileReader,
	})

	if err != nil {
		log.V2.Error().With(ctx).Str("上传文件失败").KVs(
			m.dir+"filePath", rootHash,
		).Error(err).Emit()
		return "", err
	}
	log.V2.Info().With(ctx).Str("上传文件成功").KVs(
		"filePath", m.dir+rootHash,
	).Emit()

	return rootHash, nil
}

// 下载文件
func (m *TosManager) DownloadFileFromTos(ctx context.Context, objectKey string) ([]byte, error) {
	getOutput, err := m.client.GetObjectV2(ctx, &tos.GetObjectV2Input{
		Bucket: *m.bucketName,
		Key:    m.dir + objectKey,
	})

	if err != nil {
		logs.CtxWarn(ctx, "DownloadFileFromTos, 下载文件失败, objectKey=%v", objectKey)
		return nil, err
	}

	log.V2.Info().With(ctx).Str("GetObjectV2 Request ID:", getOutput.RequestID).Emit()

	cont, err := io.ReadAll(getOutput.Content)
	if err != nil {
		log.V2.Warn().With(ctx).Str("下载文件失败").KVs(
			"objectKey", m.dir+objectKey,
		).Emit()
	}
	log.V2.Info().With(ctx).Str("下载文件成功").KVs(
		"objectKey", m.dir+objectKey,
	).Emit()

	return cont, nil
}

func (m *TosManager) IsObjectExist(ctx context.Context, objectKey string) (bool, error) {
	_, err := m.client.HeadObjectV2(ctx, &tos.HeadObjectV2Input{
		Bucket: *m.bucketName,
		Key:    m.dir + objectKey,
	})
	if err != nil {
		logs.CtxWarn(ctx, "check object exist failed: %v", err)
		return false, err
	}

	return true, nil
}

package devopsgorm

import (
	"code.byted.org/gopkg/pkg/errors"
	"fmt"
	"path/filepath"

	"code.byted.org/golf/ssconf"
	"code.byted.org/gopkg/dbutil/conf"
	"code.byted.org/gopkg/dbutil/gormdb"
	"code.byted.org/gopkg/gorm"
	"code.byted.org/gopkg/logs"
)

const (
	WriterMode = "writer"
	ReaderMode = "reader"

	DBName = "capcut_devops"
)

var (
	dBReader *gormdb.DBHandler
	dBWriter *gormdb.DBHandler
)

func InitDB() (err error) {
	dBReader, dBWriter, err = initDB(DBName, "db.conf")
	if err != nil {
		logs.Error("init db err: %s", err)
		panic(errors.Errorf("init db err: %s", err))
	}

	return nil
}

func initDB(name, confPath string) (*gormdb.DBHandler, *gormdb.DBHandler, error) {
	fullConfPath := filepath.Join("conf", confPath)
	confData, err := ssconf.LoadSsConfFile(fullConfPath)
	if err != nil {
		logs.Warn("cannot load file: %s, err: %v", fullConfPath, err)
		return nil, nil, err
	}

	logs.Debug("client_db config:%s", confData)
	readDBConf := GetDBConf(confData, name, "read")
	writeDBConf := GetDBConf(confData, name, "write")
	logs.Debug("[dbconnection.go]InitDB() readDBConf: %v, writeDBConf: %v", readDBConf, writeDBConf)
	dbReader := gormdb.NewDBHandlerWithOptional(&readDBConf)
	dbWriter := gormdb.NewDBHandlerWithOptional(&writeDBConf)
	return dbReader, dbWriter, nil
}

func GetDBConf(config map[string]string, name string, mode string) conf.DBOptional {
	logs.Debug("[dbconnection.go] GetDBConf()dbName: %v", name)
	dbConf := conf.GetDefaultDBOptional()
	dbConf.DriverName = "mysql2"
	dbConf.DBCharset = "utf8mb4"
	prefix := "ss_" + name + "_" + mode
	dbConf.DBHostname = config[prefix+"_host"]
	dbConf.Timeout = config[prefix+"_timeout"]
	dbConf.ReadTimeout = config[prefix+"_readtimeout"]
	dbConf.WriteTimeout = config[prefix+"_writetimeout"]
	return dbConf
}

func GetDBConn(mode string) (*gorm.DB, error) {
	var dbHandler *gormdb.DBHandler
	var err error

	if dBReader == nil || dBWriter == nil {
		if dBReader, dBWriter, err = initDB(DBName, "db.conf"); err != nil {
			return nil, fmt.Errorf("get dbHandler error")
		}
	}
	switch mode {
	case WriterMode:
		dbHandler = dBWriter
	case ReaderMode:
		dbHandler = dBReader
	}

	if dbHandler == nil {
		return nil, fmt.Errorf("dbHandler nil. dbMode:%s", mode)
	}

	return dbHandler.GetConnection()
}

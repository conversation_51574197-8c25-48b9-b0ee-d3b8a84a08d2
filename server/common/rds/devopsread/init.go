package devopsread

import (
	"database/sql"
	"sync"

	_ "code.byted.org/gopkg/bytedmysql"
	"code.byted.org/gopkg/logs/v2"
)

var (
	once   sync.Once
	client *sql.DB
)

func init() {
	once.Do(func() {
		// timeout = 5s
		// readTimeout = 5s
		// writeTimeout = 5s

		dsn := "@sd(toutiao.mysql.capcut_devops_read)/?charset=utf8mb4&parseTime=True&loc=Local&timeout=5s&readTimeout=5s&writeTimeout=5s"
		db, err := sql.Open("bytedmysql", dsn)
		if err != nil {
			logs.Error("link to read db failed: %v", err)
			panic(err.Error())
		}
		logs.Info("devops read db init success")
		client = db
	})
}

func GetClient() *sql.DB {
	return client
}

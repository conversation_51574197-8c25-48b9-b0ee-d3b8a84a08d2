package devops

import (
	"sync"
	"time"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/kv/goredis"
)

var (
	once   sync.Once
	client *goredis.Client
)

func init() {
	once.Do(func() {
		var redisOpt = goredis.NewOption()
		redisOpt.ReadTimeout = 1 * time.Second
		redisOpt.WriteTimeout = 1 * time.Second

		// dialTimeout = 50ms
		// readTimeout = 1s
		// writeTimeout = 1s
		// poolTimeout = 50ms
		// idleTimeout = 50ms

		c, err := goredis.NewClientWithOption("bytedance.abase2.devops", redisOpt)
		if err != nil {
			panic(err)
		}
		logs.Info("devops client init success")
		client = c
	})
}

func GetClient() *goredis.Client {
	return client
}

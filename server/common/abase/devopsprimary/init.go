package devopsprimary

import (
	"sync"
	"time"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/kv/goredis"
)

var (
	once   sync.Once
	client *goredis.Client
)

func init() {
	once.Do(func() {
		var redisOpt = goredis.NewOption()
		redisOpt.ReadTimeout = 1 * time.Second
		redisOpt.WriteTimeout = 1 * time.Second

		// dialTimeout = 50ms
		// readTimeout = 1s
		// writeTimeout = 1s
		// poolTimeout = 50ms
		// idleTimeout = 50ms
		if env.IsPPE() || env.IsProduct() {
			c, err := goredis.NewClientWithOption("bytedance.abase2.devops_primary", redisOpt)
			if err != nil {
				panic(err)
			}
			client = c
		}

		logs.Info("devops_primary client init success")

	})
}

func GetClient() *goredis.Client {
	return client
}

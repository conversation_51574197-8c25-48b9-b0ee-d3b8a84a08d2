package manager

import (
	"bufio"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
	"sort"
	"testing"
)

// DemoEntity 表示JSON中的实体结构
type DemoEntity struct {
	Type       string                 `json:"type"`
	ID         string                 `json:"id"`
	FileHash   string                 `json:"fileHash"`
	Attributes map[string]interface{} `json:"attributes"`
}

func TestProcessDemo(t *testing.T) {
	// JSON文件路径
	jsonFile := "iMovie.json"
	outputFile := "chunk.txt"

	fmt.Printf("开始处理文件: %s\n", jsonFile)
	fmt.Printf("输出文件: %s\n", outputFile)

	// 打开JSON文件
	file, err := os.Open(jsonFile)
	if err != nil {
		fmt.Println("无处理文件直接跳过本测试")
		return
	}
	defer file.Close()

	// 创建输出文件
	outFile, err := os.Create(outputFile)
	if err != nil {
		t.Fatalf("无法创建输出文件: %v", err)
	}
	defer outFile.Close()

	writer := bufio.NewWriter(outFile)
	defer writer.Flush()

	// 解析JSON文件
	decoder := json.NewDecoder(file)

	// 读取开头的 '['
	token, err := decoder.Token()
	if err != nil {
		t.Fatalf("解析JSON失败: %v", err)
	}
	if delim, ok := token.(json.Delim); !ok || delim != '[' {
		t.Fatalf("期望JSON数组开始符，但得到: %v", token)
	}

	processedCount := 0
	errorCount := 0

	// 先收集所有实体到切片中
	var entities []DemoEntity

	// 逐个读取实体
	for decoder.More() {
		var entity DemoEntity
		err := decoder.Decode(&entity)
		if err != nil {
			log.Printf("解析实体失败: %v", err)
			errorCount++
			continue
		}
		entities = append(entities, entity)
	}

	// 按多字段排序以确保处理顺序完全稳定
	sort.Slice(entities, func(i, j int) bool {
		// 首先按ID排序
		if entities[i].ID != entities[j].ID {
			return entities[i].ID < entities[j].ID
		}
		// ID相同时按Type排序
		if entities[i].Type != entities[j].Type {
			return entities[i].Type < entities[j].Type
		}
		// 最后按FileHash排序（如果存在）
		return entities[i].FileHash < entities[j].FileHash
	})

	// 处理排序后的实体
	for _, entity := range entities {
		// 处理实体
		chunk, err := processEntity(entity)
		if err != nil {
			log.Printf("处理实体失败 [%s]: %v", entity.ID, err)
			errorCount++
			continue
		}

		// 写入输出文件
		if chunk != "" {
			_, err = writer.WriteString(chunk)
			if err != nil {
				log.Printf("写入文件失败: %v", err)
				errorCount++
				continue
			}

			// 添加分隔符
			_, err = writer.WriteString("\n\n")
			if err != nil {
				log.Printf("写入分隔符失败: %v", err)
			}
		}

		processedCount++
		if processedCount%100 == 0 {
			fmt.Printf("已处理 %d 个实体...\n", processedCount)
		}
	}

	// 读取结尾的 ']'
	token, err = decoder.Token()
	if err != nil && err != io.EOF {
		log.Printf("读取JSON结尾失败: %v", err)
	}

	fmt.Printf("\n处理完成！\n")
	fmt.Printf("总共处理: %d 个实体\n", processedCount)
	fmt.Printf("处理错误: %d 个\n", errorCount)
	fmt.Printf("输出文件: %s\n", outputFile)
}

// processEntity 处理单个实体
func processEntity(entity DemoEntity) (string, error) {
	// 过滤掉不需要处理的实体类型
	if entity.Type == "file" {
		// 跳过纯文件类型，通常只包含路径信息
		return "", nil
	}

	// 确保属性处理的稳定性：创建一个有序的属性副本
	stableAttributes := make(map[string]interface{})
	for k, v := range entity.Attributes {
		stableAttributes[k] = v
	}

	// 调用 BuildChunkContent 处理实体
	chunk, err := BuildChunkContent(entity.ID, entity.Type, stableAttributes)
	if err != nil {
		return "", fmt.Errorf("BuildChunkContent failed: %w", err)
	}

	// 添加注释头部，方便识别
	header := fmt.Sprintf("# Entity: %s\n# Type: %s\n# ID: %s\n",
		getEntityName(entity), entity.Type, entity.ID)

	return header + chunk, nil
}

// getEntityName 从实体中提取名称
func getEntityName(entity DemoEntity) string {
	if name, ok := entity.Attributes["name"].(string); ok && name != "" {
		return name
	}

	// 从ID中提取名称（通常在最后一个#后面）
	if entity.ID != "" {
		parts := []rune(entity.ID)
		for i := len(parts) - 1; i >= 0; i-- {
			if parts[i] == '#' && i < len(parts)-1 {
				return string(parts[i+1:])
			}
		}
	}

	return "unknown"
}

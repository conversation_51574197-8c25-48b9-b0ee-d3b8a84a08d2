package manager

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"testing"
	"time"

	"code.byted.org/flow/datamind-code-index/dao"
	"code.byted.org/flow/datamind-code-index/model"
	"code.byted.org/flow/datamind-code-index/service"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/common/semantic/codebase/config"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func getWorkspaceId() (string, string) {
	baseUserKnowledgeId := "github.com:ngxson/wllama"
	// baseUserKnowledgeId := "code.byted.org:videocut-aigc/mweb-api"
	userKnowledgeId := baseUserKnowledgeId + "/tree/master"
	return userKnowledgeId, baseUserKnowledgeId
}

func initDefaultDB() *gorm.DB {
	dbService := dao.DBService{}
	db, err := dbService.InitDB("capcut_devops_rw:Kx6ymK9AOBkpUyB0@tcp([2605:340:cd51:4301:aa48:e858:498e:6a32]:9030)/code_index_512_16_test")
	if err != nil {
		return nil
	}
	return db
}

// GormWriter 实现 gorm logger.Writer 接口
type GormWriter struct{}

func (w GormWriter) Printf(format string, args ...interface{}) {
	log.V2.Info().With(context.Background()).Str("gorm log").KVs(
		"format", format,
		"args", args,
	).Emit()
}

func TestExtractChunkEntityEffect(t *testing.T) {
	type args struct {
		db *gorm.DB
	}

	// ============== 上传 temp chunk ==============
	testArgs := struct {
		name    string
		args    args
		wantErr bool
	}{
		name: "test-abstract-chunk-entity-effect",
		args: args{
			db: initDefaultDB(),
		},
		wantErr: false,
	}

	newLogger := logger.New(
		&GormWriter{}, // 使用自定义的 writer
		logger.Config{
			SlowThreshold:             time.Second, // 慢 SQL 阈值
			LogLevel:                  logger.Info, // 可配置的日志级别
			IgnoreRecordNotFoundError: true,        // 忽略 ErrRecordNotFound 错误
			Colorful:                  false,       // 禁用彩色打印
		},
	)

	// 更新 logger
	testArgs.args.db.Config.Logger = newLogger

	model.PartitionNum = 512

	chunkContent, err := ReadJSONFile("/Users/<USER>/dev/wllama/wllama.json")
	if err != nil {
		t.Errorf("TestExtractChunkEntityEffect() readJSONFile error = %v", err)
		return
	}

	// chunkContent := `[
	// {
	//   "type": "function",
	//   "id": "/src/dispose/disposable-utils.ts#makeSafeDisposable",
	//   "fileHash": "3775358608",
	//   "attributes": {
	//     "id": "/src/dispose/disposable-utils.ts#makeSafeDisposable",
	//     "path": "/src/dispose/disposable-utils.ts",
	//     "name": "makeSafeDisposable",
	//     "parameters": [
	//       {
	//         "name": "fn",
	//         "type": "(...args: any) => any"
	//       }
	//     ],
	//     "returnType": "SafeDisposable<{ dispose: (...args: any) => any; }>",
	//     "dependencies": [
	//       {
	//         "name": "SafeDisposable",
	//         "id": "/src/dispose/disposable-t.ts#SafeDisposable",
	//         "type": "class",
	//         "use": "call"
	//       },
	//       {
	//         "name": "SafeDisposable",
	//         "id": "/src/dispose/disposable-t.ts#fileTopLevel",
	//         "type": "filetoplevel",
	//         "use": "assign"
	//       }
	//     ],
	//     "body": "{\n  const disposable = new SafeDisposable({\n    dispose: fn,\n  });\n  return disposable;\n}",
	//     "pos": {
	//       "start": 177,
	//       "end": 330
	//     },
	//     "line": 3,
	//     "type": "fileTopLevel"
	//   }
	// }
	// ]`

	userKnowledgeId, baseUserKnowledgeId := getWorkspaceId()
	if err := UploadTempChunksFromJSONFile(context.Background(), testArgs.args.db, []byte(chunkContent), userKnowledgeId, baseUserKnowledgeId); (err != nil) != testArgs.wantErr {
		t.Errorf("TestExtractChunkEntityEffect() error = %v, wantErr %v", err, testArgs.wantErr)
	}

	t.Log("upload temp chunk success")

	// ============== 构筑索引 ==============
	llmConfig := *model.NewLlmConfig()
	llmConfig.ApiKey = "xxxxxxxxxxxxxxxxxxxx"
	llmConfig.ModelName = "volcark/ep-20250714151539-s4vvm"
	llmConfig.NullOnFailure = true
	llmConfig.OutputJson = true
	llmConfig.BatchSize = 64

	embeddingConfig := *model.NewEmbeddingModelConfig()
	extractEntityConfig := *model.NewExtractEntityConfig()
	extractEntityConfig.OverwriteLLMCache = true // prompt 词 or systemPrompt 变化时刷新缓存
	extractEntityConfig.Prompt = config.ExtractEntityDefaultPrompt
	summarizeConfig := *model.NewSummarizeEntityConfig()
	summarizeConfig.OverwriteLLMCache = true // prompt 词 or systemPrompt 变化时刷新缓存

	// 启动索引构建
	id, err := service.NewCodeIndexWriteService(testArgs.args.db, context.Background()).BuildIndex(
		userKnowledgeId,
		"",
		baseUserKnowledgeId,
		"master",
		"",
		[]string{},
		true,
		false,
		summarizeConfig,
		llmConfig,
		embeddingConfig,
		extractEntityConfig,
	)
	t.Logf("BuildIndex id: %d", id)
	if err != nil {
		t.Errorf("TestExtractChunkEntityEffect() BuildIndex error = %v", err)
		return
	}

	t.Log("BuildIndex 方法调用成功，开始等待内部协程完成")

	// 等待索引构建的内部协程完成
	err = waitForIndexBuildCompletion(context.Background(), testArgs.args.db, userKnowledgeId, baseUserKnowledgeId, 120*time.Minute)
	if err != nil {
		t.Errorf("TestExtractChunkEntityEffect() 等待索引构建完成失败: %v", err)
		return
	}

	t.Log("build index success - 所有协程已完成")
}

// waitForIndexBuildCompletion 等待索引构建的内部协程完成
func waitForIndexBuildCompletion(ctx context.Context, db *gorm.DB, userKnowledgeId, knowledgebaseId string, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	ticker := time.NewTicker(10 * time.Second) // 每2秒轮询一次
	defer ticker.Stop()

	log.V2.Info().With(ctx).Str("开始轮询索引构建状态").KVs(
		"userKnowledgeId", userKnowledgeId,
		"knowledgebaseId", knowledgebaseId,
		"timeout", timeout.String(),
	).Emit()

	for {
		select {
		case <-ctx.Done():
			return errors.New("等待索引构建完成超时")
		case <-ticker.C:
			// 查询构建状态
			buildService := service.NewBuildService(db, ctx)
			buildRecord, err := buildService.QueryLatestRelatedBuildRecord(knowledgebaseId, userKnowledgeId, "")
			if err != nil {
				log.V2.Info().With(ctx).Str("查询构建记录失败，继续等待").Error(err).Emit()
				continue
			}

			// 检查构建状态（根据实际的状态值调整）
			// 通常：0=处理中, 1=成功, -1=失败
			if buildRecord.Status == 1 {
				log.V2.Info().With(ctx).Str("索引构建成功完成").Emit()
				return nil
			} else if buildRecord.Status == 2 {
				return errors.New("索引构建失败")
			}
			// 状态为 0 或其他值，继续等待
			log.V2.Info().With(ctx).Str("索引构建仍在进行中，继续等待").KVs(
				"currentStatus", buildRecord.Status,
			).Emit()
		}
	}
}

func TestQueryChunkEntityEffect(t *testing.T) {
	type args struct {
		db *gorm.DB
	}

	// ============== 上传 temp chunk ==============
	testArgs := struct {
		name    string
		args    args
		wantErr bool
	}{
		name: "test-query-chunk-entity-effect",
		args: args{
			db: initDefaultDB(),
		},
		wantErr: false,
	}

	userKnowledgeId, _ := getWorkspaceId()
	queryChunkEntity(testArgs.args.db, userKnowledgeId)
}

// ChunkEntityResult 查询结果结构体
type ChunkEntityResult struct {
	ChunkID    string `json:"chunk_id" gorm:"column:chunk_id"`
	EntityName string `json:"entity_name" gorm:"column:entity_name"`
	EntityDesc string `json:"entity_desc" gorm:"column:entity_desc"`
	Content    string `json:"content" gorm:"column:content"`
}

func queryChunkEntity(db *gorm.DB, userKnowledgeId string) error {
	ctx := context.Background()
	partitionKey := dao.GetPartitionKey(userKnowledgeId)

	log.V2.Info().With(ctx).Str("开始查询 chunk entity 数据").KVs(
		"userKnowledgeId", userKnowledgeId,
		"partitionKey", partitionKey,
	).Emit()

	var results []ChunkEntityResult
	result := db.Raw(`
	  SELECT
			e.chunk_id,
			e.entity_name,
			e.entity_desc,
			c.content
		FROM 
				code_index_512_16.chunk_entity e
		JOIN 
				code_index_512_16.chunk c
		ON 
				e.chunk_id = c.chunk_id
		WHERE 
				e.partition_key = ? 
				AND e.user_knowledge_id = ?;`, partitionKey, userKnowledgeId).Scan(&results)

	if result.Error != nil {
		log.V2.Error().With(ctx).Str("查询 chunk entity 失败").Error(result.Error).KVs(
			"userKnowledgeId", userKnowledgeId,
			"partitionKey", partitionKey,
		).Emit()
		return result.Error
	}

	log.V2.Info().With(ctx).Str("查询 chunk entity 成功").KVs(
		"userKnowledgeId", userKnowledgeId,
		"totalCount", len(results),
	).Emit()

	// 输出查询结果到日志
	for i, item := range results {
		log.V2.Info().With(ctx).Str("chunk entity 数据项").KVs(
			"index", i+1,
			"chunkId", item.ChunkID,
			"entityName", item.EntityName,
			"entityDesc", item.EntityDesc,
			"contentLength", len(item.Content),
			"contentPreview", truncateString(item.Content, 100), // 只显示前100个字符作为预览
		).Emit()
	}

	return nil
}

// truncateString 截断字符串用于日志预览
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

func ReadJSONFile(filePath string) ([]byte, error) {
	fileContent, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	return fileContent, nil
}

// ChunkEntityBasicResult 第一个SQL查询结果结构体
type ChunkEntityBasicResult struct {
	ChunkIDs    string `json:"chunk_ids" gorm:"column:chunk_ids"`
	EntityName  string `json:"entity_name" gorm:"column:entity_name"`
	EntityDesc  string `json:"entity_desc" gorm:"column:entity_desc"`
	EntityDescs string `json:"entity_descs" gorm:"column:entity_descs"`
}

// ChunkEntityDetailResult 第二个SQL查询结果结构体
type ChunkEntityDetailResult struct {
	ChunkID    string `json:"chunk_id" gorm:"column:chunk_id"`
	EntityName string `json:"entity_name" gorm:"column:entity_name"`
	EntityDesc string `json:"entity_desc" gorm:"column:entity_desc"`
	Content    string `json:"content" gorm:"column:content"`
}

func TestExecuteSQLQueries(t *testing.T) {
	type args struct {
		db *gorm.DB
	}

	testArgs := struct {
		name    string
		args    args
		wantErr bool
	}{
		name: "test-execute-sql-queries",
		args: args{
			db: initDefaultDB(),
		},
		wantErr: false,
	}

	userKnowledgeId, _ := getWorkspaceId()

	// 执行第一个SQL查询
	if err := executeFirstSQLQuery(testArgs.args.db, userKnowledgeId); err != nil {
		t.Errorf("TestExecuteSQLQueries() executeFirstSQLQuery error = %v", err)
		return
	}

	// 执行第二个SQL查询
	if err := executeSecondSQLQuery(testArgs.args.db, userKnowledgeId); err != nil {
		t.Errorf("TestExecuteSQLQueries() executeSecondSQLQuery error = %v", err)
		return
	}

	t.Log("SQL查询执行成功，结果已保存到CSV文件")
}

// executeFirstSQLQuery 执行第一个SQL查询并保存到CSV文件
func executeFirstSQLQuery(db *gorm.DB, userKnowledgeId string) error {
	ctx := context.Background()

	log.V2.Info().With(ctx).Str("开始执行第一个SQL查询").KVs(
		"userKnowledgeId", userKnowledgeId,
	).Emit()

	var results []ChunkEntityBasicResult
	result := db.Raw(`
		SELECT chunk_ids, entity_name, entity_desc, entity_descs
		FROM entity
		WHERE user_knowledge_id = ?`, userKnowledgeId).Scan(&results)

	if result.Error != nil {
		log.V2.Error().With(ctx).Str("第一个SQL查询失败").Error(result.Error).KVs(
			"userKnowledgeId", userKnowledgeId,
		).Emit()
		return result.Error
	}

	log.V2.Info().With(ctx).Str("第一个SQL查询成功").KVs(
		"userKnowledgeId", userKnowledgeId,
		"totalCount", len(results),
	).Emit()

	// 保存到CSV文件
	if err := saveBasicResultsToCSV(results, "entity.csv"); err != nil {
		log.V2.Error().With(ctx).Str("保存第一个查询结果到CSV文件失败").Error(err).Emit()
		return err
	}

	log.V2.Info().With(ctx).Str("第一个查询结果已保存到CSV文件").KVs(
		"fileName", "entity.csv",
		"totalRecords", len(results),
	).Emit()

	return nil
}

// executeSecondSQLQuery 执行第二个SQL查询并保存到CSV文件
func executeSecondSQLQuery(db *gorm.DB, userKnowledgeId string) error {
	ctx := context.Background()

	log.V2.Info().With(ctx).Str("开始执行第二个SQL查询").KVs(
		"userKnowledgeId", userKnowledgeId,
	).Emit()

	var results []ChunkEntityDetailResult
	result := db.Raw(`
		SELECT 
			ce.chunk_id,
			ce.entity_name,
			ce.entity_desc,
			c.content
		FROM 
			chunk_entity ce
		JOIN 
			chunk c 
		ON 
			ce.chunk_id = c.chunk_id
		WHERE 
			ce.user_knowledge_id = ?`, userKnowledgeId).Scan(&results)

	if result.Error != nil {
		log.V2.Error().With(ctx).Str("第二个SQL查询失败").Error(result.Error).KVs(
			"userKnowledgeId", userKnowledgeId,
		).Emit()
		return result.Error
	}

	log.V2.Info().With(ctx).Str("第二个SQL查询成功").KVs(
		"userKnowledgeId", userKnowledgeId,
		"totalCount", len(results),
	).Emit()

	// 保存到CSV文件
	if err := saveDetailResultsToCSV(results, "chunk_entity.csv"); err != nil {
		log.V2.Error().With(ctx).Str("保存第二个查询结果到CSV文件失败").Error(err).Emit()
		return err
	}

	log.V2.Info().With(ctx).Str("第二个查询结果已保存到CSV文件").KVs(
		"fileName", "chunk_entity.csv",
		"totalRecords", len(results),
	).Emit()

	return nil
}

// saveBasicResultsToCSV 保存基础查询结果到CSV文件
func saveBasicResultsToCSV(results []ChunkEntityBasicResult, fileName string) error {
	file, err := os.Create(fileName)
	if err != nil {
		return fmt.Errorf("创建CSV文件失败: %w", err)
	}
	defer func() {
		if err := file.Close(); err != nil {
			fmt.Printf("关闭CSV文件失败: %v\n", err)
		}
	}()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入表头
	headers := []string{"Chunk ID", "Entity Name", "Entity Description"}
	if err := writer.Write(headers); err != nil {
		return fmt.Errorf("写入CSV表头失败: %w", err)
	}

	// 写入数据
	for _, result := range results {
		record := []string{
			result.ChunkIDs,
			result.EntityName,
			result.EntityDesc,
			result.EntityDescs,
		}
		if err := writer.Write(record); err != nil {
			return fmt.Errorf("写入CSV数据失败: %w", err)
		}
	}

	return nil
}

// saveDetailResultsToCSV 保存详细查询结果到CSV文件
func saveDetailResultsToCSV(results []ChunkEntityDetailResult, fileName string) error {
	file, err := os.Create(fileName)
	if err != nil {
		return fmt.Errorf("创建CSV文件失败: %w", err)
	}
	defer func() {
		if err := file.Close(); err != nil {
			fmt.Printf("关闭CSV文件失败: %v\n", err)
		}
	}()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入表头
	headers := []string{"Chunk ID", "Entity Name", "Entity Description", "Content"}
	if err := writer.Write(headers); err != nil {
		return fmt.Errorf("写入CSV表头失败: %w", err)
	}

	// 写入数据
	for _, result := range results {
		record := []string{
			result.ChunkID,
			result.EntityName,
			result.EntityDesc,
			result.Content,
		}
		if err := writer.Write(record); err != nil {
			return fmt.Errorf("写入CSV数据失败: %w", err)
		}
	}

	return nil
}

package manager

import (
	"testing"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// 从原测试改写的兼容性测试
func TestBuildChunkContent_Compatibility(t *testing.T) {
	// 原：TestBuildChunkContent_ClassMethod 的逻辑
	t.Run("TypeScript类方法处理", func(t *testing.T) {
		entityID := "/Users/<USER>/file.tsx#MyClass#myMethod"
		entityType := "function"
		attributes := map[string]interface{}{
			"id":   entityID,
			"path": "/Users/<USER>/file.tsx",
			"name": "myMethod",
			"parameters": []interface{}{
				map[string]interface{}{
					"name": "param1",
					"type": "string",
				},
			},
			"returnType": "void",
			"type":       "ClassMethod",
		}

		result, err := BuildChunkContent(entityID, entityType, attributes)
		require.NoError(t, err)
		assert.NotEmpty(t, result)

		// 验证YAML包含类信息而不是函数信息
		assert.Contains(t, result, "file_path: /Users/<USER>/file.tsx")
		assert.Contains(t, result, "class:")
		assert.Contains(t, result, "name: MyClass")
		// 不应该包含顶级function字段
		assert.NotContains(t, result, "function:")
	})

	// 原：TestBuildChunkContent_Struct 的逻辑
	t.Run("Go结构体处理", func(t *testing.T) {
		attributes := map[string]interface{}{
			"name":    "User",
			"content": "用户实体结构体，定义用户的基本属性和行为",
			"path":    "/src/user.go",
		}

		result, err := BuildChunkContent("/src/user.go", "struct", attributes)
		require.NoError(t, err)

		assert.Contains(t, result, "file_path: /src/user.go")
		assert.Contains(t, result, "struct:")
		assert.Contains(t, result, "name: User")
		assert.Contains(t, result, "content: 用户实体结构体，定义用户的基本属性和行为")
	})

	// 原：TestBuildChunkContent 中不支持类型的逻辑
	t.Run("不支持的实体类型", func(t *testing.T) {
		_, err := BuildChunkContent("/test/unknown.txt", "unsupported", map[string]interface{}{})
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not supported")
	})
}
package manager

import (
	"code.byted.org/ies/codin/common/semantic/codebase/manager/processors"

	// 导入语言包触发自动注册
	_ "code.byted.org/ies/codin/common/semantic/codebase/manager/processors/languages/golang"
	_ "code.byted.org/ies/codin/common/semantic/codebase/manager/processors/languages/kotlin"
	_ "code.byted.org/ies/codin/common/semantic/codebase/manager/processors/languages/swift"
	_ "code.byted.org/ies/codin/common/semantic/codebase/manager/processors/languages/typescript"
)

// BuildChunkContent 构建YAML格式的文本块内容
// 保持原有API接口不变，内部调用新的模块化实现
func BuildChunkContent(entityID, entityType string, attributes map[string]interface{}) (string, error) {
	return processors.BuildChunk(entityID, entityType, attributes)
}

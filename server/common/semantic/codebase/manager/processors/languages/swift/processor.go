package swift

import (
	"fmt"
	"sort"

	"code.byted.org/ies/codin/common/semantic/codebase/manager/processors/common"
)

// SwiftProcessor Swift语言处理器
type SwiftProcessor struct{}

// NewSwiftProcessor 创建Swift处理器
func NewSwiftProcessor() *SwiftProcessor {
	return &SwiftProcessor{}
}

// GetLanguage 获取语言名称
func (sp *SwiftProcessor) GetLanguage() string {
	return "swift"
}

// GetSupportedEntityTypes 获取支持的实体类型
func (sp *SwiftProcessor) GetSupportedEntityTypes() []string {
	return []string{"file", "class", "struct", "enum", "protocol", "extension", "function", "fileTopLevel"}
}

// ProcessEntity 处理实体
func (sp *SwiftProcessor) ProcessEntity(entityType string, attributes map[string]interface{}) (string, error) {
	builder := NewSwiftBuilder()
	common.UpdateFilePathIfPresent(builder.BaseBuilder, attributes)

	switch entityType {
	case "file":
		return sp.processFile(builder, attributes)
	case "class":
		return sp.processClass(builder, attributes)
	case "struct":
		return sp.processStruct(builder, attributes)
	case "enum":
		return sp.processEnum(builder, attributes)
	case "protocol":
		return sp.processProtocol(builder, attributes)
	case "extension":
		return sp.processExtension(builder, attributes)
	case "function":
		return sp.processFunction(builder, attributes)
	case "fileTopLevel":
		return sp.processFileTopLevel(builder, attributes)
	default:
		return "", fmt.Errorf("entity type '%s' is not supported by swift processor", entityType)
	}
}

// processFile 处理文件
func (sp *SwiftProcessor) processFile(builder *SwiftBuilder, attributes map[string]interface{}) (string, error) {
	return builder.Build()
}

// processClass 处理类
func (sp *SwiftProcessor) processClass(builder *SwiftBuilder, attributes map[string]interface{}) (string, error) {
	extractor := common.NewAttributeExtractor(attributes)

	classInfo := &SwiftClassInfo{
		TypeInfo: common.TypeInfo{
			BaseInfo: common.BaseInfo{
				Name:    extractor.GetString("name"),
				Content: extractor.GetString("content"),
			},
		},
		SuperClass:  extractor.GetString("superClass"),
		IsFinal:     extractor.GetBool("isFinal"),
		IsGeneric:   extractor.GetBool("isGeneric"),
		AccessLevel: extractor.GetString("accessLevel"),
	}

	// 处理协议
	if protocols := extractor.GetStringArray("protocols"); protocols != nil {
		classInfo.Protocols = protocols
	}

	// 处理泛型参数
	if genericParams := extractor.GetStringArray("genericParameters"); genericParams != nil {
		classInfo.GenericParams = genericParams
	}

	// 处理方法
	if methodsMap := extractor.GetMap("methods"); methodsMap != nil {
		for methodName, methodValue := range methodsMap {
			if method, ok := methodValue.(map[string]interface{}); ok {
				funcInfo := common.ExtractFunctionInfo(method)
				funcInfo.Name = methodName
				classInfo.Methods = append(classInfo.Methods, *funcInfo)
			}
		}
	}

	// 处理属性
	if propertiesMap := extractor.GetMap("properties"); propertiesMap != nil {
		// 获取排序后的键名以确保稳定的顺序
		propKeys := make([]string, 0, len(propertiesMap))
		for key := range propertiesMap {
			propKeys = append(propKeys, key)
		}
		sort.Strings(propKeys)

		for _, key := range propKeys {
			propValue := propertiesMap[key]
			if property, ok := propValue.(map[string]interface{}); ok {
				varInfo := common.ExtractVariableInfo(property)
				varInfo.Name = key
				classInfo.Properties = append(classInfo.Properties, *varInfo)
			}
		}
	}

	builder.SetClass(classInfo)
	return builder.Build()
}

// processStruct 处理结构体
func (sp *SwiftProcessor) processStruct(builder *SwiftBuilder, attributes map[string]interface{}) (string, error) {
	extractor := common.NewAttributeExtractor(attributes)

	structInfo := &SwiftStructInfo{
		TypeInfo: common.TypeInfo{
			BaseInfo: common.BaseInfo{
				Name:    extractor.GetString("name"),
				Content: extractor.GetString("content"),
			},
		},
		IsGeneric:   extractor.GetBool("isGeneric"),
		AccessLevel: extractor.GetString("accessLevel"),
	}

	// 处理协议
	if protocols := extractor.GetStringArray("protocols"); protocols != nil {
		structInfo.Protocols = protocols
	}

	// 处理泛型参数
	if genericParams := extractor.GetStringArray("genericParameters"); genericParams != nil {
		structInfo.GenericParams = genericParams
	}

	// 处理方法
	if methodsMap := extractor.GetMap("methods"); methodsMap != nil {
		for methodName, methodValue := range methodsMap {
			if method, ok := methodValue.(map[string]interface{}); ok {
				funcInfo := common.ExtractFunctionInfo(method)
				funcInfo.Name = methodName
				structInfo.Methods = append(structInfo.Methods, *funcInfo)
			}
		}
	}

	// 处理属性
	if propertiesMap := extractor.GetMap("properties"); propertiesMap != nil {
		// 获取排序后的键名以确保稳定的顺序
		propKeys := make([]string, 0, len(propertiesMap))
		for key := range propertiesMap {
			propKeys = append(propKeys, key)
		}
		sort.Strings(propKeys)

		for _, key := range propKeys {
			propValue := propertiesMap[key]
			if property, ok := propValue.(map[string]interface{}); ok {
				varInfo := common.ExtractVariableInfo(property)
				varInfo.Name = key
				structInfo.Properties = append(structInfo.Properties, *varInfo)
			}
		}
	}

	builder.SetStruct(structInfo)
	return builder.Build()
}

// processEnum 处理枚举
func (sp *SwiftProcessor) processEnum(builder *SwiftBuilder, attributes map[string]interface{}) (string, error) {
	extractor := common.NewAttributeExtractor(attributes)

	enumInfo := &SwiftEnumInfo{
		BaseInfo: common.BaseInfo{
			Name:    extractor.GetString("name"),
			Content: extractor.GetString("content"),
		},
		RawValueType: extractor.GetString("rawValueType"),
		IsGeneric:    extractor.GetBool("isGeneric"),
		AccessLevel:  extractor.GetString("accessLevel"),
	}

	// 处理协议
	if protocols := extractor.GetStringArray("protocols"); protocols != nil {
		enumInfo.Protocols = protocols
	}

	// 处理泛型参数
	if genericParams := extractor.GetStringArray("genericParameters"); genericParams != nil {
		enumInfo.GenericParams = genericParams
	}

	// 处理枚举案例
	if casesMap := extractor.GetMap("cases"); casesMap != nil {
		for caseName, caseValue := range casesMap {
			if caseData, ok := caseValue.(map[string]interface{}); ok {
				caseExtractor := common.NewAttributeExtractor(caseData)

				enumCase := SwiftEnumCase{
					BaseInfo: common.BaseInfo{
						Name:    caseName,
						Content: caseExtractor.GetString("content"),
					},
					RawValue: caseExtractor.GetString("rawValue"),
				}

				// 处理关联值类型
				if associatedValueTypes := caseExtractor.GetStringArray("associatedValueTypes"); associatedValueTypes != nil {
					enumCase.AssociatedValueTypes = associatedValueTypes
				}

				enumInfo.Cases = append(enumInfo.Cases, enumCase)
			}
		}
	}

	// 处理方法
	if methodsMap := extractor.GetMap("methods"); methodsMap != nil {
		for methodName, methodValue := range methodsMap {
			if method, ok := methodValue.(map[string]interface{}); ok {
				funcInfo := common.ExtractFunctionInfo(method)
				funcInfo.Name = methodName
				enumInfo.Methods = append(enumInfo.Methods, *funcInfo)
			}
		}
	}

	// 处理属性
	if propertiesMap := extractor.GetMap("properties"); propertiesMap != nil {
		for propName, propValue := range propertiesMap {
			if property, ok := propValue.(map[string]interface{}); ok {
				varInfo := common.ExtractVariableInfo(property)
				varInfo.Name = propName
				enumInfo.Properties = append(enumInfo.Properties, *varInfo)
			}
		}
	}

	builder.SetEnum(enumInfo)
	return builder.Build()
}

// processProtocol 处理协议
func (sp *SwiftProcessor) processProtocol(builder *SwiftBuilder, attributes map[string]interface{}) (string, error) {
	extractor := common.NewAttributeExtractor(attributes)

	protocolInfo := &SwiftProtocolInfo{
		BaseInfo: common.BaseInfo{
			Name:    extractor.GetString("name"),
			Content: extractor.GetString("content"),
		},
	}

	builder.SetProtocol(protocolInfo)
	return builder.Build()
}

// processExtension 处理扩展
func (sp *SwiftProcessor) processExtension(builder *SwiftBuilder, attributes map[string]interface{}) (string, error) {
	extractor := common.NewAttributeExtractor(attributes)

	extensionInfo := &SwiftExtensionInfo{
		BaseInfo: common.BaseInfo{
			Name:    extractor.GetString("name"),
			Content: extractor.GetString("content"),
		},
		ExtendedType: extractor.GetString("extendedType"),
	}

	// 处理协议
	if protocols := extractor.GetStringArray("protocols"); protocols != nil {
		extensionInfo.Protocols = protocols
	}

	// 处理泛型约束
	if genericConstraints := extractor.GetStringArray("genericConstraints"); genericConstraints != nil {
		extensionInfo.GenericConstraints = genericConstraints
	}

	// 处理方法
	if methodsMap := extractor.GetMap("methods"); methodsMap != nil {
		for methodName, methodValue := range methodsMap {
			if method, ok := methodValue.(map[string]interface{}); ok {
				funcInfo := common.ExtractFunctionInfo(method)
				funcInfo.Name = methodName
				extensionInfo.Methods = append(extensionInfo.Methods, *funcInfo)
			}
		}
	}

	// 处理属性
	if propertiesMap := extractor.GetMap("properties"); propertiesMap != nil {
		for propName, propValue := range propertiesMap {
			if property, ok := propValue.(map[string]interface{}); ok {
				varInfo := common.ExtractVariableInfo(property)
				varInfo.Name = propName
				extensionInfo.Properties = append(extensionInfo.Properties, *varInfo)
			}
		}
	}

	builder.SetExtension(extensionInfo)
	return builder.Build()
}

// processFunction 处理函数
func (sp *SwiftProcessor) processFunction(builder *SwiftBuilder, attributes map[string]interface{}) (string, error) {
	funcInfo := common.ExtractFunctionInfo(attributes)
	builder.SetFunction(funcInfo)
	return builder.Build()
}

// processFileTopLevel 处理文件顶级元素
func (sp *SwiftProcessor) processFileTopLevel(builder *SwiftBuilder, attributes map[string]interface{}) (string, error) {
	extractor := common.NewAttributeExtractor(attributes)

	// 处理变量
	var variables []common.VariableInfo
	if variablesMap := extractor.GetMap("variables"); variablesMap != nil {
		for varName, varValue := range variablesMap {
			if variable, ok := varValue.(map[string]interface{}); ok {
				varInfo := common.ExtractVariableInfo(variable)
				varInfo.Name = varName
				variables = append(variables, *varInfo)
			}
		}
	}
	builder.SetVariables(variables)

	// 处理枚举
	if enumsMap := extractor.GetMap("enums"); enumsMap != nil {
		for _, enumValue := range enumsMap {
			if enumData, ok := enumValue.(map[string]interface{}); ok {
				enumExtractor := common.NewAttributeExtractor(enumData)

				enumInfo := SwiftEnumInfo{
					BaseInfo: common.BaseInfo{
						Name:    enumExtractor.GetString("name"),
						Content: enumExtractor.GetString("content"),
					},
				}

				builder.AddEnum(enumInfo)
			}
		}
	}

	// 处理协议
	if protocolsMap := extractor.GetMap("protocols"); protocolsMap != nil {
		for _, protocolValue := range protocolsMap {
			if protocolData, ok := protocolValue.(map[string]interface{}); ok {
				protocolExtractor := common.NewAttributeExtractor(protocolData)

				protocolInfo := SwiftProtocolInfo{
					BaseInfo: common.BaseInfo{
						Name:    protocolExtractor.GetString("name"),
						Content: protocolExtractor.GetString("content"),
					},
				}

				builder.AddProtocol(protocolInfo)
			}
		}
	}

	// 处理扩展
	if extensionsMap := extractor.GetMap("extensions"); extensionsMap != nil {
		for _, extensionValue := range extensionsMap {
			if extensionData, ok := extensionValue.(map[string]interface{}); ok {
				extensionExtractor := common.NewAttributeExtractor(extensionData)

				extensionInfo := SwiftExtensionInfo{
					BaseInfo: common.BaseInfo{
						Name:    extensionExtractor.GetString("name"),
						Content: extensionExtractor.GetString("content"),
					},
					ExtendedType: extensionExtractor.GetString("extendedType"),
				}

				builder.AddExtension(extensionInfo)
			}
		}
	}

	return builder.Build()
}

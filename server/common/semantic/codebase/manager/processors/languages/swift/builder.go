package swift

import (
	"code.byted.org/ies/codin/common/semantic/codebase/manager/processors/common"
)

// SwiftBuilder Swift语言专用构建器
type SwiftBuilder struct {
	*common.BaseBuilder
}

// NewSwiftBuilder 创建Swift构建器
func NewSwiftBuilder() *SwiftBuilder {
	return &SwiftBuilder{
		BaseBuilder: common.NewBaseBuilder(),
	}
}

// SetClass 设置类信息
func (sb *SwiftBuilder) SetClass(classInfo *SwiftClassInfo) *SwiftBuilder {
	if classInfo != nil {
		sb.AddCustomField("class", classInfo)
	}
	return sb
}

// SetStruct 设置结构体信息
func (sb *SwiftBuilder) SetStruct(structInfo *SwiftStructInfo) *SwiftBuilder {
	if structInfo != nil {
		sb.AddCustomField("struct", structInfo)
	}
	return sb
}

// SetEnum 设置枚举信息
func (sb *SwiftBuilder) SetEnum(enumInfo *SwiftEnumInfo) *SwiftBuilder {
	if enumInfo != nil {
		sb.AddCustomField("enum", enumInfo)
	}
	return sb
}

// SetProtocol 设置协议信息
func (sb *SwiftBuilder) SetProtocol(protocolInfo *SwiftProtocolInfo) *SwiftBuilder {
	if protocolInfo != nil {
		sb.AddCustomField("protocol", protocolInfo)
	}
	return sb
}

// SetExtension 设置扩展信息
func (sb *SwiftBuilder) SetExtension(extensionInfo *SwiftExtensionInfo) *SwiftBuilder {
	if extensionInfo != nil {
		sb.AddCustomField("extension", extensionInfo)
	}
	return sb
}

// AddEnum 添加枚举信息
func (sb *SwiftBuilder) AddEnum(enumInfo SwiftEnumInfo) *SwiftBuilder {
	if sb.GetData()["enums"] == nil {
		sb.AddCustomField("enums", []SwiftEnumInfo{})
	}
	enums := sb.GetData()["enums"].([]SwiftEnumInfo)
	sb.AddCustomField("enums", append(enums, enumInfo))
	return sb
}

// AddProtocol 添加协议信息
func (sb *SwiftBuilder) AddProtocol(protocolInfo SwiftProtocolInfo) *SwiftBuilder {
	if sb.GetData()["protocols"] == nil {
		sb.AddCustomField("protocols", []SwiftProtocolInfo{})
	}
	protocols := sb.GetData()["protocols"].([]SwiftProtocolInfo)
	sb.AddCustomField("protocols", append(protocols, protocolInfo))
	return sb
}

// AddExtension 添加扩展信息
func (sb *SwiftBuilder) AddExtension(extensionInfo SwiftExtensionInfo) *SwiftBuilder {
	if sb.GetData()["extensions"] == nil {
		sb.AddCustomField("extensions", []SwiftExtensionInfo{})
	}
	extensions := sb.GetData()["extensions"].([]SwiftExtensionInfo)
	sb.AddCustomField("extensions", append(extensions, extensionInfo))
	return sb
}

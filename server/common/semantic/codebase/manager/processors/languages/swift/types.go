package swift

import (
	"code.byted.org/ies/codin/common/semantic/codebase/manager/processors/common"
)

// SwiftClassInfo Swift类信息
type SwiftClassInfo struct {
	common.TypeInfo `yaml:",inline"`
	SuperClass      string   `yaml:"super_class,omitempty"`
	Protocols       []string `yaml:"protocols,omitempty"`
	IsFinal         bool     `yaml:"is_final,omitempty"`
	IsGeneric       bool     `yaml:"is_generic,omitempty"`
	GenericParams   []string `yaml:"generic_parameters,omitempty"`
	AccessLevel     string   `yaml:"access_level,omitempty"`
}

// SwiftStructInfo Swift结构体信息
type SwiftStructInfo struct {
	common.TypeInfo `yaml:",inline"`
	Protocols       []string `yaml:"protocols,omitempty"`
	IsGeneric       bool     `yaml:"is_generic,omitempty"`
	GenericParams   []string `yaml:"generic_parameters,omitempty"`
	AccessLevel     string   `yaml:"access_level,omitempty"`
}

// SwiftEnumInfo Swift枚举信息
type SwiftEnumInfo struct {
	common.BaseInfo `yaml:",inline"`
	Protocols       []string              `yaml:"protocols,omitempty"`
	AccessLevel     string                `yaml:"access_level,omitempty"`
	RawValueType    string                `yaml:"raw_value_type,omitempty"`
	IsGeneric       bool                  `yaml:"is_generic,omitempty"`
	GenericParams   []string              `yaml:"generic_parameters,omitempty"`
	Cases           []SwiftEnumCase       `yaml:"cases,omitempty"`
	Methods         []common.FunctionInfo `yaml:"methods,omitempty"`
	Properties      []common.VariableInfo `yaml:"properties,omitempty"`
}

// SwiftEnumCase Swift枚举案例信息
type SwiftEnumCase struct {
	common.BaseInfo      `yaml:",inline"`
	AssociatedValueTypes []string `yaml:"associated_value_types,omitempty"`
	RawValue             string   `yaml:"raw_value,omitempty"`
}

// SwiftProtocolInfo Swift协议信息
type SwiftProtocolInfo struct {
	common.BaseInfo `yaml:",inline"`
}

// SwiftExtensionInfo Swift扩展信息
type SwiftExtensionInfo struct {
	common.BaseInfo    `yaml:",inline"`
	ExtendedType       string                `yaml:"extended_type,omitempty"`
	Protocols          []string              `yaml:"protocols,omitempty"`
	GenericConstraints []string              `yaml:"generic_constraints,omitempty"`
	Methods            []common.FunctionInfo `yaml:"methods,omitempty"`
	Properties         []common.VariableInfo `yaml:"properties,omitempty"`
}

package kotlin

import (
	"code.byted.org/ies/codin/common/semantic/codebase/manager/processors/common"
)

// KotlinBuilder Kotlin语言专用构建器
type KotlinBuilder struct {
	*common.BaseBuilder
}

// NewKotlinBuilder 创建Kotlin构建器
func NewKotlinBuilder() *KotlinBuilder {
	return &KotlinBuilder{
		BaseBuilder: common.NewBaseBuilder(),
	}
}

// SetClass 设置类信息
func (kb *KotlinBuilder) SetClass(classInfo *ClassInfo) *KotlinBuilder {
	if classInfo != nil {
		kb.AddCustomField("class", classInfo)
	}
	return kb
}

// SetInterface 设置接口信息
func (kb *KotlinBuilder) SetInterface(interfaceInfo *InterfaceInfo) *KotlinBuilder {
	if interfaceInfo != nil {
		kb.AddCustomField("interface", interfaceInfo)
	}
	return kb
}

// AddInterface 添加接口信息
func (kb *KotlinBuilder) AddInterface(interfaceInfo InterfaceInfo) *KotlinBuilder {
	if kb.GetData()["interfaces"] == nil {
		kb.AddCustomField("interfaces", []InterfaceInfo{})
	}
	interfaces := kb.GetData()["interfaces"].([]InterfaceInfo)
	kb.AddCustomField("interfaces", append(interfaces, interfaceInfo))
	return kb
}

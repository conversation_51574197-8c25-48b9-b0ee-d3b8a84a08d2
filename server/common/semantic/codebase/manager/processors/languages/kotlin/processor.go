package kotlin

import (
	"sort"

	"code.byted.org/ies/codin/common/semantic/codebase/manager/processors/common"
)

// KotlinProcessor Kotlin语言处理器
type KotlinProcessor struct{}

// NewKotlinProcessor 创建Kotlin处理器
func NewKotlinProcessor() *KotlinProcessor {
	return &KotlinProcessor{}
}

// GetLanguage 获取语言名称
func (kp *KotlinProcessor) GetLanguage() string {
	return "kotlin"
}

// GetSupportedEntityTypes 获取支持的实体类型
func (kp *KotlinProcessor) GetSupportedEntityTypes() []string {
	return []string{"file", "class", "function", "fileTopLevel"}
}

// ProcessEntity 处理实体
func (kp *KotlinProcessor) ProcessEntity(entityType string, attributes map[string]interface{}) (string, error) {
	builder := NewKotlinBuilder()
	common.UpdateFilePathIfPresent(builder.BaseBuilder, attributes)

	switch entityType {
	case "file":
		return kp.processFile(builder, attributes)
	case "class":
		return kp.processClass(builder, attributes)
	case "function":
		return kp.processFunction(builder, attributes)
	case "fileTopLevel":
		return kp.processFileTopLevel(builder, attributes)
	default:
		return "", nil
	}
}

// processFile 处理文件
func (kp *KotlinProcessor) processFile(builder *KotlinBuilder, attributes map[string]interface{}) (string, error) {
	return builder.Build()
}

// processClass 处理类
func (kp *KotlinProcessor) processClass(builder *KotlinBuilder, attributes map[string]interface{}) (string, error) {
	extractor := common.NewAttributeExtractor(attributes)

	classInfo := &ClassInfo{
		TypeInfo: common.TypeInfo{
			BaseInfo: common.BaseInfo{
				Name: extractor.GetString("name"),
			},
		},
	}

	// 处理方法
	if methodsMap := extractor.GetMap("methods"); methodsMap != nil {
		for methodName, methodValue := range methodsMap {
			if method, ok := methodValue.(map[string]interface{}); ok {
				funcInfo := common.ExtractFunctionInfo(method)
				funcInfo.Name = methodName
				classInfo.Methods = append(classInfo.Methods, *funcInfo)
			}
		}
	}

	// 处理属性
	if propertiesMap := extractor.GetMap("properties"); propertiesMap != nil {
		// 获取排序后的键名以确保稳定的顺序
		propKeys := make([]string, 0, len(propertiesMap))
		for key := range propertiesMap {
			propKeys = append(propKeys, key)
		}
		sort.Strings(propKeys)

		for _, key := range propKeys {
			propValue := propertiesMap[key]
			if property, ok := propValue.(map[string]interface{}); ok {
				varInfo := common.ExtractVariableInfo(property)
				varInfo.Name = key
				classInfo.Properties = append(classInfo.Properties, *varInfo)
			}
		}
	}

	builder.SetClass(classInfo)
	return builder.Build()
}

// processFunction 处理函数
func (kp *KotlinProcessor) processFunction(builder *KotlinBuilder, attributes map[string]interface{}) (string, error) {
	extractor := common.NewAttributeExtractor(attributes)

	// 检查是否为类方法
	methodType := extractor.GetString("type")
	if methodType == "ClassMethod" {
		// 从ID中提取类名
		entityID := extractor.GetString("id")
		className := common.ExtractClassNameFromID(entityID)

		if className == "" {
			// 如果无法提取类名，回退到普通函数处理
			funcInfo := common.ExtractFunctionInfo(attributes)
			builder.SetFunction(funcInfo)
			return builder.Build()
		}

		// 创建类信息并添加方法
		funcInfo := common.ExtractFunctionInfo(attributes)
		classInfo := &ClassInfo{
			TypeInfo: common.TypeInfo{
				BaseInfo: common.BaseInfo{
					Name: className,
				},
				Methods: []common.FunctionInfo{*funcInfo},
			},
		}

		builder.SetClass(classInfo)
	} else {
		// 处理普通函数
		funcInfo := common.ExtractFunctionInfo(attributes)
		builder.SetFunction(funcInfo)
	}

	return builder.Build()
}

// processFileTopLevel 处理文件顶级元素（复用Go的实现，因为逻辑相同）
func (kp *KotlinProcessor) processFileTopLevel(builder *KotlinBuilder, attributes map[string]interface{}) (string, error) {
	extractor := common.NewAttributeExtractor(attributes)

	var variables []common.VariableInfo

	// 处理变量
	if variablesMap := extractor.GetMap("variables"); variablesMap != nil {
		for varName, varValue := range variablesMap {
			if variable, ok := varValue.(map[string]interface{}); ok {
				varInfo := common.ExtractVariableInfo(variable)
				varInfo.Name = varName
				variables = append(variables, *varInfo)
			}
		}
	}

	builder.SetVariables(variables)

	// 处理接口
	if interfacesMap := extractor.GetMap("interfaces"); interfacesMap != nil {
		for _, interfaceValue := range interfacesMap {
			if interfaceData, ok := interfaceValue.(map[string]interface{}); ok {
				interfaceExtractor := common.NewAttributeExtractor(interfaceData)

				interfaceInfo := InterfaceInfo{
					BaseInfo: common.BaseInfo{
						Name: interfaceExtractor.GetString("name"),
					},
				}

				// 处理接口属性
				if propertiesMap := interfaceExtractor.GetMap("properties"); propertiesMap != nil {
					// 获取排序后的键名以确保稳定的顺序
					propKeys := make([]string, 0, len(propertiesMap))
					for key := range propertiesMap {
						propKeys = append(propKeys, key)
					}
					sort.Strings(propKeys)

					for _, key := range propKeys {
						propValue := propertiesMap[key]
						if prop, ok := propValue.(map[string]interface{}); ok {
							propExtractor := common.NewAttributeExtractor(prop)
							interfaceInfo.Properties = append(interfaceInfo.Properties, common.Parameter{
								Name:    propExtractor.GetString("name"),
								Type:    propExtractor.GetString("type"),
								Content: propExtractor.GetString("content"),
							})
						}
					}
				}

				builder.AddInterface(interfaceInfo)
			}
		}
	}

	return builder.Build()
}

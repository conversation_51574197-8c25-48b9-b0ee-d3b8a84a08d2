package golang

import (
	"fmt"

	"code.byted.org/ies/codin/common/semantic/codebase/manager/processors/common"
)

// GoProcessor Go语言处理器
type GoProcessor struct{}

// NewGoProcessor 创建Go处理器
func NewGoProcessor() *GoProcessor {
	return &GoProcessor{}
}

// GetLanguage 获取语言名称
func (gp *GoProcessor) GetLanguage() string {
	return "golang"
}

// GetSupportedEntityTypes 获取支持的实体类型
func (gp *GoProcessor) GetSupportedEntityTypes() []string {
	return []string{"file", "struct", "function", "fileTopLevel"}
}

// ProcessEntity 处理实体
func (gp *GoProcessor) ProcessEntity(entityType string, attributes map[string]interface{}) (string, error) {
	builder := NewGoBuilder()
	common.UpdateFilePathIfPresent(builder.BaseBuilder, attributes)

	switch entityType {
	case "file":
		return gp.processFile(builder, attributes)
	case "struct":
		return gp.processStruct(builder, attributes)
	case "function":
		return gp.processFunction(builder, attributes)
	case "fileTopLevel":
		return gp.processFileTopLevel(builder, attributes)
	default:
		return "", fmt.Errorf("entity type '%s' is not supported by golang processor", entityType)
	}
}

// processFile 处理文件
func (gp *GoProcessor) processFile(builder *GoBuilder, attributes map[string]interface{}) (string, error) {
	return builder.Build()
}

// processStruct 处理结构体
func (gp *GoProcessor) processStruct(builder *GoBuilder, attributes map[string]interface{}) (string, error) {
	extractor := common.NewAttributeExtractor(attributes)
	structInfo := &StructInfo{
		BaseInfo: common.BaseInfo{
			Name:    extractor.GetString("name"),
			Content: extractor.GetString("content"),
		},
	}
	builder.SetStruct(structInfo)
	return builder.Build()
}

// processFunction 处理函数
func (gp *GoProcessor) processFunction(builder *GoBuilder, attributes map[string]interface{}) (string, error) {
	funcInfo := common.ExtractFunctionInfo(attributes)
	builder.SetFunction(funcInfo)
	return builder.Build()
}

// processFileTopLevel 处理文件顶级元素
func (gp *GoProcessor) processFileTopLevel(builder *GoBuilder, attributes map[string]interface{}) (string, error) {
	extractor := common.NewAttributeExtractor(attributes)

	var variables []common.VariableInfo

	// 处理变量
	if variablesMap := extractor.GetMap("variables"); variablesMap != nil {
		for varName, varValue := range variablesMap {
			if variable, ok := varValue.(map[string]interface{}); ok {
				varInfo := common.ExtractVariableInfo(variable)
				varInfo.Name = varName
				variables = append(variables, *varInfo)
			}
		}
	}

	builder.SetVariables(variables)

	// 处理接口，只保留 name 和 content 字段
	if interfacesMap := extractor.GetMap("interfaces"); interfacesMap != nil {
		for _, interfaceValue := range interfacesMap {
			if interfaceData, ok := interfaceValue.(map[string]interface{}); ok {
				interfaceExtractor := common.NewAttributeExtractor(interfaceData)

				interfaceInfo := InterfaceInfo{
					BaseInfo: common.BaseInfo{
						Name:    interfaceExtractor.GetString("name"),
						Content: interfaceExtractor.GetString("content"),
					},
				}

				builder.AddInterface(interfaceInfo)
			}
		}
	}

	return builder.Build()
}

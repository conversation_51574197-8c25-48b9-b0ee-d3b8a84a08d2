package golang

import (
	"code.byted.org/ies/codin/common/semantic/codebase/manager/processors/common"
)

// GoBuilder Go语言专用构建器
type GoBuilder struct {
	*common.BaseBuilder
}

// NewGoBuilder 创建Go构建器
func NewGoBuilder() *GoBuilder {
	return &GoBuilder{
		BaseBuilder: common.NewBaseBuilder(),
	}
}

// SetStruct 设置结构体信息
func (gb *GoBuilder) SetStruct(structInfo *StructInfo) *GoBuilder {
	if structInfo != nil {
		gb.AddCustomField("struct", structInfo)
	}
	return gb
}

// SetInterface 设置接口信息
func (gb *GoBuilder) SetInterface(interfaceInfo *InterfaceInfo) *GoBuilder {
	if interfaceInfo != nil {
		gb.AddCustomField("interface", interfaceInfo)
	}
	return gb
}

// AddInterface 添加接口信息
func (gb *GoBuilder) AddInterface(interfaceInfo InterfaceInfo) *GoBuilder {
	if gb.GetData()["interfaces"] == nil {
		gb.AddCustomField("interfaces", []InterfaceInfo{})
	}
	interfaces := gb.GetData()["interfaces"].([]InterfaceInfo)
	gb.AddCustomField("interfaces", append(interfaces, interfaceInfo))
	return gb
}

package typescript

import (
	"code.byted.org/ies/codin/common/semantic/codebase/manager/processors/common"
)

// TSBuilder TypeScript语言专用构建器
type TSBuilder struct {
	*common.BaseBuilder
}

// NewTSBuilder 创建TypeScript构建器
func NewTSBuilder() *TSBuilder {
	return &TSBuilder{
		BaseBuilder: common.NewBaseBuilder(),
	}
}

// SetClass 设置类信息
func (tsb *TSBuilder) SetClass(classInfo *ClassInfo) *TSBuilder {
	if classInfo != nil {
		tsb.AddCustomField("class", classInfo)
	}
	return tsb
}

// SetInterface 设置接口信息
func (tsb *TSBuilder) SetInterface(interfaceInfo *InterfaceInfo) *TSBuilder {
	if interfaceInfo != nil {
		tsb.AddCustomField("interface", interfaceInfo)
	}
	return tsb
}

// AddInterface 添加接口信息
func (tsb *TSBuilder) AddInterface(interfaceInfo InterfaceInfo) *TSBuilder {
	if tsb.GetData()["interfaces"] == nil {
		tsb.AddCustomField("interfaces", []InterfaceInfo{})
	}
	interfaces := tsb.GetData()["interfaces"].([]InterfaceInfo)
	tsb.AddCustomField("interfaces", append(interfaces, interfaceInfo))
	return tsb
}

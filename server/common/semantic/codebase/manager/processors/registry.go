package processors

import (
	"errors"
	"fmt"
)

// LanguageRegistry 语言注册表
type LanguageRegistry struct {
	processors map[string]LanguageProcessor
}

// NewLanguageRegistry 创建语言注册表
func NewLanguageRegistry() *LanguageRegistry {
	return &LanguageRegistry{
		processors: make(map[string]LanguageProcessor),
	}
}

// RegisterLanguage 注册语言处理器
func (lr *LanguageRegistry) RegisterLanguage(processor LanguageProcessor) {
	lr.processors[processor.GetLanguage()] = processor
}

// GetLanguageProcessor 获取语言处理器
func (lr *LanguageRegistry) GetLanguageProcessor(language string) (LanguageProcessor, bool) {
	processor, exists := lr.processors[language]
	return processor, exists
}

// ProcessEntity 处理实体
func (lr *LanguageRegistry) ProcessEntity(entityID, entityType string, attributes map[string]interface{}) (string, error) {
	// 1. 检测语言
	language := DetectLanguage(entityID, attributes)

	// 2. 获取语言处理器
	processor, exists := lr.GetLanguageProcessor(language)
	if !exists {
		return "", errors.New(fmt.Sprintf("Language '%s' is not supported.", language))
	}

	// 3. 处理实体
	return processor.ProcessEntity(entityType, attributes)
}

// 全局语言注册表
var globalLanguageRegistry = NewLanguageRegistry()

// RegisterLanguage 全局注册语言处理器（供各语言包的init函数调用）
func RegisterLanguage(processor LanguageProcessor) {
	globalLanguageRegistry.RegisterLanguage(processor)
}

// GetLanguageProcessor 获取语言处理器（导出函数）
func GetLanguageProcessor(language string) (LanguageProcessor, bool) {
	return globalLanguageRegistry.GetLanguageProcessor(language)
}

// BuildChunk 构建YAML格式的文本块内容（新的入口函数）
func BuildChunk(entityID, entityType string, attributes map[string]interface{}) (string, error) {
	return globalLanguageRegistry.ProcessEntity(entityID, entityType, attributes)
}
package processors

import "strings"

// DetectLanguage 语言检测器
func DetectLanguage(entityID string, attributes map[string]interface{}) string {
	// 从文件路径检测语言
	if path, ok := attributes["path"].(string); ok {
		if strings.HasSuffix(path, ".go") {
			return "golang"
		}
		if strings.HasSuffix(path, ".ts") || strings.HasSuffix(path, ".tsx") {
			return "typescript"
		}
		if strings.HasSuffix(path, ".swift") {
			return "swift"
		}
		if strings.HasSuffix(path, ".kt") {
			return "kotlin"
		}
	}

	// 从entityID检测语言
	if strings.Contains(entityID, ".go") {
		return "golang"
	}
	if strings.Contains(entityID, ".ts") || strings.Contains(entityID, ".tsx") {
		return "typescript"
	}
	if strings.Contains(entityID, ".swift") {
		return "swift"
	}
	if strings.Contains(entityID, ".kt") {
		return "kotlin"
	}

	// 默认返回golang（向后兼容）
	return "golang"
}

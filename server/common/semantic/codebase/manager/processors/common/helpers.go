package common

import "strings"

// ExtractFunctionInfo 提取函数信息
func ExtractFunctionInfo(attributes map[string]interface{}) *FunctionInfo {
	extractor := NewAttributeExtractor(attributes)
	return &FunctionInfo{
		BaseInfo: BaseInfo{
			Name:    extractor.GetString("name"),
			Content: extractor.GetString("content"),
		},
		Parameters: extractor.ExtractParameters(),
		ReturnType: extractor.GetString("returnType"),
		Body:       extractor.GetBody(),
	}
}

// ExtractVariableInfo 提取变量信息
func ExtractVariableInfo(attributes map[string]interface{}) *VariableInfo {
	extractor := NewAttributeExtractor(attributes)
	return &VariableInfo{
		BaseInfo: BaseInfo{
			Name:    extractor.GetString("name"),
			Content: extractor.GetString("content"),
		},
		Type:         extractor.GetString("type"),
		InitialValue: extractor.GetString("initialValue"),
	}
}

// UpdateFilePathIfPresent 统一处理FilePath更新逻辑
func UpdateFilePathIfPresent(builder *BaseBuilder, attributes map[string]interface{}) {
	extractor := NewAttributeExtractor(attributes)
	if path := extractor.GetString("path"); path != "" {
		builder.SetFilePath(path)
	}
}

// ExtractClassNameFromID 从实体ID中提取类名
// ID格式: /path/to/file.tsx#ClassName#methodName
func ExtractClassNameFromID(id string) string {
	parts := strings.Split(id, "#")
	if len(parts) >= 2 {
		return parts[1]
	}
	return ""
}
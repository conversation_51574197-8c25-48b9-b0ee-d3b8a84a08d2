package common

import (
	"fmt"
	"strings"

	"gopkg.in/yaml.v3"
)

// BaseBuilder 通用Builder基类
type BaseBuilder struct {
	data map[string]interface{}
}

// NewBaseBuilder 创建新的基础构建器
func NewBaseBuilder() *BaseBuilder {
	return &BaseBuilder{
		data: make(map[string]interface{}),
	}
}

// SetFilePath 设置文件路径
func (bb *BaseBuilder) SetFilePath(path string) *BaseBuilder {
	if path != "" {
		bb.data["filePath"] = strings.Trim(path, "\"")
	}
	return bb
}

// SetName 设置名称
func (bb *BaseBuilder) SetName(name string) *BaseBuilder {
	if name != "" {
		bb.data["name"] = name
	}
	return bb
}

// SetPkgName 设置包名
func (bb *BaseBuilder) SetPkgName(pkgName string) *BaseBuilder {
	if pkgName != "" {
		bb.data["pkg_name"] = pkgName
	}
	return bb
}

// AddCustomField 添加自定义字段
func (bb *BaseBuilder) AddCustomField(key string, value interface{}) *BaseBuilder {
	if key != "" && value != nil {
		bb.data[key] = value
	}
	return bb
}

// SetFunction 设置函数信息
func (bb *BaseBuilder) SetFunction(funcInfo *FunctionInfo) *BaseBuilder {
	if funcInfo != nil {
		bb.data["function"] = funcInfo
	}
	return bb
}

// SetVariable 设置变量信息
func (bb *BaseBuilder) SetVariable(varInfo *VariableInfo) *BaseBuilder {
	if varInfo != nil {
		bb.data["variable"] = varInfo
	}
	return bb
}

// SetVariables 设置变量列表
func (bb *BaseBuilder) SetVariables(variables []VariableInfo) *BaseBuilder {
	if variables != nil {
		bb.data["variables"] = variables
	}
	return bb
}

// AddVariable 添加单个变量
func (bb *BaseBuilder) AddVariable(variable VariableInfo) *BaseBuilder {
	if bb.data["variables"] == nil {
		bb.data["variables"] = []VariableInfo{}
	}
	variables := bb.data["variables"].([]VariableInfo)
	bb.data["variables"] = append(variables, variable)
	return bb
}

// Build 构建最终的YAML内容
func (bb *BaseBuilder) Build() (string, error) {
	var node yaml.Node
	node.Kind = yaml.MappingNode

	// 1. 先插入 filePath
	if v, ok := bb.data["filePath"]; ok {
		node.Content = append(node.Content, &yaml.Node{Kind: yaml.ScalarNode, Value: "filePath"})
		valueNode := &yaml.Node{}
		err := valueNode.Encode(v)
		if err != nil {
			return "", fmt.Errorf("failed to encode filePath: %w", err)
		}
		node.Content = append(node.Content, valueNode)
	}

	// 2. 再插入其他字段
	for k, v := range bb.data {
		if k == "filePath" {
			continue
		}
		node.Content = append(node.Content, &yaml.Node{Kind: yaml.ScalarNode, Value: k})
		valueNode := &yaml.Node{}
		err := valueNode.Encode(v)
		if err != nil {
			// 添加更详细的错误信息
			return "", fmt.Errorf("failed to encode field '%s' with value type %T: %w", k, v, err)
		}
		node.Content = append(node.Content, valueNode)
	}

	out, err := yaml.Marshal(&node)
	if err != nil {
		return "", fmt.Errorf("failed to marshal chunk data to YAML: %w", err)
	}
	return string(out), nil
}

// GetData 获取原始数据
func (bb *BaseBuilder) GetData() map[string]interface{} {
	return bb.data
}
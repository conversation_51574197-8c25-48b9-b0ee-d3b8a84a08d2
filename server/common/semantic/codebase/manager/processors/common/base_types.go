package common

// BaseInfo 通用的基础信息
type BaseInfo struct {
	Name     string `yaml:"name,omitempty"`
	Content  string `yaml:"content,omitempty"`
	FilePath string `yaml:"filePath,omitempty"`
}

// FunctionInfo 通用的函数信息（90%语言都有函数）
type FunctionInfo struct {
	BaseInfo   `yaml:",inline"`
	Parameters map[string]string `yaml:"parameters,omitempty"`
	ReturnType string            `yaml:"returnType,omitempty"`
	Body       string            `yaml:"body,omitempty"`
}

// VariableInfo 通用的变量信息
type VariableInfo struct {
	BaseInfo     `yaml:",inline"`
	Type         string `yaml:"type,omitempty"`
	InitialValue string `yaml:"initialValue,omitempty"`
}

// TypeInfo 通用的类/结构体信息
type TypeInfo struct {
	BaseInfo   `yaml:",inline"`
	Methods    []FunctionInfo `yaml:"methods,omitempty"`
	Properties []VariableInfo `yaml:"properties,omitempty"`
}

// Parameter 参数信息
type Parameter struct {
	Name    string `yaml:"name,omitempty"`
	Type    string `yaml:"type,omitempty"`
	Content string `yaml:"content,omitempty"`
}
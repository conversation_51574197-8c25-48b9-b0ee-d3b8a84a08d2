package common

// AttributeExtractor 属性提取器辅助类
type AttributeExtractor struct {
	attributes map[string]interface{}
}

// NewAttributeExtractor 创建属性提取器
func NewAttributeExtractor(attributes map[string]interface{}) *AttributeExtractor {
	return &AttributeExtractor{attributes: attributes}
}

// GetString 安全获取字符串值
func (ae *AttributeExtractor) GetString(key string) string {
	if value, ok := ae.attributes[key].(string); ok {
		return value
	}
	return ""
}

// GetFloat64 安全获取float64值
func (ae *AttributeExtractor) GetFloat64(key string) float64 {
	if value, ok := ae.attributes[key].(float64); ok {
		return value
	}
	return 0
}

// GetBool 安全获取bool值
func (ae *AttributeExtractor) GetBool(key string) bool {
	if value, ok := ae.attributes[key].(bool); ok {
		return value
	}
	return false
}

// GetMap 安全获取map值
func (ae *AttributeExtractor) GetMap(key string) map[string]interface{} {
	if value, ok := ae.attributes[key].(map[string]interface{}); ok {
		return value
	}
	return nil
}

// GetSlice 安全获取slice值
func (ae *AttributeExtractor) GetSlice(key string) []interface{} {
	if value, ok := ae.attributes[key].([]interface{}); ok {
		return value
	}
	return nil
}

// GetStringArray 安全获取字符串数组
func (ae *AttributeExtractor) GetStringArray(key string) []string {
	if value, ok := ae.attributes[key].([]interface{}); ok {
		var result []string
		for _, item := range value {
			if str, ok := item.(string); ok {
				result = append(result, str)
			}
		}
		return result
	}
	return nil
}

// GetBody 安全获取 body 字段，处理特殊字符
func (ae *AttributeExtractor) GetBody() string {
	if value, ok := ae.attributes["body"].(string); ok {
		// 确保 body 字段不会导致 YAML 编码问题
		// 这里可以添加额外的清理逻辑如果需要
		return value
	}
	return ""
}

// ExtractParameters 提取参数信息
func (ae *AttributeExtractor) ExtractParameters() map[string]string {
	params := ae.GetSlice("parameters")
	if params == nil {
		return nil
	}

	parameters := make(map[string]string)
	for _, param := range params {
		if paramMap, ok := param.(map[string]interface{}); ok {
			extractor := NewAttributeExtractor(paramMap)
			name := extractor.GetString("name")
			paramType := extractor.GetString("type")
			if name != "" {
				parameters[name] = paramType
			}
		}
	}

	if len(parameters) == 0 {
		return nil
	}
	return parameters
}
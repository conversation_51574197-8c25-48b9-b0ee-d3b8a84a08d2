package manager

// ChunkData 定义chunk的结构化数据模型

type ChunkData struct {
	PkgName    string                 `yaml:"pkgName,omitempty"`
	FilePath   string                 `yaml:"filePath"`
	Name       string                 `yaml:"name,omitempty"`
	Function   *FunctionInfo          `yaml:"function,omitempty"`
	Variable   *VariableInfo          `yaml:"variable,omitempty"`
	Class      *ClassInfo             `yaml:"class,omitempty"`
	Struct     *StructInfo            `yaml:"struct,omitempty"`
	Variables  []VariableInfo         `yaml:"variables,omitempty"`
	Interfaces []InterfaceInfo        `yaml:"interfaces,omitempty"`
	Custom     map[string]interface{} `yaml:"custom,omitempty"`
}

// Parameter 参数信息
type Parameter struct {
	Name    string `yaml:"name"`
	Type    string `yaml:"type"`
	Content string `yaml:"content,omitempty"`
}

// FunctionInfo 函数信息
type FunctionInfo struct {
	Name       string            `yaml:"name,omitempty"`
	Parameters map[string]string `yaml:"parameters,omitempty"`
	ReturnType string            `yaml:"returnType,omitempty"`
	Body       string            `yaml:"body,omitempty"`
}

// VariableInfo 变量信息
type VariableInfo struct {
	Name            string `yaml:"name,omitempty"`
	Type            string `yaml:"type,omitempty"`
	InitialValue    string `yaml:"initialValue,omitempty"`
	IsClassProperty bool   `yaml:"isClassProperty,omitempty"`
	Content         string `yaml:"content,omitempty"`
}

// ClassInfo 类信息
type ClassInfo struct {
	Name       string         `yaml:"name,omitempty"`
	Methods    []FunctionInfo `yaml:"methods,omitempty"`
	Properties []VariableInfo `yaml:"properties,omitempty"`
}

// StructInfo 结构体信息
type StructInfo struct {
	Name    string `yaml:"name,omitempty"`
	Content string `yaml:"content,omitempty"`
}

// InterfaceInfo 接口信息
type InterfaceInfo struct {
	Name       string      `yaml:"name"`
	Properties []Parameter `yaml:"properties,omitempty"`
}

// ChunkBuilder 提供链式构建YAML结构化内容的能力
type ChunkBuilder struct {
	data *ChunkData
}

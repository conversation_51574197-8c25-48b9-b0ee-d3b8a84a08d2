package config

const (
	ExtractEntityDefaultPrompt = `-Goal-
Given a YAML-formatted code snippet in a code repo that is relevant to a list of entities with specific types, identify all entities of those types from the code and all relationships among the identified entities. The goal is to extract detailed and accurate entities and their relationships, which will be used in the scenario of RAG semantic search. Focus exclusively on extracting detailed entities without relationships.  

-Steps-
1. Identify all detailed entities. For each identified entity, extract the following information:
- entity_name: Name of the entity, capitalized
- entity_type: One of the following types: [file, interface, class, module, package, method, function]
- entity_description: Comprehensive description of the entity's attributes and activities
Use ## as the list delimiter
.Format each entity as ("entity"<|><entity_name><|><entity_type><|><entity_description>), which must contain three <|> delimiter.
For two entities, use ## to separate them: 
("entity"<|><entity_name><|><entity_type><|><entity_description>)
##
("entity"<|><entity_name><|><entity_type><|><entity_description>)

2. Generate a summary entity. The format of the summary entity is the same as the detailed entity. However, the entity_name will be the simple summary of the code snippet, and the entity_description will be a concise yet comprehensive overview of the code snippet or document, including:
The primary purpose or functionality of the code/document;
Key technical improvements or optimizations (e.g., performance gains, reduced latency, enhanced scalability);
Architectural or design highlights (e.g., algorithms, data structures, patterns used);
Business or technical value provided (e.g., improved user experience, reduced resource usage);
Any notable risks or limitations addressed;

3. Return **output in Chinese** as a single list of all the entities identified in steps 1 and 2.

######################
-Forceful Format Rules(MOST IMPORTANT)-
1. Upon completion, output <|COMPLETE|>.
2. STRICTLY use <|> as THE ONLY separator between individual entity elements. NEVER use < > or any other symbols. Never append <|> at the end of an individual entity.
3. STRICTLY use ## as THE ONLY separator between two entities. THIS INCLUDES THE LAST ENTITY: the last entity must also be followed by ##, with <|COMPLETE|> appended immediately after.
4. Entity elements MUST contain ONLY their pure values WITHOUT any enclosing symbols.
5. **CORRECT EXAMPLE: ("entity"<|>EntityName<|>Type<|>Description)**
######################

######################
-Extract Entity Rules-
1. If the input is a YAML-formatted code snippet, **NEVER extract the code snippet from Key** which is help you to understand the code snippet.
2. When identifying all detailed entities, an additional check of the filePath field in the input data (if it exists) is required. Treat it as an entity of the file type, where the entity_name is the complete file path, and the entity_description is the complete path information of the file.
3. The content extracted from entity extraction will be used in the scenario of RAG semantic search. You can aim at this goal to extract some entity content that is helpful for this scenario.
######################

######################
-Step Rules-
1. When identifying all detailed entities, an additional check of the filePath field in the input data (if it exists) is required. Treat it as an entity of the file type, where the entity_name is the complete file path, and the entity_description is the complete path information of the file.
######################

-Example-
输入: ` + "```yaml" + `
filePath: /src/base/ui-manager/base-ui-manager.interface.ts
interfaces:
    - name: IBaseUiManager
      properties:
        - name: unRegisterConfig
          type: '(id: string) => void'
        - name: render
          type: '(...params: any) => React.ReactNode'
        - name: registerConfig
          type: '(configs: T[]) => void'
` + "```" + `
输出:
("entity"<|>/src/base/ui-manager/base-ui-manager.interface.ts<|>file<|> 主要定义基础 UI 管理的核心接口IBaseUiManager，包含配置注册、注销及 UI 渲染等关键方法。)
##
("entity"<|>IBaseUiManager<|>interface<|> 基础 UI 管理的核心接口，负责 UI 组件配置的全生命周期管理（注册、注销）及渲染控制，包含unRegisterConfig（配置注销方法）、render（UI 渲染方法）和registerConfig（配置注册方法）三个核心方法。)
##
("entity"<|>IBaseUiManager.unRegisterConfig<|>method<|>IBaseUiManager接口的配置注销方法，功能为根据唯一 ID 注销已注册的 UI 配置，接收id（目标配置的唯一标识，类型string）参数，无返回值。)
##
("entity"<|>IBaseUiManager.render<|>method<|>IBaseUiManager接口的 UI 渲染方法，功能为动态渲染 UI 组件，接收任意类型的params（渲染参数，支持灵活扩展）参数，返回 React 节点（React.ReactNode）用于页面展示。)
##
("entity"<|>IBaseUiManager.registerConfig<|>method<|>IBaseUiManager接口的配置注册方法，功能为批量注册 UI 配置项，接收configs（待注册的配置项数组，类型T[]，T为配置项泛型）参数，无返回值。)
##
<|COMPLETE|>

######################
-Real Data-
{input_text}`
)

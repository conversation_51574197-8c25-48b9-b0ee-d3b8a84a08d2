package codeEntity

type CodeSearchResult struct {
	Description string         `json:"description"`
	Codes       []*CodeSnippet `json:"codes"`
}

type CodeSnippet struct {
	Content  string `json:"content"`
	FilePath string `json:"file_path"`
	Summary  string `json:"summary"`
	ChunkID  string `json:"chunk_id"`
}

type CodeRelationSnippet struct {
	ChunkID         string `json:"chunk_id"`
	Content         string `json:"content"`
	RelationType    string `json:"relation_type"`
	RelationSubType string `json:"relation_sub_type"`
}

type CodeEntity struct {
	EntityName string   `json:"entity_name"`
	EntityDesc string   `json:"entity_desc"`
	ChunkIDs   []string `json:"chunk_ids"`
	FilePath   []string `json:"file_paths"`
}

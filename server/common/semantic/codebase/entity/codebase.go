package codeEntity

import (
	"time"

	knowledgeEntity "code.byted.org/ies/codin/common/semantic/summary/entity"
)

// GetCodeRequest 获取代码请求参数
type GetCodeRequest struct {
	Query           string
	UserKnowledgeId string
	PathList        []string
}

type RelationType string

const (
	RelationTypeInvoke    RelationType = "invoke"
	RelationTypeInvokeBy  RelationType = "invokeBy"
	RelationTypeContain   RelationType = "contain"
	RelationTypeContainBy RelationType = "containBy"
	RelationTypeInherit   RelationType = "inherit"
	RelationTypeInheritBy RelationType = "inheritBy"
	RelationTypeImport    RelationType = "import"
	RelationTypeImportBy  RelationType = "importBy"
)

type RelationSubType string

// InterfaceToClass = 'ClassToInterface', // 接口到类 class implement interface
// MethodToClass = 'MethodToClass', // 类方法到class   () => { new Class() }
// PropertyToClass = 'PropertyToClass', // 属性到class  a = Class

// MethodToMethod = 'MethodToMethod', // 方法到方法  a = () => { b() }
// MethodToPropery = 'MethodToProperty', //   () => { c }
// PropertyToMethod = 'PropertyToMethod', // 属性到方法  a = b()
const (
	RelationSubTypeInterfaceToClass   RelationSubType = "ClassToInterface"
	RelationSubTypeMethodToClass      RelationSubType = "methodToClass"
	RelationSubTypePropertyToClass    RelationSubType = "propertyToClass"
	RelationSubTypeMethodToMethod     RelationSubType = "methodToMethod"
	RelationSubTypePropertyToProperty RelationSubType = "propertyToProperty"
	RelationSubTypeMethodToProperty   RelationSubType = "methodToProperty"
)

type GetCodeDependencyRequest struct {
	ChunkId         string
	RelationType    RelationType
	RelationSubType RelationSubType
	UserKnowledgeId string
}

type UploadChunkRequest struct {
	// origin user knowledge id
	OriginUserKnowledgeId string
	// rootHash
	RootMerkleId string
	// chunk 内容
	Content []byte
	// chunk 关系内容
	ChunkRelationContent []byte
	// 删除的 FileId
	DeletedFileIds []string
	// 用户 id
	Uid string
	// 仓库
	RepoName string
	// 分支
	Branch string
	// 路径
	RepoPath string
	// 设备id
	Did string
}

type QueryBuildRecordRequest struct {
	// 用户知识 id
	UserKnowledgeId string
	// 分支
	BranchName string
}

type FuzzyQueryBuildRecordRequest struct {
	// 用户分支 id
	UserKnowledgeId string
	// 分支
	Branch string
	// 仓库
	KnowledgebaseId string
	// 参考分支
	ReferenceBranch string
}

type ResponseMetadata struct {
	RequestId string
	Action    string
	Version   string
	Service   string
	Region    string
}

type Creator struct {
	Id          string
	Username    string
	DisplayName struct {
		Content string
		I18n    struct {
			EnUS string
		}
		Email            string
		TenantId         string
		CreatedAt        string
		Type             string
		Status           string
		AvatarURL        string
		CanPinRepository bool
		Location         string
		ThemeMode        string
		Language         string
	}
}

type RepoContent struct {
	Repository struct {
		Id                                                string
		Name                                              string
		Description                                       string
		URL                                               string
		SSHURL                                            string
		CloneURL                                          string
		CreatedAt                                         string
		UpdatedAt                                         string
		LastActivityAt                                    string
		Creator                                           Creator
		Archived                                          bool
		NamespaceId                                       string
		NamespaceDefaultPath                              string
		Path                                              string
		EnabledAutoTransitionWorkItemsAfterMergePlatforms []string
		EnabledWorkItemPlatforms                          []string
		ReviewRules                                       []struct {
			Name              string
			ApprovalsRequired int
			ReviewerSelectors []struct {
				RequiredPermissions []string
				UserSelector        struct {
					RequiredPermissions []string
				}
				Backup bool
			}
			DefaultBranch                  string
			MergeMethod                    string
			AllowedMergeMethods            []string
			RemoveSourceBranchAfterMerge   bool
			AllowedMergeRequestChangeModes string
			SquashCommitsBeforeMergeLevel  string
			AutoResolveOutdatedThreads     bool
			LFSEnabled                     bool
			LFSIntegrityCheckRequired      bool
			LowercaseRefNameCheckRequired  bool
			FileSizeCheckRequired          bool
			MaxFileSize                    int
			Properties                     []struct {
				Name        string
				DisplayName struct {
					Content string
				}
				Type  string
				Value struct {
					Text struct {
						Content string
					}
				}
			}
			RawValue string
		}
	}
}

type RepoResult struct {
	ResponseMetadata ResponseMetadata
	Result           RepoContent
}

type CommitContent struct {
	MergeRequest struct {
		Id                   string
		Number               int
		Status               string
		SourceRepoId         string
		TargetRepoId         string
		SourceBranchName     string
		TargetBranchName     string
		Title                string
		Description          string
		CreatedBy            Creator
		CreatedAt            string
		UpdatedBy            Creator
		UpdatedAt            string
		ClosedBy             Creator
		ClosedAt             string
		ChangesCount         int
		CommitsCount         int
		DivergedCommitsCount int
		Versions             []struct {
			Id             string
			Number         int
			MergeRequestId int
			CreatedBy      Creator
			CreatedAt      string
			SourceCommitId string
			TargetCommitId string
			BaseCommitId   string
			Type           string
			SourceRef      string
		}
		CurrentUser struct {
			Approved      bool
			Disapproved   bool
			CanApprove    bool
			CanDisapprove bool
			CanUpdate     bool
			CanBypass     bool
			CanMerge      bool
			CanRevert     bool
			CanCherryPick bool
		}
		ChangeMode                   string
		URL                          string
		AutoInviteReviewers          bool
		Draft                        bool
		AttentionSet                 []string
		MergeMethod                  string
		AutoMerge                    bool
		RemoveSourceBranchAfterMerge bool
		MergeInProgress              bool
		MergeCommitMessage           string
		SquashCommitMessage          string
		MergeCommitId                string
		MergeError                   string
		SquashCommits                bool
		DefaultMergeCommitMessage    string
		DefaultSquashCommitMessage   string
	}
}

type CommitResult struct {
	ResponseMetadata ResponseMetadata
	Result           CommitContent
}

type CommentContent struct {
	Files []struct {
		Path       string
		ChangeType string
		FromPath   string
		FromCommit string
		FromMode   int
		ToPath     string
		ToCommit   string
		ToMode     int
		IsBinary   bool
		RawPatch   string
		FromId     string
		ToId       string
		TooLarge   bool
	}
}

type CommentResult struct {
	ResponseMetadata ResponseMetadata
	Result           CommentContent
}

type QueryBuildRepoRequest struct {
}

type QueryBuildDBRepoItem struct {
	KnowledgebaseId string
	GMTCreate       time.Time
	GMTModified     time.Time
	Branch          string
	Status          string
}

type QueryBuildRepoItem struct {
	// 仓库
	RepoName string
	// 创建时间
	CreateAt string
	// 更新时间
	ModifyAt string
	// 分支
	Branch string
	// language
	Language string
	// uid
	Uid string
	// status
	Status string
}

type QueryBuildRepoResponse struct {
	// 查询结果
	Items []*QueryBuildRepoItem
}

type GetSummaryDataRequest struct {
	Uid      string `json:"uid"`
	RepoPath string `json:"repo_path"`
	Did      string `json:"did"`
	RepoName string `json:"repo_name"`
	Branch   string `json:"branch"`
}

type SummaryData struct {
	Knowledge knowledgeEntity.Knowledge `json:"knowledge"`
	MerkleId  string                    `json:"merkle_id"`
}

// DownloadResult 表示下载结果的结构体
type DownloadResult struct {
	Priority int    // 优先级：1=最高，2=中等，3=最低
	Data     []byte // 下载的数据
	Error    error  // 下载错误
}

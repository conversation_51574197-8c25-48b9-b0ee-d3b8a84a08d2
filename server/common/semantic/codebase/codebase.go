package codebase

import (
	"context"

	"code.byted.org/flow/datamind-code-index/model"
	code "code.byted.org/ies/codin/common/semantic/codebase/entity"
)

// CodeBase 代码库接口
type CodeBase interface {
	// GetCode 根据关键词搜索代码片段
	GetCode(ctx context.Context, req *code.GetCodeRequest) (*model.HybridQueryResult, error)
	// GetChunksByIDs 根据ChunkIDs批量查询chunks信息
	GetChunksByIDs(ctx context.Context, chunkIDs []string, userKnowledgeId string) (map[string]model.Chunk, error)
	// UploadChunk 上传chunk
	BuildIndexWithUploadChunk(ctx context.Context, req *code.UploadChunkRequest) (string, error)
	// 精确查询build 记录
	QueryBuildRecord(ctx context.Context, req *code.QueryBuildRecordRequest) (*model.BuildRecord, error)
	// 模糊查询build 记录
	FuzzyQueryBuildRecord(ctx context.Context, req *code.FuzzyQueryBuildRecordRequest) (*model.BuildRecord, error)
	// 查询build 记录是否存在
	ExistsBuildRecord(ctx context.Context, req *code.FuzzyQueryBuildRecordRequest) (bool, error)
	// 查询build 仓库
	QueryBuildRepo(ctx context.Context, req *code.QueryBuildRepoRequest) (*code.QueryBuildRepoResponse, error)
	// GetCodeDependency 根据依赖关系搜索代码片段
	GetCodeDependency(ctx context.Context, req *code.GetCodeDependencyRequest) (*[]model.QueryChunkRelation, error)
	// GetCodeReference 根据引用关系搜索代码片段
	GetCodeReference(ctx context.Context, req *code.GetCodeDependencyRequest) (*[]model.QueryChunkRelation, error)
}

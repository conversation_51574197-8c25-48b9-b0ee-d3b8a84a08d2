package summary

import (
	"fmt"
	"sort"
	"strings"

	"code.byted.org/ies/codin/common/semantic/summary/entity"
)

// FormatKnowledgeTree 将知识库转换为树形目录结构 / Convert knowledge base to tree directory structure
func FormatKnowledgeTree(knowledge *entity.Knowledge) string {
	if knowledge == nil {
		return "知识库为空 / Knowledge base is empty"
	}

	var result strings.Builder
	result.WriteString("知识库结构 / Knowledge Base Structure:\n")

	// 构建路径树 / Build path tree
	root := &pathNode{name: "project", children: make(map[string]*pathNode)}

	// 创建模块映射，用于快速查找 / Create module mapping for quick lookup
	moduleMap := make(map[string]*entity.Module)
	for i := range knowledge.Modules {
		module := &knowledge.Modules[i]
		moduleMap[module.Path] = module
	}

	// 处理所有模块 / Process all modules
	for _, module := range knowledge.Modules {
		// 移除开头的斜杠，并确保路径不以斜杠结尾 / Remove leading slash and ensure path doesn't end with slash
		path := strings.TrimPrefix(module.Path, "/")
		path = strings.TrimSuffix(path, "/")
		parts := strings.Split(path, "/")

		// 过滤空字符串 / Filter empty strings
		var filteredParts []string
		for _, part := range parts {
			if part != "" {
				filteredParts = append(filteredParts, part)
			}
		}

		// 构建路径树 / Build path tree
		current := root
		for i, part := range filteredParts {
			if _, exists := current.children[part]; !exists {
				// 如果是叶子节点，使用模块信息 / If it's a leaf node, use module info
				if i == len(filteredParts)-1 {
					// 构建功能描述 / Build feature description
					featureNames := make([]string, 0)
					for _, feature := range module.KeyFeatures {
						featureNames = append(featureNames, feature.Name)
					}

					summary := module.Name
					if len(featureNames) > 0 {
						summary = fmt.Sprintf("%s 模块功能集合[%s]", module.Name, strings.Join(featureNames, ","))
					}

					current.children[part] = &pathNode{
						name:     part,
						summary:  summary, // 使用模块的中文名称和功能 / Use module's Chinese name and features
						children: make(map[string]*pathNode),
					}
				} else {
					// 如果是中间节点，使用路径作为名称 / If it's an intermediate node, use path as name
					current.children[part] = &pathNode{
						name:     part,
						summary:  module.Name, // 使用路径段作为描述 / Use path segment as description
						children: make(map[string]*pathNode),
					}
				}
			} else {
				// 如果节点已存在，更新叶子节点的信息 / If node exists, update leaf node info
				if i == len(filteredParts)-1 {
					// 构建功能描述 / Build feature description
					featureNames := make([]string, 0)
					for _, feature := range module.KeyFeatures {
						featureNames = append(featureNames, feature.Name)
					}

					summary := module.Name
					if len(featureNames) > 0 {
						summary = fmt.Sprintf("%s 功能[%s]", module.Name, strings.Join(featureNames, ","))
					}

					current.children[part].summary = summary
				}
			}
			current = current.children[part]
		}

		// 处理子模块 / Process sub-modules
		// for _, subModule := range module.SubModules {
		// 	subPath := strings.TrimPrefix(subModule.Path, "/")
		// 	subPath = strings.TrimSuffix(subPath, "/")
		// 	subParts := strings.Split(subPath, "/")

		// 	// 过滤空字符串 / Filter empty strings
		// 	var filteredSubParts []string
		// 	for _, part := range subParts {
		// 		if part != "" {
		// 			filteredSubParts = append(filteredSubParts, part)
		// 		}
		// 	}

		// 	// 找到或创建子模块的父节点 / Find or create parent node for sub-module
		// 	subCurrent := root
		// 	for i := 0; i < len(filteredSubParts)-1; i++ {
		// 		part := filteredSubParts[i]
		// 		if _, exists := subCurrent.children[part]; !exists {
		// 			subCurrent.children[part] = &pathNode{
		// 				name:     part,
		// 				summary:  subModule.Name, // 使用路径段作为描述 / Use path segment as description
		// 				children: make(map[string]*pathNode),
		// 			}
		// 		}
		// 		subCurrent = subCurrent.children[part]
		// 	}

		// 	// 添加子模块 / Add sub-module
		// 	if len(filteredSubParts) > 0 {
		// 		lastPart := filteredSubParts[len(filteredSubParts)-1]
		// 		subCurrent.children[lastPart] = &pathNode{
		// 			name:     lastPart,
		// 			summary:  subModule.Name, // 使用子模块的中文名称 / Use sub-module's Chinese name
		// 			children: make(map[string]*pathNode),
		// 		}
		// 	}
		// }
	}

	// 格式化输出 / Format output
	formatNode(&result, root, 0, true, true)
	return result.String()
}

// pathNode 路径节点 / Path node
type pathNode struct {
	name     string
	summary  string
	children map[string]*pathNode
}

// formatNode 递归格式化节点 / Recursively format node
func formatNode(builder *strings.Builder, node *pathNode, depth int, isLast bool, isRoot bool) {
	// 获取缩进 / Get indentation
	indent := getIndent(depth, isLast)

	// 输出节点名称 / Output node name
	if !isRoot {
		builder.WriteString(fmt.Sprintf("%s%s: %s\n", indent, node.name, node.summary))
	}

	// 对子节点进行排序 / Sort children
	children := make([]*pathNode, 0, len(node.children))
	for _, child := range node.children {
		if child.name != "" {
			children = append(children, child)
		}
	}
	sort.Slice(children, func(i, j int) bool {
		return children[i].name < children[j].name
	})

	// 递归处理子节点 / Recursively process children
	for i, child := range children {
		isLastChild := i == len(children)-1
		formatNode(builder, child, depth+1, isLastChild, false)
	}
}

// getIndent 获取缩进和连接线 / Get indentation and connection lines
func getIndent(depth int, isLast bool) string {
	if depth == 0 {
		return ""
	}

	indent := strings.Repeat("    ", depth-1)
	if isLast {
		indent += "└── "
	} else {
		indent += "├── "
	}
	return indent
}

package summary

import (
	"context"
	"errors"
	"strings"
	"sync"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/common/semantic/summary/entity"
	"code.byted.org/ies/codin/common/utils"
)

// ===== 知识管理器 / Knowledge Manager =====

// KnowledgeManager 知识管理器 / Knowledge manager
// 合并service和manager功能，遵循单一职责原则 / Merge service and manager functionality, follow Single Responsibility Principle
type KnowledgeManager struct {
	namespace string            // 命名空间，用于隔离不同请求的知识库 / Namespace for isolating knowledge bases from different requests
	storage   *KnowledgeStorage // 存储依赖 / Storage dependency
	knowledge entity.Knowledge  // 缓存的知识库 / Cached knowledge base
	mutex     sync.RWMutex      // 读写锁保护并发访问 / Read-write lock for concurrent access protection
}

// NewKnowledgeManager 创建知识管理器 / Create knowledge manager
// @param basePath 基础路径 / Base path
// @param namespace 命名空间，用于隔离不同请求的知识库 / Namespace for isolating knowledge bases from different requests
// @return *KnowledgeManager 管理器实例 / Manager instance
func NewKnowledgeManager() *KnowledgeManager {
	manager := &KnowledgeManager{
		namespace: "",
		knowledge: entity.Knowledge{
			Modules:   make([]entity.Module, 0),
			UpdatedAt: time.Now(),
		},
	}

	return manager
}

// ===== 核心业务方法 / Core Business Methods =====
func (km *KnowledgeManager) SetNamespace(namespace string) {
	km.mutex.Lock()
	defer km.mutex.Unlock()
	km.namespace = namespace
}

func (km *KnowledgeManager) InitStorage(basePath string, namespace string) {
	km.mutex.Lock()
	defer km.mutex.Unlock()
	storage := NewFileStorage(basePath, namespace)
	km.storage = &storage
}

func (km *KnowledgeManager) UpdateKnowledge(ctx context.Context, knowledge *entity.Knowledge) {
	km.mutex.Lock()
	defer km.mutex.Unlock()
	km.knowledge = *knowledge
}

// SaveModule 保存模块（对应tool调用）/ Save module (corresponding to tool call)
func (km *KnowledgeManager) SaveModule(ctx context.Context, params *entity.ModuleParams) (string, error) {
	km.mutex.Lock()
	defer km.mutex.Unlock()
	// 创建模块 / Create module
	module := entity.Module{
		Path:        utils.NormalizePath(params.ModulePath),
		Name:        params.ModuleName,
		Doc:         params.ModuleDoc,
		Summary:     params.ModuleSummary,
		KeyFeatures: params.KeyFeatures,
		SubModules:  params.SubModules,
		UpdatedAt:   time.Now(),
	}

	// 添加到知识库 / Add to knowledge base
	km.addModuleToKnowledgeUnsafe(module)

	// 持久化 / Persist
	km.saveModuleUnsafe(ctx)

	log.V2.Info().With(ctx).
		Str("namespace", km.namespace).
		Str("modulePath", params.ModulePath).
		Emit()

	return params.ModulePath, nil
}

// 获取模块
func (km *KnowledgeManager) GetModule(ctx context.Context, modulePath string) *entity.Module {
	return km.getModuleUnsafe(ctx, modulePath)
}

func (km *KnowledgeManager) GetModuleBySubModulePath(ctx context.Context, subModulePath string) *entity.Module {
	nPath := utils.NormalizePath(subModulePath)
	for i := range km.knowledge.Modules {
		for j := range km.knowledge.Modules[i].SubModules {
			if utils.NormalizePath(km.knowledge.Modules[i].SubModules[j].Path) == nPath {
				// 注意：这里不应该修改数据，因为持有的是读锁
				// 返回副本而不是直接修改原数据
				module := km.knowledge.Modules[i]
				module.SubModules[j].Path = nPath
				return &module
			}
		}
	}
	return nil
}

func (km *KnowledgeManager) GetSubModule(ctx context.Context, modulePath string) (*entity.SubModuleInfo, error) {
	nPath := utils.NormalizePath(modulePath)
	for i := range km.knowledge.Modules {
		for j := range km.knowledge.Modules[i].SubModules {
			if utils.NormalizePath(km.knowledge.Modules[i].SubModules[j].Path) == nPath {
				// 注意：这里不应该修改数据，因为持有的是读锁
				// 返回副本而不是直接修改原数据
				subModule := km.knowledge.Modules[i].SubModules[j]
				subModule.Path = nPath
				return &subModule, nil
			}
		}
	}
	return nil, errors.New("subModule not found")
}

func (km *KnowledgeManager) CleanKnowledge(ctx context.Context) {
	km.mutex.Lock()
	defer km.mutex.Unlock()
	km.knowledge = entity.Knowledge{
		Modules:   make([]entity.Module, 0),
		UpdatedAt: time.Now(),
	}
}

// GetNamespace 获取当前命名空间 / Get current namespace
// @return string 当前命名空间 / Current namespace
func (km *KnowledgeManager) GetNamespace() string {
	return km.namespace
}

func (km *KnowledgeManager) GetKnowledge(ctx context.Context) *entity.Knowledge {
	return &km.knowledge
}

func (km *KnowledgeManager) UpdateModule(ctx context.Context, module *entity.Module) {
	km.mutex.Lock()
	defer km.mutex.Unlock()
	for i := range km.knowledge.Modules {
		if km.knowledge.Modules[i].Path == module.Path {
			km.knowledge.Modules[i] = *module
			return
		}
	}
	km.knowledge.Modules = append(km.knowledge.Modules, *module)
}

// DeleteModule 递归删除模块及其相关数据
// 删除逻辑：
// 1. 删除指定的模块
// 2. 递归删除该模块的所有子模块
// 3. 删除其他模块中引用该模块作为子模块的记录
// 4. 删除其他模块中引用以该模块路径为前缀的所有子模块的记录
// @param ctx 上下文 / Context
// @param modulePath 要删除的模块路径 / Module path to delete
func (km *KnowledgeManager) DeleteModule(ctx context.Context, modulePath string) bool {
	km.mutex.Lock()
	defer km.mutex.Unlock()
	moduleDeleted := km.deleteModuleUnsafe(ctx, modulePath)

	// 4. 保存更改
	if moduleDeleted {
		km.saveModuleUnsafe(ctx)
		log.V2.Info().With(ctx).
			Str("namespace", km.namespace).
			Str("modulePath", modulePath).
			Str("模块删除完成并已保存").Emit()
	} else {
		log.V2.Warn().With(ctx).
			Str("namespace", km.namespace).
			Str("modulePath", modulePath).
			Str("未找到要删除的模块").Emit()
	}

	return moduleDeleted
}

// DeleteSubModule 递归删除子模块及其相关数据
// @param ctx 上下文 / Context
// @param subModulePath 要删除的子模块路径 / Sub-module path to delete
func (km *KnowledgeManager) DeleteSubModule(ctx context.Context, subModulePath string) error {
	km.mutex.Lock()
	defer km.mutex.Unlock()
	normalizedPath := utils.NormalizePath(subModulePath)

	// 1. 删除该子模块作为独立模块的情况（如果存在）
	moduleDeleted := false
	if module := km.getModuleUnsafe(ctx, subModulePath); module != nil {
		moduleDeleted = km.deleteModuleUnsafe(ctx, subModulePath)
	}

	// 2. 删除所有模块中引用该子模块作为子模块的记录
	subModuleDeleted := km.removeSubModuleReferencesUnsafe(ctx, normalizedPath)

	// 3. 保存更改
	if subModuleDeleted || moduleDeleted {
		km.saveModuleUnsafe(ctx)
	}

	return nil
}

// removeSubModuleReferencesUnsafe 删除所有模块中引用指定路径作为子模块的记录（精确匹配）- 无锁版本
// @param ctx 上下文 / Context
// @param targetPath 目标路径 / Target path
// @return bool 是否删除了任何引用 / Whether any references were deleted
func (km *KnowledgeManager) removeSubModuleReferencesUnsafe(ctx context.Context, targetPath string) bool {
	removedCount := 0

	// 创建新的模块列表，避免在循环中修改原始切片
	newModules := make([]entity.Module, 0, len(km.knowledge.Modules))

	for _, module := range km.knowledge.Modules {
		// 过滤掉匹配的子模块
		filteredSubModules := make([]entity.SubModuleInfo, 0)
		for _, subModule := range module.SubModules {
			if utils.NormalizePath(subModule.Path) != targetPath {
				filteredSubModules = append(filteredSubModules, subModule)
			} else {
				removedCount++
			}
		}

		// 创建新的模块副本，更新子模块列表
		newModule := module
		newModule.SubModules = filteredSubModules
		newModules = append(newModules, newModule)
	}

	// 更新整个模块列表
	km.knowledge.Modules = newModules

	return removedCount > 0
}

// removeSubModuleReferencesByPrefixUnsafe 删除所有模块中引用以指定路径为前缀的所有子模块的记录
// @param ctx 上下文 / Context
// @param prefixPath 前缀路径 / Prefix path
// @return bool 是否删除了任何引用 / Whether any references were deleted
func (km *KnowledgeManager) removeSubModuleReferencesByPrefixUnsafe(ctx context.Context, prefixPath string) bool {
	removedCount := 0

	// 创建新的模块列表，避免在循环中修改原始切片
	newModules := make([]entity.Module, 0, len(km.knowledge.Modules))

	for _, module := range km.knowledge.Modules {
		// 过滤掉匹配的子模块
		filteredSubModules := make([]entity.SubModuleInfo, 0)
		for _, subModule := range module.SubModules {
			if !strings.HasPrefix(utils.NormalizePath(subModule.Path), prefixPath) {
				filteredSubModules = append(filteredSubModules, subModule)
			} else {
				removedCount++
			}
		}

		// 创建新的模块副本，更新子模块列表
		newModule := module
		newModule.SubModules = filteredSubModules
		newModules = append(newModules, newModule)
	}

	// 更新整个模块列表
	km.knowledge.Modules = newModules

	return removedCount > 0
}

// addModuleToKnowledgeUnsafe 将模块添加到知识库中 / Add module to knowledge base
// 注意：此方法假设调用者已经持有写锁
func (km *KnowledgeManager) addModuleToKnowledgeUnsafe(module entity.Module) {
	// 检查是否已存在该模块 / Check if module already exists
	for i := range km.knowledge.Modules {
		if km.knowledge.Modules[i].Path == module.Path {
			// 更新已存在的模块 / Update existing module
			km.knowledge.Modules[i] = module
			return
		}
	}
	// 添加新模块 / Add new module
	km.knowledge.Modules = append(km.knowledge.Modules, module)
}

// saveModuleUnsafe 保存模块到存储
// 注意：此方法假设调用者已经持有锁（读锁或写锁）
func (km *KnowledgeManager) saveModuleUnsafe(ctx context.Context) {
	if km.storage == nil {
		log.V2.Error().With(ctx).Str("namespace", km.namespace).Str("storage is nil").Emit()
		return
	}
	(*km.storage).Save(ctx, &km.knowledge)
}

// getModuleUnsafe 无锁版本的获取模块方法
func (km *KnowledgeManager) getModuleUnsafe(ctx context.Context, modulePath string) *entity.Module {
	nPath := utils.NormalizePath(modulePath)
	for i := range km.knowledge.Modules {
		if utils.NormalizePath(km.knowledge.Modules[i].Path) == nPath {
			// 注意：不应该在无锁版本中修改数据
			// 返回副本而不是直接修改原数据
			module := km.knowledge.Modules[i]
			module.Path = nPath
			return &module
		}
	}
	return nil
}

// deleteModuleUnsafe 无锁版本的删除模块方法
func (km *KnowledgeManager) deleteModuleUnsafe(ctx context.Context, modulePath string) bool {
	normalizedPath := utils.NormalizePath(modulePath)
	moduleDeleted := false

	// 1. 删除指定的模块及其所有子模块
	filteredModules := make([]entity.Module, 0, len(km.knowledge.Modules))

	for _, module := range km.knowledge.Modules {
		moduleNormalizedPath := utils.NormalizePath(module.Path)

		// 删除精确匹配的模块和所有以该模块路径为前缀的子模块
		if moduleNormalizedPath == normalizedPath || strings.HasPrefix(moduleNormalizedPath, normalizedPath+"/") {
			moduleDeleted = true
		} else {
			filteredModules = append(filteredModules, module)
		}
	}

	// 2. 更新模块列表
	km.knowledge.Modules = filteredModules

	// 3. 删除所有模块中引用以该模块路径为前缀的所有子模块的记录
	km.removeSubModuleReferencesByPrefixUnsafe(ctx, normalizedPath)

	return moduleDeleted
}

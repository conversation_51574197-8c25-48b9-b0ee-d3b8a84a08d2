package entity

import "time"

// ===== 领域实体 / Domain Entities =====

// Knowledge 知识库（分层结构）/ Knowledge base with layered structure
type Knowledge struct {
	// todo(liboti)：这里早期做数据转换的时候，把layers改成了modules，但是因为layers的数据已经落盘，所以这里先不能改回来，后续summary清空重建之后，再处理
	Modules   []Module  `json:"layers"`     // 分层数据 / Layered data
	UpdatedAt time.Time `json:"updated_at"` // 最后更新时间 / Last updated time
}

// Module 模块信息 / Module information
type Module struct {
	// 基础信息（对应tool参数）/ Basic info (corresponding to tool parameters)
	Path        string           `json:"path"`         // 模块路径 / Module path
	Name        string           `json:"name"`         // 模块名称 / Module name
	Doc         string           `json:"doc"`          // 模块文档 / Module doc
	Summary     string           `json:"summary"`      // 模块总结 / Module summary
	KeyFeatures []KeyFeatureInfo `json:"key_features"` // 关键功能 / Key features
	SubModules  []SubModuleInfo  `json:"sub_modules"`  // 子模块 / Sub-modules
	UpdatedAt   time.Time        `json:"updated_at"`   // 更新时间 / Updated time
}

// KeyFeatureInfo 关键功能信息（对应tool参数）/ Key feature info (corresponding to tool parameters)
type KeyFeatureInfo struct {
	Name        string `json:"name"`        // 功能名称 / Feature name
	Description string `json:"description"` // 功能描述 / Feature description
	CallMethod  string `json:"call_method"` // 调用方式 / Call method
}

// SubModuleInfo 子模块信息（对应tool参数）/ Sub-module info (corresponding to tool parameters)
type SubModuleInfo struct {
	Name        string `json:"name"`        // 子模块名称 / Sub-module name
	Path        string `json:"path"`        // 子模块路径 / Sub-module path
	Reason      string `json:"reason"`      // 记录知识的原因 / Reason for recording knowledge
	Description string `json:"description"` // 子模块描述 / Sub-module description
}

// ===== 数据传输对象 / Data Transfer Objects =====

// ModuleParams 模块参数（对应tool参数）/ Module parameters (corresponding to tool parameters)
type ModuleParams struct {
	ModulePath    string           `json:"module_path"`
	ModuleName    string           `json:"module_name"`
	ModuleSummary string           `json:"module_summary"`
	ModuleDoc     string           `json:"module_doc"`
	KeyFeatures   []KeyFeatureInfo `json:"key_features"`
	SubModules    []SubModuleInfo  `json:"sub_modules"`
}

package summary

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/common/semantic/summary/entity"
)

// ===== 存储接口 / Storage Interface =====

// KnowledgeStorage 知识存储接口 / Knowledge storage interface
// 遵循接口分离原则 / Follow Interface Segregation Principle
type KnowledgeStorage interface {
	// Save 保存知识库 / Save knowledge base
	Save(ctx context.Context, knowledge *entity.Knowledge) error

	// Load 加载知识库 / Load knowledge base
	Load(ctx context.Context) (*entity.Knowledge, error)

	// GetNamespace 获取当前命名空间 / Get current namespace
	// @return string 当前命名空间 / Current namespace
	GetNamespace() string
}

// ===== 文件存储实现 / File Storage Implementation =====

// FileStorage 文件存储实现 / File storage implementation
// 遵循依赖反转原则 / Follow Dependency Inversion Principle
type FileStorage struct {
	filePath  string // 存储文件路径 / Storage file path
	namespace string // 命名空间，用于隔离不同请求的知识库 / Namespace for isolating knowledge bases from different requests
}

// NewFileStorage 创建文件存储实例 / Create file storage instance
// @param basePath 存储根目录路径 / Base storage directory path
// @param namespace 命名空间，用于隔离不同请求的知识库 / Namespace for isolating knowledge bases from different requests
// @return KnowledgeStorage 知识存储接口 / Knowledge storage interface
func NewFileStorage(basePath string, namespace string) KnowledgeStorage {
	// 使用命名空间作为文件名的一部分，确保不同命名空间存储在不同文件中
	// Use namespace as part of filename to ensure different namespaces store in different files
	escapedNamespace := url.QueryEscape(namespace)
	fileName := fmt.Sprintf("knowledge_%s.json", escapedNamespace)
	filePath := filepath.Join(basePath, fileName)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); err == nil {
		log.V2.Info().With(context.Background()).Str("filePath", filePath).Str("知识库文件已存在 / Knowledge file already exists").Emit()
	}

	// 确保目录存在 / Ensure directory exists
	if err := os.MkdirAll(basePath, 0755); err != nil {
		log.V2.Error().With(context.Background()).Str("basePath", basePath).Str("创建目录失败 / Failed to create directory").Error(err).Emit()
		// 注意：这里不能直接返回错误，因为这是一个构造函数
		// 目录创建失败会在后续的 Save/Load 操作中处理
	}

	return &FileStorage{
		filePath:  filePath,
		namespace: namespace,
	}
}

// Save 保存知识库到文件 / Save knowledge base to file
func (fs *FileStorage) Save(ctx context.Context, knowledge *entity.Knowledge) error {
	knowledge.UpdatedAt = time.Now()

	data, err := json.MarshalIndent(knowledge, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化知识库失败 / failed to marshal knowledge: %w", err)
	}

	// 确保目录存在 / Ensure directory exists
	dir := filepath.Dir(fs.filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败 / failed to create directory: %w", err)
	}

	if err := os.WriteFile(fs.filePath, data, 0644); err != nil {
		return fmt.Errorf("写入知识库文件失败 / failed to write knowledge file: %w", err)
	}

	return nil
}

// Load 从文件加载知识库 / Load knowledge base from file
func (fs *FileStorage) Load(ctx context.Context) (*entity.Knowledge, error) {
	if _, err := os.Stat(fs.filePath); os.IsNotExist(err) {
		log.V2.Info().With(ctx).Str("filePath", fs.filePath).Str("知识库文件不存在 / Knowledge file does not exist").Emit()
		// 文件不存在，返回空知识库 / File doesn't exist, return empty knowledge base
		return &entity.Knowledge{
			Modules:   make([]entity.Module, 0),
			UpdatedAt: time.Now(),
		}, nil
	}

	data, err := os.ReadFile(fs.filePath)
	if err != nil {
		log.V2.Error().With(ctx).Str("filePath", fs.filePath).Str("读取知识库文件失败 / failed to read knowledge file").Error(err).Emit()
		return nil, fmt.Errorf("读取知识库文件失败 / failed to read knowledge file: %w", err)
	}

	var knowledge entity.Knowledge
	if err := json.Unmarshal(data, &knowledge); err != nil {
		log.V2.Error().With(ctx).Str("filePath", fs.filePath).Str("反序列化知识库失败 / failed to unmarshal knowledge").Error(err).Emit()
		return nil, fmt.Errorf("反序列化知识库失败 / failed to unmarshal knowledge: %w", err)
	}
	log.V2.Info().With(ctx).Str("filePath", fs.filePath).Str("知识库文件存在 / Knowledge file exists").Emit()

	return &knowledge, nil
}

// GetNamespace 获取当前命名空间 / Get current namespace
// @return string 当前命名空间 / Current namespace
func (fs *FileStorage) GetNamespace() string {
	return fs.namespace
}

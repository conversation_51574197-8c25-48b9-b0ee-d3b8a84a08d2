package tos

import (
	"code.byted.org/gopkg/logs/v2"
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/common/group"
	codebaseEntity "code.byted.org/ies/codin/common/semantic/codebase/entity"
	"code.byted.org/ies/codin/common/semantic/tos/config"
	"code.byted.org/ies/codin/common/tos"
)

const tosDir = "summary/"

func NewSummaryTosManager() *tos.TosManager {
	tosManager := tos.NewTosManager()
	tosManager.SetDir(tosDir)
	return tosManager
}

func GetUserSummaryData(ctx context.Context, userKnowledgeId string) (*codebaseEntity.SummaryData, error) {
	tosManager := NewSummaryTosManager()

	// 初始化 TOS 管理器
	if err := tosManager.InitTosClientIfNeeded(config.GetSummaryStorageConfig()); err != nil {
		log.V2.Error().With(ctx).Str("初始化TOS客户端失败").Error(err).Emit()
		return nil, fmt.Errorf("初始化TOS客户端失败: %w", err)
	}

	data, err := tosManager.DownloadFileFromTos(ctx, userKnowledgeId)
	if err != nil {
		log.V2.Error().With(ctx).Str("下载用户知识库失败").Error(err).Emit()
		return nil, fmt.Errorf("下载用户知识库失败: %w", err)
	}

	var summaryData codebaseEntity.SummaryData
	if err := json.Unmarshal(data, &summaryData); err != nil {
		log.V2.Error().With(ctx).Str("解析用户知识库失败").Error(err).Emit()
		return nil, fmt.Errorf("解析用户知识库失败: %w", err)
	}

	return &summaryData, nil
}

/**
 * downloadFilesConcurrently 并发下载多个文件
 * @param ctx - 上下文信息
 * @param tosManager - TOS 管理器接口
 * @param filePaths - 文件路径映射，key为优先级，value为文件路径
 * @returns map[int]*DownloadResult - 下载结果映射
 */
func DownloadFilesConcurrently(ctx context.Context, tosManager *tos.TosManager, filePaths map[int]string) map[int]*codebaseEntity.DownloadResult {
	results := make(map[int]*codebaseEntity.DownloadResult)
	var mu sync.Mutex

	handlers := make([]func() error, 0)
	for priority, filePath := range filePaths {
		p := priority
		path := filePath
		handlers = append(handlers, func() error {
			result := &codebaseEntity.DownloadResult{
				Priority: p,
			}

			// 下载文件
			data, err := tosManager.DownloadFileFromTos(ctx, path)
			if err != nil {
				result.Error = err
				logs.CtxWarn(ctx, "DownloadFilesConcurrently, 文件下载失败, path = %v", path)
			} else {
				result.Data = data
				logs.CtxInfo(ctx, "DownloadFilesConcurrently, 文件下载成功, path = %v", path)
			}

			// 线程安全地存储结果
			mu.Lock()
			results[p] = result
			mu.Unlock()
			return nil
		})
	}

	// 使用 group.GoAndWait 并发执行所有下载任务
	group.GoAndWait(handlers...)

	return results
}

/**
 * selectBestResultAndParse 根据优先级选择最佳结果并解析
 * @param results - 下载结果映射
 * @returns *SummaryData - 最佳的知识库结果
 * @returns error - 错误信息
 */
func SelectBestResultAndParse(results map[int]*codebaseEntity.DownloadResult, filePaths map[int]string) (*codebaseEntity.SummaryData, string, error) {
	// 按照优先级顺序检查结果
	priorities := []int{1, 2, 3}

	for _, priority := range priorities {
		if result, exists := results[priority]; exists && result.Data != nil && result.Error == nil {
			// 尝试解析为 SummaryData 结构
			var summaryData codebaseEntity.SummaryData
			if err := json.Unmarshal(result.Data, &summaryData); err != nil {
				log.V2.Error().With(context.Background()).Str("解析知识库失败").KVs(
					"priority", priority,
				).Error(err).Emit()
				continue // 解析失败，尝试下一个优先级
			}

			log.V2.Info().With(context.Background()).Str("选择优先级并解析成功").Int(priority).KVs(
				"priority", priority,
				"modulesCount", len(summaryData.Knowledge.Modules),
			).Emit()
			return &summaryData, filePaths[priority], nil
		}
	}

	return nil, "", fmt.Errorf("没有找到有效的知识库数据")
}

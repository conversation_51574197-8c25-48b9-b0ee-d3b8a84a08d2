package config

import "code.byted.org/gopkg/env"

func GetMerkleStorageConfig() TosConfig {
	c := TosConfig{
		BucketName: "merklestorage",
		AccessKey:  "AKLTMmM0NzE4MTliZjc3NGU3NmIxNTJjYjIyOGI1ZTBhMWE",
		SecretKey:  "TlRNd1pEVXdOMlEwWTJGaE5HWXpNMkkzTkRJNVlUQTVNMkpoTVdWaE9UUQ==",
		Endpoint:   "tos-cn-beijing.ivolces.com",
		Region:     "cn-beijing",
	}

	if env.IsBoe() {
		c.Endpoint = "tos-cn-beijing.volces.com"
	}
	return c
}

func GetSummaryStorageConfig() TosConfig {
	c := TosConfig{
		BucketName: "summarystorage",
		AccessKey:  "AKLTNjczM2FhMjNkYTZjNDhkODg0OGMxMTVhYjIyZjI0Zjc",
		SecretKey:  "WXpjNVlXUm1OR0ppTjJFek5EVTNNems1WldSbFl6VTFPVEUwTVRZMk16UQ==",
		Endpoint:   "tos-cn-beijing.ivolces.com",
		Region:     "cn-beijing",
	}

	if env.IsBoe() {
		c.Endpoint = "tos-cn-beijing.volces.com"
	}
	return c
}

package repo

import (
	"code.byted.org/gopkg/jsonx"
	"code.byted.org/ies/codin/common/semantic/repo/config"
	"code.byted.org/ies/codin/common/tcc"
	"context"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestRepoManager_fetchRepoItemList(t *testing.T) {
	var (
		ctx = context.Background()
	)

	repoItemMap := Repo.fetchRepoItemList(ctx)
	t.Log(jsonx.ToString(repoItemMap))

	actualRepoUrlList := lo.Map(config.RepoConfigList, func(item tcc.RepoConfigItem, _ int) string {
		return item.RepoURL
	})

	assert.True(t, len(actualRepoUrlList) > 0)
	for _, repoItem := range repoItemMap {
		assert.True(t, lo.Contains(actualRepoUrlList, repoItem.RepoURL))
	}
}

func TestRepoManager_GetRepoURL(t *testing.T) {
	var (
		ctx = context.Background()
	)

	repoItemMap := Repo.fetchRepoItemList(ctx)
	t.Log(jsonx.ToString(repoItemMap))

	url, exist := Repo.GetRepoURL(ctx, "ies/lvweb")
	assert.True(t, exist)
	assert.Equal(t, url, "https://code.byted.org/ies/lvweb")
}

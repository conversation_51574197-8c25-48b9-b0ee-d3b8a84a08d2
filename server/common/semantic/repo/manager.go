package repo

import (
	"context"
	"github.com/samber/lo"
	"sync"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/jsonx"
	"code.byted.org/gopkg/logs/v2"
	config "code.byted.org/ies/codin/common/semantic/repo/config"
	"code.byted.org/ies/codin/common/tcc"
)

var Repo *repoManager
var once sync.Once

func init() {
	once.Do(func() {
		Repo = &repoManager{}
	})
}

/**
 * repoManager 负责维护 repo_name 到仓库的一些信息
 */
type repoManager struct{}

func (m *repoManager) fetchRepoItemList(ctx context.Context) map[string]config.BusinessRepoConfig {
	var repoConfigList = config.RepoConfigList

	if env.IsProduct() {
		repoConfigList, _ = tcc.GetTccReader().GetCodesearchRepoConfig(ctx)
	}

	logs.CtxInfo(ctx, "repo config list: %v", jsonx.ToString(repoConfigList))
	return lo.SliceToMap(repoConfigList, func(item tcc.RepoConfigItem) (string, config.BusinessRepoConfig) {
		return item.RepoName, config.BusinessRepoConfig{
			RepoName: item.RepoName,
			RepoURL:  item.RepoURL,
			PathList: item.PathList,
		}
	})
}

/**
 * GetRepoURL 获取指定 business+platform 的 git 仓库地址
 * GetRepoURL returns the git repo URL for the given business and platform
 */
func (m *repoManager) GetRepoURL(ctx context.Context, repoName string) (string, bool) {
	repoURL, ok := m.fetchRepoItemList(ctx)[repoName]
	if !ok {
		return "", false
	}
	return repoURL.RepoURL, true
}

/**
 * GetBusinessRepoConfig 获取指定 business+platform 的 BusinessRepoConfig
 * GetBusinessRepoConfig returns the BusinessRepoConfig for the given business and platform
 */
func (m *repoManager) GetRepoItem(ctx context.Context, repoName string) (*config.BusinessRepoConfig, bool) {
	repoItemMap := m.fetchRepoItemList(ctx)
	repoConfig, ok := repoItemMap[repoName]
	if !ok {
		logs.CtxWarn(ctx, "[GetRepoItem] repo item not found, repoName = %s, repoItemMap = %v", repoName, jsonx.ToString(repoItemMap))
		return &config.BusinessRepoConfig{}, false
	}
	return &repoConfig, true
}

func (m *repoManager) GetRepoPathList(ctx context.Context, repoName string) ([]string, bool) {
	repoURL, ok := m.fetchRepoItemList(ctx)[repoName]
	if !ok {
		return []string{}, false
	}
	return repoURL.PathList, true
}

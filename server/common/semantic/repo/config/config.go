package config

import "code.byted.org/ies/codin/common/tcc"

type BusinessRepoConfig struct {
	RepoName string   // 仓库名称
	RepoURL  string   // 仓库URL
	PathList []string // 仓库路径列表
}

var RepoConfigList = []tcc.RepoConfigItem{
	{
		// demo
		RepoURL:  "https://code.byted.org/ies/code-index",
		PathList: []string{"apps/dreamina", "apps/story-agent"},
		RepoName: "ies/code-index",
	},
	{
		// kotlin demo 仓库
		RepoURL:  "https://code.byted.org/ies/codin-index-kotlin-demo",
		PathList: []string{""},
		RepoName: "ies/codin-index-kotlin-demo",
	},
	{
		// swift demo 仓库
		RepoURL:  "https://code.byted.org/ies/codin-index-swift-demo",
		PathList: []string{""},
		RepoName: "ies/codin-index-swift-demo",
	},
	{
		// qukecheng 测试
		RepoURL:  "https://code.byted.org/ies/qukecheng_lvweb",
		PathList: []string{""},
		RepoName: "ies/qukecheng_lvweb",
	},

	// ------------------------------ 下面是业务的，上面demo仓库 ------------------------------
	{
		// 即梦web仓库
		RepoURL:  "https://code.byted.org/ies/lvweb",
		PathList: []string{"apps/dreamina", "apps/story-agent"},
		RepoName: "ies/lvweb",
	},
	{
		RepoURL: "https://code.byted.org/ies/lv-lynx",
		PathList: []string{
			"apps/dreamina-h5",
			"apps/dreamina-lynx",
			"packages/dreamina-services",
			"packages/dreamina-lynx-rs",
			"packages/ai-dreamina",
		},
		RepoName: "ies/lv-lynx",
	},
	{
		// 即梦安卓仓库
		RepoURL:  "https://code.byted.org/faceu-android/vega",
		PathList: []string{"apps/dreamina", "modules/dreamina", "modules/dreaminabase"},
		RepoName: "faceu-android/vega",
	},
	{
		// 即梦 server 生成服务仓库
		RepoURL:  "https://code.byted.org/videocut-aigc/content_generate",
		PathList: []string{""},
		RepoName: "videocut-aigc/content_generate",
	},
	{
		// 即梦 server mweb-api 服务仓库
		RepoURL:  "https://code.byted.org/videocut-aigc/mweb-api",
		PathList: []string{""},
		RepoName: "videocut-aigc/mweb-api",
	},
	{
		// 即梦 server aigcUtils
		RepoURL:  "https://code.byted.org/videocut-aigc/aigc-utils",
		PathList: []string{""},
		RepoName: "videocut-aigc/aigc-utils",
	},
	{
		// 即梦 server aigcUtils
		RepoURL:  "https://code.byted.org/videocut-aigc/idl",
		PathList: []string{""},
		RepoName: "videocut-aigc/idl",
	},
	{
		// 即梦 server aigcUtils
		RepoURL:  "https://code.byted.org/videocut-aigc/videocut_mweb_distribute",
		PathList: []string{""},
		RepoName: "videocut-aigc/videocut_mweb_distribute",
	},
	{
		// 即梦ios仓库
		RepoURL:  "https://code.byted.org/faceu-ios/iMovie",
		PathList: []string{"Modules/JMMain", "Modules/JMStory", "Modules/JMPaymentCenter", "Modules/JMBase"},
		RepoName: "faceu-ios/iMovie",
	},
	// ------------------------------ 下面是旧的，上面是新的 ------------------------------

	{
		// 即梦 server agent 服务仓库
		RepoURL:  "https://code.byted.org/videocut-aigc/dreamina_agent_core.git",
		PathList: []string{""},
		RepoName: "videocut-aigc:dreamina_agent_core_server",
	},

	{
		// android demo 工程仓库
		RepoURL:  "https://code.byted.org/qingyingliu.671/codin-android-demo.git",
		PathList: []string{""},
		RepoName: "qingyingliu.671:codin-android-demo",
	},

	// {
	// 	// pippit lynx仓库
	// 	RepoURL:     "https://code.byted.org/ies/lv-lynx",
	// 	PathList: []string{"apps/pippit-lynx", "packages/pippit"},
	// 	RepoName: "pippit_lynx",
	// },
	{
		// douyin web仓库
		RepoURL:  "https://bits.bytedance.net/code/ies/douyin_web",
		PathList: []string{""},
		RepoName: "douyin_web",
	},
	{
		RepoURL:  "https://code.byted.org/capcut-server/capcut_web_common_handler.git",
		PathList: []string{""},
		RepoName: "capcut_web_common_handler",
	},
	{
		RepoURL:  "https://code.byted.org/capcut-business/business_api.git",
		PathList: []string{""},
		RepoName: "capcut-business-business_api",
	},
}

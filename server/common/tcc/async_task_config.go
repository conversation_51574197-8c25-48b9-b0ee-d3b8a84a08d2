package tcc

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/pkg/errors"
	"context"

	tccclientV3 "code.byted.org/gopkg/tccclient/v3"
)

var (
	asyncTaskConfigGetter tccclientV3.Getter
)

type AsyncTaskConfig struct {
	SK       string `json:"sk"`
	CloudIDE struct {
		PipelineID string `json:"pipeline_id"`
		Mirror     string `json:"mirror"`
	} `json:"cloud_ide"`
}

func (i *impl) GetAsyncTaskConfig(ctx context.Context) (*AsyncTaskConfig, error) {
	inf, err := asyncTaskConfigGetter(ctx)
	if err != nil {
		logs.CtxError(ctx, "[GetAsyncTaskConfig] tcc err: %v", err)
		return nil, err
	}
	logs.CtxDebug(ctx, "GetAsyncTaskConfig: %v", inf)
	config, ok := inf.(AsyncTaskConfig)
	if !ok {
		logs.CtxError(ctx, "config illegal, inf: %v", inf)
		return nil, errors.Errorf("config illegal, inf: %v", inf)
	}

	return &config, nil
}

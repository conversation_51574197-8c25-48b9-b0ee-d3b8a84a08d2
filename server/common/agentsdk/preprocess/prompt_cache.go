package preprocess

import (
	"context"

	"code.byted.org/flow/eino-byted-ext/components/model/bytedgpt"
	"code.byted.org/ies/codin/common/agentsdk/einox"
	"code.byted.org/ies/codinmodel/kitex_gen/base"
	"github.com/cloudwego/eino/schema"
)

func isClaude(modelType base.ModelType) bool {
	return modelType == base.ModelType_Claude || modelType == base.ModelType_Claude4_Opus || modelType == base.ModelType_Claude4_Sonnet
}

func bindCachePoint(msg *schema.Message) *schema.Message {
	if msg.Content == "" && len(msg.MultiContent) == 0 {
		// 必须补一下，否则claude会报错
		msg.MultiContent = append(msg.MultiContent, schema.ChatMessagePart{
			Type: schema.ChatMessagePartTypeText,
			Text: "(no content)",
		})
	}
	// 生成新的CachePoint
	newMsg := bytedgpt.SetMessageBreakpointOfClaude(msg)
	return newMsg
}

func MakePromptCacheHandler(modelType base.ModelType) einox.PreProcessHandler {
	return func(ctx context.Context, input []*schema.Message) ([]*schema.Message, error) {
		if !isClaude(modelType) {
			return input, nil
		}

		// 需要拷贝一份
		// 设置CachePoint会进行拷贝，拷贝应用到ret上，不要直接修改到state上
		// 否则会导致state上累计的cache point超过4个
		clone := make([]*schema.Message, 0, len(input))
		clone = append(clone, input...)

		// 可以加四个cache point，我们的策略是
		// 1. 最后一条 system message
		// 2. 最后一条 assistant 前面最近的 user/tool message
		// 3. 最后一条 assistant message
		// 4. 最后一条 user/tool message

		// 先给最后一个sp加上
		lastSystemIdx := 0
		for i := len(clone) - 1; i >= 0; i-- {
			if clone[i].Role == schema.System {
				lastSystemIdx = i
				break
			}
		}
		if lastSystemIdx != 0 {
			clone[lastSystemIdx] = bindCachePoint(clone[lastSystemIdx])
		}

		// 最后一条assistant加上
		lastAssistantIdx := 0
		for i := len(clone) - 1; i >= 0; i-- {
			if clone[i].Role == schema.Assistant {
				lastAssistantIdx = i
				break
			}
		}
		if lastAssistantIdx != 0 {
			clone[lastAssistantIdx] = bindCachePoint(clone[lastAssistantIdx])
		}

		// 最后一条assistant 前面最近的 user/tool message
		beforeAssistantIdx := 0
		for i := lastAssistantIdx - 1; i >= 0; i-- {
			if clone[i].Role == schema.User || clone[i].Role == schema.Tool {
				beforeAssistantIdx = i
				break
			}
		}
		if beforeAssistantIdx != 0 {
			clone[beforeAssistantIdx] = bindCachePoint(clone[beforeAssistantIdx])
		}

		// 最后一条user和tool，选择后者，加上
		lastClientIdx := 0
		for i := len(clone) - 1; i >= 0; i-- {
			if clone[i].Role == schema.User || clone[i].Role == schema.Tool {
				lastClientIdx = i
				break
			}
		}
		if lastClientIdx != 0 {
			clone[lastClientIdx] = bindCachePoint(clone[lastClientIdx])
		}

		return clone, nil
	}
}

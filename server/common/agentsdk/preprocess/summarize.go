package preprocess

import (
	"context"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/common/agentsdk/einox"
	"code.byted.org/ies/codin/common/agentsdk/preprocess/internal/summarize"
	"code.byted.org/ies/codin/common/llm"
	"code.byted.org/ies/codinmodel/kitex_gen/base"
	"github.com/cloudwego/eino/schema"
)

const coefficient = 0.7

func MakeSummarizeHandler(modelType base.ModelType) einox.PreProcessHandler {
	maxTokens := int32(float64(llm.GetInputTokenByModelType(modelType)) * coefficient)

	return func(ctx context.Context, input []*schema.Message) ([]*schema.Message, error) {
		messagesToSummary := input
		lastMessage := messagesToSummary[len(messagesToSummary)-1]
		var queryMessage *schema.Message

		if lastMessage.Role == schema.User {
			// 用户消息的时候才有query
			queryMessage = lastMessage
			messagesToSummary = messagesToSummary[:len(messagesToSummary)-1]
		}

		// 前置压缩对话消息
		summariedMsgs, err := summarize.SummaryConversationMessage(ctx, messagesToSummary, maxTokens, queryMessage)

		if err != nil {
			logs.CtxError(ctx, "summary conversation message error: %v", err)
			return nil, err
		}

		if queryMessage != nil {
			summariedMsgs = append(summariedMsgs, queryMessage)
		}
		return summariedMsgs, nil
	}
}

package summarize

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/common/fornax"
	"code.byted.org/ies/codin/common/metrics"
	"code.byted.org/ies/codin/common/utils"

	"code.byted.org/ies/codin/common/llm"
	"github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/schema"
)

// preprocessedMessages 包含待摘要消息和相关记录信息的容器
type preprocessedMessages struct {
	// messagesToSummarize 待摘要的消息列表
	messagesToSummarize []*schema.Message

	// existingSystemMessage 现有的系统消息（不参与摘要）
	existingSystemMessage *schema.Message

	// summarizedMessage 已摘要的消息列表
	summarizedMessage *schema.Message
}

const (
	summaryPromptKey = "capcut.devops.common_sp_msg_summary_prompt"
)

func SummaryConversationMessage(ctx context.Context, input []*schema.Message, maxTokens int32, queryMessage *schema.Message) (output []*schema.Message, err error) {
	if maxTokens == 0 {
		return input, nil
	}

	preprocessed, err := preprocessMessages(ctx, input, maxTokens)
	if err != nil {
		logs.CtxError(ctx, "preprocessMessages error, %v", err)
		return input, err
	}
	if len(preprocessed.messagesToSummarize) == 0 {
		logs.CtxInfo(ctx, "no messages to summarize")
		return input, nil
	}

	output, err = summarizeMessages(ctx, preprocessed, queryMessage)
	if err != nil {
		logs.CtxError(ctx, "summarizeMessages error, %v", err)
		return input, err
	}
	return output, nil
}

// preprocessMessages 预处理消息以进行摘要
func preprocessMessages(ctx context.Context, messages []*schema.Message, maxTokens int32) (*preprocessedMessages, error) {
	// 如果消息列表为空，则直接返回
	if len(messages) == 0 {
		return &preprocessedMessages{
			messagesToSummarize:   []*schema.Message{},
			existingSystemMessage: nil,
		}, nil
	}

	var existingSystemMessage *schema.Message
	// 首先处理系统消息（如果存在）
	if len(messages) > 0 && isSystemMessage(messages[0]) {
		existingSystemMessage = messages[0]
		// 从待摘要的消息列表中移除系统消息
		messages = messages[1:]
	}

	// 如果没有消息，返回空的预处理结果
	if len(messages) == 0 {
		return &preprocessedMessages{
			messagesToSummarize:   []*schema.Message{},
			existingSystemMessage: existingSystemMessage,
		}, nil
	}

	// 计算消息的token数量
	tokenCount := int32(0)

	// 处理已有的摘要消息（如果存在）
	var summarizedMessage *schema.Message
	if isSummaryMessage(messages[0]) {
		tokenCount += utils.CountTokensApproximately([]*schema.Message{messages[0]})
		summarizedMessage = messages[0]
		messages = messages[1:]
	}

	// 将tool call ID映射到对应的tool消息
	toolCallIDToToolMessage := make(map[string]*schema.Message)

	// 计算剩下的消息是否进行摘要
	for _, message := range messages {
		tokenCount += utils.CountTokensApproximately([]*schema.Message{message})

		if len(message.ToolCalls) > 0 {
			for _, toolCall := range message.ToolCalls {
				toolCallID := toolCall.ID
				if toolCallID != "" {
					toolCallIDToToolMessage[toolCallID] = message
				}
			}
		}

		if message.ToolCallID != "" {
			delete(toolCallIDToToolMessage, message.ToolCallID)
		}

		if tokenCount > maxTokens && len(toolCallIDToToolMessage) == 0 {
			logs.CtxInfo(ctx, "ready summary tokenCount: %d, maxTokens: %d", tokenCount, maxTokens)
			return &preprocessedMessages{
				messagesToSummarize:   messages,
				existingSystemMessage: existingSystemMessage,
				summarizedMessage:     summarizedMessage,
			}, nil
		}
	}
	logs.CtxInfo(ctx, "preprocessMessages done, tokenCount: %d, maxTokens: %d", tokenCount, maxTokens)
	// 不需要压缩，这时候保留所有的message
	return &preprocessedMessages{
		messagesToSummarize:   []*schema.Message{},
		existingSystemMessage: existingSystemMessage,
		summarizedMessage:     summarizedMessage,
	}, nil
}

func metricsCount(ctx context.Context, startTime time.Time, messageCount int) {
	cost := time.Since(startTime)
	metrics.Count(ctx, "summarization.throughput", map[string]string{
		"cost":          strconv.FormatInt(cost.Milliseconds(), 10),
		"psm":           env.PSM(),
		"message_count": strconv.FormatInt(int64(messageCount), 10),
	})
}

func summarizeMessages(ctx context.Context, preprocessed *preprocessedMessages, queryMessage *schema.Message) ([]*schema.Message, error) {
	defer func(t time.Time) {
		metricsCount(ctx, t, len(preprocessed.messagesToSummarize))
	}(time.Now())

	chatModel, err := llm.CreateSeed16(ctx)
	if err != nil {
		logs.CtxError(ctx, "create chat model error, %v", err)
		return nil, err
	}

	summaryMessage, err := generateSummaryMessage(ctx, chatModel, preprocessed, queryMessage)

	if err != nil {
		logs.CtxError(ctx, "generate summary message error, %v", err)
		return nil, err
	}
	result := []*schema.Message{}
	// sp
	if preprocessed.existingSystemMessage != nil {
		result = append(result, preprocessed.existingSystemMessage)
	}

	resultUserMessage := &schema.Message{
		Role:    schema.System,
		Content: fmt.Sprintf("Summary of the conversation so far: %s", summaryMessage.Content),
	}
	result = append(result, resultUserMessage)

	return result, nil
}

// 辅助函数：判断消息类型和获取相关信息
// Helper functions: determine message types and get related information

func isSystemMessage(message *schema.Message) bool {
	return message.Role == schema.System && (message.Extra == nil || message.Extra["summary"] == nil)
}

func isSummaryMessage(message *schema.Message) bool {
	return message.Role == schema.System && message.Extra != nil && message.Extra["summary"] != nil
}

func makeSummaryPrompt(ctx context.Context, preprocessed *preprocessedMessages, queryMessage *schema.Message) []*schema.Message {
	result := []*schema.Message{}

	systemPromptReader := fornax.NewSystemPromptReader()
	promptTml, err := systemPromptReader.GetPrompt(ctx, summaryPromptKey)
	if err != nil {
		logs.CtxError(ctx, "get summary prompt error, %v", err)
		return nil
	}

	if len(preprocessed.messagesToSummarize) > 0 {
		xmlContent := ""
		for _, msg := range preprocessed.messagesToSummarize {
			switch msg.Role {
			case schema.User:
				xmlContent += fmt.Sprintf("User: %s\n", msg.Content)
			case schema.Assistant:
				xmlContent += fmt.Sprintf("Assistant: %s\n", msg.Content)
			case schema.Tool:
				toolContent := msg.Content
				// 如果有ToolCalls信息，可以进一步丰富tool内容
				if len(msg.ToolCalls) > 0 {
					for _, tc := range msg.ToolCalls {
						toolContent += fmt.Sprintf(" [ToolCall: %s]", tc.Function.Name)
					}
				}
				xmlContent += fmt.Sprintf("Tool: %s\n", toolContent)
			default:
				// 其他角色直接忽略或按需处理2
			}
		}

		queryContent := ""

		if queryMessage != nil {
			queryContent = queryMessage.Content
		}

		promptTml = strings.ReplaceAll(promptTml, "${query}", queryContent)
		promptTml = strings.ReplaceAll(promptTml, "${conversation}", xmlContent)

		result = append(result, &schema.Message{
			Role:    schema.User,
			Content: promptTml,
		})
	}

	return result
}

func generateSummaryMessage(ctx context.Context, chatModel model.ToolCallingChatModel, preprocessed *preprocessedMessages, queryMessage *schema.Message) (*schema.Message, error) {
	prompt := makeSummaryPrompt(ctx, preprocessed, queryMessage)

	response, err := chatModel.Generate(ctx, prompt)
	if err != nil {
		logs.CtxError(ctx, "message value")
		return nil, err
	}
	logs.CtxInfo(ctx, "summary result: %v", response)

	return response, nil
}

package preprocess

import (
	"context"

	"code.byted.org/ies/codin/common/agentsdk/einox"
	"github.com/cloudwego/eino/schema"
)

// MultiPreprocessFactory 多预处理工厂，用于组合多个预处理函数
type MultiPreprocessFactory interface {
	// AddPreprocess 添加预处理函数
	AddPreprocess(handler einox.PreProcessHandler) MultiPreprocessFactory
	// Build 构建预处理函数
	Build() einox.PreProcessHandler
}

func NewMultiPreprocessFactory() MultiPreprocessFactory {
	return &multiImpl{}
}

type multiImpl struct {
	handlers []einox.PreProcessHandler
}

func (m *multiImpl) AddPreprocess(handler einox.PreProcessHandler) MultiPreprocessFactory {
	m.handlers = append(m.handlers, handler)
	return m
}

func (m *multiImpl) Build() einox.PreProcessHandler {
	return func(ctx context.Context, input []*schema.Message) ([]*schema.Message, error) {
		var err error
		for _, handler := range m.handlers {
			input, err = handler(ctx, input)
			if err != nil {
				return nil, err
			}
		}
		return input, nil
	}
}

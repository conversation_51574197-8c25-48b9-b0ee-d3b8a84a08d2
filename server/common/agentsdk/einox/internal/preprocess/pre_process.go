package preprocess

import (
	"context"

	"code.byted.org/ies/codin/common/agentsdk/einox/internal/state"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/schema"
)

const (
	lambdaTypePreProcess = "pre_process"
)

type PreProcessHandler func(ctx context.Context, input []*schema.Message) ([]*schema.Message, error)

func MakePreProcessLambda(state *state.State, handler PreProcessHandler) *compose.Lambda {
	return compose.InvokableLambda(func(ctx context.Context, input []*schema.Message) (output []*schema.Message, err error) {
		state.Messages = append(state.Messages, input...)
		return handler(ctx, state.Messages)
	}, compose.WithLambdaType(lambdaTypePreProcess))
}

func DefaultPreProcessHandler(ctx context.Context, input []*schema.Message) ([]*schema.Message, error) {
	return input, nil
}

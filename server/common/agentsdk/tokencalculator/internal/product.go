package internal

import (
	"context"

	"code.byted.org/overpass/capcut_devops_expense/kitex_gen/expense"
	"code.byted.org/overpass/capcut_devops_expense/kitex_gen/expense/expenseservice"
)

type productImpl struct {
	client expenseservice.Client
}

func NewProductClient(client expenseservice.Client) *productImpl {
	return &productImpl{
		client: client,
	}
}

func (i *productImpl) RecordTokenUsage(ctx context.Context, request *expense.RecordTokenUsageRequest) (*expense.RecordTokenUsageResponse, error) {
	return i.client.RecordTokenUsage(ctx, request)
}

func (i *productImpl) GetRemainingToken(ctx context.Context, request *expense.GetRemainingTokenRequest) (*expense.GetRemainingTokenResponse, error) {
	return i.client.GetRemainingToken(ctx, request)
}

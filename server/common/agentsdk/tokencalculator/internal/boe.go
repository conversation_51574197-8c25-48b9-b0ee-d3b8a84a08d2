package internal

import (
	"context"
	"math"

	"code.byted.org/ies/codinmodel/kitex_gen/base"
	"code.byted.org/overpass/capcut_devops_expense/kitex_gen/expense"
)

type boeImpl struct{}

func NewBoeClient() *boeImpl {
	return &boeImpl{}
}

func (i *boeImpl) RecordTokenUsage(ctx context.Context, request *expense.RecordTokenUsageRequest) (*expense.RecordTokenUsageResponse, error) {
	return &expense.RecordTokenUsageResponse{
		BaseResp: &base.BaseResp{
			StatusCode:    0,
			StatusMessage: "success",
		},
	}, nil
}

func (i *boeImpl) GetRemainingToken(ctx context.Context, request *expense.GetRemainingTokenRequest) (*expense.GetRemainingTokenResponse, error) {
	return &expense.GetRemainingTokenResponse{
		RemainingTokens:      math.MaxInt32,
		TotalRemainingTokens: math.MaxInt32,
		BaseResp: &base.BaseResp{
			StatusCode:    0,
			StatusMessage: "success",
		},
	}, nil
}

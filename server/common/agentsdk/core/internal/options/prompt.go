package options

import (
	"fmt"

	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/ies/codinmodel/kitex_gen/agenterrcode"
	"code.byted.org/ies/codinmodel/kitex_gen/userinput"
)

type fileContent struct {
	Path    string
	Content string
}

type folderContent struct {
	Path    string
	Content string
}

type imageContent struct {
	Base64 string
	Name   string
}

type parsedResult struct {
	FileContents   []fileContent
	FolderContents []folderContent
	ImageContents  []imageContent
	UserQuery      string
}

func ParseUserPrompt(userInput userinput.UserInput) (*string, error) {
	if userInput.Prompt == nil {
		// 没有输入内容的时候返回空字符串
		return nil, rpcerr.New(agenterrcode.ErrCode_ArgumentError, "user input is empty")
	}

	result := &parsedResult{
		FileContents:   make([]fileContent, 0),
		FolderContents: make([]folderContent, 0),
		UserQuery:      "",
	}

	if userInput.GlobalInfos != nil {
		for _, globalInfo := range userInput.GlobalInfos {
			switch globalInfo.GetType() {
			case userinput.GlobalInfoType_File:
				if globalInfo.GetFile() != nil {
					result.FileContents = append(result.FileContents, fileContent{
						Path:    globalInfo.GetFile().Path,
						Content: globalInfo.GetFile().Content,
					})
				}
			case userinput.GlobalInfoType_Folder:
				if globalInfo.GetFolder() != nil {
					result.FolderContents = append(result.FolderContents, folderContent{
						Path:    globalInfo.GetFolder().Path,
						Content: globalInfo.GetFolder().Content,
					})
				}
			case userinput.GlobalInfoType_Image:
				if globalInfo.GetImage() != nil {
					result.ImageContents = append(result.ImageContents, imageContent{
						Base64: globalInfo.GetImage().Base64,
						Name:   globalInfo.GetImage().Name,
					})
				}
			}
		}
	}

	// 解析prompt内容，提取文件内容、文件夹内容和用户查询，其中在UserInput里面会存放一些全局信息
	if userInput.Prompt != nil {
		promptItems := userInput.Prompt.GetItems()
		for _, promptItem := range promptItems {
			for _, item := range promptItem {
				switch item.GetType() {
				case userinput.PromptItemType_Text:
					if item.GetText() != nil {
						result.UserQuery += item.GetText().Text
					}
				case userinput.PromptItemType_File:
					if item.GetFile() != nil {
						result.UserQuery += fmt.Sprintf(" @%s ", item.GetFile().Path)
					}
				case userinput.PromptItemType_Folder:
					if item.GetFolder() != nil {
						result.UserQuery += fmt.Sprintf(" @%s ", item.GetFolder().Path)
					}
				}
			}
		}
	}

	str, err := renderTemplate(result)
	if err != nil {
		return nil, err
	}

	return &str, nil
}

// renderTemplate 渲染模板
func renderTemplate(result *parsedResult) (string, error) {
	var output string

	// Render attached files
	if len(result.FileContents) > 0 {
		output += renderAttachedFiles(result.FileContents) + "\n"
	}

	// Render attached folders
	if len(result.FolderContents) > 0 {
		output += renderAttachedFolders(result.FolderContents) + "\n"
	}

	// Render attached images
	if len(result.ImageContents) > 0 {
		output += renderAttachedImages(result.ImageContents) + "\n"
	}

	// Render user query
	if result.UserQuery != "" {
		output += renderUserQuery(result.UserQuery) + "\n"
	}

	return output, nil
}

func renderAttachedFolders(folders []folderContent) string {
	var result string
	result += "Here are some folder(s) I manually attached to my message:\n"
	for _, folder := range folders {
		result += fmt.Sprintf("Folder: %s\nContents of directory:\n\n%s\n", folder.Path, folder.Content)
	}
	return result
}

func renderAttachedFiles(files []fileContent) string {
	var result string
	result += "Below are some potentially helpful/relevant pieces of information for figuring out to respond\n<attached_files>\n"
	for _, file := range files {
		result += fmt.Sprintf("<file_contents>\n```path=%s\n%s\n```\n</file_contents>\n", file.Path, file.Content)
	}
	result += "</attached_files>"
	return result
}

func renderAttachedImages(images []imageContent) string {
	var result string
	result += "Below are some potentially helpful/relevant pieces of information for figuring out to respond\n<attached_images>\n"
	for _, image := range images {
		result += fmt.Sprintf("<image_contents>\n```base64=%s\n%s\n```\n</image_contents>\n", image.Base64, image.Name)
	}
	result += "</attached_images>"
	return result
}

func renderUserQuery(content string) string {
	return fmt.Sprintf("<user_query>\n%s\n</user_query>", content)
}

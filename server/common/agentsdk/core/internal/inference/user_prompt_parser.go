package inference

import (
	"code.byted.org/gopkg/jsonx"
	"code.byted.org/ies/codin/common/agentsdk/conversation/model"
	"code.byted.org/ies/codin/common/agentsdk/core/internal/options"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/ies/codinmodel/kitex_gen/agenterrcode"
	"github.com/cloudwego/eino/schema"
)

func processClientMessage(convOptions *options.ConversationOptions) ([]*schema.Message, error) {
	if convOptions.RawMessage != "" {
		return []*schema.Message{
			{
				Role:    schema.User,
				Content: convOptions.RawMessage,
			},
		}, nil
	}

	userMessage := convOptions.UserMessage
	toolMessages := convOptions.ToolMessages

	results := make([]*schema.Message, 0)

	// 工具的要放在前面
	for _, toolMessage := range toolMessages {
		// 工具消息获取
		m := &schema.Message{
			Role:       schema.Tool,
			Content:    toolMessage.GetContent(),
			ToolCallID: toolMessage.GetRequestId(),
		}
		model.SetFromUser(m)
		results = append(results, m)
	}

	if userMessage != nil {
		userInput := userMessage.GetContent()

		content, err := options.ParseUserPrompt(*userInput)
		if err != nil {
			return nil, err
		}
		// 用户消息获取
		m := &schema.Message{
			Role:    schema.User,
			Content: *content,
		}
		model.SetOriginalInput(m, jsonx.ToString(userInput))
		if userMessage.GetHiddenToUser() {
			model.SetHiddenToUser(m)
		}

		results = append(results, m)
	}

	if len(results) == 0 {
		return nil, rpcerr.New(agenterrcode.ErrCode_ArgumentError, "userMessage or toolMessage is required")
	}

	return results, nil
}

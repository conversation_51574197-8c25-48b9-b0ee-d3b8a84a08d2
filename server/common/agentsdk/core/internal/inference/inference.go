package inference

import (
	"context"
	"encoding/json"

	"code.byted.org/flow/eino-byted-ext/components/model/bytedgpt"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/common/agentsdk/conversation"
	conversationmodel "code.byted.org/ies/codin/common/agentsdk/conversation/model"
	"code.byted.org/ies/codin/common/agentsdk/core/internal/connector"
	"code.byted.org/ies/codin/common/agentsdk/core/internal/options"
	"code.byted.org/ies/codin/common/agentsdk/core/internal/processor"
	"code.byted.org/ies/codin/common/agentsdk/einox"
	"code.byted.org/ies/codin/common/agentsdk/lock"
	"code.byted.org/ies/codin/common/agentsdk/trace"
	"code.byted.org/ies/codin/common/group"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/ies/codinmodel/kitex_gen/agenterrcode"

	"github.com/cloudwego/eino/callbacks"
	"github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/schema"
)

type InferenceTask struct {
	// 外部构造传入
	ConvOptions   *options.ConversationOptions
	StreamOptions *options.StreamOptions
	AgentOptions  *options.AgentOptions

	ConversationId   string
	ConversationRepo conversation.ReaderWriter
	Locker           lock.Locker

	// 内部生成
	historyMessages []*schema.Message
	conversation    *conversationmodel.Conversation
	connector       connector.Connector
	processor       processor.Processor
	unlock          lock.UnlockFunc
}

func (i *InferenceTask) inference(ctx context.Context) (*schema.StreamReader[*schema.Message], error) {
	einomessages := []*schema.Message{
		{
			Role:    "system",
			Content: i.AgentOptions.SystemPrompt,
		},
	}
	einomessages = append(einomessages, i.historyMessages...)

	handlers := []callbacks.Handler{
		einox.CallbackToHandler(i.connector.GetEinoxCallback()),
		einox.CallbackToHandler(trace.NewCallback(i.ConversationId)),
	}
	if len(i.AgentOptions.EinoxCallbacks) > 0 {
		for _, c := range i.AgentOptions.EinoxCallbacks {
			handlers = append(handlers, einox.CallbackToHandler(c))
		}
	}
	if len(i.AgentOptions.Callbacks) > 0 {
		handlers = append(handlers, i.AgentOptions.Callbacks...)
	}

	opts := make([]compose.Option, 0)
	extraJson, _ := json.Marshal(map[string]string{
		"session_id": i.ConversationId,
	})
	opts = append(opts, compose.WithChatModelOption([]model.Option{
		bytedgpt.WithIsClaude(),
		bytedgpt.WithExtraHeader(map[string]string{
			"extra": string(extraJson),
		}),
	}...))

	output, err := i.AgentOptions.Agent.Stream(ctx, einomessages, handlers, opts...)
	if err != nil {
		if err == einox.ErrCanceled {
			logs.CtxInfo(ctx, "[agentinference] eino stream canceled, id: %s", i.ConversationId)
		} else {
			logs.CtxError(ctx, "[agentinference] eino stream failed: %v, id: %s", err, i.ConversationId)
		}
		return nil, err
	}
	return output, nil
}

func (i *InferenceTask) Inference(ctx context.Context) error {
	logs.CtxInfo(ctx, "[agentinference] inference, id: %s", i.ConversationId)
	unlock, err := i.Locker.Lock(ctx, i.ConversationId, i.AgentOptions.Agent.Timeout())
	if err != nil {
		logs.CtxError(ctx, "[agentinference] lock conversation failed: %v, id: %s", err, i.ConversationId)
		return rpcerr.Wrap(err, agenterrcode.ErrCode_LockFailed, "lock conversation failed")
	}

	err = i.prepare(ctx)
	if err != nil {
		unlock(ctx) // unlock immediately if prepare fails
		logs.CtxError(ctx, "[agentinference] prepare failed: %v, id: %s", err, i.ConversationId)
		// 已经是rpc error
		return err
	}
	// 解锁挂到任务上，updatestatus要用
	i.unlock = unlock

	return group.Go(ctx, i.AgentOptions.Agent.Timeout(), func(ctx context.Context) {
		closeFunc, ctx := trace.StartSpan(ctx, i.ConversationId, i.conversation.ParentId)
		// trace跟着eino生命周期走，不跟processor生命周期
		defer closeFunc()

		i.inference(ctx)
	})
}

func (i *InferenceTask) InferenceSync(ctx context.Context) (*schema.StreamReader[*schema.Message], error) {
	logs.CtxInfo(ctx, "[agentinference] inference, id: %s", i.ConversationId)
	unlock, err := i.Locker.Lock(ctx, i.ConversationId, i.AgentOptions.Agent.Timeout())
	if err != nil {
		logs.CtxError(ctx, "[agentinference] lock conversation failed: %v, id: %s", err, i.ConversationId)
		return nil, rpcerr.Wrap(err, agenterrcode.ErrCode_LockFailed, "lock conversation failed")
	}

	err = i.prepare(ctx)
	if err != nil {
		unlock(ctx) // unlock immediately if prepare fails
		logs.CtxError(ctx, "[agentinference] prepare failed: %v, id: %s", err, i.ConversationId)
		// 已经是rpc error
		return nil, err
	}
	// 解锁挂到任务上，updatestatus要用
	i.unlock = unlock

	closeFunc, ctx := trace.StartSpan(ctx, i.ConversationId, i.conversation.ParentId)
	// trace跟着eino生命周期走，不跟processor生命周期
	defer closeFunc()

	return i.inference(ctx)
}

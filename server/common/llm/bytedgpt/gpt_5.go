package bytedgpt

import (
	"context"

	"code.byted.org/ies/codin/common/llm/config"
	"code.byted.org/ies/codin/common/llm/interfaces"
	"code.byted.org/ies/codinmodel/kitex_gen/base"
)

func CreateGPT_5(ctx context.Context, apiKey string, model string) (*interfaces.ChatModelWithName, error) {
	return createFromBytedGPT(ctx, &BytedGPTConfig{
		AuthenticationConfig: &config.AuthenticationConfig{
			APIKey: apiKey,
		},
		ModelConfig: &config.ModelConfig{
			Model:     model,
			BaseURL:   "https://search.bytedance.net/gpt/openapi/online/v2/crawl",
			ModelType: base.ModelType_GPT_5,
		},
		OutputConfig: &config.OutputConfig{
			MaxTokens: 20000,
			// Temperature:      &config.TemperatureZero, // 目前只支持 1
			FrequencyPenalty: &config.FrequencyPenaltyZero,
			PresencePenalty:  &config.PresencePenaltyZero,
			Timeout:          config.DefaultTimeout,
		},
		ByAzure: true,
	})
}

package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"log"
	"regexp"
	"strings"

	"github.com/beevik/etree"
)

// ExtractXMLFromResponse 从UI模型返回的内容中提取XML字符串
// 处理各种可能的格式：纯XML、包含解释文本的XML、代码块包装的XML等
// 根据UI Agent技术方案，轻微的XML语法错误不影响后续Coding模型的代码生成，因此采用宽松的提取策略
func ExtractXMLFromResponse(response string) (string, error) {
	if response == "" {
		return "", fmt.Errorf("响应内容为空")
	}

	// 移除首尾空白字符
	response = strings.TrimSpace(response)

	// 情况1: 检查是否为纯XML格式（优先判断）
	// 当内容看起来像纯XML时，直接返回，不进行严格语法验证
	if isLikelyPureXML(response) {
		return response, nil
	}

	// 情况2: 提取被代码块包裹的XML（```xml ... ``` 或 ``` ... ```）
	// 支持多种格式：```xml ... ```, ``` ... ```, 以及带有标题的格式
	if extractedXML := extractFromCodeBlock(response); extractedXML != "" {
		return extractedXML, nil
	}

	// 情况3: 处理带有标题的代码块格式（如 "# XML压缩结果 ```xml ... ```"）
	// 匹配格式：任意文本 ```xml 内容 ``` 任意文本
	if extractedXML := extractFromTitleCodeBlock(response); extractedXML != "" {
		return extractedXML, nil
	}

	// 情况4: 查找第一个完整的XML文档结构
	if candidateXML, err := findCompleteXMLDocument(response); err == nil && candidateXML != "" {
		return candidateXML, nil
	}

	// 情况5: 尝试智能识别XML内容（新增策略）
	if extractedXML := intelligentXMLExtraction(response); extractedXML != "" {
		return extractedXML, nil
	}

	// 如果所有策略都失败，返回错误
	return "", fmt.Errorf("无法从响应中提取有效的XML内容")
}

// isLikelyPureXML 判断内容是否可能是纯XML格式
// 使用宽松的启发式规则，不依赖严格的XML语法验证
func isLikelyPureXML(content string) bool {
	// 快速排除明显不是XML的情况
	if strings.Contains(content, "```") || strings.Contains(content, "#") {
		return false
	}

	// 检查是否以XML标签开始（支持XML声明或直接标签）
	content = strings.TrimSpace(content)
	if strings.HasPrefix(content, "<?xml") || strings.HasPrefix(content, "<") {
		// 进行基本的XML结构检查，但不严格验证语法
		return hasBasicXMLStructure(content)
	}

	return false
}

// hasBasicXMLStructure 检查是否具有基本的XML结构特征
func hasBasicXMLStructure(content string) bool {
	// 检查是否包含XML标签对
	tagPattern := regexp.MustCompile(`<[^>]+>`)
	tags := tagPattern.FindAllString(content, -1)

	// 至少包含一个标签
	if len(tags) == 0 {
		return false
	}

	// 检查是否有成对的开始和结束标签，或自闭合标签
	hasOpeningTag := false
	hasClosingOrSelfClosing := false

	for _, tag := range tags {
		if strings.HasPrefix(tag, "</") {
			hasClosingOrSelfClosing = true
		} else if strings.HasSuffix(tag, "/>") {
			hasClosingOrSelfClosing = true
		} else if !strings.HasPrefix(tag, "<?") && !strings.HasPrefix(tag, "<!--") {
			hasOpeningTag = true
		}
	}

	return hasOpeningTag || hasClosingOrSelfClosing
}

// extractFromCodeBlock 从代码块中提取XML内容
func extractFromCodeBlock(response string) string {
	// 匹配 ```xml ... ``` 或 ``` ... ``` 格式
	codeBlockPattern := regexp.MustCompile("(?s)```(?:xml)?\\s*(.+?)\\s*```")
	matches := codeBlockPattern.FindStringSubmatch(response)
	if len(matches) > 1 {
		candidate := strings.TrimSpace(matches[1])
		// 使用宽松的XML检查而不是严格的语法验证
		if hasBasicXMLStructure(candidate) {
			return candidate
		}
		// 如果基本检查失败，尝试在内容中查找XML结构
		if extracted, err := findCompleteXMLDocument(candidate); err == nil {
			return extracted
		}
	}
	return ""
}

// extractFromTitleCodeBlock 从带标题的代码块中提取XML内容
func extractFromTitleCodeBlock(response string) string {
	// 匹配格式：任意文本 ```xml 内容 ``` 任意文本
	titleCodeBlockPattern := regexp.MustCompile("(?s).*?```(?:xml)?\\s*(.+?)\\s*```.*")
	matches := titleCodeBlockPattern.FindStringSubmatch(response)
	if len(matches) > 1 {
		candidate := strings.TrimSpace(matches[1])
		// 使用宽松的XML检查
		if hasBasicXMLStructure(candidate) {
			return candidate
		}
		// 如果基本检查失败，尝试在内容中查找XML结构
		if extracted, err := findCompleteXMLDocument(candidate); err == nil {
			return extracted
		}
	}
	return ""
}

// intelligentXMLExtraction 智能XML提取策略
// 适用于各种复杂格式的UI模型返回内容
func intelligentXMLExtraction(response string) string {
	// 策略1: 查找以'<'开始的行，可能是XML的开始
	lines := strings.Split(response, "\n")
	var xmlLines []string
	inXMLContent := false
	xmlStartFound := false

	for _, line := range lines {
		trimmedLine := strings.TrimSpace(line)

		// 识别XML开始的标志
		if !xmlStartFound && (strings.HasPrefix(trimmedLine, "<?xml") ||
			(strings.HasPrefix(trimmedLine, "<") && !strings.HasPrefix(trimmedLine, "<!--") &&
				!strings.Contains(trimmedLine, "```"))) {
			xmlStartFound = true
			inXMLContent = true
		}

		// 如果在XML内容中，收集行
		if inXMLContent {
			xmlLines = append(xmlLines, line)

			// 检查是否到达XML结束
			if xmlStartFound && trimmedLine != "" && strings.Contains(trimmedLine, ">") {
				// 尝试验证当前收集的内容是否形成有效的XML结构
				candidate := strings.Join(xmlLines, "\n")
				if hasBasicXMLStructure(candidate) {
					// 进一步检查是否是完整的XML文档
					if extracted, err := findCompleteXMLDocument(candidate); err == nil {
						return extracted
					}
				}
			}
		}
	}

	// 策略2: 使用更宽松的正则表达式查找XML模式
	xmlPattern := regexp.MustCompile("(?s)(<[^>]+>.*?</[^>]+>|<[^>]+/>)")
	matches := xmlPattern.FindAllString(response, -1)

	// 寻找最长和最完整的XML片段
	var bestCandidate string
	for _, match := range matches {
		if len(match) > len(bestCandidate) && hasBasicXMLStructure(match) {
			bestCandidate = match
		}
	}

	if bestCandidate != "" {
		return bestCandidate
	}

	// 策略3: 查找任何包含XML标签的连续文本块
	xmlBlockPattern := regexp.MustCompile("(?s)[^<]*(<[^>]+>.*?>[^<]*)")
	blockMatches := xmlBlockPattern.FindAllString(response, -1)

	for _, block := range blockMatches {
		if hasBasicXMLStructure(block) {
			return strings.TrimSpace(block)
		}
	}

	return ""
}

// findCompleteXMLDocument 查找并提取完整的XML文档
func findCompleteXMLDocument(content string) (string, error) {
	// 移除首尾空白
	trimmed := strings.TrimSpace(content)

	// 查找XML声明的位置
	xmlDeclPattern := regexp.MustCompile(`<\?xml[^>]*\?>`)
	xmlDeclMatch := xmlDeclPattern.FindStringIndex(trimmed)

	// 查找第一个XML根元素标签的位置（支持更多格式）
	rootTagPattern := regexp.MustCompile(`<([a-zA-Z_][\w\-:.]*)\s*(?:[^>]*?)?\s*(?:/>|>)`)
	rootTagMatches := rootTagPattern.FindAllStringSubmatchIndex(trimmed, -1)

	if len(rootTagMatches) == 0 {
		return "", fmt.Errorf("未找到有效的XML标签")
	}

	var startPos int
	var firstRootTagMatch []int

	// 确定XML文档的起始位置
	if xmlDeclMatch != nil {
		startPos = xmlDeclMatch[1] // 从XML声明结束位置开始
		// 找到XML声明后的第一个根元素
		for _, match := range rootTagMatches {
			if match[0] > xmlDeclMatch[1] { // 标签位置在XML声明之后
				firstRootTagMatch = match
				break
			}
		}
	} else {
		// 没有XML声明，使用文档开始位置
		startPos = 0
		firstRootTagMatch = rootTagMatches[0]
	}

	if firstRootTagMatch == nil {
		return "", fmt.Errorf("未找到有效的根元素标签")
	}

	// 提取根元素标签名
	rootTagName := trimmed[firstRootTagMatch[2]:firstRootTagMatch[3]]

	// 检查是否是自闭合标签
	tagContent := trimmed[firstRootTagMatch[0]:firstRootTagMatch[1]]
	if strings.HasSuffix(strings.TrimSpace(tagContent), "/>") {
		// 自闭合标签，直接返回从开始位置到标签结束的内容
		return trimmed[startPos:firstRootTagMatch[1]], nil
	}

	// 非自闭合标签，需要找到对应的结束标签
	endPos, err := findMatchingEndTag(trimmed, firstRootTagMatch[1], rootTagName)
	if err != nil {
		return "", fmt.Errorf("未找到匹配的结束标签: %w", err)
	}

	// 返回从开始位置到结束标签的完整内容
	return trimmed[startPos:endPos], nil
}

// findMatchingEndTag 找到匹配的结束标签位置
func findMatchingEndTag(content string, searchStart int, tagName string) (int, error) {
	// 创建开始标签和结束标签的正则表达式
	openTagPattern := regexp.MustCompile(fmt.Sprintf(`<%s\s*(?:[^>]*?)?\s*(?:/>|>)`, regexp.QuoteMeta(tagName)))
	closeTagPattern := regexp.MustCompile(fmt.Sprintf(`</%s\s*>`, regexp.QuoteMeta(tagName)))

	tagCount := 1 // 已经有一个开始标签
	pos := searchStart

	for pos < len(content) && tagCount > 0 {
		// 在剩余内容中查找下一个开始标签或结束标签
		remainingContent := content[pos:]

		openMatch := openTagPattern.FindStringIndex(remainingContent)
		closeMatch := closeTagPattern.FindStringIndex(remainingContent)

		// 判断哪个标签更靠前
		if openMatch != nil && (closeMatch == nil || openMatch[0] < closeMatch[0]) {
			// 找到开始标签
			tagContent := remainingContent[openMatch[0]:openMatch[1]]
			if !strings.HasSuffix(strings.TrimSpace(tagContent), "/>") {
				// 不是自闭合标签，计数增加
				tagCount++
			}
			pos += openMatch[1]
		} else if closeMatch != nil {
			// 找到结束标签
			tagCount--
			pos += closeMatch[1]

			if tagCount == 0 {
				// 找到匹配的结束标签
				return pos, nil
			}
		} else {
			// 没有找到更多标签
			break
		}
	}

	return 0, fmt.Errorf("未找到匹配的结束标签 </%s>", tagName)
}

// removeSpecialCharsFromComments 移除XML注释内容中的<与/>符号，避免后续匹配失败
func removeSpecialCharsFromComments(xmlContent string) string {
	// 匹配XML注释的正则表达式
	commentPattern := regexp.MustCompile(`<!--([\s\S]*?)-->`)

	return commentPattern.ReplaceAllStringFunc(xmlContent, func(match string) string {
		// 提取注释开始和结束标记
		start := "<!--"
		end := "-->"

		// 提取注释内容
		content := match[len(start) : len(match)-len(end)]

		// 移除注释内容中的所有XML标签相关符号，避免干扰后续的标签匹配
		// 按顺序处理，避免重复替换
		content = strings.ReplaceAll(content, "</", "") // 移除结束标签开始符号
		content = strings.ReplaceAll(content, "/>", "") // 移除自闭合标签结束符号
		content = strings.ReplaceAll(content, "<", "")  // 移除所有剩余的开始标签符号
		content = strings.ReplaceAll(content, ">", "")  // 移除所有剩余的标签结束符号

		// 重新组装注释
		return start + content + end
	})
}

// AddUniqueIDsToXML 为XML中所有标签添加唯一ID，并为整个XML生成唯一ID
// 返回处理后的XML字符串和XML的唯一ID
func AddUniqueIDsToXML(xmlContent string) (string, string, error) {
	if xmlContent == "" {
		return "", "", fmt.Errorf("XML内容为空")
	}

	// 移除首尾空白字符
	xmlContent = strings.TrimSpace(xmlContent)

	// 预处理：移除注释内容中的<与/>符号，避免后续匹配失败
	xmlContent = removeSpecialCharsFromComments(xmlContent)

	// 在外层包装一个DOM节点来挂载全局的xml-id
	xmlContent, xmlID, err := WrapXMLWithGlobalID(xmlContent)
	if err != nil {
		return "", "", fmt.Errorf("包装XML全局ID失败: %w", err)
	}

	// 为所有标签添加唯一ID
	xmlContent, err = addIDsToAllTags(xmlContent)
	if err != nil {
		return "", "", fmt.Errorf("为标签添加唯一ID失败: %w", err)
	}

	return xmlContent, xmlID, nil
}

// generateUniqueID 生成唯一的ID
func generateUniqueID() (string, error) {
	bytes := make([]byte, 8)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// wrapXMLWithGlobalID 在外层包装一个DOM节点来挂载全局的xml-id
// 如果顶层已经是MainUI节点，只添加xml-id属性；否则包装一个新的MainUI节点
// 根据UI Agent技术方案，轻微XML错误不影响Coding模型生成代码，因此采用容错策略
func WrapXMLWithGlobalID(xmlContent string) (string, string, error) {

	// 生成XML的唯一ID
	xmlID, err := generateUniqueID()
	if err != nil {
		return "", "", fmt.Errorf("生成XML唯一ID失败: %w", err)
	}

	// 查找XML声明的位置
	xmlDeclPattern := regexp.MustCompile(`<\?xml[^>]*\?>`)
	xmlDeclMatch := xmlDeclPattern.FindStringIndex(xmlContent)

	var preContent string
	var mainContent string

	if xmlDeclMatch != nil {
		// 有XML声明，分离XML声明和主内容
		preContent = xmlContent[:xmlDeclMatch[1]]
		mainContent = strings.TrimSpace(xmlContent[xmlDeclMatch[1]:])
	} else {
		// 没有XML声明，直接处理所有内容
		mainContent = strings.TrimSpace(xmlContent)
	}

	// 检查顶层是否已经是MainUI节点
	if isTopLevelMainUI(mainContent) {
		// 记录调试信息，帮助排查为什么进入MainUI分支
		log.Printf("[XML_DEBUG] 检测到MainUI节点，即将处理: xmlID=%s, mainContent=%s", xmlID, mainContent)

		// 如果已经是MainUI节点，尝试添加xml-id属性
		updatedContent, err := addXMLIDToMainUI(mainContent, xmlID)
		if err != nil {
			// 记录错误日志，但不中断处理流程
			log.Printf("[XML_PARSE_ERROR] 添加xml-id属性失败，返回原内容进行后续处理: %v, xmlID: %s, content: %s", err, xmlID, xmlContent)
			// 返回原内容，确保后续Coding模型能继续处理
			return xmlContent, xmlID, nil
		}

		// 如果有XML声明，将其放在更新后的内容之前
		if preContent != "" {
			return preContent + "\n" + updatedContent, xmlID, nil
		}
		return updatedContent, xmlID, nil
	}

	// 如果不是MainUI节点，按原逻辑包装
	log.Printf("[XML_DEBUG] 非MainUI节点，进行包装处理: xmlID=%s, mainContent=%s", xmlID, mainContent)
	wrappedContent := fmt.Sprintf(`<MainUI xml-id="%s">%s</MainUI>`, xmlID, mainContent)

	// 如果有XML声明，将其放在包装节点之前
	if preContent != "" {
		return preContent + "\n" + wrappedContent, xmlID, nil
	}

	return wrappedContent, xmlID, nil
}

// isTopLevelMainUI 检查顶层是否已经是MainUI节点
func isTopLevelMainUI(content string) bool {
	content = strings.TrimSpace(content)

	// 使用正则表达式匹配MainUI开始标签
	// 支持带属性的MainUI标签，如：<MainUI>, <MainUI attr="value">, <MainUI />
	mainUIPattern := regexp.MustCompile(`^<MainUI\s*(?:[^>]*)?>`)

	return mainUIPattern.MatchString(content)
}

// addXMLIDToMainUI 在MainUI节点中添加或更新xml-id属性
// 采用容错策略，支持不完整的XML标签
func addXMLIDToMainUI(content string, xmlID string) (string, error) {
	content = strings.TrimSpace(content)

	// 更宽松的正则表达式，支持多行属性和不完整的标签
	// 匹配 <MainUI...> 或 <MainUI...（没有闭合的>）
	mainUIPattern := regexp.MustCompile(`(?s)^(<MainUI\s*)([^>]*)(>.*)?$`)
	matches := mainUIPattern.FindStringSubmatch(content)

	if len(matches) < 3 {
		return "", fmt.Errorf("无法解析MainUI标签结构: 内容不符合预期格式")
	}

	tagStart := matches[1]   // "<MainUI"
	attributes := matches[2] // 现有属性
	var tagEnd string
	if len(matches) > 3 && matches[3] != "" {
		tagEnd = matches[3] // ">...rest of content" 或空
	} else {
		// 如果没有找到闭合标签，补充一个
		tagEnd = ">"
	}

	// 检查是否已经有xml-id属性
	xmlIDPattern := regexp.MustCompile(`xml-id\s*=\s*["'][^"']*["']`)

	var newAttributes string
	if xmlIDPattern.MatchString(attributes) {
		// 如果已经有xml-id属性，替换它
		newAttributes = xmlIDPattern.ReplaceAllString(attributes, fmt.Sprintf(`xml-id="%s"`, xmlID))
	} else {
		// 如果没有xml-id属性，添加它
		if strings.TrimSpace(attributes) == "" {
			newAttributes = fmt.Sprintf(`xml-id="%s"`, xmlID)
		} else {
			newAttributes = strings.TrimSpace(attributes) + fmt.Sprintf(` xml-id="%s"`, xmlID)
		}
	}

	// 重新组装标签
	result := tagStart + newAttributes + tagEnd

	return result, nil
}

// addIDsToAllTags 为所有XML标签添加唯一ID
func addIDsToAllTags(xmlContent string) (string, error) {
	// 匹配所有XML标签的正则表达式
	// 包括开始标签、结束标签、自闭合标签
	tagPattern := regexp.MustCompile(`<([^>]+)>`)

	// 用于存储已处理的标签，避免重复处理
	processedTags := make(map[string]bool)

	// 替换函数
	replaceFunc := func(match string) string {
		// 跳过已经处理过的标签
		if processedTags[match] {
			return match
		}

		// 跳过结束标签（以</开头）
		if strings.HasPrefix(match, "</") {
			processedTags[match] = true
			return match
		}

		// 跳过XML声明
		if strings.HasPrefix(match, "<?xml") {
			processedTags[match] = true
			return match
		}

		// 跳过注释标签
		if strings.HasPrefix(match, "<!--") {
			processedTags[match] = true
			return match
		}

		// 跳过CDATA标签
		if strings.HasPrefix(match, "<![CDATA[") {
			processedTags[match] = true
			return match
		}

		// 跳过处理指令
		if strings.HasPrefix(match, "<?") && !strings.HasPrefix(match, "<?xml") {
			processedTags[match] = true
			return match
		}

		// 为开始标签和自闭合标签添加唯一ID
		generatedID, err := generateUniqueID()
		if err != nil {
			// 如果生成ID失败，返回原标签
			return match
		}

		// 检查标签是否已经有id属性
		if strings.Contains(match, `id=`) {
			// 如果已经有id属性，替换现有的id值
			idPattern := regexp.MustCompile(`id\s*=\s*["'][^"']*["']`)
			newIDAttr := fmt.Sprintf(`id="%s"`, generatedID)
			newTag := idPattern.ReplaceAllString(match, newIDAttr)
			processedTags[match] = true
			return newTag
		}

		// 在标签末尾（>或/>之前）添加id属性
		var newTag string
		if strings.HasSuffix(strings.TrimSpace(match), "/>") {
			// 自闭合标签
			insertPos := strings.LastIndex(match, "/>")
			newTag = match[:insertPos] + fmt.Sprintf(` id="%s"`, generatedID) + match[insertPos:]
		} else {
			// 开始标签
			insertPos := strings.LastIndex(match, ">")
			newTag = match[:insertPos] + fmt.Sprintf(` id="%s"`, generatedID) + match[insertPos:]
		}

		processedTags[match] = true
		return newTag
	}

	// 执行替换
	result := tagPattern.ReplaceAllStringFunc(xmlContent, replaceFunc)

	return result, nil
}

// ExtractNodeByID 从XML字符串中根据id属性提取对应节点及其所有子节点，返回XML字符串
// 依赖 github.com/beevik/etree
func ExtractNodeByID(xmlContent string, nodeID string) (string, error) {
	if xmlContent == "" || nodeID == "" {
		return "", fmt.Errorf("XML内容或节点ID为空")
	}

	doc := etree.NewDocument()
	err := doc.ReadFromString(xmlContent)
	if err != nil {
		return "", fmt.Errorf("解析XML失败: %w", err)
	}

	// 查找id属性等于nodeID的节点
	node := findElementByID(doc.Root(), nodeID)
	if node == nil {
		return "", fmt.Errorf("未找到id为%s的节点", nodeID)
	}

	// 序列化该节点及其所有子节点
	doc2 := etree.NewDocument()
	doc2.SetRoot(node.Copy())
	result, err := doc2.WriteToString()
	if err != nil {
		return "", fmt.Errorf("序列化节点失败: %w", err)
	}
	return result, nil
}

// findElementByID 递归查找id属性等于nodeID的节点
func findElementByID(elem *etree.Element, nodeID string) *etree.Element {
	if elem == nil {
		return nil
	}
	if idAttr := elem.SelectAttr("id"); idAttr != nil && idAttr.Value == nodeID {
		return elem
	}
	for _, child := range elem.ChildElements() {
		if found := findElementByID(child, nodeID); found != nil {
			return found
		}
	}
	return nil
}

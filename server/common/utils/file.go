package utils

import (
	"encoding/json"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
)

/**
 * CollectFilePathsOptions 收集文件路径的选项
 * @param {int} Depth - 递归深度，0表示全量递归（不限制），>0表示限制层级
 * @param {[]string} IgnorePaths - 需要忽略的路径列表
 * @param {bool} CollectFile - 是否收集文件路径
 * @param {bool} CollectDir - 是否收集目录路径
 */
type CollectFilePathsOptions struct {
	// 递归深度，0表示全量递归（不限制），>0表示限制层级
	Depth int
	// 需要忽略的路径列表
	IgnorePaths []string
	// 是否收集文件
	CollectFile bool
	// 是否收集目录
	CollectDir bool
}

/*
GetFilePath 获取文件路径
@param {string} filePath - 文件路径
@param {string} rootPath - 项目根目录路径
@returns {string} 返回文件路径

处理预期可以看 UT 函数 TestGetFilePath，后续改动时需要跑过当前 UT Case
*/
func GetFilePath(filePath, rootPath string) string {
	var (
		cleanedFilePath, cleanedRootPath string
	)

	cleanedFilePath = strings.ReplaceAll(filePath, "\\", "/") // 兼容 Windows 路径
	cleanedRootPath = strings.ReplaceAll(rootPath, "\\", "/")

	cleanedFilePath = normalizePath(cleanedFilePath)
	cleanedRootPath = filepath.Clean(cleanedRootPath)

	if cleanedFilePath == "." || cleanedFilePath == "./" || cleanedFilePath == "" {
		return cleanedRootPath
	}

	// 优先尝试直接拼接
	candidate := filepath.Join(cleanedRootPath, cleanedFilePath)
	if fileExists(candidate) {
		return candidate
	}

	// 如果 filePath 已包含完整 rootPath 前缀，直接返回
	if strings.HasPrefix(cleanedFilePath, cleanedRootPath) {
		return cleanedFilePath
	}

	// 处理 filePath 与 rootPath 部分重复的情况
	relativePath := tryRemoveOverlap(cleanedFilePath, cleanedRootPath)
	candidate = filepath.Join(cleanedRootPath, relativePath)
	if fileExists(candidate) {
		return candidate
	}

	// 默认兜底返回拼接路径
	return candidate
}

// normalizePath 去除路径前缀、清洗格式
func normalizePath(p string) string {

	// 定义需要去除的前缀集合
	prefixes := []string{".", "/", "./"}

	// 循环去除前缀
	for _, prefix := range prefixes {
		for strings.HasPrefix(p, prefix) {
			p = strings.TrimPrefix(p, prefix)
		}
	}

	return filepath.Clean(p)
}

// tryRemoveOverlap 处理 rootPath 和 filePath 有部分重叠的情况
func tryRemoveOverlap(filePath, rootPath string) string {
	fileParts := strings.Split(filePath, string(os.PathSeparator))
	rootDir := filepath.Base(rootPath)

	if len(fileParts) > 0 && fileParts[0] == rootDir {
		return filepath.Join(fileParts[1:]...)
	}
	return filePath
}

// fileExists 检查文件或文件夹是否存在
func fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

/**
 * CollectFilePaths 递归收集目录下的所有文件/目录路径
 * @param {string} dirPath - 要扫描的子目录路径
 * @param {string} rootPath - 根路径 （实际的作用是： 会返回相对于rootPath的相对路径）
 * @param {CollectFilePathsOptions} options - 收集选项
 * @returns {[]string} 返回文件/目录路径列表
 */
func CollectFilePaths(dirPath string, rootPath string, options CollectFilePathsOptions) ([]string, error) {
	var paths []string
	path := GetFilePath(dirPath, rootPath)

	// 计算根目录深度
	rootDepth := len(strings.Split(filepath.Clean(path), string(os.PathSeparator)))

	// depth为0时，表示全量递归（不限制）
	realDepth := options.Depth
	if options.Depth == 0 {
		realDepth = -1
	}

	err := filepath.Walk(path, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			log.V2.Error().Str("error path").Str(path).Error(err).Emit()
			return err
		}

		// 获取相对路径
		relPath, err := filepath.Rel(rootPath, path)
		if err != nil {
			log.V2.Error().Str("error relPath").Str(relPath).Error(err).Emit()
			return err
		}

		// 检查是否在忽略列表中
		if ShouldIgnore(relPath, options.IgnorePaths) {
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		// 判断当前深度
		if realDepth >= 0 {
			curDepth := len(strings.Split(filepath.Clean(path), string(os.PathSeparator))) - rootDepth
			if curDepth > realDepth {
				return nil
			}
		}

		// 收集文件或目录
		if info.IsDir() && options.CollectDir && relPath != "." && relPath != "" {
			paths = append(paths, relPath+"/")
		} else if !info.IsDir() && options.CollectFile {
			paths = append(paths, relPath)
		}

		return nil
	})

	return paths, err
}

/**
 * shouldIgnore 检查路径是否应该被忽略
 * @param {string} path - 要检查的路径
 * @param {[]string} ignorePaths - 忽略路径列表
 * @returns {bool} 如果路径应该被忽略则返回 true
 */
func ShouldIgnore(path string, ignorePaths []string) bool {
	// 标准化路径 - 使用斜杠作为分隔符
	path = filepath.ToSlash(path)

	// 提取文件名和扩展名
	filename := filepath.Base(path)
	// ext := strings.ToLower(filepath.Ext(path)) // 当前未使用此变量

	for _, rule := range ignorePaths {
		// 清理和标准化规则
		rule = strings.TrimSpace(rule)
		if rule == "" {
			continue
		}
		rule = filepath.ToSlash(rule)

		// 扩展名匹配 (*.ext)
		if strings.HasPrefix(rule, "*.") {
			ruleExt := rule[1:] // 包含点号的扩展名
			if strings.HasSuffix(strings.ToLower(path), ruleExt) {
				// fmt.Printf("11111 rule: %v\n", rule, "   ", path)
				return true
			}
			continue
		}

		// 目录匹配 (dir/ 或 /dir/)
		if strings.HasSuffix(rule, "/") {
			dirPattern := strings.TrimSuffix(rule, "/")

			// 处理 ./dir/ 格式
			if strings.HasPrefix(dirPattern, "./") {
				dirPattern = dirPattern[2:]
			}

			// 检查目录匹配
			// 1. path 是否为该目录
			// 2. path 是否包含该目录作为路径组件
			if path == dirPattern ||
				strings.HasPrefix(path, dirPattern+"/") ||
				strings.Contains(path, "/"+dirPattern+"/") {
				// fmt.Printf("22222 rule: %v\n", rule, "   ", path)
				return true
			}
			continue
		}

		// 特定文件匹配
		if !strings.Contains(rule, "/") && !strings.Contains(rule, "*") {
			// 这是一个没有路径分隔符和通配符的简单文件名规则
			if filename == rule {
				// fmt.Printf("33333 rule: %v\n", rule, "   ", path)
				return true
			}
			continue
		}
	}

	return false
}

// 新增带depth的getFilePathsWithDepth
/**
 * matchAny 检查内容是否匹配正则表达式
 * @param {string} path - 待搜索的目录
 * @param {string} rootPath - 根路径 （实际的作用是： 会返回相对于rootPath的相对路径）
 */
func getFilePathsWithDepth(path string, rootPath string, options CollectFilePathsOptions) ([]string, error) {
	// fileInfo, err := os.Stat(path)
	// if err != nil {
	// 	return nil, fmt.Errorf("检查路径失败: %v", err)
	// }

	// var filePaths []string
	// if fileInfo.IsDir() {
	filePaths, err := CollectFilePaths(path, rootPath, options)
	if err != nil {
		return nil, fmt.Errorf("收集文件路径失败: %v", err)
	}
	return filePaths, nil
	// } else {
	// 	return []string{path}, nil
	// }
}

/**
 * matchAny 检查内容是否匹配正则表达式
 * @param {string} content - 文件内容
 * @param {string} searchPattern - 正则表达式模式
 * @returns {bool} 如果内容匹配正则表达式则返回 true
 */
func MatchAny(content, searchPattern string) bool {
	// 将正则表达式编译为不区分大小写的模式
	re, err := regexp.Compile("(?i)" + searchPattern)
	if err != nil {
		// 如果正则表达式编译失败，回退到 | 分隔的匹配逻辑
		contentLower := strings.ToLower(content)
		for _, kw := range strings.Split(searchPattern, "|") {
			kw = strings.TrimSpace(kw)
			if kw != "" && strings.Contains(contentLower, strings.ToLower(kw)) {
				return true
			}
		}
		return false
	}
	return re.MatchString(content)
}

/**
 * FindFileContent 查找文件内容
 * @param {string} path - 要搜索的目录
 * @param {string} rootPath - 根路径 （实际的作用是： 会返回相对于rootPath的相对路径）
 * @param {string} searchPattern - 搜索模式（支持|分隔的多关键词）
 * @param {[]string} ignorePaths - 需要忽略的路径列表
 * @returns {map[string]string} 返回文件路径到文件内容的映射
 * @returns {error} 返回可能的错误
 */
func FindFileContent(path string, rootPath string, searchPattern string, options CollectFilePathsOptions) (map[string]string, error) {
	// 存储结果
	results := make(map[string]string)

	// 获取文件路径列表
	filePaths, err := getFilePathsWithDepth(path, rootPath, options)
	if err != nil {
		return nil, err
	}

	// 遍历收集到的文件路径
	for _, relPath := range filePaths {
		// 构建完整路径
		// fullPath := relPath
		// if isDir {
		fullPath := GetFilePath(relPath, rootPath)
		// }

		// 读取文件内容
		content, err := os.ReadFile(fullPath)
		if err != nil {
			log.V2.Error().Str("error read file").Str("fullPath", fullPath).Error(err).Emit()
			continue
		}

		// 如果提供了搜索模式，检查文件内容是否匹配（支持多关键词）
		if searchPattern != "" {
			if !MatchAny(string(content), searchPattern) {
				continue
			}
		}
		// 将文件内容添加到结果中
		results[relPath] = string(content)
	}

	return results, nil
}

// getLinesByRange 按行截取内容，返回指定范围的字符串
// @param content 文件内容
// @param startLine 起始行（1开始）
// @param endLine 结束行（1开始，包含）
// @return 截取后的内容
func GetLinesByRange(content string, startLine, endLine int) string {
	lines := strings.Split(content, "\n")
	start := startLine - 1
	end := endLine
	if start < 0 {
		start = 0
	}
	if end > len(lines) || end <= 0 {
		end = len(lines)
	}
	if start > end {
		start = end
	}
	selected := lines[start:end]
	return strings.Join(selected, "\n")
}

// 去除前缀
func RemovePrefix(path string, prefix string) string {
	rel := path
	if strings.HasPrefix(path, prefix) {
		rel = strings.TrimPrefix(path, prefix)
		rel = strings.TrimPrefix(rel, "/") // 防止多余的/
	}
	return rel
}

/**
 * @description 生成日志键，处理路径并提取最后一个/后的内容作为key
 * @param path 原始路径
 * @return string 生成的日志键
 */
func GeneratePathKey(path string) string {
	// 去除路径开头和结尾的 / / Remove leading and trailing /
	trimmedPath := strings.Trim(path, "/")

	// 如果路径为空，返回默认值 / If path is empty, return default value
	if trimmedPath == "" {
		return fmt.Sprintf("%s_default", time.Now().Format("2006-01-02 15:04:05"))
	}

	// 按 / 分割路径 / Split path by /
	pathParts := strings.Split(trimmedPath, "/")

	// 获取最后一个部分 / Get the last part
	lastPart := pathParts[len(pathParts)-1]

	// URL编码最后一个部分 / URL encode the last part
	encodedPath := url.QueryEscape(lastPart)

	// 生成日志键 / Generate log key
	return fmt.Sprintf("%s_%s", time.Now().Format("2006-01-02 15:04:05"), encodedPath)
}

func GetLastPart(path string) string {
	// 去除路径开头和结尾的 / / Remove leading and trailing /
	trimmedPath := strings.Trim(path, "/")

	// 如果路径为空，返回默认值 / If path is empty, return default value
	if trimmedPath == "" {
		return ""
	}

	// 按 / 分割路径 / Split path by /
	pathParts := strings.Split(trimmedPath, "/")

	// 获取最后一个部分 / Get the last part
	lastPart := pathParts[len(pathParts)-1]

	// URL编码最后一个部分 / URL encode the last part
	encodedPath := url.QueryEscape(lastPart)

	// 生成日志键 / Generate log key
	return encodedPath
}

func GetFileName(path string) string {
	return filepath.Base(path)
}

/**
 * @description 将路径数组格式化成树形结构
 * @param paths 路径数组（目录以 / 结尾）
 * @return string 格式化的树形结构字符串
 */
func FormatPathsToTree(paths []string) string {
	if len(paths) == 0 {
		return "空目录"
	}

	// 创建树形结构
	tree := make(map[string]interface{})

	// 处理每个路径
	for _, path := range paths {
		parts := strings.Split(strings.TrimSuffix(path, "/"), "/")
		current := tree

		for i, part := range parts {
			if part == "" {
				continue
			}

			// 检查是否是最后一个部分（文件）
			isFile := i == len(parts)-1 && !strings.HasSuffix(path, "/")

			if _, exists := current[part]; !exists {
				if isFile {
					current[part] = "📄 " + part // 文件用📄标识
				} else {
					current[part] = make(map[string]interface{}) // 目录用map标识
				}
			}

			// 移动到下一层
			if !isFile {
				current = current[part].(map[string]interface{})
			}
		}
	}

	// 递归构建树形字符串
	return buildTreeString(tree, "", true)
}

/**
 * @description 递归构建树形字符串
 * @param node 当前节点
 * @param prefix 前缀字符串
 * @param isLast 是否是最后一个节点
 * @return string 格式化的树形字符串
 */
func buildTreeString(node map[string]interface{}, prefix string, isLast bool) string {
	var result strings.Builder

	// 获取所有键并排序
	keys := make([]string, 0, len(node))
	for key := range node {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	for i, key := range keys {
		value := node[key]
		isLastItem := i == len(keys)-1

		// 构建当前行的前缀
		currentPrefix := prefix
		if isLast {
			currentPrefix += "    "
		} else {
			currentPrefix += "│   "
		}

		// 构建连接线
		connector := "├── "
		if isLastItem {
			connector = "└── "
		}

		// 处理不同类型的节点
		switch v := value.(type) {
		case string:
			// 文件节点
			result.WriteString(prefix + connector + v + "\n")
		case map[string]interface{}:
			// 目录节点
			dirName := "📁 " + key
			result.WriteString(prefix + connector + dirName + "\n")

			// 递归处理子节点
			subTree := buildTreeString(v, currentPrefix, isLastItem)
			result.WriteString(subTree)
		}
	}

	return result.String()
}

func WriteJsonToDir(key string, logDir string, logContent any) {
	jsonData, err := json.MarshalIndent(logContent, "", "\n")
	if err != nil {
		fmt.Printf("WriteJsonToDir error: %v\n", err)
		return
	}
	WriteLogToDir(key, logDir, string(jsonData))
}

// WriteLogToDir 将日志内容写入本地文件，每条新起一行
func WriteLogToDir(key string, logDir string, logContent string) {
	WriteFile(key+".log", "codesearch_logs/"+logDir, logContent)
}

func WriteFile(key string, logDir string, logContent string) {
	// 创建日志目录
	if err := os.MkdirAll(logDir, 0755); err != nil {
		fmt.Println(err)
		return
	}

	// 构建日志文件路径
	logFile := filepath.Join(logDir, key)
	f, err := os.OpenFile(logFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer f.Close()

	// 写入日志内容
	// 将字符串中的\n转换为实际的换行符
	formattedContent := strings.ReplaceAll(logContent, "\\n", "\n")
	line := time.Now().Format("2006-01-02 15:04:05") + "\n " + formattedContent + "\n"
	f.WriteString(line)

	// 检查并清理旧日志
	cleanOldLogs(logDir)
}

// cleanOldLogs 清理旧日志文件，保留最新的100个
func cleanOldLogs(logDir string) {
	// 读取目录下的所有日志文件
	files, err := os.ReadDir(logDir)
	if err != nil {
		return
	}

	// 如果文件数量小于等于100，不需要清理
	if len(files) <= 100 {
		return
	}

	// 按修改时间排序
	type fileInfo struct {
		name    string
		modTime time.Time
	}
	var fileInfos []fileInfo
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".log") {
			info, err := file.Info()
			if err != nil {
				continue
			}
			fileInfos = append(fileInfos, fileInfo{
				name:    file.Name(),
				modTime: info.ModTime(),
			})
		}
	}

	// 按修改时间排序
	sort.Slice(fileInfos, func(i, j int) bool {
		return fileInfos[i].modTime.Before(fileInfos[j].modTime)
	})

	// 删除最旧的文件，直到剩余100个
	for i := 0; i < len(fileInfos)-100; i++ {
		os.Remove(filepath.Join(logDir, fileInfos[i].name))
	}
}

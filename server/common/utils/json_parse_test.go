package utils

import (
	"encoding/json"
	"io/ioutil"
	"strings"
	"testing"
)

func TestExtractJSONFromResponse(t *testing.T) {
	tests := []struct {
		name    string
		input   string
		wantErr bool
	}{
		{
			name: "简单JSON代码块",
			input: `这是一个简单的JSON数据：
` + "```json" + `
{
  "nodes": [
    {
      "id": "test",
      "type": "FRAME"
    }
  ]
}
` + "```" + `
解析完成`,
			wantErr: false,
		},
		{
			name:    "空输入",
			input:   "",
			wantErr: true,
		},
		{
			name:    "无效JSON",
			input:   "这不是JSON",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ExtractJSONFromResponse(tt.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExtractJSONFromResponse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err == nil {
				// 验证提取的是有效的JSON
				var obj interface{}
				if err := json.Unmarshal([]byte(got), &obj); err != nil {
					t.Errorf("提取的内容不是有效的JSON: %v", err)
				}

				// 验证JSON中不包含反引号
				if strings.Contains(got, "`") {
					t.Errorf("提取的JSON不应该包含反引号")
				}
			}
		})
	}
}

func TestExtractJSONFromResponse_DemoFile(t *testing.T) {
	// 直接读取demo.txt文件内容
	data, err := ioutil.ReadFile("demo.txt")
	if err != nil {
		t.Fatalf("无法读取demo.txt文件: %v", err)
	}

	input := string(data)
	t.Logf("文件内容长度: %d 字符", len(input))

	got, err := ExtractJSONFromResponse(input)
	if err != nil {
		t.Logf("提取失败，错误: %v", err)
		// 打印原始输入的前500个字符用于调试
		t.Logf("原始输入前500个字符: %s", input[:min(500, len(input))])

		// 查找```json标记
		if idx := strings.Index(input, "```json"); idx != -1 {
			t.Logf("找到```json标记位置: %d", idx)
			// 显示标记周围的内容
			start := max(0, idx-50)
			end := min(len(input), idx+200)
			t.Logf("```json标记周围内容: %s", input[start:end])
		}

		t.Fatalf("处理demo.txt数据失败: %v", err)
	}

	t.Logf("提取的JSON长度: %d 字符", len(got))
	t.Logf("提取的JSON前200个字符: %s", got[:min(200, len(got))])

	// 验证提取的是有效的JSON
	var obj interface{}
	if err := json.Unmarshal([]byte(got), &obj); err != nil {
		t.Errorf("提取的内容不是有效的JSON: %v", err)
		return
	}

	// 验证JSON结构
	jsonMap, ok := obj.(map[string]interface{})
	if !ok {
		t.Error("JSON不是一个对象")
		return
	}

	// 验证必要字段
	requiredFields := []string{"nodes", "components", "globalVars"}
	for _, field := range requiredFields {
		if _, exists := jsonMap[field]; !exists {
			t.Errorf("JSON缺少必要字段: %s", field)
		}
	}

	// 验证nodes数组
	nodes, ok := jsonMap["nodes"].([]interface{})
	if !ok || len(nodes) == 0 {
		t.Error("nodes不是一个非空数组")
		return
	}

	t.Logf("成功解析，nodes数组包含 %d 个元素", len(nodes))

	// 验证第一个节点的必要字段
	firstNode, ok := nodes[0].(map[string]interface{})
	if !ok {
		t.Error("第一个节点不是一个对象")
		return
	}

	nodeRequiredFields := []string{"id", "type"}
	for _, field := range nodeRequiredFields {
		if _, exists := firstNode[field]; !exists {
			t.Errorf("节点缺少必要字段: %s", field)
		}
	}
}

// 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

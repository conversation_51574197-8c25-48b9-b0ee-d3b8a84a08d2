package utils

import "strings"

/**
 * @description 标准化路径，移除前后的斜杠
 * @param path 原始路径
 * @return string 标准化后的路径
 */
func NormalizePath(path string) string {
	// 标准化路径分隔符
	normalized := strings.ReplaceAll(path, "\\", "/")
	// 移除开头的斜杠，但保留根路径 "/"
	if normalized != "/" && strings.HasPrefix(normalized, "/") {
		normalized = strings.TrimPrefix(normalized, "/")
	}
	if normalized != "/" && strings.HasSuffix(normalized, "/") {
		normalized = strings.TrimSuffix(normalized, "/")
	}
	return normalized
}

package utils

import (
	"strings"

	"code.byted.org/flow/datamind-code-index/model"
	code "code.byted.org/ies/codin/common/semantic/codebase/entity"
)

func ConvertDependencyToCodeRelationSnippets(chunks *[]model.QueryChunkRelation) []*code.CodeRelationSnippet {
	var codeRelationSnippets []*code.CodeRelationSnippet
	for _, chunk := range *chunks {
		codeRelationSnippet := &code.CodeRelationSnippet{
			Content:         chunk.Content,
			ChunkID:         chunk.ChunkID,
			RelationType:    chunk.RelationType,
			RelationSubType: chunk.RelationSubType,
		}
		codeRelationSnippets = append(codeRelationSnippets, codeRelationSnippet)
	}
	return codeRelationSnippets
}

// 构建chunkID到chunk的映射，便于查找
/**
 * 将查询结果转换为代码片段数组
 * Convert query results to code snippets array
 * @param result 混合查询结果 / Hybrid query result
 * @return 代码片段数组 / Array of code snippets
 */
func ConvertToCodeSnippets(result *model.HybridQueryResult) []*code.CodeSnippet {
	if len(result.Entities) == 0 {
		return []*code.CodeSnippet{}
	}
	// 构建chunkID到chunk的映射，便于查找
	chunkMap := make(map[string]model.Chunk)
	var codeSnippets []*code.CodeSnippet
	for _, chunk := range result.Chunks {
		// fmt.Printf("chunk.ChunkID: %v\n", chunk.ChunkID)
		chunkMap[chunk.ChunkID] = chunk
		// chunk表里面包括naive rag的chunk，和graph rag的entities的chunk
		codeSnippet := &code.CodeSnippet{
			Content:  chunk.Content,
			FilePath: NormalizeChunkIDToPath(chunk.ChunkID),
			Summary:  "",
			ChunkID:  chunk.ChunkID,
		}
		codeSnippets = append(codeSnippets, codeSnippet)
	}
	// 此处是提取entities里面的chunk
	// for _, entity := range result.Entities {
	// 	if entity.RelevanceScore < 0.5 {
	// 		continue
	// 	}
	// 	var contentBuilder strings.Builder
	// 	var filePath string

	// 	// 遍历实体的所有chunk
	// 	for _, chunkID := range entity.ChunkIDs {
	// 		// fmt.Printf("chunkID: %v\n", chunkID)
	// 		if chunk, ok := chunkMap[chunkID]; ok {
	// 			contentBuilder.WriteString(chunk.Content)
	// 			// 此处一个entity可能对应多个chunk
	// 			if filePath == "" {
	// 				// filePath = chunk.ChunkID
	// 				// 因为marsCode那边的切分，导致这个path有问题，所以先简单的处理一下path前后缀的问题
	// 				filePath = normalizeChunkIDToPath(chunk.ChunkID)
	// 			}
	// 		}
	// 	}
	// 	content := contentBuilder.String()
	// 	if content == "" {
	// 		continue
	// 	}
	// 	// 创建代码片段
	// 	codeSnippet := &code.CodeSnippet{
	// 		Content:    contentBuilder.String(),
	// 		FilePath:   filePath,
	// 		Summary:    entity.EntityDesc,
	// 		EntityName: entity.EntityName,
	// 	}
	// 	codeSnippets = append(codeSnippets, codeSnippet)
	// }

	return codeSnippets
}

// normalizeChunkIDToPath 规范化chunkID为文件路径
// 英文：normalize chunkID to file path
func NormalizeChunkIDToPath(chunkID string) string {
	// 去除最后一个/后的#及其后缀（如a/b/c.e#f -> a/b/c.e）
	lastSlash := strings.LastIndex(chunkID, "/")
	if lastSlash >= 0 {
		file := chunkID[lastSlash+1:]
		if dotIdx := strings.Index(file, "#"); dotIdx > 0 {
			chunkID = chunkID[:lastSlash+1+dotIdx]
		}
	}
	return chunkID
}

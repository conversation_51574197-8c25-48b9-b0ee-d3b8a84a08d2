package think

import (
	"context"

	toolUtils "code.byted.org/ies/codin/common/tool/utils"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type ThinkTool struct {
}

func NewThinkTool() tool.InvokableTool {
	return &ThinkTool{}
}

func (t *ThinkTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "think",
		Desc: `Describe on what you know so far, any new context you see, and how that aligns with your objective and the user's intent. You can play through different scenarios, weigh options, and reason about possible next next steps. Be concise and to the point. The user will not see any of your thoughts here, so you can think freely.
Description: This think tool acts as a scratchpad you can use to spend extra time thinking in hard situations. You are allowed to use this tool by itself without any other tools. Use this tool in the following situations:

You MUST use the think tool in the following situation:
- Before telling the user that you have completed the task. You need to reflect on whether you actually fulfilled the full intent of the task. 
- User request to replan.

You MAY use the think tool in the following situations if there are multiple options for what to do next:
- if you tried multiple approaches to solve a problem but nothing seems to work, so you need to reflect about alternatives
- you are in a situation that is difficult and would benefit from some extra thought to get it right

In other situations, you are not permitted to utilize the think tool and will be penalized if you do. Note that think tool calls are always scrubbed from your previous action, so you will not see your past uses of the think tool.

Important: you are only allowed to output at most one think tool per response and if you use it, it always must be the very first tool you output.
		`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"explanation": {
				Type:     "string",
				Desc:     "One sentence explanation as to why this tool is being used, and how it contributes to the goal.",
				Required: false,
			},
			"content": {
				Type:     "string",
				Desc:     "The content of your thoughts",
				Required: true,
			},
		}),
	}, nil
}

func (t *ThinkTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	return toolUtils.CreateToolResult(toolUtils.ToolResultOptions{
		ToolStatus: toolUtils.ToolStatusSuccess,
		ToolOutput: "Your thought process has been successfully encrypted and recorded; only you can access it, and users cannot view it.",
	})
}

package xmlgenerater


//长文本测试
var TestXmlFile = `
<?xml version="1.0" encoding="UTF-8"?>
<design name="即梦Action">
  <globalVars>
    <styleVar id="layout_V2Q4LH"><![CDATA[{"height":"75px","mode":"none","flexGrow":1}]]></styleVar>
    <styleVar id="layout_AT0WSG"><![CDATA[{"height":"96px","mode":"row","justifyContent":"center","alignItems":"center","gap":"10px","padding":"12px","alignSelf":"stretch"}]]></styleVar>
    <styleVar id="layout_YZXCEH"><![CDATA[{"width":"112px","height":"64px","mode":"none"}]]></styleVar>
    <styleVar id="layout_G7OIMT"><![CDATA[{"mode":"row","justifyContent":"center","alignItems":"center","gap":"10px","alignSelf":"stretch"}]]></styleVar>
    <styleVar id="layout_M8NEG3"><![CDATA[{"position":"absolute","width":"100.00%","height":"100.00%","top":"0.00%","left":"0.00%","mode":"none"}]]></styleVar>
    <styleVar id="layout_EO68W7"><![CDATA[{"width":"36px","height":"36px","mode":"column","justifyContent":"center","alignItems":"center","gap":"10px"}]]></styleVar>
    <styleVar id="layout_TI00S5"><![CDATA[{"mode":"row","justifyContent":"center","alignItems":"center","gap":"10px","padding":"16px 48px","alignSelf":"stretch"}]]></styleVar>
    <styleVar id="stroke_T574EU"><![CDATA[{"colors":[{"rgba":"rgba(224, 245, 255, 0.60)"}],"strokeWeight":"1px","align":"CENTER"}]]></styleVar>
    <styleVar id="layout_8HSLE7"><![CDATA[{"height":"96px","mode":"row","justifyContent":"center","alignItems":"center","gap":"10px","padding":"24px 48px","alignSelf":"stretch"}]]></styleVar>
    <styleVar id="layout_ZGDCB8"><![CDATA[{"mode":"row","alignItems":"center","gap":"4px","alignSelf":"stretch"}]]></styleVar>
    <styleVar id="layout_TI6TXH"><![CDATA[{"width":"16px","height":"16px","mode":"none"}]]></styleVar>
    <styleVar id="layout_84Z37T"><![CDATA[{"mode":"row","justifyContent":"center","alignItems":"center","gap":"10px","padding":"12px 48px","alignSelf":"stretch"}]]></styleVar>
    <styleVar id="stroke_U6BS7N"><![CDATA[{"colors":[{"rgba":"rgba(204, 221, 255, 0.12)"}],"strokeWeight":"1px","strokeDashes":[1,3],"strokeWeights":"0px 0px 0px 1px","align":"CENTER"}]]></styleVar>
    <styleVar id="layout_HTJRMC"><![CDATA[{"mode":"row","justifyContent":"center","alignItems":"center","gap":"4px","padding":"6px 12px"}]]></styleVar>
  </globalVars>
  <frame name="内容" id="6608:125841" layout-inline="{"mode":"row","alignItems":"center"}">
    <instance name="侧边栏" id="6608:125842" componentId="3610:56777" layout-inline="{"height":"821px","mode":"column","justifyContent":"space-between","alignItems":"center","gap":"48px","padding":"20px 0px"}">
      <instance name="侧边栏" id="I6608:125842;7039:103403" componentId="1446:89590" layout-inline="{"height":"812px","mode":"column","justifyContent":"space-between","alignItems":"center","gap":"48px","padding":"20px 0px 20px 20px"}">
        <instance name="Logo" id="I6608:125842;7039:103403;1446:60470" componentId="895:72236" layout-inline="{"width":"40px","height":"40px","mode":"none"}" borderRadius="12px">
          <boolean_operation name="Logo" id="I6608:125842;7039:103403;1446:60470;279:102186" layout-inline="{"position":"absolute","width":"24px","height":"25px","top":"50%","left":"50%","mode":"none","transform":"translateY(-50%) translateX(-50%)"}">
            <image-svg name="Vector" id="I6608:125842;7039:103403;1446:60470;279:102187" layout-inline="{"position":"absolute","width":"12px","height":"9px","top":"50%","left":"50%","mode":"none","transform":"translateY(-50%) translateX(-50%)"}" />
            <image-svg name="Vector" id="I6608:125842;7039:103403;1446:60470;279:102188" layout-inline="{"position":"absolute","width":"24px","height":"22px","top":"50%","left":"50%","mode":"none","transform":"translateY(-50%) translateX(-50%)"}" />
          </boolean_operation>
        </instance>
        <frame name="Tabs" id="I6608:125842;7039:103403;1891:115263" layout-inline="{"width":"40px","mode":"column","justifyContent":"center","gap":"16px","padding":"8px 0px"}" borderRadius="50px">
          <frame name="Tab" id="I6608:125842;7039:103403;1891:115264" layout-inline="{"width":"40px","height":"40px","mode":"column","justifyContent":"center","alignItems":"center","gap":"4px","padding":"8px"}" borderRadius="12px">
            <frame name="Frame 2131127010" id="I6608:125842;7039:103403;1891:115265" layout-inline="{"width":"20px","height":"20px","mode":"row","justifyContent":"center","alignItems":"center","gap":"8px","padding":"8px"}">
              <instance name="add" id="I6608:125842;7039:103403;1891:115266" componentId="1:113" layout-inline="{"width":"20px","height":"20px","mode":"none"}">
                <image-svg name="Outlined" id="I6608:125842;7039:103403;1891:115266;19220:41719" layout-inline="{"position":"absolute","width":"79.99%","height":"79.99%","top":"10.01%","left":"10.00%","mode":"none"}" />
              </instance>
            </frame>
          </frame>
          <frame name="Tab" id="I6608:125842;7039:103403;1891:115267" layout-inline="{"width":"40px","height":"40px","mode":"column","justifyContent":"center","alignItems":"center","gap":"4px","padding":"8px"}" borderRadius="12px">
            <frame name="Frame 2131126841" id="I6608:125842;7039:103403;1891:115268" layout-inline="{"width":"36px","height":"36px","mode":"row","justifyContent":"center","alignItems":"center","gap":"10px"}">
              <frame name="Frame 2131127011" id="I6608:125842;7039:103403;1891:131987" layout-inline="{"width":"20px","height":"20px","mode":"none"}">
                <rectangle name="Rectangle 279335885" id="I6608:125842;7039:103403;1891:134548" layout-inline="{"position":"absolute","width":"12px","height":"15px","top":"3px","left":"8px","mode":"none"}" borderRadius="2px" />
                <boolean_operation name="Subtract" id="I6608:125842;7039:103403;1891:136893" layout-inline="{"position":"absolute","width":"9px","height":"14px","top":"3px","left":"0px","mode":"none"}">
                  <rectangle name="Rectangle 279335884" id="I6608:125842;7039:103403;1891:133011" layout-inline="{"position":"absolute","width":"12px","height":"15px","top":"-0px","left":"-0px","mode":"none"}" borderRadius="2px" />
                  <image-svg name="Rectangle 279335886 (Stroke)" id="I6608:125842;7039:103403;1891:136472" layout-inline="{"position":"absolute","width":"16px","height":"18px","top":"-2px","left":"6px","mode":"none"}" />
                </boolean_operation>
              </frame>
            </frame>
          </frame>
        </frame>
        <frame name="底部" id="I6608:125842;7039:103403;6934:151135" layout-inline="{"mode":"column","justifyContent":"center","alignItems":"center","gap":"14px"}">
          <frame name="会员标识" id="I6608:125842;7039:103403;8294:439914" layout-inline="{"width":"40px","height":"40px","mode":"none"}">
            <instance name="积分-0414" id="I6608:125842;7039:103403;8294:439915" componentId="8294:438210" strokes-inline="{"colors":[{"rgba":"rgba(204, 221, 255, 0.08)"}],"strokeWeight":"1px","align":"INSIDE"}" layout-inline="{"position":"absolute","width":"48px","height":"48px","top":"-4px","left":"-4px","mode":"none"}" borderRadius="8px">
              <text name="高级" id="I6608:125842;7039:103403;8294:439915;14315:801988" text="高级" layout-inline="{"position":"absolute","width":"44px","height":"10px","top":"50%","left":"50%","mode":"none","transform":"translateY(-50%) translateX(-50%)"}" />
              <frame name="Frame 2131127174" id="I6608:125842;7039:103403;8294:439915;14315:957736" layout-inline="{"position":"absolute","width":"34px","height":"18px","top":"6px","left":"50%","mode":"row","alignItems":"center","gap":"2px","transform":"translateX(-50%)"}">
                <image-svg name="Vector" id="I6608:125842;7039:103403;8294:439915;14315:801989" layout-inline="{"width":"10px","height":"10px","mode":"none"}" />
                <text name="32" id="I6608:125842;7039:103403;8294:439915;14315:801990" text="1.5万" />
              </frame>
            </instance>
          </frame>
          <frame name="Frame 2131126668" id="I6608:125842;7039:103403;6934:151137" layout-inline="{"width":"40px","height":"40px","mode":"row","justifyContent":"center","alignItems":"center","gap":"10px"}">
            <frame name="中按钮" id="I6608:125842;7039:103403;6934:151138" layout-inline="{"width":"28px","height":"28px","mode":"none"}" borderRadius="88px" />
          </frame>
        </frame>
      </instance>
    </instance>
    <frame name="内容区域" id="6608:125843" layout-inline="{"height":"821px","mode":"column","alignItems":"center","flexGrow":1}">
      <instance name="顶bar" id="6608:125844" componentId="1458:72204" layout-inline="{"mode":"column","gap":"10px","padding":"16px 0px 0px","alignSelf":"stretch"}">
        <frame name="Frame 2131126721" id="I6608:125844;1446:89634" layout-inline="{"height":"48px","mode":"row","justifyContent":"space-between","alignItems":"center","gap":"128px","padding":"0px 24px","alignSelf":"stretch"}">
          <frame name="左侧" id="I6608:125844;5308:137436" layout-inline="{"mode":"row","alignItems":"center","gap":"8px"}">
            <instance name="左侧" id="I6608:125844;1458:90009" componentId="1446:90152" layout-inline="{"mode":"row","alignItems":"center","gap":"4px"}">
              <frame name="左侧" id="I6608:125844;1458:90009;1458:90222" effects-inline="{"boxShadow":"2px 3px 12px 0px rgba(0, 32, 169, 0.01)"}" layout-inline="{"mode":"row","alignItems":"center","gap":"4px","padding":"8px 0px"}" borderRadius="100px">
                <frame name="Frame 2131126836" id="I6608:125844;1458:90009;1458:90223" layout-inline="{"mode":"row","alignItems":"center","gap":"4px","padding":"9px 0px"}">
                  <text name="我的天才女友" id="I6608:125844;1458:90009;1458:90224" text="氧气之爱" />
                  <frame name="icon" id="I6608:125844;1458:90009;1458:90225" layout-inline="{"width":"14px","height":"14px","mode":"none"}">
                    <boolean_operation name="Union" id="I6608:125844;1458:90009;1458:90226" layout-inline="{"position":"absolute","width":"7px","height":"4px","top":"50%","left":"50%","mode":"none","transform":"translateY(-50%) translateX(-50%)"}">
                      <image-svg name="Polygon 2" id="I6608:125844;1458:90009;1458:90227" layout-inline="{"position":"absolute","width":"8px","height":"5px","top":"-0px","left":"-1px","mode":"none"}" borderRadius="1px" />
                    </boolean_operation>
                  </frame>
                </frame>
              </frame>
            </instance>
            <frame name="切换产物" id="I6608:125844;5308:150574" layout="layout_EO68W7" borderRadius="8px">
              <frame name="Frame 2131127270" id="I6608:125844;5308:150575" layout="layout_TI6TXH">
                <image-svg name="Rectangle 279335930" id="I6608:125844;5308:150576" strokes-inline="{"colors":[{"hex":"#F5FBFF"}],"strokeWeight":"1px","align":"CENTER"}" layout-inline="{"position":"absolute","width":"10px","height":"12px","top":"2px","left":"3px","mode":"none"}" borderRadius="0px 0px 0px 0px" />
                <image-svg name="Vector 6604" id="I6608:125844;5308:150577" strokes-inline="{"colors":[{"hex":"#F5FBFF"}],"strokeWeight":"1px","align":"CENTER"}" layout-inline="{"position":"absolute","width":"5px","height":"0px","top":"7px","left":"6px","mode":"none"}" />
                <image-svg name="Vector 6605" id="I6608:125844;5308:150578" strokes-inline="{"colors":[{"hex":"#F5FBFF"}],"strokeWeight":"1px","align":"CENTER"}" layout-inline="{"position":"absolute","width":"3px","height":"0px","top":"10px","left":"6px","mode":"none"}" />
              </frame>
            </frame>
          </frame>
          <instance name="右侧" id="I6608:125844;1458:72189" componentId="3704:120925" layout="layout_ZGDCB8">
            <frame name="Frame 2131127066" id="I6608:125844;1458:72189;3707:125682" layout-inline="{"mode":"row","alignItems":"center","gap":"4px"}" opacity="0.00">
              <frame name="Tab" id="I6608:125844;1458:72189;3704:120926" layout="layout_EO68W7" borderRadius="8px">
                <instance name="undo" id="I6608:125844;1458:72189;3704:120927" componentId="1:153" layout="layout_TI6TXH">
                  <image-svg name="Outlined" id="I6608:125844;1458:72189;3704:120927;19220:43028" layout-inline="{"position":"absolute","width":"83.25%","height":"74.46%","top":"10.72%","left":"8.18%","mode":"none"}" />
                </instance>
              </frame>
              <frame name="Tab" id="I6608:125844;1458:72189;3704:120928" layout="layout_EO68W7" borderRadius="8px">
                <instance name="undo" id="I6608:125844;1458:72189;3704:120929" componentId="1:153" layout="layout_TI6TXH">
                  <image-svg name="Outlined" id="I6608:125844;1458:72189;3704:120929;19220:43028" layout-inline="{"position":"absolute","width":"83.25%","height":"74.46%","top":"10.72%","left":"8.57%","mode":"none"}" />
                </instance>
              </frame>
            </frame>
            <image-svg name="Vector" id="I6608:125844;1458:72189;3704:120930" strokes-inline="{"colors":[{"rgba":"rgba(204, 221, 255, 0.12)"}],"strokeWeight":"1px","align":"CENTER"}" layout-inline="{"width":"0px","height":"8px","mode":"none"}" opacity="0.00" />
            <frame name="Frame 2131127067" id="I6608:125844;1458:72189;3707:126439" layout-inline="{"mode":"row","justifyContent":"flex-end","alignItems":"center","gap":"4px","padding":"0px 0px 0px 12px"}">
              <frame name="Tab" id="I6608:125844;1458:72189;8261:411704" layout="layout_EO68W7" borderRadius="8px">
                <instance name="download" id="I6608:125844;1458:72189;8261:411705" componentId="640:136522" layout="layout_TI6TXH">
                  <image-svg name="Outlined" id="I6608:125844;1458:72189;8261:411705;19220:43031" layout-inline="{"position":"absolute","width":"75.00%","height":"79.17%","top":"8.34%","left":"12.50%","mode":"none"}" />
                </instance>
                <frame name="下拉菜单" id="I6608:125844;1458:72189;8261:411706" strokes-inline="{"colors":[{"rgba":"rgba(204, 221, 255, 0.08)"}],"strokeWeight":"1px","align":"INSIDE"}" effects-inline="{"boxShadow":"2px 6px 12px 0px rgba(0, 32, 169, 0.03)","backdropFilter":"blur(120px)"}" layout-inline="{"position":"absolute","width":"148px","height":"56px","top":"40px","left":"-112px","mode":"column","gap":"8px","padding":"8px"}" borderRadius="16px" opacity="0.00">
                  <frame name="菜单" id="I6608:125844;1458:72189;8261:411707" layout-inline="{"mode":"column","gap":"4px"}">
                    <frame name="重命名" id="I6608:125844;1458:72189;8261:411708" layout-inline="{"mode":"row","justifyContent":"space-between","alignItems":"center","gap":"10px","padding":"2px 8px","alignSelf":"stretch"}" borderRadius="8px">
                      <frame name="icon" id="I6608:125844;1458:72189;8261:411709" layout-inline="{"mode":"row","justifyContent":"center","alignItems":"center","gap":"8px","padding":"10px"}" borderRadius="8px">
                        <instance name="file" id="I6608:125844;1458:72189;8261:411710" componentId="4666:82120" layout="layout_TI6TXH">
                          <image-svg name="Outlined" id="I6608:125844;1458:72189;8261:411710;19465:16134" layout-inline="{"position":"absolute","width":"76.35%","height":"74.52%","top":"11.55%","left":"11.82%","mode":"none"}" />
                        </instance>
                      </frame>
                      <frame name="Texts" id="I6608:125844;1458:72189;8261:411711" layout-inline="{"width":"80px","mode":"column","justifyContent":"center"}" borderRadius="4px">
                        <frame name="Frame 2082893662" id="I6608:125844;1458:72189;8261:411712" layout="layout_ZGDCB8">
                          <text name="Title" id="I6608:125844;1458:72189;8261:411713" text="下载所有素材" layout-inline="{"width":"79px","mode":"none"}" />
                        </frame>
                      </frame>
                    </frame>
                  </frame>
                </frame>
              </frame>
            </frame>
          </instance>
          <instance name="多tab" id="I6608:125844;5308:126885" componentId="5308:126871" layout-inline="{"position":"absolute","width":"312px","height":"32px","top":"8px","left":"314px","mode":"row","justifyContent":"center","alignItems":"center","gap":"4px","padding":"2px"}" borderRadius="60px">
            <frame name="选项卡" id="I6608:125844;5308:126885;6473:389355" layout="layout_HTJRMC" borderRadius="100px">
              <frame name="大纲" id="I6608:125844;5308:126885;6473:389356" layout="layout_TI6TXH">
                <image-svg name="Vector 4247" id="I6608:125844;5308:126885;6473:389359" strokes="stroke_T574EU" layout-inline="{"position":"absolute","width":"37.50%","height":"0.00%","top":"50.00%","left":"53.12%","mode":"none"}" />
                <image-svg name="Vector 4248" id="I6608:125844;5308:126885;6473:389360" strokes="stroke_T574EU" layout-inline="{"position":"absolute","width":"37.50%","height":"0.00%","top":"18.75%","left":"53.12%","mode":"none"}" />
                <image-svg name="Vector 4249" id="I6608:125844;5308:126885;6473:389361" strokes="stroke_T574EU" layout-inline="{"position":"absolute","width":"37.50%","height":"0.00%","top":"81.25%","left":"53.12%","mode":"none"}" />
                <boolean_operation name="Union" id="I6608:125844;5308:126885;6473:389362" layout-inline="{"position":"absolute","width":"6px","height":"11px","top":"2px","left":"1px","mode":"none"}">
                  <image-svg name="Vector 6610" id="I6608:125844;5308:126885;6473:389363" strokes="stroke_T574EU" layout-inline="{"position":"absolute","width":"70.57%","height":"0.00%","top":"50.00%","left":"11.78%","mode":"none"}" borderRadius="1px" />
                  <image-svg name="Vector 6611" id="I6608:125844;5308:126885;6473:389364" strokes="stroke_T574EU" layout-inline="{"position":"absolute","width":"76.43%","height":"44.11%","top":"5.89%","left":"11.78%","mode":"none"}" borderRadius="0px 0px 0px 0px" />
                  <image-svg name="Vector 6612" id="I6608:125844;5308:126885;6473:389365" strokes="stroke_T574EU" layout-inline="{"position":"absolute","width":"76.43%","height":"44.11%","top":"50.00%","left":"11.78%","mode":"none"}" borderRadius="0px 0px 0px 0px" />
                </boolean_operation>
              </frame>
              <text name="大纲" id="I6608:125844;5308:126885;6473:389366" text="大纲" />
            </frame>
            <frame name="选项卡" id="I6608:125844;5308:126885;5958:219143" layout="layout_HTJRMC" borderRadius="100px">
              <frame name="图文" id="I6608:125844;5308:126885;5958:219144" layout="layout_TI6TXH">
                <image-svg name="Rectangle 279335758" id="I6608:125844;5308:126885;5958:219145" strokes="stroke_T574EU" layout-inline="{"position":"absolute","width":"65.00%","height":"40.00%","top":"15.00%","left":"17.50%","mode":"none"}" borderRadius="1px" />
                <image-svg name="Vector 4247" id="I6608:125844;5308:126885;5958:219146" strokes="stroke_T574EU" layout-inline="{"position":"absolute","width":"60.00%","height":"0.00%","top":"70.00%","left":"17.50%","mode":"none"}" borderRadius="1px" />
                <image-svg name="Vector 4248" id="I6608:125844;5308:126885;5958:219147" strokes="stroke_T574EU" layout-inline="{"position":"absolute","width":"25.00%","height":"0.00%","top":"85.00%","left":"17.50%","mode":"none"}" borderRadius="1px" />
              </frame>
              <text name="故事" id="I6608:125844;5308:126885;5958:219148" text="故事" />
            </frame>
            <frame name="选项卡" id="I6608:125844;5308:126885;5958:219149" layout="layout_HTJRMC" borderRadius="100px">
              <frame name="表格" id="I6608:125844;5308:126885;5958:219150" layout="layout_TI6TXH">
                <frame name="icon" id="I6608:125844;5308:126885;5958:219151" layout-inline="{"position":"absolute","width":"16px","height":"16px","top":"50%","left":"50%","mode":"none","transform":"translateY(-50%) translateX(-50%)"}">
                  <boolean_operation name="Union" id="I6608:125844;5308:126885;5958:219152" layout-inline="{"position":"absolute","width":"12px","height":"12px","top":"2px","left":"2px","mode":"none"}">
                    <boolean_operation name="Original" id="I6608:125844;5308:126885;5958:219153" />
                    <image-svg name="Rectangle 279335758" id="I6608:125844;5308:126885;5958:219155" strokes="stroke_T574EU" layout-inline="{"position":"absolute","width":"89.17%","height":"89.17%","top":"5.41%","left":"5.41%","mode":"none"}" borderRadius="1px" />
                    <image-svg name="Vector 4247" id="I6608:125844;5308:126885;5958:219156" strokes="stroke_T574EU" layout-inline="{"position":"absolute","width":"81.06%","height":"0.00%","top":"50.00%","left":"9.47%","mode":"none"}" borderRadius="1px" />
                    <image-svg name="Vector 4248" id="I6608:125844;5308:126885;5958:219157" strokes="stroke_T574EU" layout-inline="{"position":"absolute","width":"0.00%","height":"81.06%","top":"9.42%","left":"50.04%","mode":"none"}" borderRadius="1px" />
                  </boolean_operation>
                </frame>
              </frame>
              <text name="分镜表" id="I6608:125844;5308:126885;5958:219158" text="分镜表" />
            </frame>
            <frame name="选项卡" id="I6608:125844;5308:126885;5958:219159" layout="layout_HTJRMC" borderRadius="100px">
              <frame name="视频模式" id="I6608:125844;5308:126885;5958:219160" layout-inline="{"mode":"column","justifyContent":"center","alignItems":"center","gap":"6px"}">
                <frame name="icon" id="I6608:125844;5308:126885;5958:219161" layout="layout_TI6TXH">
                  <image-svg name="Rectangle 279335758" id="I6608:125844;5308:126885;5958:219163" strokes-inline="{"colors":[{"rgba":"rgba(224, 245, 255, 0.60)"}],"strokeWeight":"1px","align":"INSIDE"}" layout-inline="{"position":"absolute","width":"87.50%","height":"75.00%","top":"12.50%","left":"6.25%","mode":"none"}" borderRadius="4px" />
                  <image-svg name="Polygon 75" id="I6608:125844;5308:126885;5958:219164" layout-inline="{"position":"absolute","width":"22.92%","height":"20.83%","top":"39.59%","left":"41.67%","mode":"none"}" borderRadius="0px" />
                </frame>
              </frame>
              <text name="时间线" id="I6608:125844;5308:126885;5958:219165" text="时间线" />
            </frame>
          </instance>
        </frame>
      </instance>
      <frame name="canvas" id="6608:125845" layout-inline="{"mode":"column","gap":"36px","padding":"48px 0px 60px","alignSelf":"stretch","flexGrow":1}">
        <frame name="Frame 2131127096" id="6608:125846" layout-inline="{"mode":"column","gap":"16px","padding":"0px 60px"}">
          <frame name="Frame 2131127291" id="6608:125847" layout-inline="{"width":"844px","mode":"column","gap":"24px"}">
            <frame name="Frame 2131127291" id="7221:394503" layout-inline="{"width":"844px","mode":"column","gap":"24px"}">
              <frame name="场景title" id="7221:394516" layout-inline="{"width":"844px","mode":"row","justifyContent":"space-between","alignItems":"center","gap":"10px"}">
                <text name="场景1 EXT - 太空基地通道 - 傍晚" id="7221:394517" text="场景1  EXT - 太空基地通道 - 傍晚" />
                <frame name="功能区" id="7221:394518" strokes-inline="{"colors":[{"rgba":"rgba(224, 245, 255, 0.60)"}],"strokeWeight":"0px","align":"INSIDE"}" layout-inline="{"mode":"row","justifyContent":"center","alignItems":"center","gap":"4px","padding":"6px 8px"}" borderRadius="100px">
                  <frame name="icon" id="7221:394519" layout="layout_TI6TXH">
                    <image-svg name="Union" id="7221:394522" layout-inline="{"position":"absolute","width":"19.97%","height":"18.53%","top":"46.98%","left":"47.92%","mode":"none"}" />
                    <image-svg name="Rectangle 279335758" id="7221:394523" strokes-inline="{"colors":[{"rgba":"rgba(224, 245, 255, 0.60)"}],"strokeWeight":"1px","align":"INSIDE"}" layout-inline="{"position":"absolute","width":"71.88%","height":"59.38%","top":"28.12%","left":"21.88%","mode":"none"}" borderRadius="4px" />
                    <image-svg name="Rectangle 279335759 (Stroke)" id="7221:394524" layout-inline="{"position":"absolute","width":"68.75%","height":"56.25%","top":"12.50%","left":"6.25%","mode":"none"}" borderRadius="0px 0px 0px 0px" />
                  </frame>
                  <text name="批量生成视频" id="7221:394525" text="批量生成视频" />
                </frame>
              </frame>
            </frame>
          </frame>
          <frame name="表单" id="6608:125870" strokes-inline="{"colors":[{"rgba":"rgba(204, 221, 255, 0.12)"}],"strokeWeight":"0px","align":"OUTSIDE"}" layout-inline="{"mode":"row","alignItems":"center"}" borderRadius="8px 0px 0px 8px">
            <frame name="内容" id="6608:125871" layout-inline="{"width":"64px","mode":"column"}">
              <frame name="选项卡" id="6608:125872" layout="layout_84Z37T" borderRadius="8px 0px 0px 0px">
                <text name="镜号" id="6608:125873" text="镜号" />
              </frame>
              <frame name="选项卡" id="6608:125874" layout="layout_8HSLE7">
                <text name="1" id="6608:125875" text="1" />
              </frame>
              <frame name="选项卡" id="6608:125876" layout="layout_8HSLE7">
                <text name="2" id="6608:125877" text="2" />
              </frame>
              <frame name="选项卡" id="6608:125878" layout="layout_8HSLE7">
                <text name="3" id="6608:125879" text="3" />
              </frame>
              <frame name="选项卡" id="6608:125880" layout="layout_8HSLE7">
                <text name="4" id="6608:125881" text="4" />
              </frame>
              <frame name="选项卡" id="6608:125882" layout="layout_8HSLE7">
                <text name="5" id="6608:125883" text="5" />
              </frame>
              <frame name="选项卡" id="6608:125884" layout="layout_8HSLE7">
                <text name="6" id="6608:125885" text="6" />
              </frame>
            </frame>
            <frame name="内容" id="6608:125886" strokes="stroke_U6BS7N" layout-inline="{"width":"144px","mode":"column"}">
              <frame name="选项卡" id="6608:125887" layout-inline="{"mode":"row","justifyContent":"center","alignItems":"center","padding":"12px 48px","alignSelf":"stretch"}">
                <text name="画面" id="6608:125888" text="画面" />
              </frame>
              <frame name="选项卡" id="6608:125889" layout="layout_TI00S5">
                <instance name="媒体" id="6608:125890" componentId="4909:52492" layout="layout_YZXCEH" borderRadius="4px">
                  <rectangle name="image" id="I6608:125890;4909:52465" layout="layout_M8NEG3" />
                </instance>
              </frame>
              <frame name="选项卡" id="6608:125891" layout="layout_TI00S5">
                <instance name="媒体1" id="6608:125892" componentId="4909:52492" layout="layout_YZXCEH" borderRadius="4px">
                  <rectangle name="image" id="I6608:125892;4909:52465" layout="layout_M8NEG3" />
                </instance>
              </frame>
              <frame name="选项卡" id="6608:125897" layout="layout_TI00S5">
                <instance name="媒体" id="6608:125898" componentId="4909:52492" layout="layout_YZXCEH" borderRadius="4px">
                  <rectangle name="image" id="I6608:125898;4909:52465" layout="layout_M8NEG3" />
                </instance>
              </frame>
              <frame name="选项卡" id="6608:125899" layout="layout_TI00S5">
                <instance name="媒体" id="6608:125900" componentId="4909:52492" layout="layout_YZXCEH" borderRadius="4px">
                  <rectangle name="image" id="I6608:125900;4909:52465" layout="layout_M8NEG3" />
                </instance>
              </frame>
              <frame name="选项卡" id="6608:125901" layout="layout_TI00S5">
                <instance name="媒体" id="6608:125902" componentId="4909:52492" layout="layout_YZXCEH">
                  <rectangle name="image" id="I6608:125902;4909:52465" layout="layout_M8NEG3" />
                </instance>
              </frame>
              <frame name="选项卡" id="6608:125903" layout="layout_TI00S5">
                <instance name="媒体" id="6608:125904" componentId="4909:52492" layout="layout_YZXCEH">
                  <rectangle name="image" id="I6608:125904;4909:52465" layout="layout_M8NEG3" />
                </instance>
              </frame>
            </frame>
            <frame name="内容" id="6608:125905" strokes="stroke_U6BS7N" layout-inline="{"width":"280px","mode":"column"}">
              <frame name="选项卡" id="6608:125906" layout="layout_84Z37T">
                <text name="首帧图提示词" id="6608:125907" text="首帧图提示词" />
              </frame>
              <frame name="选项卡" id="6608:125908" layout="layout_AT0WSG">
                <text name="走廊内的应急标识泛着微弱红光，时不时闪烁两下，为这寂静的空间增添一丝不安 最多展示3行，超过..." id="6608:125909" text="走廊内的应急标识泛着微弱红光，时不时闪烁两下，为这寂静的空间增添一丝不安
最多展示3行，超过..." />
              </frame>
              <frame name="选项卡" id="6608:125910" layout="layout_AT0WSG">
                <text name="林妈妈坐在泛着冷光的智能诊疗椅上，椅背上的监测管线与她的身体相连。最多展示3行，超过..." id="6608:125911" text="林妈妈坐在泛着冷光的智能诊疗椅上，椅背上的监测管线与她的身体相连。最多展示3行，超过..." />
              </frame>
              <frame name="选项卡" id="6608:125912" layout="layout_AT0WSG">
                <text name="三年后，已经 15 岁的小明来到太空基地他走进林妈妈曾经的房间，房间内最多展示3行，超过..." id="6608:125913" text="三年后，已经 15 岁的小明来到太空基地他走进林妈妈曾经的房间，房间内最多展示3行，超过..." />
              </frame>
              <frame name="选项卡" id="6608:125914" layout="layout_AT0WSG">
                <text name="林妈妈的房间被暖黄色的怀旧灯光笼罩，墙面贴满了泛黄的老照片，书架上摆放最多展示3行，超过..." id="6608:125915" text="林妈妈的房间被暖黄色的怀旧灯光笼罩，墙面贴满了泛黄的老照片，书架上摆放最多展示3行，超过..." />
              </frame>
              <frame name="选项卡" id="6608:125916" layout="layout_AT0WSG">
                <text name="林妈妈的房间被暖黄色的怀旧灯光笼罩，墙面贴满了泛黄的老照片，书架上摆最多展示3行，超过..." id="6608:125917" text="林妈妈的房间被暖黄色的怀旧灯光笼罩，墙面贴满了泛黄的老照片，书架上摆最多展示3行，超过..." />
              </frame>
              <frame name="选项卡" id="6608:125918" layout="layout_AT0WSG">
                <text name="林妈妈的房间被暖黄色的怀旧灯光笼罩，墙面贴满了泛黄的老照片，书架上摆放着小明儿时的玩具和母子俩的合影" id="6608:125919" text="林妈妈的房间被暖黄色的怀旧灯光笼罩，墙面贴满了泛黄的老照片，书架上摆放着小明儿时的玩具和母子俩的合影" />
              </frame>
            </frame>
            <frame name="内容" id="6608:125951" strokes="stroke_U6BS7N" layout-inline="{"width":"280px","mode":"column"}">
              <frame name="选项卡" id="6608:125952" layout="layout_84Z37T">
                <text name="视频描述词" id="6608:125953" text="视频描述词" />
              </frame>
              <frame name="选项卡" id="6608:125954" layout="layout_AT0WSG">
                <text name="幽蓝冷调、金属质感、步履蹒跚、苍白憔悴、眼神坚毅、星际浩瀚、全息光影..." id="6608:125955" text="幽蓝冷调、金属质感、步履蹒跚、苍白憔悴、眼神坚毅、星际浩瀚、全息光影..." />
              </frame>
              <frame name="选项卡" id="6608:125956" layout="layout_AT0WSG">
                <text name="冷白科技光效、生命警报红光、凝重医患对话、记忆数据载体、AI 情感寄托..." id="6608:125957" text="冷白科技光效、生命警报红光、凝重医患对话、记忆数据载体、AI 情感寄托..." />
              </frame>
              <frame name="选项卡" id="6608:125958" layout="layout_AT0WSG">
                <text name="神经数据紫蓝光带、悬浮医疗舱床、科幻头盔管线、浩瀚星河背景、记忆碎片..." id="6608:125959" text="神经数据紫蓝光带、悬浮医疗舱床、科幻头盔管线、浩瀚星河背景、记忆碎片..." />
              </frame>
              <frame name="选项卡" id="6608:125960" layout="layout_AT0WSG">
                <text name="暖黄怀旧灯光、童年记忆陈设、全息母爱投影、时光沉淀氛围、冷暖光影交织..." id="6608:125961" text="暖黄怀旧灯光、童年记忆陈设、全息母爱投影、时光沉淀氛围、冷暖光影交织..." />
              </frame>
              <frame name="选项卡" id="6608:125962" layout="layout_AT0WSG">
                <text name="暖黄怀旧灯光、童年记忆陈设、全息母爱投影、时光沉淀氛围、冷暖光影交织..." id="6608:125963" text="暖黄怀旧灯光、童年记忆陈设、全息母爱投影、时光沉淀氛围、冷暖光影交织..." />
              </frame>
              <frame name="选项卡" id="6608:125964" layout="layout_AT0WSG">
                <text name="暖黄怀旧灯光、童年记忆陈设、全息母爱投影、时光沉淀氛围、冷暖光影交织、少年思念特写" id="6608:125965" text="暖黄怀旧灯光、童年记忆陈设、全息母爱投影、时光沉淀氛围、冷暖光影交织、少年思念特写" />
              </frame>
            </frame>
            <frame name="内容" id="6608:126011" strokes="stroke_U6BS7N" layout-inline="{"width":"90px","mode":"column"}">
              <frame name="选项卡" id="6608:126012" layout="layout_84Z37T" borderRadius="0px 8px 0px 0px">
                <text name="镜头" id="6608:126013" text="镜头" />
              </frame>
              <frame name="选项卡" id="6608:126014" layout="layout_8HSLE7">
                <text name="中远景" id="6608:126015" text="中远景" />
              </frame>
              <frame name="选项卡" id="6608:126016" layout="layout_8HSLE7">
                <text name="特写" id="6608:126017" text="特写" />
              </frame>
              <frame name="选项卡" id="6608:126018" layout="layout_8HSLE7">
                <text name="远景" id="6608:126019" text="远景" />
              </frame>
              <frame name="选项卡" id="6608:126020" layout="layout_8HSLE7">
                <text name="中景" id="6608:126021" text="中景" />
              </frame>
              <frame name="选项卡" id="6608:126022" layout="layout_8HSLE7">
                <text name="中景" id="6608:126023" text="中景" />
              </frame>
              <frame name="选项卡" id="6608:126024" layout="layout_8HSLE7">
                <text name="中景" id="6608:126025" text="中景" />
              </frame>
            </frame>
            <frame name="内容" id="6608:126026" strokes="stroke_U6BS7N" layout-inline="{"width":"90px","mode":"column"}">
              <frame name="选项卡" id="6608:126027" layout="layout_84Z37T" borderRadius="0px 8px 0px 0px">
                <text name="时长" id="6608:126028" text="时长" />
              </frame>
              <frame name="选项卡" id="6608:126029" layout="layout_8HSLE7">
                <text name="5秒" id="6608:126030" text="5秒" />
              </frame>
              <frame name="选项卡" id="6608:126031" layout="layout_8HSLE7">
                <text name="5秒" id="6608:126032" text="5秒" />
              </frame>
              <frame name="选项卡" id="6608:126033" layout="layout_8HSLE7">
                <text name="5秒" id="6608:126034" text="5秒" />
              </frame>
              <frame name="选项卡" id="6608:126035" layout="layout_8HSLE7">
                <text name="5秒" id="6608:126036" text="5秒" />
              </frame>
              <frame name="选项卡" id="6608:126037" layout="layout_8HSLE7">
                <text name="5秒" id="6608:126038" text="5秒" />
              </frame>
              <frame name="选项卡" id="6608:126039" layout="layout_8HSLE7">
                <text name="5秒" id="6608:126040" text="5秒" />
              </frame>
            </frame>
          </frame>
        </frame>
        <frame name="hover功能" id="6608:126047" effects-inline="{"boxShadow":"0px 4px 4px 0px rgba(0, 0, 0, 0.25)"}" layout-inline="{"position":"absolute","width":"127px","height":"42px","top":"691px","left":"406px","mode":"row","justifyContent":"center","alignItems":"center","gap":"4px","padding":"6px 16px"}" borderRadius="22px" opacity="0.00">
          <frame name="Frame 2082892903" id="6608:126048" layout-inline="{"mode":"column","justifyContent":"center","alignItems":"center","gap":"10px"}">
            <instance name="icon" id="6608:126049" componentId="1:2234" layout-inline="{"width":"24px","height":"24px","mode":"none"}">
              <boolean_operation name="Original" id="I6608:126049;697:56576" />
              <image-svg name="Union" id="I6608:126049;918:100648" layout-inline="{"position":"absolute","width":"87.50%","height":"75.00%","top":"12.50%","left":"6.25%","mode":"none"}" />
            </instance>
          </frame>
          <text name="生成成片" id="6608:126050" text="生成成片" />
        </frame>
        <frame name="插入" id="6608:126051" layout-inline="{"position":"absolute","width":"1166px","height":"24px","top":"223px","left":"48px","mode":"none"}">
          <image-svg name="Rectangle 279335956" id="6608:126052" layout-inline="{"position":"absolute","width":"1154px","height":"1px","top":"12px","left":"12px","mode":"none"}" borderRadius="2px" />
        </frame>
        <instance name="剧本段落" id="6608:126061" componentId="3850:158874" strokes-inline="{"colors":[{"rgba":"rgba(204, 221, 255, 0.16)"}],"strokeWeight":"0px","align":"INSIDE"}" effects-inline="{"boxShadow":"0px 4px 30px 0px rgba(0, 0, 0, 0.30)","backdropFilter":"blur(120px)"}" layout-inline="{"position":"absolute","width":"540px","height":"60px","top":"239px","left":"200px","mode":"column","gap":"10px"}" borderRadius="16px">
          <frame name="输入区" id="I6608:126061;3850:158875" effects-inline="{"backdropFilter":"blur(60px)"}" layout-inline="{"mode":"row","justifyContent":"flex-end","alignItems":"center","gap":"8px","padding":"12px 12px 12px 20px","alignSelf":"stretch"}" borderRadius="16px">
            <frame name="左侧" id="I6608:126061;3850:158876" layout-inline="{"mode":"row","alignItems":"center","gap":"4px","flexGrow":1}">
              <text name="这里是修改意见" id="I6608:126061;3850:158877" text="生成新的分镜，近景特写" />
            </frame>
            <instance name="icon" id="I6608:126061;4024:217556" componentId="1458:92315" layout-inline="{"mode":"row","justifyContent":"flex-end","alignItems":"center","gap":"6px","padding":"4px"}">
              <frame name="Send" id="I6608:126061;4024:217556;5352:307337" effects-inline="{"backdropFilter":"blur(40px)"}" layout-inline="{"width":"28px","height":"28px","mode":"none"}" borderRadius="14px">
                <instance name="Icon" id="I6608:126061;4024:217556;5352:307338" componentId="5352:290890" layout-inline="{"position":"absolute","width":"16px","height":"16px","top":"50%","left":"50%","mode":"none","transform":"translateY(-50%) translateX(-50%)"}">
                  <boolean_operation name="Union" id="I6608:126061;4024:217556;5352:307338;669:127004" layout-inline="{"position":"absolute","width":"10px","height":"12px","top":"2px","left":"3px","mode":"none"}">
                    <image-svg name="Vector" id="I6608:126061;4024:217556;5352:307338;446:75830" strokes-inline="{"colors":[{"rgba":"rgba(224, 245, 255, 0.20)"}],"strokeWeight":"2px","align":"CENTER"}" layout-inline="{"position":"absolute","width":"0.00%","height":"83.33%","top":"8.33%","left":"50.01%","mode":"none"}" />
                    <image-svg name="Vector" id="I6608:126061;4024:217556;5352:307338;446:75831" strokes-inline="{"colors":[{"rgba":"rgba(224, 245, 255, 0.20)"}],"strokeWeight":"2px","align":"CENTER"}" layout-inline="{"position":"absolute","width":"80.01%","height":"66.65%","top":"8.33%","left":"10.00%","mode":"none"}" />
                  </boolean_operation>
                </instance>
              </frame>
            </instance>
          </frame>
        </instance>
      </frame>
    </frame>
    <frame name="chatbox母版" id="6608:126062" layout-inline="{"width":"440px","height":"821px","mode":"none"}">
      <frame name="chatbox母版" id="6608:126063" strokes-inline="{"colors":[{"type":"GRADIENT_LINEAR","gradientHandlePositions":[{"x":0.49999996967068594,"y":-0.01768292640391819},{"x":0.4999999696706867,"y":0.9999999734618503},{"x":-0.008841480262198331,"y":-0.017682926403918157}],"gradientStops":[{"position":0,"color":{"hex":"#FFFFFF","opacity":0}},{"position":0.25,"color":{"hex":"#D7DAE0","opacity":0.5}},{"position":0.5,"color":{"hex":"#B7BDC8","opacity":0.92}},{"position":0.75,"color":{"hex":"#AAB1BE","opacity":0.5}},{"position":1,"color":{"hex":"#7A8599","opacity":0}}]}],"strokeWeight":"1px","strokeWeights":"0px 0px 0px 1px","align":"INSIDE"}" effects-inline="{"backdropFilter":"blur(120px)"}" layout-inline="{"position":"absolute","width":"440px","height":"821px","top":"0px","right":"0px","mode":"row","justifyContent":"space-between","alignItems":"center","gap":"10px","padding":"20px 48px"}">
        <frame name="Chatbox" id="6608:126064" layout-inline="{"mode":"column","justifyContent":"center","alignItems":"center","alignSelf":"stretch"}" borderRadius="24px">
          <frame name="导航" id="6608:126067" layout-inline="{"width":"440px","height":"44px","mode":"none"}">
            <text name="Agent" id="6608:126068" text="Agent" layout-inline="{"position":"absolute","width":"47px","height":"16px","top":"50%","left":"80px","mode":"none","transform":"translateY(-50%)"}" />
            <frame name="标签" id="6608:126069" layout-inline="{"position":"absolute","width":"30px","height":"16px","top":"50%","left":"131px","mode":"none","transform":"translateY(-50%)"}" borderRadius="4px">
              <image-svg name="Rectangle" id="6608:126070" strokes-inline="{"colors":[{"hex":"#F5FBFF"}],"strokeWeight":"1px","align":"INSIDE"}" layout-inline="{"position":"absolute","width":"30px","height":"16px","top":"50%","left":"0px","mode":"none","transform":"translateY(-50%)"}" borderRadius="4px 4px 4px 4px" />
              <text name="Beta" id="6608:126071" text="Beta" layout-inline="{"position":"absolute","width":"24px","height":"16px","top":"-1px","left":"4px","mode":"none"}" />
            </frame>
            <frame name="logo" id="6608:126072" layout-inline="{"position":"absolute","width":"24px","height":"24px","top":"50%","left":"48px","mode":"none","transform":"translateY(-50%)"}">
              <text name="✨" id="6608:126073" text="✨" layout-inline="{"position":"absolute","width":"20px","height":"20px","top":"50%","left":"50%","mode":"none","transform":"translateY(-50%) translateX(-50%)"}" />
            </frame>
          </frame>
          <frame name="对话区" id="6608:126074" layout-inline="{"width":"344px","mode":"column","alignItems":"flex-end","gap":"32px","padding":"24px 0px 0px","flexGrow":1}">
            <instance name="输入" id="6608:126075" componentId="1458:91224" layout-inline="{"width":"116px","mode":"row","justifyContent":"center","alignItems":"center","gap":"10px","padding":"11px 16px"}" borderRadius="12px">
              <text name="这是一段非常长的文本输入气泡这是一段非常长的文本输入气泡这是一段非常长的文本输入气泡这是一段非常长的文本输入气泡这是一段非常长的文本输入气泡这是一段非常长的文本输入气泡这是一段非常长的文本输入气泡这是一段非常长的文本输入气泡这是一段非常长的文本输入气泡这是一段非常长的文本输入气泡这是一段非常长的文本输入气泡这是一段非常长的文本输入气泡这是一段非常长的文本输入气泡这是一段非常长的文本输入气泡" id="I6608:126075;1458:91225" text="创作完整故事" />
            </instance>
            <frame name="输出" id="6608:126076" layout-inline="{"width":"344px","mode":"row","justifyContent":"center","gap":"12px"}" borderRadius="100px">
              <frame name="剧本" id="6608:126077" layout-inline="{"mode":"column","gap":"12px","flexGrow":1}">
                <frame name="正文" id="6608:126079" layout="layout_G7OIMT">
                  <text name="好的，我现在讲继续创作故事" id="6608:126080" text="好的，我现在讲继续创作故事" />
                </frame>
                <frame name="正文" id="6608:126082" layout="layout_G7OIMT">
                  <text name="我先将按故事节奏脉络发展把故事撰写完成" id="6608:126083" text="我先将按故事节奏脉络发展把故事撰写完成" />
                </frame>
                <instance name="工具组件" id="6608:126084" componentId="4666:81320" strokes-inline="{"colors":[{"rgba":"rgba(204, 221, 255, 0.16)"}],"strokeWeight":"1px","align":"INSIDE"}" layout-inline="{"mode":"column","justifyContent":"center","gap":"12px","padding":"12px 16px 12px 12px","alignSelf":"stretch"}" borderRadius="12px">
                  <frame name="Frame 2131126745" id="I6608:126084;7229:188721" layout-inline="{"width":"316px","mode":"row","justifyContent":"space-between","alignItems":"center","gap":"4px"}">
                    <frame name="标题" id="I6608:126084;7229:236390" layout-inline="{"height":"24px","mode":"column","justifyContent":"center","alignItems":"center","gap":"10px","padding":"4px 10px 4px 8px"}" borderRadius="100px">
                      <frame name="Frame 2131127064" id="I6608:126084;7229:236391" layout="layout_ZGDCB8">
                        <frame name="Frame 2131127071" id="I6608:126084;7229:236392" layout-inline="{"mode":"row","alignItems":"center","gap":"6px"}">
                          <frame name="Frame 2131174280" id="I6608:126084;7229:236393" layout-inline="{"mode":"row","alignItems":"center","gap":"2px"}">
                            <instance name="工具" id="I6608:126084;7229:236394" componentId="7176:389837" layout="layout_TI6TXH">
                              <frame name="Frame 2131174274" id="I6608:126084;7229:236394;7176:389838" layout-inline="{"position":"absolute","width":"16px","height":"16px","top":"-0px","left":"-0px","mode":"none"}">
                                <boolean_operation name="Union" id="I6608:126084;7229:236394;7176:389839" strokes-inline="{"colors":[{"hex":"#00CAE0"}],"strokeWeight":"1px","align":"CENTER"}" />
                                <boolean_operation name="Subtract" id="I6608:126084;7229:236394;7176:389840" layout-inline="{"position":"absolute","width":"11px","height":"11px","top":"3px","left":"3px","mode":"none"}">
                                  <image-svg name="Vector 6554" id="I6608:126084;7229:236394;7176:389841" strokes-inline="{"colors":[{"hex":"#39ACFF"}],"strokeWeight":"0px","align":"CENTER"}" layout-inline="{"position":"absolute","width":"16px","height":"16px","top":"-3px","left":"-3px","mode":"none"}" borderRadius="0px 0px 0px 0px" />
                                  <image-svg name="Vector 6555 (Stroke)" id="I6608:126084;7229:236394;7176:389842" layout-inline="{"position":"absolute","width":"3px","height":"3px","top":"3px","left":"3px","mode":"none"}" borderRadius="0px" opacity="0.50" />
                                </boolean_operation>
                              </frame>
                            </instance>
                            <text name="故事大纲" id="I6608:126084;7229:236395" text="编写故事大纲" />
                          </frame>
                          <text name="猫咪侦探" id="I6608:126084;7229:236396" text="猫咪侦探" />
                        </frame>
                      </frame>
                    </frame>
                    <frame name="arrow down" id="I6608:126084;7229:188723" layout-inline="{"width":"12px","height":"12px","mode":"none"}">
                      <image-svg name="Outlined" id="I6608:126084;7229:188726" layout-inline="{"position":"absolute","width":"77.97%","height":"43.59%","top":"31.77%","left":"11.01%","mode":"none"}" />
                    </frame>
                  </frame>
                </instance>
                <frame name="正文" id="6608:126085" layout="layout_G7OIMT">
                  <text name="接着我将分章节创作分镜图片：" id="6608:126086" text="接着我将分章节创作分镜图片：" />
                </frame>
                <instance name="工具组件" id="6608:126087" componentId="4666:81422" layout-inline="{"mode":"column","justifyContent":"center","gap":"12px","padding":"12px 16px 16px 12px","alignSelf":"stretch"}" borderRadius="12px">
                  <frame name="Frame 2131174238" id="I6608:126087;7229:178222" layout-inline="{"width":"316px","mode":"row","justifyContent":"space-between","alignItems":"center","gap":"4px"}">
                    <instance name="Frame 2131174236" id="I6608:126087;7229:178223" componentId="7176:389996" layout-inline="{"height":"24px","mode":"column","justifyContent":"center","alignItems":"center","gap":"10px","padding":"4px 10px 4px 8px"}" borderRadius="100px">
                      <frame name="Frame 2131127064" id="I6608:126087;7229:178223;7176:389997" layout="layout_ZGDCB8">
                        <frame name="Frame 2131127071" id="I6608:126087;7229:178223;7176:389998" layout-inline="{"mode":"row","alignItems":"center","gap":"2px"}">
                          <instance name="工具" id="I6608:126087;7229:178223;7176:389999" componentId="7176:389926" layout="layout_TI6TXH">
                            <frame name="Icon_Replace" id="I6608:126087;7229:178223;7176:389999;7176:389927" layout-inline="{"position":"absolute","width":"13px","height":"13px","top":"1px","left":"1px","mode":"none"}">
                              <image-svg name="Outlined" id="I6608:126087;7229:178223;7176:389999;7176:389931" layout-inline="{"position":"absolute","width":"75.73%","height":"86.33%","top":"7.60%","left":"12.14%","mode":"none"}" />
                            </frame>
                          </instance>
                          <text name="画面风格" id="I6608:126087;7229:178223;7176:390000" text="创建角色形象" />
                        </frame>
                      </frame>
                    </instance>
                    <frame name="arrow down" id="I6608:126087;7229:178224" layout-inline="{"width":"12px","height":"12px","mode":"none"}">
                      <image-svg name="Outlined" id="I6608:126087;7229:178227" layout-inline="{"position":"absolute","width":"77.97%","height":"43.59%","top":"24.64%","left":"11.01%","mode":"none"}" />
                    </frame>
                  </frame>
                  <frame name="Frame 2131174236" id="I6608:126087;7229:179599" layout-inline="{"mode":"column","gap":"8px","padding":"0px 0px 0px 4px","alignSelf":"stretch"}">
                    <text name="小白侦探" id="I6608:126087;7229:179600" text="小白侦探" />
                    <frame name="Frame 2131126747" id="I6608:126087;7229:179601" layout="layout_ZGDCB8">
                      <instance name="媒体" id="I6608:126087;7229:179602" componentId="4909:52491" strokes-inline="{"colors":[{"hex":"#FFFFFF"}],"strokeWeight":"1px","align":"INSIDE"}" layout="layout_V2Q4LH" borderRadius="8px">
                        <rectangle name="image" id="I6608:126087;7229:179602;4909:52469" layout="layout_M8NEG3" />
                      </instance>
                      <instance name="媒体" id="I6608:126087;7229:179603" componentId="4909:52491" layout="layout_V2Q4LH" borderRadius="8px">
                        <rectangle name="image" id="I6608:126087;7229:179603;4909:52469" layout="layout_M8NEG3" />
                      </instance>
                      <instance name="媒体" id="I6608:126087;7229:179604" componentId="4909:52491" layout="layout_V2Q4LH" borderRadius="8px">
                        <rectangle name="image" id="I6608:126087;7229:179604;4909:52469" layout="layout_M8NEG3" />
                      </instance>
                      <instance name="媒体" id="I6608:126087;7229:179605" componentId="4909:52491" layout="layout_V2Q4LH" borderRadius="8px">
                        <rectangle name="image" id="I6608:126087;7229:179605;4909:52469" layout="layout_M8NEG3" />
                      </instance>
                    </frame>
                  </frame>
                </instance>
                <frame name="Frame 2131127245" id="6608:126089" layout-inline="{"mode":"column","gap":"24px","alignSelf":"stretch"}">
                  <frame name="Frame 2131127063" id="6608:126090" layout-inline="{"mode":"column","gap":"12px","alignSelf":"stretch"}">
                    <frame name="更多引导建议" id="6608:126091" layout="layout_G7OIMT">
                      <text name="我已经为你创作完成了&quot;星际奇遇：未知边界&quot;这个太空探险故事。这个故事探索了人类与外星文明接触的可能性，以及面对未知时的勇气、好奇心和责任感。 接下来你希望我做什么？" id="6608:126092" text="我已经为你创作完成了&quot;星际奇遇：未知边界&quot;这个太空探险故事。这个故事探索了人类与外星文明接触的可能性，以及面对未知时的勇气、好奇心和责任感。
接下来你希望我做什么？" />
                    </frame>
                  </frame>
                  <frame name="Frame 2131127244" id="6608:126094" layout-inline="{"mode":"column","gap":"12px","alignSelf":"stretch"}">
                    <frame name="操作区域" id="6608:126095" layout-inline="{"height":"36px","mode":"row","alignItems":"center","gap":"4px","alignSelf":"stretch"}">
                      <frame name="操作组合" id="6608:126096" layout-inline="{"mode":"row","gap":"4px"}">
                        <instance name="Button-0325" id="6608:126097" componentId="4518:89680" layout-inline="{"height":"36px","mode":"row","justifyContent":"center","alignItems":"center","gap":"4px","padding":"4px 16px 4px 14px"}" borderRadius="8px">
                          <text name="text" id="I6608:126097;5760:52579" text="生成视频" />
                        </instance>
                        <instance name="Button-0325" id="6608:126098" componentId="4518:89737" layout-inline="{"height":"36px","mode":"row","justifyContent":"center","alignItems":"center","gap":"4px","padding":"4px 16px"}" borderRadius="8px">
                          <text name="text" id="I6608:126098;5760:52593" text="修改故事" />
                        </instance>
                      </frame>
                    </frame>
                    <frame name="输出" id="6608:126099" layout-inline="{"mode":"row","justifyContent":"center","gap":"12px","alignSelf":"stretch"}" borderRadius="100px">
                      <frame name="剧本" id="6608:126100" layout-inline="{"mode":"column","gap":"16px","flexGrow":1}">
                        <frame name="正文" id="6608:126102" layout-inline="{"mode":"row","justifyContent":"center","alignItems":"center","gap":"4px"}">
                          <frame name="icon" id="6608:126103" layout="layout_TI6TXH">
                            <ellipse name="Ellipse 65692859" id="6608:126104" strokes-inline="{"colors":[{"rgba":"rgba(224, 245, 255, 0.35)"}],"strokeWeight":"1px","align":"INSIDE"}" layout-inline="{"position":"absolute","width":"12px","height":"12px","top":"2px","left":"2px","mode":"none"}" />
                            <image-svg name="Vector 6553" id="6608:126105" strokes-inline="{"colors":[{"rgba":"rgba(224, 245, 255, 0.35)"}],"strokeWeight":"1px","align":"CENTER"}" layout-inline="{"position":"absolute","width":"2px","height":"3px","top":"5px","left":"8px","mode":"none"}" />
                          </frame>
                          <text name="30秒后自动执行" id="6608:126106" text="30秒后自动执行" />
                        </frame>
                      </frame>
                    </frame>
                  </frame>
                </frame>
              </frame>
            </frame>
          </frame>
          <instance name="输入框" id="6608:126115" componentId="1458:91267" layout-inline="{"width":"344px","mode":"column","justifyContent":"flex-end","alignItems":"center","gap":"48px"}">
            <frame name="基础框" id="I6608:126115;3121:65188" layout-inline="{"mode":"column","justifyContent":"center","alignItems":"center","padding":"12px","alignSelf":"stretch"}" borderRadius="16px">
              <frame name="Frame 2131126552" id="I6608:126115;3121:65191" layout-inline="{"mode":"column","gap":"16px","alignSelf":"stretch"}">
                <frame name="Frame 2131126682" id="I6608:126115;3121:65192" layout-inline="{"mode":"row","justifyContent":"space-between","alignItems":"center","gap":"16px","alignSelf":"stretch"}">
                  <frame name="Frame 2131127058" id="I6608:126115;3121:65193" layout-inline="{"mode":"row","alignItems":"center","gap":"4px","flexGrow":1}">
                    <instance name="icon" id="I6608:126115;5352:299599" componentId="5352:297443" layout-inline="{"mode":"row","justifyContent":"flex-end","alignItems":"center","padding":"8px"}">
                      <instance name="icon-add-circle" id="I6608:126115;5352:299599;5352:299592" componentId="5352:290859" layout-inline="{"width":"20px","height":"20px","mode":"none"}">
                        <image-svg name="Original" id="I6608:126115;5352:299599;5352:299592;5:7133" layout-inline="{"position":"absolute","width":"83.33%","height":"83.33%","top":"8.33%","left":"8.33%","mode":"none"}" />
                      </instance>
                    </instance>
                    <text name="聊聊你的想法" id="I6608:126115;3121:65200" text="聊聊你的想法" />
                  </frame>
                  <instance name="icon" id="I6608:126115;3121:82195" componentId="1458:92302" layout-inline="{"mode":"row","justifyContent":"flex-end","alignItems":"center","gap":"6px","padding":"4px"}">
                    <frame name="Send" id="I6608:126115;3121:82195;5352:303481" effects-inline="{"backdropFilter":"blur(40px)"}" layout-inline="{"width":"28px","height":"28px","mode":"none"}" borderRadius="14px">
                      <instance name="Icon" id="I6608:126115;3121:82195;5352:303482" componentId="5352:290890" layout-inline="{"position":"absolute","width":"16px","height":"16px","top":"50%","left":"50%","mode":"none","transform":"translateY(-50%) translateX(-50%)"}">
                        <boolean_operation name="Union" id="I6608:126115;3121:82195;5352:303482;669:127004" layout-inline="{"position":"absolute","width":"10px","height":"12px","top":"2px","left":"3px","mode":"none"}">
                          <image-svg name="Vector" id="I6608:126115;3121:82195;5352:303482;446:75830" strokes-inline="{"colors":[{"rgba":"rgba(5, 24, 56, 0.36)"}],"strokeWeight":"2px","align":"CENTER"}" layout-inline="{"position":"absolute","width":"0.00%","height":"83.33%","top":"8.33%","left":"50.01%","mode":"none"}" />
                          <image-svg name="Vector" id="I6608:126115;3121:82195;5352:303482;446:75831" strokes-inline="{"colors":[{"rgba":"rgba(5, 24, 56, 0.36)"}],"strokeWeight":"2px","align":"CENTER"}" layout-inline="{"position":"absolute","width":"80.01%","height":"66.65%","top":"8.33%","left":"9.99%","mode":"none"}" />
                        </boolean_operation>
                      </instance>
                    </frame>
                  </instance>
                </frame>
              </frame>
            </frame>
          </instance>
        </frame>
      </frame>
    </frame>
  </frame>
</design>
`
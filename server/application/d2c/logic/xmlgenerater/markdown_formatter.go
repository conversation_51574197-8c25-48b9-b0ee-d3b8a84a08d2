package xmlgenerater

import "fmt"

func FormatMarkdown(xml string, componentUse string, dslPrompt string) string {
	if componentUse != "" {
		emphasize := `The "#### MainUI XML" section is the core structure of the UI of the page, please pay close attention. The components in the "#### Component Library" are reusable content. When implementing the code, please reuse as many of these components as possible.`
		return fmt.Sprintf(dslPrompt + "\n\n#### MainUI XML\n```xml\n" + xml + "\n```\n\n#### Component Library\n```xml\n" + componentUse + "\n```\n\n" + emphasize)
	}
	return fmt.Sprintf(dslPrompt + "\n\n#### MainUI XML\n```xml\n" + xml + "\n```")
}

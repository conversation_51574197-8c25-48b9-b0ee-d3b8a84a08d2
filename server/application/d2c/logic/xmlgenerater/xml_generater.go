package xmlgenerater

import (
	"context"
	"fmt"
	"sync"

	"code.byted.org/gopkg/logs/v2/log"
	getcomponentlist "code.byted.org/ies/codin/application/d2c/logic/getcomponentlist"
	"code.byted.org/ies/codin/application/d2c/logic/getfigmainfo/getoriginalimg"
	"code.byted.org/ies/codin/application/d2c/logic/getfigmainfo/getoriginaljson"
	figmaapiUtils "code.byted.org/ies/codin/application/d2c/logic/getfigmainfo/utils"
	"code.byted.org/ies/codin/application/d2c/logic/resultstore/resultstoresetter"
	"code.byted.org/ies/codin/application/d2c/logic/xmlcompressor"
	"code.byted.org/ies/codin/application/d2c/repo/config"
	"code.byted.org/ies/codin/application/d2c/repo/systemprompt"
	systempromptFactory "code.byted.org/ies/codin/application/d2c/repo/systemprompt/factory"
	"code.byted.org/ies/codin/application/d2c/repo/xmlgenerater"
	xmlgeneraterFactory "code.byted.org/ies/codin/application/d2c/repo/xmlgenerater/factory"
	"code.byted.org/ies/codin/common/group"
	commonUtils "code.byted.org/ies/codin/common/utils"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c"
)

type XmlGenerater struct {
	getOriginalJson   *getoriginaljson.GetApiResult
	getOriginalImg    *getoriginalimg.GetApiResult
	imageCompressor   figmaapiUtils.ImageCompressor
	systemprompt      systemprompt.Reader
	xmlGenerater      xmlgenerater.XmlGenerater
	resultstoreSetter *resultstoresetter.ResultStoreSetter
	xmlcompressor     *xmlcompressor.XmlCompressor
	getComponentList  *getcomponentlist.GetComponentList
}

func NewXmlGenerater(config *config.Config) *XmlGenerater {
	return &XmlGenerater{
		getOriginalJson:   getoriginaljson.NewGetApiResult(),
		getOriginalImg:    getoriginalimg.NewGetApiResult(),
		imageCompressor:   figmaapiUtils.NewImageCompressor(),
		systemprompt:      systempromptFactory.NewImpl(config),
		xmlGenerater:      xmlgeneraterFactory.NewImpl(),
		resultstoreSetter: resultstoresetter.NewResultStoreSetter(),
		xmlcompressor:     xmlcompressor.NewXmlCompressor(config),
		getComponentList:  getcomponentlist.NewGetComponentList(config),
	}
}

const quality = 80

func (g *XmlGenerater) GenerateXml(ctx context.Context, request *d2c.GenerateXmlRequest) (string, error) {

	commonRequest := &d2c.D2cAgentCommonRequest{
		Url:        request.Url,
		FigmaToken: request.FigmaToken,
	}

	var mu sync.Mutex
	var jsonStr string
	var imgBase64 string
	var componentList []getcomponentlist.ComponentInfo = make([]getcomponentlist.ComponentInfo, 0)
	var systempromptStr string
	var dslCodegenPrompt string

	handlers := make([]func() error, 0)
	handlers = append(handlers, func() error {
		jsonResp, err := g.getOriginalJson.GetD2cOriginalJson(ctx, commonRequest)
		if err != nil {
			log.V2.Error().With(ctx).KVs("getOriginalJson failed", err)
			return nil
		}

		compressedStr, err := figmaapiUtils.CompressAndStringify(jsonResp)
		if err != nil {
			log.V2.Error().With(ctx).KVs("compressAndStringify failed", err)
			return nil
		}

		mu.Lock()
		jsonStr = compressedStr
		mu.Unlock()
		return nil
	})

	handlers = append(handlers, func() error {
		imgInfo, err := g.getOriginalImg.GetD2cOriginalImg(ctx, commonRequest)
		if err != nil {
			log.V2.Error().With(ctx).KVs("getOriginalImg failed", err)
			return nil
		}

		imgUrl := getFirstImageURL(imgInfo.Images)
		imgBase64Str, err := g.imageCompressor.CompressImageFromURL(ctx, imgUrl, quality)
		if err != nil {
			log.V2.Error().With(ctx).KVs("getOriginalImg failed", err)
			return nil
		}
		mu.Lock()
		imgBase64 = imgBase64Str
		mu.Unlock()

		componentList, err = g.getComponentList.GetComponentList(ctx, getcomponentlist.GetComponentListRequest{
			ImageURL:       imgUrl,
			ImageBase64:    imgBase64Str,
			Business:       request.Business,
			ConversationId: request.ConversationId,
		})
		if err != nil {
			log.V2.Error().With(ctx).KVs("getComponentList failed", err)
		}
		return nil
	})

	// 并行获取XML生成提示词
	handlers = append(handlers, func() error {
		prompt, err := g.systemprompt.GetD2CSystemPromptForXmlGenerater(ctx)
		if err != nil {
			log.V2.Error().With(ctx).KVs("getXmlGeneraterSystemPrompt failed", err)
			return nil
		}
		mu.Lock()
		systempromptStr = prompt
		mu.Unlock()
		return nil
	})

	// 并行获取DSL代码生成提示词
	handlers = append(handlers, func() error {
		prompt, err := g.systemprompt.GetD2CSystemPromptForDslCodegen(ctx)
		if err != nil {
			log.V2.Error().With(ctx).KVs("getDslCodegenSystemPrompt failed", err)
			return nil
		}
		mu.Lock()
		dslCodegenPrompt = prompt
		mu.Unlock()
		return nil
	})

	group.GoAndWait(handlers...)
	log.V2.Info().With(ctx).KVs("xml generater parallel processing success", jsonStr, imgBase64).Emit()

	// 检查并行获取的提示词是否成功
	if systempromptStr == "" {
		log.V2.Error().With(ctx).KVs("systempromptStr is empty, parallel fetch may have failed")
		return "", fmt.Errorf("获取XML生成提示词失败")
	}

	log.V2.Info().With(ctx).KVs("systempromptStr", systempromptStr).Emit()

	// 测试从 压缩XML 生成 DSL XML===============================================
	// xmlTest := false

	// if xmlTest {
	// 	jsonStr = TestXmlFile

	// 	systempromptStr_cxml2aixml, err := g.systemprompt.GetD2CSystemPromptForCxml2Aixml(ctx)
	// 	if err != nil {
	// 		log.V2.Error().With(ctx).KVs("getSystemPrompt failed", err)
	// 		return "", err
	// 	}
	// 	systempromptStr = systempromptStr_cxml2aixml

	// 	log.V2.Info().With(ctx).KVs("xmlTest systempromptStr", systempromptStr).Emit()

	// }

	// end of xmlTest ===========================================================

	results, err := g.xmlGenerater.GenerateXml(ctx, &xmlgenerater.XmlGeneraterRequest{
		ImgBase64:      []string{imgBase64},
		JsonString:     jsonStr,
		SystemPrompt:   systempromptStr,
		ModalType:      request.ModalType,
		ConversationId: request.ConversationId,
	})

	if err != nil {
		log.V2.Error().With(ctx).KVs("generateXml failed", err)
		return "", err
	}

	// 打印 UI模型返回的原始XML
	log.V2.Info().With(ctx).KVs("UI Model results", results[0]).Emit()

	xmlStr, err := commonUtils.ExtractXMLFromResponse(results[0])
	if err != nil {
		log.V2.Error().With(ctx).KVs("extractJSONFromResponse failed", err)
		return "", err
	}

	// 打印 提取 XML后的结果
	log.V2.Info().With(ctx).KVs("extractXMLFromResponse success", xmlStr).Emit()

	if request.NeedCompress != nil && *request.NeedCompress == "false" {
		xmlStr, _, err = commonUtils.WrapXMLWithGlobalID(xmlStr)
		if err != nil {
			log.V2.Error().With(ctx).KVs("wrapXMLWithGlobalID failed", err)
			return "", err
		}

		// 使用并行获取的DSL代码生成提示词
		if dslCodegenPrompt == "" {
			log.V2.Warn().With(ctx).KVs("dslCodegenPrompt is empty, parallel fetch may have failed, using empty string")
		}
		componentUse := FormatComponentUse(componentList)

		return FormatMarkdown(xmlStr, componentUse, dslCodegenPrompt), nil
	}

	xmlStr, xmlID, err := commonUtils.AddUniqueIDsToXML(xmlStr)
	if err != nil {
		log.V2.Error().With(ctx).KVs("addUniqueIDsToXML failed", err)
		return "", err
	}
	log.V2.Info().With(ctx).KVs("xml generater success", xmlID, xmlStr).Emit()

	g.resultstoreSetter.SetD2CResult(ctx, d2c.ContentType_RENDER_TREE_XML, xmlID, xmlStr)

	compressedXmlStr, err := g.xmlcompressor.CompressXml(ctx, xmlStr)
	if err != nil {
		log.V2.Error().With(ctx).KVs("compressAndStringify failed", err)
		return "", err
	}

	return compressedXmlStr, nil
}

func getFirstImageURL(images map[string]string) string {
	if len(images) == 0 {
		return ""
	}

	// 获取map中的第一个值
	// Get the first value from the map
	for _, url := range images {
		return url
	}
	return ""
}

package xmlgenerater

import (
	"fmt"
	"html"
	"regexp"
	"strings"
	"unicode"

	"code.byted.org/ies/codin/application/d2c/logic/getcomponentlist"
)

// preprocessText 预处理文本内容，确保不会破坏XML结构
// 处理逻辑：去掉前后空格、去掉换行、多个空格只保留一个、尽量缩减长度
func preprocessText(text string) string {
	if text == "" {
		return ""
	}

	// 1. 去掉前后空格
	text = strings.TrimSpace(text)

	// 2. 将所有类型的换行符和回车符替换为空格
	text = strings.ReplaceAll(text, "\n", " ")
	text = strings.ReplaceAll(text, "\r", " ")
	text = strings.ReplaceAll(text, "\t", " ")

	// 3. 使用正则表达式将多个连续的空白字符（包括空格、制表符等）替换为单个空格
	multiSpaceRe := regexp.MustCompile(`\s+`)
	text = multiSpaceRe.ReplaceAllString(text, " ")

	// 4. 再次去掉前后空格（防止处理过程中产生的首尾空格）
	text = strings.TrimSpace(text)

	// 5. 长度控制 - 如果文本过长，进行智能截断
	maxLength := 2000 // 设置最大长度为2000字符
	if len(text) > maxLength {
		// 尝试在句号、感叹号、问号处截断
		sentenceEnd := regexp.MustCompile(`[.!?。！？]\s`)
		matches := sentenceEnd.FindAllStringIndex(text[:maxLength], -1)

		if len(matches) > 0 {
			// 在最后一个句子结束处截断
			lastMatch := matches[len(matches)-1]
			text = text[:lastMatch[1]] + "..."
		} else {
			// 如果没有合适的句子分割点，在单词边界截断
			if maxLength > 3 {
				// 寻找最后一个空格位置进行截断
				lastSpace := strings.LastIndex(text[:maxLength-3], " ")
				if lastSpace > maxLength/2 { // 确保截断位置不会太靠前
					text = text[:lastSpace] + "..."
				} else {
					text = text[:maxLength-3] + "..."
				}
			} else {
				text = text[:maxLength]
			}
		}
	}

	// 6. 处理特殊字符，避免影响XML解析
	// 移除或替换可能影响XML结构的特殊字符
	text = strings.ReplaceAll(text, "<!--", "")
	text = strings.ReplaceAll(text, "-->", "")
	text = strings.ReplaceAll(text, "<![CDATA[", "")
	text = strings.ReplaceAll(text, "]]>", "")

	// 7. 清理控制字符（除了基本的可打印字符）
	cleanText := strings.Builder{}
	for _, r := range text {
		if unicode.IsPrint(r) || r == ' ' {
			cleanText.WriteRune(r)
		}
	}

	return cleanText.String()
}

// GenerateComponentLibraryXML 将组件信息列表转换为 ComponentLibrary XML 字符串
func GenerateComponentLibraryXML(componentList []getcomponentlist.ComponentInfo) string {
	if len(componentList) == 0 {
		return ""
	}

	var builder strings.Builder
	// 添加代码生成指导注释
	// builder.WriteString(`    <!--
	// ============ 业务组件库使用指导 ============
	// 优先使用以下成熟业务组件，按匹配策略顺序执行：
	// 1. 先name匹配：根据组件名称精确匹配功能需求
	// 2. desc确认：通过描述内容进行语义匹配确认
	// 3. demo参考：匹配成功后查看demo了解使用方法
	// 4. 无匹配降级：使用基础UI构建
	//   -->` + "\n")
	builder.WriteString("    <ComponentLibrary>\n")

	for _, component := range componentList {
		// 预处理文本内容，清理换行、多余空格，并控制长度
		processedName := preprocessText(component.Name)
		processedDesc := preprocessText(component.Description)
		processedDemo := preprocessText(component.UseDemo)

		// 转义XML特殊字符
		escapedName := html.EscapeString(processedName)
		escapedDesc := html.EscapeString(processedDesc)
		escapedDemo := html.EscapeString(processedDemo)

		builder.WriteString(fmt.Sprintf(`        <CustomComponent name="%s" desc="%s" demo="%s" />`,
			escapedName, escapedDesc, escapedDemo))
		builder.WriteString("\n")
	}

	builder.WriteString("    </ComponentLibrary>")
	return builder.String()
}

// WrapWithUIAgentDSL 将 MainUI XML 和 ComponentLibrary XML 包装为完整的 ui-agent-dsl 结构
func WrapWithUIAgentDSL(mainUIXML string, componentList []getcomponentlist.ComponentInfo) (string, error) {
	// 生成 ComponentLibrary XML
	componentLibraryXML := GenerateComponentLibraryXML(componentList)

	// 去掉 XML 声明（如果存在的话）
	cleanedMainUIXML := mainUIXML
	xmlDeclPattern := `(?i)^\s*<\?xml[^>]*\?>\s*`
	re := regexp.MustCompile(xmlDeclPattern)
	cleanedMainUIXML = re.ReplaceAllString(cleanedMainUIXML, "")
	cleanedMainUIXML = strings.TrimSpace(cleanedMainUIXML)

	// 构建完整的 ui-agent-dsl 结构
	var builder strings.Builder
	builder.WriteString(`<?xml version="1.0" encoding="utf-8"?>` + "\n")
	builder.WriteString(`<ui-agent-dsl  version="1.0">` + "\n")

	// 直接添加清理后的 MainUI XML，不再包装 MainUI 标签
	if cleanedMainUIXML != "" {
		// 给每行添加适当的缩进
		lines := strings.Split(cleanedMainUIXML, "\n")
		for _, line := range lines {
			if strings.TrimSpace(line) != "" {
				builder.WriteString("    " + line + "\n")
			} else if line != "" {
				builder.WriteString(line + "\n")
			}
		}
	}

	// 添加 ComponentLibrary（如果有组件的话）
	if len(componentList) > 0 {
		builder.WriteString(componentLibraryXML + "\n")
	}

	builder.WriteString("</ui-agent-dsl>")

	return builder.String(), nil
}

func FormatComponentUse(componentList []getcomponentlist.ComponentInfo) string {
	componentLibraryXML := GenerateComponentLibraryXML(componentList)
	return componentLibraryXML
}

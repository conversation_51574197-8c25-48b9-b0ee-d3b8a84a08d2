package jsoncompressor

import (
	"context"
	"sync"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/d2c/logic/getfigmainfo/getoriginalimg"
	"code.byted.org/ies/codin/application/d2c/logic/getfigmainfo/getoriginaljson"
	figmaapiUtils "code.byted.org/ies/codin/application/d2c/logic/getfigmainfo/utils"
	"code.byted.org/ies/codin/application/d2c/repo/config"
	"code.byted.org/ies/codin/application/d2c/repo/jsoncompressor"
	jsoncompressorFactory "code.byted.org/ies/codin/application/d2c/repo/jsoncompressor/factory"
	"code.byted.org/ies/codin/application/d2c/repo/systemprompt"
	systempromptFactory "code.byted.org/ies/codin/application/d2c/repo/systemprompt/factory"
	group "code.byted.org/ies/codin/common/group"
	commonUtils "code.byted.org/ies/codin/common/utils"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c"
)

type JsonCompressor struct {
	getOriginalJson *getoriginaljson.GetApiResult
	getOriginalImg  *getoriginalimg.GetApiResult
	imageCompressor figmaapiUtils.ImageCompressor
	systemprompt    systemprompt.Reader
	jsonCompressor  jsoncompressor.JsonCompressor
}

func NewJsonCompressor(config *config.Config) *JsonCompressor {
	return &JsonCompressor{
		getOriginalJson: getoriginaljson.NewGetApiResult(),
		getOriginalImg:  getoriginalimg.NewGetApiResult(),
		imageCompressor: figmaapiUtils.NewImageCompressor(),
		systemprompt:    systempromptFactory.NewImpl(config),
		jsonCompressor:  jsoncompressorFactory.NewImpl(),
	}
}

const quality = 80

func (g *JsonCompressor) CompressJson(ctx context.Context, req *d2c.CompressJsonRequest) (string, error) {

	commonRequest := &d2c.D2cAgentCommonRequest{
		Url:        req.Url,
		FigmaToken: req.FigmaToken,
	}

	var mu sync.Mutex
	var jsonStr string
	var imgBase64 string

	handlers := make([]func() error, 0)
	handlers = append(handlers, func() error {
		jsonResp, err := g.getOriginalJson.GetD2cOriginalJson(ctx, commonRequest)
		if err != nil {
			log.V2.Error().With(ctx).KVs("getOriginalJson failed", err)
			return nil
		}

		compressedStr, err := figmaapiUtils.CompressAndStringify(jsonResp)
		if err != nil {
			log.V2.Error().With(ctx).KVs("compressAndStringify failed", err)
			return nil
		}

		mu.Lock()
		jsonStr = compressedStr
		mu.Unlock()
		return nil
	})

	handlers = append(handlers, func() error {
		imgInfo, err := g.getOriginalImg.GetD2cOriginalImg(ctx, commonRequest)
		if err != nil {
			log.V2.Error().With(ctx).KVs("getOriginalImg failed", err)
			return nil
		}

		imgUrl := getFirstImageURL(imgInfo.Images)
		imgBase64Str, err := g.imageCompressor.CompressImageFromURL(ctx, imgUrl, quality)
		if err != nil {
			log.V2.Error().With(ctx).KVs("getOriginalImg failed", err)
			return nil
		}
		mu.Lock()
		imgBase64 = imgBase64Str
		mu.Unlock()
		return nil
	})

	group.GoAndWait(handlers...)
	log.V2.Info().With(ctx).KVs("Get original json and img success", jsonStr, imgBase64).Emit()

	systempromptStr, err := g.systemprompt.GetD2CSystemPromptForJsonCompressor(ctx)
	if err != nil {
		log.V2.Error().With(ctx).KVs("getSystemPrompt failed", err)
		return "", err
	}

	results, err := g.jsonCompressor.CompressJson(ctx, imgBase64, jsonStr, systempromptStr, req.ModalType)
	if err != nil {
		log.V2.Error().With(ctx).KVs("compress json failed", err)
		return "", err
	}

	jsonStr, err = commonUtils.ExtractJSONFromResponse(results[0])
	if err != nil {
		log.V2.Error().With(ctx).KVs("extractJSONFromResponse failed", err)
		return "", err
	}

	log.V2.Info().With(ctx).KVs("compressJson success", jsonStr).Emit()

	return jsonStr, nil
}

func getFirstImageURL(images map[string]string) string {
	if images == nil || len(images) == 0 {
		return ""
	}

	// 获取map中的第一个值
	// Get the first value from the map
	for _, url := range images {
		return url
	}
	return ""
}

package tools

import (
	"context"
	"encoding/json"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/d2c/logic"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type XmlGenerater struct {
	app        *logic.App
	figmaToken *string
}

func NewXmlGenerater(app *logic.App, figmaToken *string) *XmlGenerater {
	return &XmlGenerater{
		app:        app,
		figmaToken: figmaToken,
	}
}

func (t *XmlGenerater) Info(ctx context.Context) (*schema.ToolInfo, error) {
	log.V2.Info().With(ctx).Str("XmlGenerater Info").Emit()
	return &schema.ToolInfo{
		Name: "xml_generater",
		Desc: "如果需要获取设计稿信息，请使用此工具，使用时需要提供figma token。该工具可以将设计稿转为xml，后续可直接基于xml实现UI代码。",
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"url": {
				Type:     "string",
				Desc:     "需要获取原始json的figma selection链接",
				Required: true,
			},
		}),
	}, nil
}

func (t *XmlGenerater) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	var params d2c.GenerateXmlRequest
	log.V2.Info().With(ctx).Str("XmlGenerater InvokableRun", argumentsInJSON).Emit()
	if err := json.Unmarshal([]byte(argumentsInJSON), &params); err != nil {
		log.V2.Error().With(ctx).KVs("XmlGenerater InvokableRun Unmarshal", argumentsInJSON, opts).Error(err).Emit()
		return "", err
	}
	if t.figmaToken != nil {
		params.FigmaToken = *t.figmaToken
	}
	xml, err := t.app.XmlGenerater.GenerateXml(ctx, &params)
	if err != nil {
		log.V2.Error().With(ctx).KVs("XmlGenerater InvokableRun GenerateXml", argumentsInJSON, opts).Error(err).Emit()
		return "", err
	}

	return xml, nil
}

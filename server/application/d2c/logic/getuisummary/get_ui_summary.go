package getuisummary

import (
	"context"
	"encoding/json"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	entity "code.byted.org/ies/codin/application/d2c/entity/const"
	"code.byted.org/ies/codin/application/d2c/logic/getfigmainfo/getoriginalimg"
	"code.byted.org/ies/codin/application/d2c/logic/getfigmainfo/getoriginaljson"
	figmaapi "code.byted.org/ies/codin/application/d2c/logic/getfigmainfo/utils"
	"code.byted.org/ies/codin/application/d2c/logic/getsplitmodules"
	"code.byted.org/ies/codin/application/d2c/logic/resultstore/resultstoresetter"
	"code.byted.org/ies/codin/application/d2c/repo/config"
	"code.byted.org/ies/codin/application/d2c/repo/systemprompt"
	systempromptFactory "code.byted.org/ies/codin/application/d2c/repo/systemprompt/factory"
	analyzeimg "code.byted.org/ies/codin/common/analyzeimg"
	analyzeimgFactory "code.byted.org/ies/codin/common/analyzeimg/factory"
	"code.byted.org/ies/codin/common/group"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c"
)

type GetUiSummary struct {
	getOriginalJson   *getoriginaljson.GetApiResult
	getOriginalImg    *getoriginalimg.GetApiResult
	imageCompressor   figmaapi.ImageCompressor
	systemprompt      systemprompt.Reader
	getSplitModules   *getsplitmodules.GetSplitModulesInfo
	resultstoreSetter *resultstoresetter.ResultStoreSetter
}

func NewGetUiSummary(config *config.Config) *GetUiSummary {
	return &GetUiSummary{
		getOriginalJson:   getoriginaljson.NewGetApiResult(),
		getOriginalImg:    getoriginalimg.NewGetApiResult(),
		getSplitModules:   getsplitmodules.NewGetSplitModulesInfo(config),
		imageCompressor:   figmaapi.NewImageCompressor(),
		systemprompt:      systempromptFactory.NewImpl(config),
		resultstoreSetter: resultstoresetter.NewResultStoreSetter(),
	}
}

// getFirstImageURL 从Images map中获取第一张图片的URL
// getFirstImageURL gets the first image URL from Images map
func getFirstImageURL(images map[string]string) string {
	if images == nil || len(images) == 0 {
		return ""
	}

	// 获取map中的第一个值
	// Get the first value from the map
	for _, url := range images {
		return url
	}
	return ""
}

func (g *GetUiSummary) PreFetchUiDetail(ctx context.Context, request *d2c.GetUiSummaryRequest) error {
	commonRequest := &d2c.D2cAgentCommonRequest{
		Url:        request.Url,
		FigmaToken: request.FigmaToken,
	}

	splitResp, err := g.getSplitModules.GetSplitModulesByUrl(ctx, commonRequest)
	if err != nil {
		log.V2.Error().With(ctx).KVs("getSplitModules failed", err)
	}
	log.V2.Info().With(ctx).KVs("splitResp", splitResp)
	params, err := g.getSplitModules.FormatParams(ctx, commonRequest)
	if err != nil {
		log.V2.Error().With(ctx).KVs("FormatParams failed", err)
	}
	key := entity.PrefetchPrefix + params.FileKey + "_" + params.NodeIds[0]
	splitRespStr, err := json.Marshal(splitResp)
	if err != nil {
		log.V2.Error().With(ctx).KVs("Marshal splitResp failed", err)
	}
	g.resultstoreSetter.SetD2CResult(ctx, d2c.ContentType_COMBINATION, key, string(splitRespStr))
	return nil
}

func (g *GetUiSummary) GetUiSummary(ctx context.Context, request *d2c.GetUiSummaryRequest) (*d2c.GetUiSummaryResponse, error) {
	response := &d2c.GetUiSummaryResponse{}

	commonRequest := &d2c.D2cAgentCommonRequest{
		Url:        request.Url,
		FigmaToken: request.FigmaToken,
	}

	asyncCtx := group.BuildAsyncCtx(ctx)
	group.Go(asyncCtx, 3*time.Minute, func(ctx context.Context) {
		g.PreFetchUiDetail(ctx, request)
	})

	imgResp, err := g.getOriginalImg.GetD2cOriginalImg(ctx, commonRequest)
	if err != nil {
		log.V2.Error().With(ctx).KVs("getOriginalImg failed", err)
	} else {
		firstImageURL := getFirstImageURL(imgResp.Images)
		if firstImageURL != "" {
			compressedBase64, err := g.imageCompressor.CompressImageFromURL(ctx, firstImageURL, 80)
			if err != nil {
				log.V2.Error().With(ctx).Str("图片压缩失败").Error(err).KVs(
					"imageURL", firstImageURL,
				).Emit()
			} else {
				imgSystemPrompt, err := g.systemprompt.GetD2CSystemPromptForImgSummaryNL(ctx)
				if err != nil {
					log.V2.Error().With(ctx).KVs("GetD2CSystemPromptForImg failed", err)
				}
				imgs, err := analyzeimgFactory.NewImpl().Img2NL(ctx, &analyzeimg.Img2NLRequest{
					ImgBase64:    []string{compressedBase64},
					SystemPrompt: imgSystemPrompt,
					UserPrompt:   request.Description,
				})
				if err != nil {
					log.V2.Error().With(ctx).KVs("Img2NL failed", err)
				} else {
					response.Summary = &imgs[0]
				}
			}
		}
	}

	return response, nil
}

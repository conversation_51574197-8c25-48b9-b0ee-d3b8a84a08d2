package componentstore

import (
	"context"
	"encoding/json"

	"code.byted.org/ies/codin/application/d2c/repo/store"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c"
)

const (
	ComponentSummaryCollection = "component_summary"
)

type ComponentSummaryStore struct {
	componentSummaryStore *store.ComponentSummaryStore
}

func NewComponentSummaryStore() *ComponentSummaryStore {
	return &ComponentSummaryStore{
		componentSummaryStore: store.NewComponentSummaryStore(),
	}
}

func (c *ComponentSummaryStore) SetComponentSummary(ctx context.Context, componentKey string, business string, description string, useDemo string) (string, error) {
	result, err := c.componentSummaryStore.UpsertOne(ctx, ComponentSummaryCollection, componentKey, business, description, useDemo)
	if err != nil {
		return "", err // 错误已在 repo 层处理
	}

	insertId, err := json.Marshal(result.UpsertedID)
	if err != nil {
		return "", rpcerr.Wrap(err, d2c.D2cErrorCode_JsonMarshalFailed, "插入ID序列化失败")
	}
	return string(insertId), nil
}

func (c *ComponentSummaryStore) GetComponentSummary(ctx context.Context, componentKey string, business string) (*d2c.ComponentSummary, error) {
	summary, err := c.componentSummaryStore.FindOne(ctx, ComponentSummaryCollection, componentKey, business)
	if err != nil {
		return nil, err // 错误已在 repo 层处理
	}
	return summary, nil
}

func (c *ComponentSummaryStore) GetComponentSummaryByBusiness(ctx context.Context, business string) ([]*d2c.ComponentSummary, error) {
	summaries, err := c.componentSummaryStore.FindByBusiness(ctx, ComponentSummaryCollection, business)
	if err != nil {
		return nil, err // 错误已在 repo 层处理
	}
	return summaries, nil
}

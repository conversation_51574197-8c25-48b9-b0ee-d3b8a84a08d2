package xmlcompressor

import (
	"context"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/d2c/repo/config"
	"code.byted.org/ies/codin/application/d2c/repo/systemprompt"
	systempromptFactory "code.byted.org/ies/codin/application/d2c/repo/systemprompt/factory"
	"code.byted.org/ies/codin/application/d2c/repo/xmlcompressor"
	xmlcompressorFactory "code.byted.org/ies/codin/application/d2c/repo/xmlcompressor/factory"
	commonUtils "code.byted.org/ies/codin/common/utils"
)

type XmlCompressor struct {
	systemprompt  systemprompt.Reader
	xmlCompressor xmlcompressor.XmlCompressor
}

func NewXmlCompressor(config *config.Config) *XmlCompressor {
	return &XmlCompressor{
		systemprompt:  systempromptFactory.NewImpl(config),
		xmlCompressor: xmlcompressorFactory.NewImpl(),
	}
}

func (g *XmlCompressor) CompressXml(ctx context.Context, originalXml string) (string, error) {

	systempromptStr, err := g.systemprompt.GetD2CSystemPromptForXmlCompressor(ctx)
	if err != nil {
		log.V2.Error().With(ctx).KVs("getSystemPrompt failed", err)
		return "", err
	}

	results, err := g.xmlCompressor.CompressXml(ctx, originalXml, systempromptStr)
	if err != nil {
		log.V2.Error().With(ctx).KVs("analyzeimg failed", err)
		return "", err
	}

	xmlStr, err := commonUtils.ExtractXMLFromResponse(results[0])
	if err != nil {
		log.V2.Error().With(ctx).KVs("extractJSONFromResponse failed", err)
		return "", err
	}

	log.V2.Info().With(ctx).KVs("compressXml success", xmlStr).Emit()

	return xmlStr, nil
}

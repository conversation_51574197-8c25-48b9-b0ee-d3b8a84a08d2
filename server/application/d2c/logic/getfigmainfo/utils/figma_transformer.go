package figmaapi

import (
	"encoding/json"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c"
)

func CompressAndStringify(data interface{}) (string, error) {
	if data == nil {
		return "", nil
	}

	// 首先需要将数据转换为ParseFigmaResponse支持的类型
	// First convert data to types supported by ParseFigmaResponse
	var parsableData interface{}

	// 检查是否是GetD2cOriginalJsonResponse类型，需要转换为GetFileNodesResponse
	// Check if it's GetD2cOriginalJsonResponse type, needs conversion to GetFileNodesResponse
	if resp, ok := data.(*d2c.GetFileNodesResponse); ok {
		// 通过JSON序列化/反序列化进行类型转换
		// Convert through JSON serialization/deserialization
		jsonBytes, err := json.Marshal(resp)
		if err != nil {
			log.V2.Error().Str("json.<PERSON> failed during type conversion").Error(err).Emit()
			return "", err
		}

		var getFileNodesResponse GetFileNodesResponse
		if err := json.Unmarshal(jsonBytes, &getFileNodesResponse); err != nil {
			log.V2.Error().Str("json.Unmarshal failed during type conversion").Error(err).Emit()
			return "", err
		}

		parsableData = getFileNodesResponse
	} else {
		// 其他类型直接使用
		// Use other types directly
		parsableData = data
	}

	// 使用figmaapi包中的压缩函数
	// Use compression function from figmaapi package
	compressed, err := ParseFigmaResponse(parsableData)
	if err != nil {
		log.V2.Error().Str("ParseFigmaResponse failed").Error(err).Emit()
		return "", err
	}

	// 序列化为JSON
	// Serialize to JSON
	jsonBytes, err := json.Marshal(compressed)
	if err != nil {
		log.V2.Error().Str("json.Marshal failed").Error(err).Emit()
		return "", err
	}

	// 移除空格、换行符、双引号和反斜线以进一步压缩
	// Remove spaces, newlines, quotes and backslashes for further compression
	jsonStr := string(jsonBytes)
	jsonStr = strings.ReplaceAll(jsonStr, " ", "")
	jsonStr = strings.ReplaceAll(jsonStr, "\n", "")
	jsonStr = strings.ReplaceAll(jsonStr, "\r", "")
	jsonStr = strings.ReplaceAll(jsonStr, "\t", "")
	jsonStr = strings.ReplaceAll(jsonStr, "\"", "") // 移除双引号
	jsonStr = strings.ReplaceAll(jsonStr, "\\", "") // 移除反斜线

	return jsonStr, nil
}

// !!!!!! 注意，运行的时候需要删除 image_compression_test.go image_compression.go 和 image_demo.go
// 执行命令 go test -v -run TestFigmaParser
// 原始数据放在 origin-data.json 中
package figmaapi

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

// TestResult 测试结果结构体
type TestResult struct {
	Success      bool              `json:"success"`
	ErrorMessage *string           `json:"errorMessage,omitempty"`
	ParsedData   *SimplifiedDesign `json:"parsedData,omitempty"`
	Statistics   Statistics        `json:"statistics"`
}

// Statistics 解析统计信息
type Statistics struct {
	TotalNodes       int   `json:"totalNodes"`
	TotalComponents  int   `json:"totalComponents"`
	TotalStyleVars   int   `json:"totalStyleVars"`
	ProcessingTimeMs int64 `json:"processingTimeMs"`
}

// countNodes 递归计算节点数量
func countNodes(nodes []SimplifiedNode) int {
	count := len(nodes)
	for _, node := range nodes {
		if len(node.Children) > 0 {
			count += countNodes(node.Children)
		}
	}
	return count
}

// testFigmaParser 测试Figma解析器
func testFigmaParser() *TestResult {
	fmt.Println("🚀 开始测试Figma解析器...")

	// 1. 读取测试数据文件
	fmt.Println("📖 读取origin-data2.json文件...")
	currentDir, err := os.Getwd()
	if err != nil {
		errorMsg := fmt.Sprintf("获取当前目录失败: %v", err)
		return &TestResult{
			Success:      false,
			ErrorMessage: &errorMsg,
		}
	}

	dataFile := filepath.Join(currentDir, "origin-data2.json")
	data, err := ioutil.ReadFile(dataFile)
	if err != nil {
		errorMsg := fmt.Sprintf("读取文件失败: %v", err)
		return &TestResult{
			Success:      false,
			ErrorMessage: &errorMsg,
		}
	}

	fmt.Printf("✅ 成功读取文件，大小: %d 字节\n", len(data))

	// 2. 解析JSON数据
	fmt.Println("🔍 解析JSON数据...")
	var figmaResponse GetFileNodesResponse
	if err := json.Unmarshal(data, &figmaResponse); err != nil {
		errorMsg := fmt.Sprintf("JSON解析失败: %v", err)
		return &TestResult{
			Success:      false,
			ErrorMessage: &errorMsg,
		}
	}

	fmt.Printf("✅ JSON解析成功，文档名称: %s\n", figmaResponse.Name)
	fmt.Printf("📅 最后修改时间: %s\n", figmaResponse.LastModified)
	fmt.Printf("🔢 节点数量: %d\n", len(figmaResponse.Nodes))

	// 3. 使用ParseFigmaResponse函数解析
	fmt.Println("⚙️  调用ParseFigmaResponse函数...")
	result, err := ParseFigmaResponse(figmaResponse)
	if err != nil {
		errorMsg := fmt.Sprintf("解析失败: %v", err)
		return &TestResult{
			Success:      false,
			ErrorMessage: &errorMsg,
		}
	}

	// 4. 统计解析结果
	fmt.Println("📊 统计解析结果...")
	totalNodes := countNodes(result.Nodes)

	statistics := Statistics{
		TotalNodes:      totalNodes,
		TotalComponents: len(result.Components),
		TotalStyleVars:  len(result.GlobalVars.Styles),
	}

	fmt.Printf("✅ 解析成功！\n")
	fmt.Printf("   - 总节点数: %d\n", statistics.TotalNodes)
	fmt.Printf("   - 组件数: %d\n", statistics.TotalComponents)
	fmt.Printf("   - 样式变量数: %d\n", statistics.TotalStyleVars)

	return &TestResult{
		Success:    true,
		ParsedData: result,
		Statistics: statistics,
	}
}

// printDetailedResults 打印详细结果
func printDetailedResults(result *TestResult) {
	if !result.Success {
		fmt.Printf("❌ 测试失败: %s\n", *result.ErrorMessage)
		return
	}

	design := result.ParsedData

	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("📋 详细解析结果")
	fmt.Println(strings.Repeat("=", 60))

	// 基本信息
	fmt.Printf("🏷️  设计名称: %s\n", design.Name)
	fmt.Printf("🕒 最后修改: %s\n", design.LastModified)
	fmt.Printf("🖼️  缩略图: %s\n", design.ThumbnailURL)

	// 统计信息
	fmt.Printf("\n📊 统计信息:\n")
	fmt.Printf("   - 根节点数: %d\n", len(design.Nodes))
	fmt.Printf("   - 总节点数: %d\n", result.Statistics.TotalNodes)
	fmt.Printf("   - 组件定义数: %d\n", len(design.Components))
	fmt.Printf("   - 组件集数: %d\n", len(design.ComponentSets))
	fmt.Printf("   - 全局样式数: %d\n", len(design.GlobalVars.Styles))

	// 显示前几个根节点
	fmt.Printf("\n🌳 根节点列表:\n")
	for i, node := range design.Nodes {
		if i >= 3 { // 只显示前3个
			fmt.Printf("   ... (共%d个根节点)\n", len(design.Nodes))
			break
		}
		fmt.Printf("   %d. %s (ID: %s, Type: %s)\n", i+1, node.Name, node.ID, node.Type)
		if len(node.Children) > 0 {
			fmt.Printf("      └── 子节点数: %d\n", len(node.Children))
		}
	}

	// 显示组件信息
	if len(design.Components) > 0 {
		fmt.Printf("\n🧩 组件列表:\n")
		count := 0
		for _, comp := range design.Components {
			if count >= 5 { // 只显示前5个
				fmt.Printf("   ... (共%d个组件)\n", len(design.Components))
				break
			}
			fmt.Printf("   - %s (ID: %s, Key: %s)\n", comp.Name, comp.ID, comp.Key)
			count++
		}
	}

	// 显示样式变量
	if len(design.GlobalVars.Styles) > 0 {
		fmt.Printf("\n🎨 样式变量示例:\n")
		count := 0
		for styleID, style := range design.GlobalVars.Styles {
			if count >= 3 { // 只显示前3个
				fmt.Printf("   ... (共%d个样式变量)\n", len(design.GlobalVars.Styles))
				break
			}
			styleJSON, _ := json.MarshalIndent(style, "      ", "  ")
			fmt.Printf("   - %s:\n      %s\n", styleID, string(styleJSON))
			count++
		}
	}
}

// saveResultToFile 保存结果到文件
func saveResultToFile(result *TestResult) error {
	outputFile := "test_result.json"

	// 首先生成格式化的JSON用于调试
	formattedJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化结果失败: %v", err)
	}

	// 生成压缩的JSON（移除空格和换行符）
	compactJSON, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("序列化压缩结果失败: %v", err)
	}

	// 进一步移除JSON中的空格和换行符
	compactedJSONStr := removeWhitespaceAndNewlines(string(compactJSON))

	// 写入压缩的JSON文件
	if err := ioutil.WriteFile(outputFile, []byte(compactedJSONStr), 0644); err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	// 同时保存格式化版本用于人类阅读
	formattedOutputFile := "test_result_formatted2.json"
	if err := ioutil.WriteFile(formattedOutputFile, formattedJSON, 0644); err != nil {
		return fmt.Errorf("写入格式化文件失败: %v", err)
	}

	fmt.Printf("\n💾 测试结果已保存到: %s (压缩版本)\n", outputFile)
	fmt.Printf("📄 格式化版本已保存到: %s (便于阅读)\n", formattedOutputFile)
	fmt.Printf("📊 文件大小对比: 压缩版 %d 字节 vs 格式化版 %d 字节\n", len(compactedJSONStr), len(formattedJSON))

	return nil
}

// removeWhitespaceAndNewlines 移除字符串中的空格和换行符
// Remove whitespaces and newlines from string
func removeWhitespaceAndNewlines(jsonStr string) string {
	// 移除所有空格、制表符、换行符和回车符
	// Remove all spaces, tabs, newlines and carriage returns
	result := strings.ReplaceAll(jsonStr, " ", "")
	result = strings.ReplaceAll(result, "\t", "")
	result = strings.ReplaceAll(result, "\n", "")
	result = strings.ReplaceAll(result, "\r", "")

	return result
}

// validateParsedData 验证解析数据的完整性
func validateParsedData(design *SimplifiedDesign) []string {
	var issues []string

	// 检查基本字段
	if design.Name == "" {
		issues = append(issues, "设计名称为空")
	}

	if design.LastModified == "" {
		issues = append(issues, "最后修改时间为空")
	}

	if len(design.Nodes) == 0 {
		issues = append(issues, "没有解析到任何节点")
	}

	// 检查节点完整性
	for i, node := range design.Nodes {
		if node.ID == "" {
			issues = append(issues, fmt.Sprintf("第%d个根节点ID为空", i+1))
		}
		if node.Name == "" {
			issues = append(issues, fmt.Sprintf("第%d个根节点名称为空", i+1))
		}
		if node.Type == "" {
			issues = append(issues, fmt.Sprintf("第%d个根节点类型为空", i+1))
		}
	}

	return issues
}

func TestFigmaParser(t *testing.T) {
	fmt.Println("🧪 Figma解析器测试程序")
	fmt.Println("作者: AI Assistant")
	fmt.Println("描述: 测试figma_parser.go对origin-data.json的解析能力")
	fmt.Println()

	// 执行测试
	result := testFigmaParser()

	// 如果测试成功，进行数据验证
	if result.Success {
		fmt.Println("\n🔍 验证解析数据完整性...")
		issues := validateParsedData(result.ParsedData)

		if len(issues) == 0 {
			fmt.Println("✅ 数据验证通过，解析结果完整正确")
		} else {
			fmt.Println("⚠️  发现以下问题:")
			for _, issue := range issues {
				fmt.Printf("   - %s\n", issue)
			}
		}
	}

	// 打印详细结果
	printDetailedResults(result)

	// 保存结果到文件
	if err := saveResultToFile(result); err != nil {
		log.Printf("保存结果失败: %v", err)
	}

	// 最终结果
	fmt.Println("\n" + strings.Repeat("=", 60))
	if result.Success {
		fmt.Println("🎉 测试完成！解析器工作正常")
	} else {
		fmt.Printf("💥 测试失败！错误: %s\n", *result.ErrorMessage)
		os.Exit(1)
	}
}

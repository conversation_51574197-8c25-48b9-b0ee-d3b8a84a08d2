package getd2cresult

import (
	"context"
	"encoding/json"
	"strings"

	"code.byted.org/ies/codin/application/d2c/logic/getsplitmodules"
	"code.byted.org/ies/codin/application/d2c/logic/xmlgenerater"
	"code.byted.org/ies/codin/application/d2c/repo/config"
	"code.byted.org/ies/codin/application/d2c/repo/store"
	commonUtils "code.byted.org/ies/codin/common/utils"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c"
)

type GetD2CResult struct {
	getSplitModules *getsplitmodules.GetSplitModulesInfo
	xmlGenerater    *xmlgenerater.XmlGenerater
}

func NewGetD2CResult(config *config.Config) *GetD2CResult {
	return &GetD2CResult{
		getSplitModules: getsplitmodules.NewGetSplitModulesInfo(config),
		xmlGenerater:    xmlgenerater.NewXmlGenerater(config),
	}
}

// GetD2CResult 从Redis中获取D2C结果值
func (g *GetD2CResult) GetD2cJsonResult(ctx context.Context, key string, url *string, figmaToken *string) (*store.D2cResultItemValue, error) {
	result, err := store.GetD2cResultValueFromRedis(ctx, key)
	if err != nil || result == nil {
		if url != nil && figmaToken != nil {
			splitResult, err := g.getSplitModules.GetSplitModulesByUrl(ctx, &d2c.D2cAgentCommonRequest{
				Url:        *url,
				FigmaToken: *figmaToken,
			})
			if err != nil {
				return nil, err
			}
			content, _ := json.Marshal(splitResult)
			return &store.D2cResultItemValue{
				Type:    d2c.ContentType_COMBINATION,
				Content: string(content),
			}, nil
		}
		return &store.D2cResultItemValue{
			Type:    d2c.ContentType_SINGLE_COMPONENT,
			Content: "key is unavailable, please use figma url to get split modules",
		}, nil
	}
	return result, nil
}

func (g *GetD2CResult) GetD2CXMLResult(ctx context.Context, key string, url *string, figmaToken *string) (*store.D2cResultItemValue, error) {
	// 安全地分割key，避免数组越界
	parts := strings.Split(key, "_")
	if len(parts) < 2 {
		return &store.D2cResultItemValue{
			Type:    d2c.ContentType_RENDER_TREE_XML,
			Content: "invalid key format, expected format: xmlId_nodeId",
		}, nil
	}
	xmlId, nodeId := parts[0], parts[1]

	result, err := store.GetD2cResultValueFromRedis(ctx, xmlId)
	if err != nil || result == nil {
		if url != nil && figmaToken != nil {
			return g.GetD2CXMLResultUrl(ctx, *url, *figmaToken)
		}
		return &store.D2cResultItemValue{
			Type:    d2c.ContentType_RENDER_TREE_XML,
			Content: "key is unavailable, please use figma url to get split modules",
		}, nil
	}
	xmlContent, err := commonUtils.ExtractNodeByID(result.Content, nodeId)
	if err != nil || xmlContent == "" {
		return result, err
	}
	return &store.D2cResultItemValue{
		Type:    d2c.ContentType_RENDER_TREE_XML,
		Content: xmlContent,
	}, nil
}

func (g *GetD2CResult) GetD2CXMLResultUrl(ctx context.Context, url string, figmaToken string) (*store.D2cResultItemValue, error) {
	xmlResult, err := g.xmlGenerater.GenerateXml(ctx, &d2c.GenerateXmlRequest{
		Url:        url,
		FigmaToken: figmaToken,
	})
	if err != nil {
		return nil, err
	}
	return &store.D2cResultItemValue{
		Type:    d2c.ContentType_RENDER_TREE_XML,
		Content: xmlResult,
	}, nil
}

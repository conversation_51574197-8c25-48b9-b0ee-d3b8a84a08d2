package generatermodal

import (
	"context"
	"errors"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/d2c/repo/xmlgenerater"
	agentsdk "code.byted.org/ies/codin/common/agentsdk/core"
	"code.byted.org/ies/codin/common/agentsdk/tokencalculator"
	"code.byted.org/ies/codin/common/llm"
	"code.byted.org/ies/codin/common/login"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/ies/codinmodel/kitex_gen/agentserver"
	"code.byted.org/ies/codinmodel/kitex_gen/base"
	"code.byted.org/ies/codinmodel/kitex_gen/userinput"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c"
	"code.byted.org/overpass/capcut_devops_expense/kitex_gen/expense"
	"github.com/cloudwego/eino/schema"
)

// 多模态分析器
type impl struct {
	AgentSDK agentsdk.AgentSDK
}

// NewImpl 创建多模态分析器
func NewImpl() xmlgenerater.XmlGenerater {
	return &impl{
		AgentSDK: agentsdk.NewAgentSDK(),
	}
}

// GenerateXml 根据 json+img 生成xml
func (i *impl) GenerateXml(
	ctx context.Context,
	request *xmlgenerater.XmlGeneraterRequest,
) ([]string, error) {
	log.V2.Info().With(ctx).Str("开始生成xml").Emit()
	if len(request.ImgBase64) == 0 {
		return nil, nil
	}

	message, err := i.generateXmlWithRetry(
		ctx,
		request,
		i.AgentSDK,
	)

	if err != nil {
		log.V2.Error().With(ctx).Str("生成xml失败").Error(err).Emit()
		return nil, rpcerr.Wrap(err, d2c.D2cErrorCode_XmlGenerateFailed, "生成xml失败")
	}
	log.V2.Info().With(ctx).Str("生成xml成功").Str("xml", cleanDescription(message.Content)).Emit()
	return []string{message.Content}, nil
}

// generateXmlWithRetry 带重试机制的xml生成
func (i *impl) generateXmlWithRetry(
	ctx context.Context,
	request *xmlgenerater.XmlGeneraterRequest,
	agentsdkimpl agentsdk.AgentSDK,
) (*schema.Message, error) {
	// 首先使用原始模型类型尝试
	message, err := i.generateXmlWithModel(ctx, request, agentsdkimpl)
	if err != nil {
		// 检查是否为429错误
		if i.is429Error(err) {
			log.V2.Warn().With(ctx).Str("检测到429错误，切换到claude_3_5_sonnet模型重试").Error(err).Emit()
			// 如果原始模型不是claude_3_5_sonnet，则切换到claude_3_5_sonnet重试
			if request.ModalType != "claude_3_5_sonnet" {
				return i.generateXmlWithModel(ctx, &xmlgenerater.XmlGeneraterRequest{
					ImgBase64:      request.ImgBase64,
					JsonString:     request.JsonString,
					SystemPrompt:   request.SystemPrompt,
					ModalType:      "claude_3_5_sonnet",
					ConversationId: request.ConversationId,
				}, agentsdkimpl)
			}
		}
		return nil, err
	}
	return message, nil
}

// generateXmlWithModel 使用指定模型生成xml
func (i *impl) generateXmlWithModel(
	ctx context.Context,
	request *xmlgenerater.XmlGeneraterRequest,
	agentsdkimpl agentsdk.AgentSDK,
) (*schema.Message, error) {
	var modal *llm.ChatModelWithName
	var err error
	modalType := request.ModalType
	if modalType == "seed16_thinking" {
		modal, err = llm.GetChatModel(ctx, base.ModelType_Seed1_6_Thinking)
	} else if modalType == "doubao_vision_pro" {
		modal, err = llm.GetChatModel(ctx, base.ModelType_DoubaoThinkingPro)
	} else if modalType == "gemini_2_5_pro" {
		modal, err = llm.GetChatModel(ctx, base.ModelType_Gemini2_5_Pro)
	} else if modalType == "claude_3_5_sonnet" {
		modal, err = llm.GetChatModel(ctx, base.ModelType_Claude)
	} else if modalType == "gpt_o3" {
		modal, err = llm.GetChatModel(ctx, base.ModelType_GPT_O3)
	} else {
		modal, err = llm.GetChatModel(ctx, base.ModelType_Gemini2_5_Pro) // 默认使用Gemini 2.5 Pro
	}

	if err != nil {
		log.V2.Error().With(ctx).Str("创建模型失败").Error(err).Emit()
		return nil, err
	}

	return generateXmlResult(ctx, modal, request, agentsdkimpl)
}

// is429Error 检查是否为429错误
func (i *impl) is429Error(err error) bool {
	if err == nil {
		return false
	}

	// 检查错误码是否为429
	if rpcerr.Code(err) == 429 {
		return true
	}

	// 检查错误消息是否包含"429 Too Many Requests"
	errMsg := err.Error()
	return strings.Contains(errMsg, "429") || strings.Contains(errMsg, "Too Many Requests")
}

// generateXmlResult 生成xml结果
func generateXmlResult(
	ctx context.Context,
	model *llm.ChatModelWithName,
	request *xmlgenerater.XmlGeneraterRequest,
	agentsdkimpl agentsdk.AgentSDK,
) (*schema.Message, error) {
	uid := login.GetUid(ctx)
	if uid == "" {
		return nil, errors.New("you need to login first")
	}
	message, err := agentsdkimpl.Ask(ctx, &agentsdk.ConversationOptions{
		ConversationId: request.ConversationId,
		Uid:            uid,
		UserMessage: &agentserver.UserMessage{
			Content: &userinput.UserInput{
				Prompt: &userinput.UserPrompt{
					Items: [][]*userinput.PromptItem{
						{
							{
								Type: userinput.PromptItemType_Text,
								Text: &userinput.TextPromptItem{
									Text: request.JsonString,
								},
							},
						},
					},
				},
				GlobalInfos: []*userinput.GlobalInfo{
					{
						Type: userinput.GlobalInfoType_Image,
						Image: &userinput.Image{
							Base64: request.ImgBase64[0],
						},
					},
				},
			},
		},
	}, &agentsdk.ChatOptions{
		ChatModel:    model,
		SystemPrompt: request.SystemPrompt,
		TokenCalculator: tokencalculator.NewUserCalculator(ctx, &tokencalculator.UserConfig{
			Uid:    uid,
			ConvId: request.ConversationId,
			Scene:  expense.Scene_XmlGenerator,
		}),
	})
	if err != nil {
		return nil, err
	}

	return message, nil
}

// cleanDescription 清理描述内容，移除换行符、制表符和多余空格
func cleanDescription(description string) string {
	cleanedContent := strings.ReplaceAll(description, "\n", " ")
	cleanedContent = strings.ReplaceAll(cleanedContent, "\r", " ")
	cleanedContent = strings.ReplaceAll(cleanedContent, "\t", " ")
	cleanedContent = strings.Join(strings.Fields(cleanedContent), " ")
	return cleanedContent
}

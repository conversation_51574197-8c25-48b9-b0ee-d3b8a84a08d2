package compressormodal

import (
	"context"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/d2c/repo/xmlcompressor"
	"code.byted.org/ies/codin/common/llm"
	"github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/schema"
)

// 多模态分析器
type impl struct{}

// NewImpl 创建多模态分析器
func NewImpl() xmlcompressor.XmlCompressor {
	return &impl{}
}

// CompressXml 根据 json+img 生成xml
func (i *impl) CompressXml(
	ctx context.Context,
	xmlString string,
	systemPrompt string,
) ([]string, error) {
	log.V2.Info().With(ctx).Str("开始压缩xml").Emit()

	modal, err := llm.CreateClaude(ctx)

	if err != nil {
		log.V2.Error().With(ctx).Str("创建模型失败").Error(err).Emit()
		return nil, err
	}

	// 默认对第一张图片进行拆分
	message, err := generateXmlResult(ctx, modal, xmlString, systemPrompt)
	if err != nil {
		log.V2.Error().With(ctx).Str("压缩xml失败").Error(err).Emit()
		return nil, err
	}
	log.V2.Info().With(ctx).Str("压缩xml成功").Str("xml", cleanDescription(message.Content)).Emit()
	return []string{cleanDescription(message.Content)}, nil
}

// generateXmlResult 生成xml结果
func generateXmlResult(ctx context.Context, model model.ToolCallingChatModel, xmlString string, systemPrompt string) (*schema.Message, error) {
	var messages []*schema.Message
	if systemPrompt != "" {
		messages = append(messages, &schema.Message{
			Role:    "system",
			Content: systemPrompt,
		})
	}

	parts := []schema.ChatMessagePart{
		{
			Type: schema.ChatMessagePartTypeText,
			Text: xmlString,
		},
	}

	messages = append(messages, &schema.Message{
		Role:         "user",
		MultiContent: parts,
	})

	message, err := model.Generate(ctx, messages)
	if err != nil {
		return nil, err
	}

	return message, nil
}

// cleanDescription 清理描述内容，移除换行符、制表符和多余空格
func cleanDescription(description string) string {
	cleanedContent := strings.ReplaceAll(description, "\n", " ")
	cleanedContent = strings.ReplaceAll(cleanedContent, "\r", " ")
	cleanedContent = strings.ReplaceAll(cleanedContent, "\t", " ")
	cleanedContent = strings.Join(strings.Fields(cleanedContent), " ")
	return cleanedContent
}

package systemprompt

import (
	"context"
)

type Reader interface {
	GetD2CSystemPrompt(ctx context.Context) (string, error)
	GetD2CSystemPromptForSplit(ctx context.Context) (string, error)
	GetD2CSystemPromptForImgSummary(ctx context.Context) (string, error)
	GetD2CSystemPromptForImgSummaryNL(ctx context.Context) (string, error)
	GetD2CSystemPromptForComponent(ctx context.Context) (string, error)
	GetD2CSystemPromptForXmlGenerater(ctx context.Context) (string, error)
	GetD2CSystemPromptForXmlCompressor(ctx context.Context) (string, error)
	GetD2CSystemPromptForJsonCompressor(ctx context.Context) (string, error)
	GetD2CSystemPromptForCxml2Aixml(ctx context.Context) (string, error)
	GetD2CSystemPromptForDslCodegen(ctx context.Context) (string, error)
}

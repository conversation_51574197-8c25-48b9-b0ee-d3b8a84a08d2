package store

import (
	"context"

	"code.byted.org/bytedoc/mongo-go-driver/bson"
	"code.byted.org/bytedoc/mongo-go-driver/mongo"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c"
)

type ComponentSummaryStore struct {
	Client *BytedocClient
}

// NewComponentSummaryStore 创建新的组件概要存储实例
func NewComponentSummaryStore() *ComponentSummaryStore {
	return &ComponentSummaryStore{
		Client: &mongoInstance,
	}
}

// ComponentSummary 组件概要信息
type ComponentSummary struct {
	Key         string `bson:"key"`
	Business    string `bson:"business"`
	Description string `bson:"description"`
	UseDemo     string `bson:"useDemo"`
}

func (s *ComponentSummaryStore) UpsertOne(ctx context.Context, collection string, componentKey string, business string, description string, useDemo string) (*mongo.UpdateResult, error) {
	filter := bson.D{
		{Key: "key", Value: componentKey},
		{Key: "business", Value: business},
	}
	update := bson.D{{Key: "$set", Value: bson.D{
		{Key: "description", Value: description},
		{Key: "useDemo", Value: useDemo},
	}}}

	result, err := s.Client.UpdateOne(ctx, collection, filter, update)
	if err != nil {
		return nil, rpcerr.Wrap(err, d2c.D2cErrorCode_MongoWriteFailed, "MongoDB写入失败")
	}
	return result, nil
}

func (s *ComponentSummaryStore) FindOne(ctx context.Context, collection string, componentKey string, business string) (*d2c.ComponentSummary, error) {
	filter := bson.D{
		{Key: "key", Value: componentKey},
		{Key: "business", Value: business},
	}
	doc, err := s.Client.FindOne(ctx, collection, filter)
	if err != nil {
		return nil, rpcerr.Wrap(err, d2c.D2cErrorCode_MongoReadFailed, "MongoDB读取失败")
	}

	summary := &ComponentSummary{
		Key:      componentKey,
		Business: business,
	}

	for _, elem := range doc {
		switch elem.Key {
		case "description":
			if val, ok := elem.Value.(string); ok {
				summary.Description = val
			}
		case "useDemo":
			if val, ok := elem.Value.(string); ok {
				summary.UseDemo = val
			}
		}
	}

	return &d2c.ComponentSummary{
		Key:         summary.Key,
		Business:    summary.Business,
		Description: summary.Description,
		UseDemo:     summary.UseDemo,
	}, nil
}

func (s *ComponentSummaryStore) FindByBusiness(ctx context.Context, collection string, business string) ([]*d2c.ComponentSummary, error) {
	filter := bson.D{{Key: "business", Value: business}}
	cursor, err := s.Client.Find(ctx, collection, filter)
	if err != nil {
		return nil, rpcerr.Wrap(err, d2c.D2cErrorCode_MongoReadFailed, "MongoDB查询失败")
	}

	var summaries []*d2c.ComponentSummary
	for cursor.Next(ctx) {
		var doc bson.D
		if err := cursor.Decode(&doc); err != nil {
			return nil, rpcerr.Wrap(err, d2c.D2cErrorCode_DataDeserializationFailed, "数据解码失败")
		}

		summary := &ComponentSummary{Business: business}
		for _, elem := range doc {
			switch elem.Key {
			case "key":
				if val, ok := elem.Value.(string); ok {
					summary.Key = val
				}
			case "description":
				if val, ok := elem.Value.(string); ok {
					summary.Description = val
				}
			case "useDemo":
				if val, ok := elem.Value.(string); ok {
					summary.UseDemo = val
				}
			}
		}
		summaries = append(summaries, &d2c.ComponentSummary{
			Key:         summary.Key,
			Business:    summary.Business,
			Description: summary.Description,
			UseDemo:     summary.UseDemo,
		})
	}

	return summaries, nil
}

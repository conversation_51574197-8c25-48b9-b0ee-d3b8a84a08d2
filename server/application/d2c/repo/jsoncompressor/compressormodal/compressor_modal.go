package compressormodal

import (
	"context"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/d2c/repo/jsoncompressor"
	"code.byted.org/ies/codin/common/llm"
	"github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/schema"
)

// 多模态分析器
type impl struct{}

// NewImpl 创建多模态分析器
func NewImpl() jsoncompressor.JsonCompressor {
	return &impl{}
}

// CompressJson 压缩json
func (i *impl) CompressJson(
	ctx context.Context,
	imgBase64 string,
	originalJson string,
	systemPrompt string,
	modalType string,
) ([]string, error) {
	log.V2.Info().With(ctx).Str("开始压缩json").Emit()

	var modal model.ToolCallingChatModel
	var err error
	if modalType == "seed16_thinking" {
		modal, err = llm.CreateSeed16_Thinking(ctx)
	} else if modalType == "doubao_vision_pro" {
		modal, err = llm.CreateDoubaoThinkingPro(ctx)
	} else if modalType == "gemini_2_5_pro" {
		modal, err = llm.CreateGemini2_5_Pro(ctx)
	} else if modalType == "claude_3_5_sonnet" {
		modal, err = llm.CreateClaude(ctx)
	} else if modalType == "deepseek_r1" {
		modal, err = llm.CreateDeepseekR1(ctx)
	} else if modalType == "gpt_o3" {
		modal, err = llm.CreateGPT_O3(ctx)
	} else {
		modal, err = llm.CreateGemini2_5_Pro(ctx)
	}
	if err != nil {
		log.V2.Error().With(ctx).Str("创建模型失败").Error(err).Emit()
		return nil, err
	}

	message, err := generateJsonResult(ctx, modal, imgBase64, originalJson, systemPrompt)
	if err != nil {
		log.V2.Error().With(ctx).Str("压缩json失败").Error(err).Emit()
		return nil, err
	}
	log.V2.Info().With(ctx).Str("压缩xml成功").Str("xml", cleanDescription(message.Content)).Emit()
	return []string{cleanDescription(message.Content)}, nil
}

// generateJsonResult 生成json结果
func generateJsonResult(ctx context.Context, model model.ToolCallingChatModel, imgBase64 string, jsonString string, systemPrompt string) (*schema.Message, error) {
	var messages []*schema.Message
	if systemPrompt != "" {
		messages = append(messages, &schema.Message{
			Role:    "system",
			Content: systemPrompt,
		})
	}

	parts := []schema.ChatMessagePart{
		{
			Type: schema.ChatMessagePartTypeImageURL,
			ImageURL: &schema.ChatMessageImageURL{
				URL: imgBase64,
			},
		},
		{
			Type: schema.ChatMessagePartTypeText,
			Text: jsonString,
		},
	}

	messages = append(messages, &schema.Message{
		Role:         "user",
		MultiContent: parts,
	})

	message, err := model.Generate(ctx, messages)
	if err != nil {
		return nil, err
	}

	return message, nil
}

// cleanDescription 清理描述内容，移除换行符、制表符和多余空格
func cleanDescription(description string) string {
	cleanedContent := strings.ReplaceAll(description, "\n", " ")
	cleanedContent = strings.ReplaceAll(cleanedContent, "\r", " ")
	cleanedContent = strings.ReplaceAll(cleanedContent, "\t", " ")
	cleanedContent = strings.Join(strings.Fields(cleanedContent), " ")
	return cleanedContent
}

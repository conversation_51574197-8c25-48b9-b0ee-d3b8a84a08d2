package main

import (
	"code.byted.org/ies/codin/application/agw/biz/decorate"
	"code.byted.org/ies/codin/application/agw/biz/handler"
	"code.byted.org/middleware/hertz/pkg/app/server"
)

// customizeRegister register customize routers.
func customizeRegister(r *server.Hertz) {
	// 通用agent会话相关
	r.OPTIONS("/api/conversation/create", handler.ConversationCreate)
	r.POST("/api/conversation/create", decorate.LoginCheck(handler.ConversationCreate))

	r.OPTIONS("/api/conversation/cancel", handler.ConversationCancel)
	r.POST("/api/conversation/cancel", decorate.LoginCheck(handler.ConversationCancel))

	r.OPTIONS("/api/conversation/history", handler.ConversationHistory)
	r.GET("/api/conversation/history", decorate.LoginCheck(handler.ConversationHistory))

	r.OPTIONS("/api/conversation/stream-range", handler.ConversationStreamRange)
	r.GET("/api/conversation/stream-range", decorate.LoginCheck(handler.ConversationStreamRange))

	r.OPTIONS("/api/conversation/list", handler.ConversationList)
	r.GET("/api/conversation/list", decorate.LoginCheck(handler.ConversationList))

	r.OPTIONS("/api/conversation/calc-token", handler.ConversationToken)
	r.POST("/api/conversation/calc-token", decorate.LoginCheck(handler.ConversationToken))

	r.OPTIONS("/api/conversation/code", handler.AgentCode)
	r.POST("/api/conversation/code", decorate.LoginCheck(handler.AgentCode))

	r.OPTIONS("/api/conversation/retry-code", handler.AgentRetryCode)
	r.POST("/api/conversation/retry-code", decorate.LoginCheck(handler.AgentRetryCode))

	r.OPTIONS("/api/conversation/plan", handler.AgentPlan)
	r.POST("/api/conversation/plan", decorate.LoginCheck(handler.AgentPlan))

	r.OPTIONS("/api/conversation/retry-plan", handler.AgentRetryPlan)
	r.POST("/api/conversation/retry-plan", decorate.LoginCheck(handler.AgentRetryPlan))

	r.OPTIONS("/api/conversation/understanding", handler.AgentUnderstanding)
	r.POST("/api/conversation/understanding", decorate.LoginCheck(handler.AgentUnderstanding))

	r.OPTIONS("/api/conversation/retry-understanding", handler.AgentRetryUnderstanding)
	r.POST("/api/conversation/retry-understanding", decorate.LoginCheck(handler.AgentRetryUnderstanding))

	r.OPTIONS("/api/conversation/d2c", handler.AgentD2c)
	r.POST("/api/conversation/d2c", decorate.LoginCheck(handler.AgentD2c))

	r.OPTIONS("/api/conversation/retry-d2c", handler.AgentRetryD2c)
	r.POST("/api/conversation/retry-d2c", decorate.LoginCheck(handler.AgentRetryD2c))

	r.OPTIONS("/api/conversation/benchmark", handler.AgentBenchmark)
	r.POST("/api/conversation/benchmark", decorate.LoginCheck(handler.AgentBenchmark))

	r.OPTIONS("/api/conversation/retry-benchmark", handler.AgentRetryBenchmark)
	r.POST("/api/conversation/retry-benchmark", decorate.LoginCheck(handler.AgentRetryBenchmark))

	r.OPTIONS("/api/conversation/benchmark/get_report_list", handler.GetBenchmarkReportList)
	r.POST("/api/conversation/benchmark/get_report_list", decorate.LoginCheck(handler.GetBenchmarkReportList))

	r.OPTIONS("/api/conversation/benchmark/create_task", handler.CreateBenchmarkTask)
	r.POST("/api/conversation/benchmark/create_task", decorate.LoginCheck(handler.CreateBenchmarkTask))

	r.OPTIONS("/api/conversation/benchmark/batch_create_task", handler.BatchCreateBenchmarkTask)
	r.POST("/api/conversation/benchmark/batch_create_task", decorate.LoginCheck(handler.BatchCreateBenchmarkTask))

	r.OPTIONS("/api/conversation/codereview", handler.AgentCodeReview)
	r.POST("/api/conversation/codereview", decorate.LoginCheck(handler.AgentCodeReview))

	r.OPTIONS("/api/conversation/codesearch", handler.AgentCodeSearch)
	r.POST("/api/conversation/codesearch", decorate.LoginCheck(handler.AgentCodeSearch))

	r.OPTIONS("/api/conversation/retry-codesearch", handler.AgentRetryCodeSearch)
	r.POST("/api/conversation/retry-codesearch", decorate.LoginCheck(handler.AgentRetryCodeSearch))

	r.OPTIONS("/api/codesearch/get_usage", handler.GetUsage)
	r.POST("/api/codesearch/get_usage", decorate.LoginCheck(handler.GetUsage))

	// 导出文档路由
	r.OPTIONS("/api/prd/exportdoc", handler.PrdExportDoc)
	r.POST("/api/prd/exportdoc", handler.PrdExportDoc)

	// merklet tree diff
	r.OPTIONS("/api/merklet/diff", handler.MerkleDiff)
	r.POST("/api/merklet/diff", decorate.LoginCheck(handler.MerkleDiff))

	// 上传chunk merkle tree，做索引更新
	r.OPTIONS("/api/merklet/upload", handler.MerkleTreeUpload)
	r.POST("/api/merklet/upload", decorate.LoginCheck(handler.MerkleTreeUpload))

	// 查询索引构建状态
	r.OPTIONS("/api/merklet/query-build", handler.MerkleQueryBuild)
	r.POST("/api/merklet/query-build", decorate.LoginCheck(handler.MerkleQueryBuild))

	// 构建 master索引专用
	r.OPTIONS("/api/merklet/build-index", handler.BuildIndex)
	r.POST("/api/merklet/build-index", decorate.LoginCheck(handler.BuildIndex))

	// 获取 summary 远端和本地的差异，拿到需要更新的文件
	r.OPTIONS("/api/summary/get-update-files", handler.GetSummaryUpdateFiles)
	r.POST("/api/summary/get-update-files", decorate.LoginCheck(handler.GetSummaryUpdateFiles))

	// 查询 summary 构建状态
	r.OPTIONS("/api/summary/query-build", handler.SummaryBuildQuery)
	r.POST("/api/summary/query-build", decorate.LoginCheck(handler.SummaryBuildQuery))

	// 触发 summary 更新
	r.OPTIONS("/api/summary/update", handler.UpdateSummary)
	r.POST("/api/summary/update", decorate.LoginCheck(handler.UpdateSummary))

	// 查询构建数据
	r.OPTIONS("/api/merklet/query-build-repo", handler.QueryBuildRepo)
	r.POST("/api/merklet/query-build-repo", decorate.LoginCheck(handler.QueryBuildRepo))

	// 调用 dtc openapi，获取设计稿生成的 jsx 代码
	r.OPTIONS("/api/d2c/original-jsx", handler.GetD2cOriginalJsx)
	r.POST("/api/d2c/original-jsx", handler.GetD2cOriginalJsx)

	// 调用figma openapi，获取设计稿原始 json
	r.OPTIONS("/api/d2c/original-json", handler.GetD2cOriginalJson)
	r.POST("/api/d2c/original-json", handler.GetD2cOriginalJson)

	// 调用figma openapi，获取压缩精简后的figma json
	r.OPTIONS("/api/d2c/compress-json", handler.GetFigmaCompressedJSON)
	r.POST("/api/d2c/compress-json", handler.GetFigmaCompressedJSON)

	// 调用figma openapi，获取设计稿原始图片
	r.OPTIONS("/api/d2c/original-img", handler.GetD2cOriginalImg)
	r.POST("/api/d2c/original-img", handler.GetD2cOriginalImg)

	// 调用 openapi，获取设计稿相关信息，含jsx、json、img
	r.OPTIONS("/api/d2c/original-info", handler.GetD2cOriginalInfo)
	r.POST("/api/d2c/original-info", handler.GetD2cOriginalInfo)

	// 调用 openapi，获取设计稿拆分模块
	r.OPTIONS("/api/d2c/split-module", handler.GetSplitModule)
	r.POST("/api/d2c/split-module", handler.GetSplitModule)

	// 调用 openapi，获取/更新设计稿组件文档
	r.OPTIONS("/api/d2c/component-doc", handler.ComponentDocHandler)
	r.POST("/api/d2c/component-doc", handler.ComponentDocHandler)

	// 调用 openapi，获取/更新设计稿组件文档
	r.OPTIONS("/api/d2c/component-summary", handler.ComponentSummaryHandler)
	r.POST("/api/d2c/component-summary", handler.ComponentSummaryHandler)

	// 调用 openapi，生成xml
	r.OPTIONS("/api/d2c/generate-xml", handler.GenerateXml)
	r.POST("/api/d2c/generate-xml", handler.GenerateXml)

	r.OPTIONS("/api/user/getjwt", handler.UserGetJwt)
	r.POST("/api/user/getjwt", handler.UserGetJwt)

	r.OPTIONS("/api/user/auth_config", handler.UserGetAuthClientConfig)
	r.POST("/api/user/auth_config", handler.UserGetAuthClientConfig)

	r.OPTIONS("/api/user/get_info", handler.UserGetInfo)
	// TODO(niurouwan): 这个接口可以优化下，不走logincheck，只用调一次下游
	r.POST("/api/user/get_info", decorate.LoginCheck(handler.UserGetInfo))

	// 异步任务提交
	r.OPTIONS("/api/async/task/submit", handler.AsyncTaskSubmit)
	r.POST("/api/async/task/submit", decorate.LoginCheck(handler.AsyncTaskSubmit))

	// 异步任务查询
	r.OPTIONS("/api/async/task/list", handler.AsyncTaskList)
	r.POST("/api/async/task/list", handler.AsyncTaskList)

	// 异步任务结果上报
	r.OPTIONS("/api/async/task/report", handler.AsyncTaskReport)
	r.POST("/api/async/task/report", handler.AsyncTaskReport)

	r.OPTIONS("/api/expense/token-usage", handler.GetTokenUsage)
	r.GET("/api/expense/token-usage", decorate.LoginCheck(handler.GetTokenUsage))
}

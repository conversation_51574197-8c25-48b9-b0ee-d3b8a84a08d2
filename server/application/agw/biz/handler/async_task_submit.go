package handler

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/application/agw/biz/utils"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/overpass/capcut_devops_asset/kitex_gen/asset"
	"code.byted.org/overpass/capcut_devops_asset/rpc/capcut_devops_asset"
	"code.byted.org/overpass/common/option/calloption"
	"context"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"net/http"
	"time"
)

type AsyncTaskSubmitData struct {
	TaskID string `json:"task_id"`
}

func AsyncTaskSubmit(ctx context.Context, c *app.RequestContext) {
	// 处理预检请求
	if string(c.Request.Method()) == "OPTIONS" {
		setCORSHeaders(c)
		c.Status(http.StatusNoContent)
		return
	}

	// 为所有请求设置跨域头
	setCORSHeaders(c)

	var req *asset.AsyncTaskSubmitRequest
	if err := jsoniter.Unmarshal(c.Request.Body(), &req); err != nil {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    http.StatusBadRequest,
			"message": err.Error(),
		})
		return
	}

	if req.CaseInfo == nil || len(req.CaseInfo.Prompt) == 0 || len(req.RepoInfos) == 0 {
		logs.CtxError(ctx, "[AsyncTaskSubmit] illegal req: %s", c.Request.Body())
		utils.SetResponse(ctx, c, rpcerr.RetUnknown, fmt.Sprintf("[AsyncTaskSubmit] illegal req: %s", c.Request.Body()))
		return
	}

	req.JWT = c.Request.Header.Get("x-jwt-token")
	rpcRsp, rpcErr := capcut_devops_asset.RawCall.AsyncTaskSubmit(ctx, req, calloption.WithRPCTimeout(10*time.Second))
	if rpcErr != nil {
		logs.CtxError(ctx, "[AsyncTaskSubmit] rpcErr: %v", rpcErr)
		utils.SetResponse(ctx, c, rpcerr.RetUnknown, rpcErr.Error())
		return
	}

	utils.SetResponse(ctx, c, rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.GetStatusMessage(), AsyncTaskSubmitData{TaskID: rpcRsp.TaskID})

	logs.CtxInfo(ctx, "AsyncTaskSubmit req: %s", c.Request.Body())
	logs.CtxInfo(ctx, "AsyncTaskSubmit rsp: %s", c.Response.Body())
}

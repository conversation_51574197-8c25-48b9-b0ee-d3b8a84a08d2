package handler

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/application/agw/biz/utils"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/overpass/capcut_devops_asset/kitex_gen/asset"
	"code.byted.org/overpass/capcut_devops_asset/rpc/capcut_devops_asset"
	"code.byted.org/overpass/common/option/calloption"
	"context"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"net/http"
	"time"
)

func AsyncTaskList(ctx context.Context, c *app.RequestContext) {
	// 处理预检请求
	if string(c.Request.Method()) == "OPTIONS" {
		setCORSHeaders(c)
		c.Status(http.StatusNoContent)
		return
	}

	// 为所有请求设置跨域头
	setCORSHeaders(c)

	var req *asset.AsyncTaskListRequest
	if err := jsoniter.Unmarshal(c.Request.Body(), &req); err != nil {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    http.StatusBadRequest,
			"message": err.Error(),
		})
		return
	}

	if len(req.TaskID) == 0 {
		logs.CtxError(ctx, "[AsyncTaskList] illegal req: %s", c.Request.Body())
		utils.SetResponse(ctx, c, rpcerr.RetUnknown, fmt.Sprintf("[AsyncTaskList] illegal req: %s", c.Request.Body()))
		return
	}

	rpcRsp, rpcErr := capcut_devops_asset.RawCall.AsyncTaskList(ctx, req, calloption.WithRPCTimeout(10*time.Second))
	if rpcErr != nil {
		logs.CtxError(ctx, "[AsyncTaskList] rpcErr: %v", rpcErr)
		utils.SetResponse(ctx, c, rpcerr.RetUnknown, rpcErr.Error())
		return
	}

	utils.SetResponse(ctx, c, rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.GetStatusMessage(), rpcRsp.AsyncTaskInfos)

	logs.CtxInfo(ctx, "AsyncTaskList req: %s", c.Request.Body())
	logs.CtxInfo(ctx, "AsyncTaskList rsp: %s", c.Response.Body())
}

package handler

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/application/agw/biz/utils"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/overpass/capcut_devops_asset/kitex_gen/asset"
	"code.byted.org/overpass/capcut_devops_asset/rpc/capcut_devops_asset"
	"code.byted.org/overpass/common/option/calloption"
	"context"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"net/http"
	"time"
)

func AsyncTaskReport(ctx context.Context, c *app.RequestContext) {
	// 处理预检请求
	if string(c.Request.Method()) == "OPTIONS" {
		setCORSHeaders(c)
		c.Status(http.StatusNoContent)
		return
	}

	// 为所有请求设置跨域头
	setCORSHeaders(c)

	var req *asset.AsyncTaskReportRequest
	if err := jsoniter.Unmarshal(c.Request.Body(), &req); err != nil {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    http.StatusBadRequest,
			"message": err.Error(),
		})
		return
	}

	if len(req.TaskID) == 0 || (len(req.ConvID) == 0 && req.TaskStatus <= 0) {
		logs.CtxError(ctx, "[AsyncTaskReport] illegal req: %s", c.Request.Body())
		utils.SetResponse(ctx, c, rpcerr.RetUnknown, fmt.Sprintf("[AsyncTaskReport] illegal req: %s", c.Request.Body()))
		return
	}

	rpcRsp, rpcErr := capcut_devops_asset.RawCall.AsyncTaskReport(ctx, req, calloption.WithRPCTimeout(10*time.Second))
	if rpcErr != nil {
		logs.CtxError(ctx, "[AsyncTaskReport] rpcErr: %v", rpcErr)
		utils.SetResponse(ctx, c, rpcerr.RetUnknown, rpcErr.Error())
		return
	}

	utils.SetResponse(ctx, c, rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.GetStatusMessage())

	logs.CtxInfo(ctx, "AsyncTaskReport req: %s", c.Request.Body())
	logs.CtxInfo(ctx, "AsyncTaskReport rsp: %s", c.Response.Body())
}

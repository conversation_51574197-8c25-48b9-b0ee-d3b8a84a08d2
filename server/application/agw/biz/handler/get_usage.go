package handler

import (
	"context"
	"net/http"
	"time"

	"code.byted.org/ies/codin/application/agw/biz/utils"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/kite/kitex/client"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch/codesearchservice"
	"github.com/cloudwego/hertz/pkg/app"
)

// GetUsage
// @router /api/codesearch/get_usage [POST]
func GetUsage(ctx context.Context, c *app.RequestContext) {
	req := &codesearch.GetUsageRequest{}
	if err := c.BindJSON(req); err != nil {
		utils.SetResponse(ctx, c, http.StatusBadRequest, err.Error())
		return
	}

	client := codesearchservice.MustNewClient("capcut.devops.codesearch", client.WithRPCTimeout(5*time.Minute))
	resp, err := client.GetUsage(ctx, req)
	if err != nil {
		utils.SetResponse(ctx, c, rpcerr.RetUnknown, err.Error())
		return
	}

	utils.SetResponse(ctx, c, resp.BaseResp.StatusCode, resp.BaseResp.GetStatusMessage(), resp)
}

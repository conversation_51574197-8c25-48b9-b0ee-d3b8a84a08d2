package handler

import (
	"context"
	"io"
	"net/http"
	"time"

	"code.byted.org/kite/kitex/client"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c/d2cservice"
)

// ComponentSummaryRequest 组件文档请求体结构
type ComponentSummaryRequest struct {
	Key         string `json:"key"`
	UseDemo     string `json:"use_demo"`
	Business    string `json:"business"`
	Description string `json:"description"`
	Operation   string `json:"operation"`
}

func ComponentSummaryHandler(ctx context.Context, c *app.RequestContext) {
	// 处理预检请求
	if string(c.Request.Method()) == "OPTIONS" {
		setCORSHeaders(c)
		c.Status(http.StatusNoContent)
		return
	}

	// 为所有请求设置跨域头
	setCORSHeaders(c)

	// 从请求体中解析参数
	var req ComponentSummaryRequest
	if err := c.BindJSON(&req); err != nil {
		// 改进错误处理，提供更清晰的错误信息
		errorMsg := err.Error()
		if err == io.EOF {
			errorMsg = "请求体不能为空，请提供有效的 JSON 数据"
		}
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    http.StatusBadRequest,
			"message": "请求体解析失败: " + errorMsg,
		})
		return
	}

	// 验证必需参数
	if req.Key == "" || req.Operation == "" {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    http.StatusBadRequest,
			"message": "key and operation are required",
		})
		return
	}

	if req.Operation == "set" {
		if req.UseDemo == "" || req.Business == "" || req.Description == "" {
			c.JSON(http.StatusBadRequest, map[string]interface{}{
				"code":    http.StatusBadRequest,
				"message": "document is required for set operation",
			})
			return
		}
		client := d2cservice.MustNewClient(
			"capcut.devops.d2c",
			client.WithConnectTimeout(2*time.Minute),
			client.WithRPCTimeout(2*time.Minute),
		)
		d2cReq := &d2c.SetComponentSummaryRequest{
			Key:         req.Key,
			Business:    req.Business,
			Description: req.Description,
			UseDemo:     req.UseDemo,
		}
		resp, err := client.SetComponentSummary(ctx, d2cReq)
		if err != nil {
			c.JSON(http.StatusInternalServerError, map[string]interface{}{
				"code":    http.StatusInternalServerError,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusOK, map[string]interface{}{
			"code":    http.StatusOK,
			"message": "success",
			"data":    resp,
		})
		return
	} else if req.Operation == "get" {
		client := d2cservice.MustNewClient(
			"capcut.devops.d2c",
			client.WithConnectTimeout(2*time.Minute),
			client.WithRPCTimeout(2*time.Minute),
		)
		d2cReq := &d2c.GetComponentDocRequest{
			Key: req.Key,
		}
		resp, err := client.GetComponentDoc(ctx, d2cReq)
		if err != nil {
			c.JSON(http.StatusInternalServerError, map[string]interface{}{
				"code":    http.StatusInternalServerError,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusOK, map[string]interface{}{
			"code":    http.StatusOK,
			"message": "success",
			"data":    resp,
		})
		return
	} else {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    http.StatusBadRequest,
			"message": "invalid operation, must be 'set' or 'get'",
		})
	}
}

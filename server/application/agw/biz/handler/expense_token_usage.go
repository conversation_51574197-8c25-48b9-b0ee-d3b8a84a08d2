package handler

import (
	"context"
	"net/http"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/application/agw/biz/utils"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/overpass/capcut_devops_agw/kitex_gen/agw"
	"code.byted.org/overpass/capcut_devops_expense/kitex_gen/expense"
	"code.byted.org/overpass/capcut_devops_expense/kitex_gen/expense/expenseservice"
)

func GetTokenUsage(ctx context.Context, c *app.RequestContext) {
	// 处理预检请求
	if string(c.Request.Method()) == "OPTIONS" {
		setCORSHeaders(c)
		c.Status(http.StatusNoContent)
		return
	}

	// 为所有请求设置跨域头
	setCORSHeaders(c)

	// 从查询参数获取cid
	req := &agw.GetTokenUsageRequest{}
	if err := c.BindQuery(req); err != nil {
		logs.CtxWarn(ctx, "get token usage failed: %v", err)
	}

	client := expenseservice.MustNewClient(
		"capcut.devops.expense",
	)

	resp, err := client.GetTokenUsage(ctx, &expense.GetTokenUsageRequest{
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	})
	if err != nil {
		utils.SetResponse(ctx, c, rpcerr.RetUnknown, err.Error())
		return
	}
	utils.SetResponse(ctx, c, resp.BaseResp.StatusCode, resp.BaseResp.GetStatusMessage(), resp)
}

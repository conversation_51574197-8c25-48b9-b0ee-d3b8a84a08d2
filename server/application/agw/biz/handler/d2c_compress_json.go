package handler

import (
	"context"
	"net/http"
	"time"

	"code.byted.org/kite/kitex/client"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c/d2cservice"
)

func GetFigmaCompressedJSON(ctx context.Context, c *app.RequestContext) {
	// 处理预检请求
	if string(c.Request.Method()) == "OPTIONS" {
		setCORSHeaders(c)
		c.Status(http.StatusNoContent)
		return
	}

	// 为所有请求设置跨域头
	setCORSHeaders(c)

	url := c.Query("url")
	figmaToken := c.Query("figma_token")

	if url == "" || figmaToken == "" {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    http.StatusBadRequest,
			"message": "url and figma_token are required",
		})
		return
	}

	client := d2cservice.MustNewClient(
		"capcut.devops.d2c",
		client.WithConnectTimeout(2*time.Minute),
		client.WithRPCTimeout(2*time.Minute),
	)

	req := &d2c.D2cAgentCommonRequest{
		Url:        url,
		FigmaToken: figmaToken,
	}

	resp, err := client.GetFigmaCompressedJSON(ctx, req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    http.StatusInternalServerError,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, map[string]interface{}{
		"code":    http.StatusOK,
		"message": "success",
		"data":    resp,
	})
}

package handler

import (
	"context"
	"net/http"
	"time"

	"code.byted.org/gopkg/metainfo"
	"code.byted.org/ies/codin/application/agw/biz/utils"
	"code.byted.org/kite/kitex/client"
	"code.byted.org/kite/kitutil"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c"
	"code.byted.org/overpass/capcut_devops_d2c/kitex_gen/d2c/d2cservice"
	"github.com/google/uuid"
)

func GenerateXml(ctx context.Context, c *app.RequestContext) {
	// 处理预检请求
	if string(c.Request.Method()) == "OPTIONS" {
		setCORSHeaders(c)
		c.Status(http.StatusNoContent)
		return
	}

	// 为所有请求设置跨域头
	setCORSHeaders(c)

	url := c.Query("url")
	figmaToken := c.Query("figma_token")
	modalType := c.Query("modal_type")
	needCompress := c.Query("need_compress")
	needComponent := c.Query("need_component")
	business := c.Query("business")

	logID, _ := kitutil.GetCtxLogID(ctx)

	if url == "" && figmaToken == "" {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    http.StatusBadRequest,
			"message": "url or figma_token is required",
		})
		return
	}

	client := d2cservice.MustNewClient(
		"capcut.devops.d2c",
		client.WithConnectTimeout(10*time.Minute),
		client.WithRPCTimeout(10*time.Minute),
	)

	uid, email, err := utils.GetUidByJwt(ctx, c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"code":    http.StatusUnauthorized,
			"message": err.Error(),
			"log_id":  logID,
		})
		return
	}

	cid := uuid.New().String()
	ctx = metainfo.WithPersistentValues(ctx, "uid", uid, "email", email)

	req := &d2c.GenerateXmlRequest{
		Url:            url,
		FigmaToken:     figmaToken,
		ModalType:      modalType,
		ConversationId: cid,
		NeedCompress:   &needCompress,
		NeedComponent:  &needComponent,
		Business:       &business,
	}

	resp, err := client.GenerateXml(ctx, req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    http.StatusInternalServerError,
			"message": err.Error(),
			"log_id":  logID,
		})
		return
	}

	if resp.Xml != "" {
		c.Data(http.StatusOK, "text/markdown;charset=utf-8", []byte(resp.Xml+"\n\n生成序列号："+logID))
	} else {
		c.JSON(http.StatusOK, map[string]interface{}{
			"code":    http.StatusOK,
			"message": "success",
			"data":    resp,
			"log_id":  logID,
		})
	}
}

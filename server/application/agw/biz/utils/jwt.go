package utils

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"

	"code.byted.org/gopkg/logs"
	"code.byted.org/middleware/hertz/byted"
	"code.byted.org/middleware/hertz/pkg/app"
	appClient "code.byted.org/middleware/hertz/pkg/app/client"
	"code.byted.org/overpass/capcut_devops_asset/kitex_gen/asset"
	"code.byted.org/overpass/capcut_devops_asset/kitex_gen/asset/assetservice"
	"github.com/cloudwego/hertz/pkg/network/standard"
	"github.com/cloudwego/hertz/pkg/protocol"
)

type UserInfo struct {
	Username string `json:"username"`
}

const GetUserInfoUrl = "https://cloud.bytedance.net/auth/api/v1/userinfo"

var httpCli *byted.Client

func getHttpClient() *byted.Client {
	if httpCli == nil {
		c, err := byted.NewClient(byted.WithAppClientOptions(appClient.WithDialer(standard.NewDialer())))
		if err != nil {
			panic(err)
		}
		httpCli = c
	}

	return httpCli
}

func getUsernameByJwt(ctx context.Context, c *app.RequestContext) (string, error) {
	jwtToken := c.Request.Header.Get("x-jwt-token")
	if jwtToken == "" {
		return "", errors.New("not jwt")
	}

	httpClient := getHttpClient()
	req := &protocol.Request{}
	res := &protocol.Response{}
	req.SetMethod(string(http.MethodGet))
	req.SetRequestURI(GetUserInfoUrl)
	req.SetHeader("x-jwt-token", jwtToken)
	req.SetHeader("Content-Type", "application/json; charset=utf-8")

	err := httpClient.Do(ctx, req, res)
	if err != nil {
		logs.CtxWarn(ctx, "get user info failed: %v", err)
		return "", err
	}

	var userInfo UserInfo
	err = json.Unmarshal(res.BodyBytes(), &userInfo)
	if err != nil {
		logs.CtxWarn(ctx, "get user info failed: %v", err)
		return "", err
	}
	return userInfo.Username, nil
}

func GetUidByJwt(ctx context.Context, c *app.RequestContext) (string, string, error) {
	username, err := getUsernameByJwt(ctx, c)
	if err != nil {
		return "", "", err
	}

	email := username + "@bytedance.com"

	assetClient := assetservice.MustNewClient("capcut.devops.asset")
	resp, err := assetClient.GetUserIdByEmail(ctx, &asset.GetUserIdByEmailRequest{
		Emails: []string{email},
	})

	if err != nil {
		logs.CtxError(ctx, "get user id by email failed: %v", err)
		return "", email, err
	}

	if resp.UserIdList == nil || len(resp.UserIdList) == 0 || resp.UserIdList[0] == nil {
		logs.CtxError(ctx, "get user id by email failed: %v", err)
		return "", email, err
	}

	return resp.UserIdList[0].UserID, email, nil
}

package merklet

type DiffType string

const (
	DiffTypeModify DiffType = "modify"
	DiffTypeAdd    DiffType = "add"
	DiffTypeDelete DiffType = "delete"
)

// DiffResult represents the result of a diff operation.
type DiffResult struct {
	Path string   `json:"path"` // File/directory path
	Type DiffType `json:"type"` // Diff type: "modify", "add", "delete"
	Hash string   `json:"hash"` // File hash
}

type diffInfo struct {
	Type DiffType
	Hash string
}

// DiffTrees compares two Merkle trees and returns the differences.
// The logic is based on the TypeScript implementation in /Users/<USER>/dev/codin/web/packages/merkle/src/diff-merkle-tree.ts
// node1 is the server tree, node2 is the local tree
func DiffTrees(node1, node2 *TreeNode) []DiffResult {
	if node1 == nil && node2 == nil {
		return []DiffResult{}
	}

	changeset := make(map[string]diffInfo)

	if node1 == nil {
		// All nodes in node2 are additions
		addNodesAs(node2, "add", changeset)
		return toDiffResultSlice(changeset)
	}
	if node2 == nil {
		// All nodes in node1 are deletions
		addNodesAs(node1, "delete", changeset)
		return toDiffResultSlice(changeset)
	}

	changeset = diffNodes(node1, node2)
	return toDiffResultSlice(changeset)
}

func toDiffResultSlice(changeset map[string]diffInfo) []DiffResult {
	var differences []DiffResult
	for path, diff := range changeset {
		differences = append(differences, DiffResult{
			Path: path,
			Type: diff.Type,
			Hash: diff.Hash,
		})
	}
	return differences
}

// addNodesAs recursively adds file nodes to the changeset with a given type.
func addNodesAs(node *TreeNode, diffType DiffType, changeset map[string]diffInfo) {
	if node == nil {
		return
	}
	if node.Type == "file" {
		changeset[node.Path] = diffInfo{Type: diffType, Hash: node.Hash}
		return
	}

	if node.Type == "dir" {
		for _, child := range node.Children {
			addNodesAs(child, diffType, changeset)
		}
	}
}

// diffNodes is the recursive diff function.
func diffNodes(node1, node2 *TreeNode) map[string]diffInfo {
	changeset := make(map[string]diffInfo)

	// Type mismatch logic from TS: add everything from node2, delete nothing from node1.
	if node1.Type != node2.Type {
		addNodesAs(node1, "delete", changeset)
		addNodesAs(node2, "add", changeset)
		return changeset
	}

	// Both are files
	if node1.Type == "file" {
		if node1.Hash != node2.Hash {
			// node1 是server tree, 这里的 modify client后续流程要先删后增 得返回server node 的 hash
			changeset[node1.Path] = diffInfo{Type: "modify", Hash: node1.Hash}
		}
		return changeset
	}

	// Both are directories
	if node1.Type == "dir" {
		children1Map := make(map[string]*TreeNode)
		for _, child := range node1.Children {
			children1Map[child.Path] = child
		}

		children2Map := make(map[string]*TreeNode)
		for _, child := range node2.Children {
			children2Map[child.Path] = child
		}

		// Added nodes
		for path, child2 := range children2Map {
			if _, exists := children1Map[path]; !exists {
				addNodesAs(child2, "add", changeset)
			}
		}

		// Deleted nodes
		for path, child1 := range children1Map {
			if _, exists := children2Map[path]; !exists {
				addNodesAs(child1, "delete", changeset)
			}
		}

		// Common children
		for path, child1 := range children1Map {
			if child2, exists := children2Map[path]; exists {
				childChangeset := diffNodes(child1, child2)
				for p, diff := range childChangeset {
					changeset[p] = diff
				}
			}
		}
	}

	return changeset
}

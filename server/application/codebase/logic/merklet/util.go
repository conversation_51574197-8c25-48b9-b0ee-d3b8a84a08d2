package merklet

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"
	"strings"

	"code.byted.org/ies/codin/common/utils"
)

// LoadTreeFromJSON 保持不变
func DeserializeTree(uncompressedData []byte) (*TreeNode, error) {
	// 3. 反序列化JSON
	var treeNode TreeNode
	if err := json.Unmarshal(uncompressedData, &treeNode); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %v", err)
	}

	return &treeNode, nil
}

func SerializeTree(node *TreeNode) ([]byte, error) {
	jsonData, err := json.MarshalIndent(node, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal JSON: %v", err)
	}

	return jsonData, nil
}

// GzipCompress 压缩数据
func GzipCompress(data []byte) ([]byte, error) {
	var buf bytes.Buffer
	gz := gzip.NewWriter(&buf)

	_, err := gz.Write(data)
	if err != nil {
		return nil, err
	}

	// 必须调用 Close()，否则数据可能不完整
	if err := gz.Close(); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

func GzipDecompress(data []byte) ([]byte, error) {
	r, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, err
	}
	defer r.Close()
	return io.ReadAll(r)
}

// JSONEncode 结构体转 JSON
func JSONEncode(v interface{}) ([]byte, error) {
	return json.Marshal(v)
}

func JSONDecode(data []byte, v interface{}) error {
	return json.Unmarshal(data, v)
}

/**
 * GetNodeByPath 递归查找指定路径的节点
 * 通过公共前缀优化查找性能
 * @param node - 当前节点
 * @param path - 要查找的路径
 * @return *TreeNode - 找到的节点，如果没找到则返回nil
 */
func GetNodeByPath(node *TreeNode, path string) *TreeNode {
	normalizedPath := utils.NormalizePath(path)
	normalizedNodePath := utils.NormalizePath(node.Path)

	// 精确匹配
	if normalizedNodePath == normalizedPath {
		return node
	}

	// 如果目标路径不是当前节点路径的前缀，直接返回nil
	// 这样可以避免不必要的递归查找
	hasPrefix := strings.HasPrefix(normalizedPath, normalizedNodePath+"/") || normalizedNodePath == "."
	if !hasPrefix && normalizedPath != normalizedNodePath {
		return nil
	}

	// 递归查找子节点
	for _, child := range node.Children {
		if result := GetNodeByPath(child, path); result != nil {
			return result
		}
	}

	return nil
}

package merklet

import (
	"sort"
	"testing"

	"github.com/stretchr/testify/assert"
)

func cloneTree(node *TreeNode) *TreeNode {
	if node == nil {
		return nil
	}
	newNode := &TreeNode{
		Path: node.Path,
		Hash: node.Hash,
		Type: node.Type,
	}
	if node.Children != nil {
		newChildren := make([]*TreeNode, len(node.Children))
		for i, child := range node.Children {
			newChildren[i] = cloneTree(child)
		}
		newNode.Children = newChildren
	}
	return newNode
}

func TestDiffTrees(t *testing.T) {
	baseTree := &TreeNode{
		Hash: "11937558242112389485",
		Path: "",
		Type: "dir",
		Children: []*TreeNode{
			{Hash: "14697187964081066686", Path: "package.json", Type: "file"},
			{
				Hash: "11494775664087591111",
				Path: "src",
				Type: "dir",
				Children: []*TreeNode{
					{
						Hash: "5306262115156382817",
						Path: "src/components",
						Type: "dir",
						Children: []*TreeNode{
							{
								Hash: "10749171634431881025",
								Path: "src/components/badge",
								Type: "dir",
								Children: []*TreeNode{
									{
										Hash: "14939248226370953672",
										Path: "src/components/badge/styles",
										Type: "dir",
										Children: []*TreeNode{
											{
												Hash: "5831522704997746323",
												Path: "src/components/badge/styles/index.scss",
												Type: "file",
											},
										},
									},
								},
							},
							{Hash: "205388093071038717", Path: "src/components/button.ts", Type: "file"},
						},
					},
					{Hash: "11038473166345079591", Path: "src/index.ts", Type: "file"},
				},
			},
		},
	}

	t.Run("same tree", func(t *testing.T) {
		tree2 := cloneTree(baseTree)
		diff := DiffTrees(baseTree, tree2)
		assert.Empty(t, diff)
	})

	t.Run("add file", func(t *testing.T) {
		tree2 := cloneTree(baseTree)
		// A bit complex to add a node, let's simulate it by adding to children list
		tree2.Children[1].Children[0].Children = append(tree2.Children[1].Children[0].Children, &TreeNode{
			Hash: "12345678", Path: "src/components/avatar.ts", Type: "file",
		})
		diff := DiffTrees(baseTree, tree2)
		expected := []DiffResult{{Path: "src/components/avatar.ts", Type: "add", Hash: "12345678"}}
		assert.ElementsMatch(t, expected, diff)
	})

	t.Run("add empty dir", func(t *testing.T) {
		tree2 := cloneTree(baseTree)
		tree2.Children[1].Children[0].Children = append(tree2.Children[1].Children[0].Children, &TreeNode{
			Hash: "12345678", Path: "src/components/avatar", Type: "dir", Children: []*TreeNode{},
		})
		diff := DiffTrees(baseTree, tree2)
		assert.Empty(t, diff)
	})

	t.Run("modify file", func(t *testing.T) {
		tree2 := cloneTree(baseTree)
		tree2.Children[0].Hash = "12345678"
		tree2.Children[1].Children[0].Children[0].Children[0].Children[0].Hash = "123456789"
		diff := DiffTrees(baseTree, tree2)
		expected := []DiffResult{
			{Path: "package.json", Type: "modify", Hash: "14697187964081066686"},
			{Path: "src/components/badge/styles/index.scss", Type: "modify", Hash: "5831522704997746323"},
		}
		sort.Slice(diff, func(i, j int) bool { return diff[i].Path < diff[j].Path })
		sort.Slice(expected, func(i, j int) bool { return expected[i].Path < expected[j].Path })
		assert.Equal(t, expected, diff)
	})

	t.Run("delete file", func(t *testing.T) {
		tree2 := cloneTree(baseTree)
		deletedNode := tree2.Children[1].Children[0].Children[0].Children[0].Children[0]
		tree2.Children[1].Children[0].Children[0].Children[0].Children = []*TreeNode{}
		diff := DiffTrees(baseTree, tree2)
		expected := []DiffResult{
			{Path: "src/components/badge/styles/index.scss", Type: "delete", Hash: deletedNode.Hash},
		}
		assert.ElementsMatch(t, expected, diff)
	})

	t.Run("should detect deleted files", func(t *testing.T) {
		tree1 := &TreeNode{
			Path: "/", Hash: "dir-hash-1", Type: "dir",
			Children: []*TreeNode{
				{Path: "/file1.txt", Hash: "11111", Type: "file"},
				{Path: "/file2.txt", Hash: "22222", Type: "file"},
			},
		}
		tree2 := &TreeNode{
			Path: "/", Hash: "dir-hash-2", Type: "dir",
			Children: []*TreeNode{
				{Path: "/file1.txt", Hash: "11111", Type: "file"},
			},
		}
		diff := DiffTrees(tree1, tree2)
		expected := []DiffResult{{Path: "/file2.txt", Type: "delete", Hash: "22222"}}
		assert.ElementsMatch(t, expected, diff)
	})

	t.Run("should detect modified files", func(t *testing.T) {
		tree1 := &TreeNode{
			Path: "/", Hash: "dir-hash-1", Type: "dir",
			Children: []*TreeNode{{Path: "/file1.txt", Hash: "11111-old", Type: "file"}},
		}
		tree2 := &TreeNode{
			Path: "/", Hash: "dir-hash-2", Type: "dir",
			Children: []*TreeNode{{Path: "/file1.txt", Hash: "11111-new", Type: "file"}},
		}
		diff := DiffTrees(tree1, tree2)
		expected := []DiffResult{{Path: "/file1.txt", Type: "modify", Hash: "11111-new"}}
		assert.ElementsMatch(t, expected, diff)
	})

	t.Run("should handle complex changes", func(t *testing.T) {
		tree1 := &TreeNode{
			Path: "/", Hash: "root-hash-1", Type: "dir",
			Children: []*TreeNode{
				{Path: "/file1.txt", Hash: "11111-old", Type: "file"},
				{Path: "/file2.txt", Hash: "22222", Type: "file"},
				{
					Path: "/subdir", Hash: "subdir-hash-1", Type: "dir",
					Children: []*TreeNode{{Path: "/subdir/file3.txt", Hash: "file3-hash", Type: "file"}},
				},
			},
		}
		tree2 := &TreeNode{
			Path: "/", Hash: "root-hash-2", Type: "dir",
			Children: []*TreeNode{
				{Path: "/file1.txt", Hash: "11111-new", Type: "file"}, // modified
				{
					Path: "/subdir", Hash: "subdir-hash-2", Type: "dir",
					Children: []*TreeNode{
						{Path: "/subdir/file3.txt", Hash: "file3-hash", Type: "file"},
						{Path: "/subdir/file4.txt", Hash: "file4-hash", Type: "file"}, // added
					},
				},
				{Path: "/file5.txt", Hash: "file5-hash", Type: "file"}, // added
			},
		}

		diff := DiffTrees(tree1, tree2)
		expected := []DiffResult{
			{Path: "/file1.txt", Type: "modify", Hash: "11111-new"},
			{Path: "/file2.txt", Type: "delete", Hash: "22222"},
			{Path: "/subdir/file4.txt", Type: "add", Hash: "file4-hash"},
			{Path: "/file5.txt", Type: "add", Hash: "file5-hash"},
		}
		assert.ElementsMatch(t, expected, diff)
	})

	t.Run("should handle file to directory change", func(t *testing.T) {
		tree1 := &TreeNode{
			Path: "/item", Hash: "item-hash-old", Type: "file",
		}
		tree2 := &TreeNode{
			Path: "/item", Hash: "item-hash-new", Type: "dir",
			Children: []*TreeNode{{Path: "/item/file.txt", Hash: "file-hash", Type: "file"}},
		}

		diff := DiffTrees(tree1, tree2)
		expected := []DiffResult{
			{Path: "/item/file.txt", Type: "add", Hash: "file-hash"},
		}
		assert.ElementsMatch(t, expected, diff)
	})

	t.Run("should handle directory to file change", func(t *testing.T) {
		tree1 := &TreeNode{
			Path: "/item", Hash: "item-hash-old", Type: "dir",
			Children: []*TreeNode{{Path: "/item/file.txt", Hash: "file-hash", Type: "file"}},
		}
		tree2 := &TreeNode{
			Path: "/item", Hash: "item-hash-new", Type: "file",
		}

		diff := DiffTrees(tree1, tree2)
		expected := []DiffResult{
			{Path: "/item", Type: "add", Hash: "item-hash-new"},
		}
		assert.ElementsMatch(t, expected, diff)
	})
}

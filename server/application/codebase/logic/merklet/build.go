package merklet

import (
	"bytes"
	"code.byted.org/gopkg/logs/v2"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"sort"
	"strconv"
	"strings"

	"code.byted.org/ies/codin/application/codebase/repo/workspace"
	"github.com/cespare/xxhash/v2"
)

func getMaxConcurrency() int {
	if runtime.NumCPU() > 16 {
		return 32
	}
	return runtime.NumCPU() * 2
}

// 定义排除的目录集合
var excludedDirs = map[string]bool{
	".git":         true,
	"node_modules": true,
	"vendor":       true,
}

func processPath(path string, stats *TreeStats) (*TreeNode, error) {
	info, err := os.Stat(path)
	if err != nil {
		return nil, err
	}

	node := &TreeNode{
		Path: path,
	}

	if info.IsDir() {
		node.Type = "dir"
		stats.TotalDirs++
		entries, err := os.ReadDir(path)
		if err != nil {
			return nil, err
		}

		var filteredEntries []os.DirEntry
		for _, entry := range entries {
			name := entry.Name()
			if excludedDirs[name] {
				continue
			}
			filteredEntries = append(filteredEntries, entry)
		}

		sort.Slice(filteredEntries, func(i, j int) bool {
			return filteredEntries[i].Name() < filteredEntries[j].Name()
		})

		// 串行处理子目录
		children := make([]*TreeNode, len(filteredEntries))
		childHashes := make([]FileHashInfo, 0, len(filteredEntries))
		for i, entry := range filteredEntries {
			childPath := filepath.Join(path, entry.Name())
			childNode, err := processPath(childPath, stats)
			if err != nil {
				return nil, err
			}
			children[i] = childNode
			childHashes = append(childHashes, FileHashInfo{
				Hash:     childNode.Hash,
				Filename: entry.Name(),
			})
		}

		node.Children = children
		node.Hash = hashStrings(childHashes)
	} else {
		node.Type = "file"
		node.Children = nil
		stats.TotalFiles++
		hash, err := fastHashFile(path)
		if err != nil {
			return nil, err
		}
		node.Hash = hash
	}

	return node, nil
}

// 和前端共用相同的merkle tree版本
func BuildMerkleTree(ctx context.Context, repoName, branch string, pathList []string) (*map[string]interface{}, error) {
	treeName := fmt.Sprintf("tree_%s.json", repoName)
	treeName = strings.ReplaceAll(treeName, "/", "_")
	outputFile, err := os.Create(treeName)
	if err != nil {
		logs.CtxError(ctx, "create tree.json error: %v", err)
		return nil, err
	}
	defer outputFile.Close()

	npxPath, err := exec.LookPath("npx")
	if err != nil {
		logs.CtxError(ctx, "npx 未找到: %v", err)
	}
	logs.CtxInfo(ctx, "npx 路径:", npxPath)
	jsonPathStr, err := json.Marshal(pathList)
	if err != nil {
		// 如果解析报错就所有路径都要用
		jsonPathStr = []byte(`[""]`)
	}
	tempWS := workspace.Workspace.GetTempWorkspace(repoName, branch)
	if tempWS == nil {
		logs.CtxError(ctx, "get temp workspace nil")
		return nil, err
	}
	cmd := exec.Command("npx", "@byted-image/merkle", "-r", tempWS.Path, "-w", string(jsonPathStr))
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	logs.CtxInfo(ctx, "执行命令: %s\n", cmd.String())
	err = cmd.Run()

	if err != nil {
		logs.CtxError(ctx, "merkle命令执行失败:", err)
		return nil, err
	}

	// // 4. 写入文件
	if err = os.WriteFile(treeName, stdout.Bytes(), 0644); err != nil {
		logs.CtxError(ctx, "写入文件失败:", err)
		return nil, err
	}
	logs.CtxInfo(ctx, "tree.json 已生成，字节数:", stdout.Len())

	treeFileData, err := os.ReadFile(treeName)
	if err != nil {
		logs.CtxError(ctx, "read tree.json error: %v", err)
		return nil, err
	}

	var treeFileMap map[string]interface{}
	if err = json.Unmarshal(treeFileData, &treeFileMap); err != nil {
		logs.CtxError(ctx, "unmarshal tree.json error: %v", err)
		return nil, err
	}

	err = os.Remove(treeName)
	if err != nil {
		logs.CtxError(ctx, "remove tree.json error: %v", err)
	}

	return &treeFileMap, nil
}

func fastHashFile(filePath string) (string, error) {
	// 使用单个hasher减少内存分配
	hasher := xxhash.New()
	// 哈希文件内容
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 使用64KB缓冲区
	buf := make([]byte, 64<<10)
	if _, err := io.CopyBuffer(hasher, file, buf); err != nil {
		return "", err
	}

	// 获取64位哈希值并转为10进制字符串
	hashValue := hasher.Sum64()
	return strconv.FormatUint(hashValue, 10), nil
}

func hashStrings(strs []FileHashInfo) string {
	hasher := xxhash.New()
	sort.Slice(strs, func(i, j int) bool {
		return strs[i].Filename < strs[j].Filename
	})
	for _, s := range strs {
		hasher.Write([]byte(s.Hash))
	}
	// 获取64位哈希值并转为10进制字符串
	hashValue := hasher.Sum64()
	return strconv.FormatUint(hashValue, 10)
}

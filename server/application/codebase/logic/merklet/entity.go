package merklet

import (
	"time"
)

type TreeNode struct {
	Path     string      `json:"path"`
	Type     string      `json:"type"`
	Hash     string      `json:"hash"`
	Children []*TreeNode `json:"children,omitempty"` // 添加 omitempty 标签
}

type TreeStats struct {
	TotalFiles int           `json:"totalFiles"`
	TotalDirs  int           `json:"totalDirs"`
	Duration   time.Duration `json:"duration"`
}

type FileHashInfo struct {
	Hash     string `json:"hash"`
	Filename string `json:"filename"`
}

package summary

import (
	"code.byted.org/ies/codin/common/contexts"
	"context"
	"fmt"
	"strings"
	"sync/atomic"
	"time"

	"code.byted.org/gopkg/jsonx"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codebase/entity"
	"code.byted.org/ies/codin/application/codebase/logic"
	"code.byted.org/ies/codin/application/codebase/logic/summary/builder"
	agentTools "code.byted.org/ies/codin/application/codebase/logic/summary/tools/agent"
	"code.byted.org/ies/codin/application/codebase/logic/summary/update"
	"code.byted.org/ies/codin/application/codebase/logic/summary/update/debug"
	summaryUpdate "code.byted.org/ies/codin/application/codebase/logic/summary/update/summary_update"
	planEntity "code.byted.org/ies/codin/application/codebase/repo/plan/entity"
	plan "code.byted.org/ies/codin/application/codebase/repo/plan/manager"
	"code.byted.org/ies/codin/application/codebase/repo/virtualfile"
	commonCodebaseEntity "code.byted.org/ies/codin/common/semantic/codebase/entity"
	summaryManager "code.byted.org/ies/codin/common/semantic/summary"

	virtualFile "code.byted.org/ies/codin/application/codebase/repo/virtualfile"

	codebaseEntity "code.byted.org/ies/codin/application/codebase/entity"
	"code.byted.org/ies/codin/application/codebase/logic/merklet"
	"code.byted.org/ies/codin/application/codebase/repo/workspace"
	"code.byted.org/ies/codin/common/group"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/ies/codin/common/semantic/tos/config"
	"code.byted.org/ies/codin/common/tos"
	commonUtils "code.byted.org/ies/codin/common/utils"
	"code.byted.org/overpass/capcut_devops_agw/kitex_gen/codesearch"
	"code.byted.org/overpass/capcut_devops_codebase/kitex_gen/codebase"
)

// TaskProcessParams 任务处理参数
type TaskProcessParams struct {
	Depth       int
	LogKey      string
	LogDir      string
	TempManager *logic.TempManager
	Uid         string
}

type CreateSummaryRequest struct {
	// 仓库名
	RepoName string
	// 仓库地址
	RepoURL string
	// 仓库分支
	Branch string
	// 用户id`
	Uid string
}

// DefaultTaskProcessor 默认任务处理器实现
type DefaultTaskProcessor struct{}

// ProcessTask 处理单个任务的核心流程：analyze -> plan -> explore
func (p *DefaultTaskProcessor) ProcessTask(ctx context.Context, task *planEntity.TaskRecord, plan *planEntity.PlanRecord, params *TaskProcessParams) error {
	log.V2.Info().With(ctx).Str("=== ProcessTask 开始 ===").Str("task_id", task.TaskID).Str("target_path", task.TargetPath).Emit()

	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		log.V2.Info().With(ctx).Str("=== ProcessTask 结束 ===").Str("task_id", task.TaskID).Str("总耗时", duration.String()).Emit()
	}()

	planRecordManager := params.TempManager.PlanRecordManager
	params.LogKey = "action"

	// 开始任务
	log.V2.Info().With(ctx).Str("开始任务").Str("task_id", task.TaskID).Emit()
	err := planRecordManager.StartTask(ctx, plan.PlanID, task.TaskID)
	if err != nil {
		log.V2.Error().With(ctx).Str("开始任务失败").Error(err).Str("task_id", task.TaskID).Emit()
		return err
	}

	defer func() {
		// 完成任务
		if err = planRecordManager.CompleteTask(ctx, plan.PlanID, task.TaskID); err != nil {
			logs.CtxInfo(ctx, "CompleteTask failed, err=%v, planID=%v, taskID=%v", err, plan.PlanID, task.TaskID)
		}
		log.V2.Info().With(ctx).Str("任务完成").Str("task_id", task.TaskID).Emit()
	}()

	// 1. 模块分析阶段
	isComplexModule, err := p.analyzeModule(ctx, task, params)
	if err != nil {
		log.V2.Error().With(ctx).Str("模块分析失败").Error(err).Str("task_id", task.TaskID).Emit()
		return err
	}
	log.V2.Info().With(ctx).Str("模块分析阶段完成").Str("task_id", task.TaskID).Str("isComplexModule", fmt.Sprintf("%t", isComplexModule)).Emit()

	// 2. 如果是复杂模块，进行规划
	if isComplexModule {
		if err = p.planModule(ctx, task, params); err != nil {
			log.V2.Error().With(ctx).Str("模块规划失败").Error(err).Str("task_id", task.TaskID).Emit()
			return err
		}
		log.V2.Info().With(ctx).Str("模块规划阶段完成").Str("task_id", task.TaskID).Emit()
	}

	// 3. 探索模块
	if err = p.exploreModule(ctx, task, isComplexModule, params); err != nil {
		log.V2.Error().With(ctx).Str("模块探索失败").Error(err).Str("task_id", task.TaskID).Emit()
		return err
	}
	log.V2.Info().With(ctx).Str("模块探索阶段完成").Str("task_id", task.TaskID).Emit()

	return nil
}

// analyzeModule 分析模块复杂度
func (p *DefaultTaskProcessor) analyzeModule(ctx context.Context, task *planEntity.TaskRecord, params *TaskProcessParams) (bool, error) {
	var (
		path     = task.TargetPath
		repoName = contexts.GetRepoContext(ctx).RepoName
	)

	if repoName == "" {
		return false, fmt.Errorf("RepoContext err, task=%v", jsonx.ToString(task))
	}

	logLine := "onToolStart: \n module analyzed  \n"
	commonUtils.WriteLogToDir(params.LogKey, params.LogDir, logLine)
	commonUtils.WriteLogToDir(params.LogKey, params.LogDir, "path="+path)
	commonUtils.WriteLogToDir(params.LogKey, params.LogDir, "repoName="+repoName)
	log.V2.Info().With(ctx).Str("开始模块分析").Str("task_id", task.TaskID).Str("path", task.TargetPath).Emit()

	moduleAnalyzedResult, err := agentTools.ExecuteModuleAnalyze(ctx, params.TempManager, path, repoName, params.LogDir, params.Uid)
	logLine = "onToolEnd: \n module analyzed  "
	commonUtils.WriteLogToDir(params.LogKey, params.LogDir, logLine)
	commonUtils.WriteJsonToDir(params.LogKey, params.LogDir, moduleAnalyzedResult)
	log.V2.Info().With(ctx).Str("模块分析完成").Str("task_id", task.TaskID).Emit()
	// 默认都为复杂模块，可能存在llm没有严格返回json的情况
	isComplexModule := true
	if moduleAnalyzedResult != nil {
		isComplexModule = moduleAnalyzedResult.IsComplexModule
	}
	log.V2.Info().With(ctx).Str("模块复杂度判定").Str("task_id=", task.TaskID).Str("IsComplexModule=", fmt.Sprintf("%t", isComplexModule)).Emit()
	return isComplexModule, err
}

// planModule 规划复杂模块
func (p *DefaultTaskProcessor) planModule(ctx context.Context, task *planEntity.TaskRecord, params *TaskProcessParams) error {
	repoCtx := contexts.GetRepoContext(ctx)

	if repoCtx == nil {
		return fmt.Errorf("planModule, RepoContext is nil, task=%v", jsonx.ToString(task))
	}

	planningParams := &agentTools.SummaryPlanningParams{
		Path:     task.TargetPath,
		Depth:    params.Depth,
		RepoName: repoCtx.RepoName,
		Uid:      repoCtx.Uid,
	}

	logLine := "onToolStart: \n summary planning \n  "
	commonUtils.WriteLogToDir(params.LogKey, params.LogDir, logLine)
	commonUtils.WriteJsonToDir(params.LogKey, params.LogDir, planningParams)
	log.V2.Info().With(ctx).Str("开始摘要规划").Str("task_id", task.TaskID).Emit()

	summaryStr, err := agentTools.ExecuteSummaryPlanning(ctx, params.TempManager, planningParams, params.LogDir)
	logLine = "onToolEnd: \n summary planning  "
	commonUtils.WriteLogToDir(params.LogKey, params.LogDir, logLine)
	commonUtils.WriteLogToDir(params.LogKey, params.LogDir, summaryStr)
	log.V2.Info().With(ctx).Str("摘要规划完成").Str("task_id", task.TaskID).Emit()

	return err
}

// exploreModule 探索模块
func (p *DefaultTaskProcessor) exploreModule(ctx context.Context, task *planEntity.TaskRecord, isComplexModule bool, params *TaskProcessParams) error {
	repoCtx := contexts.GetRepoContext(ctx)
	if repoCtx == nil {
		return fmt.Errorf("exploreModule, RepoContext is nil, task=%v", jsonx.ToString(task))
	}

	exploreParams := &agentTools.SummaryExploreParams{
		Path:            task.TargetPath,
		Depth:           params.Depth,
		IsComplexModule: isComplexModule,
		Task:            task.Definition,
		RepoName:        repoCtx.RepoName,
		Uid:             repoCtx.Uid,
	}

	logLine := "onToolStart: \n summary explore \n  "
	commonUtils.WriteJsonToDir(params.LogKey, params.LogDir, exploreParams)
	log.V2.Info().With(ctx).Str("开始摘要探索").Str("task_id", task.TaskID).Emit()

	summaryExploreResult, err := agentTools.ExecuteSummaryExplore(ctx, params.TempManager, exploreParams, params.LogDir)
	logLine = "onToolEnd: \n summary explore  "
	commonUtils.WriteLogToDir(params.LogKey, params.LogDir, logLine)
	commonUtils.WriteLogToDir(params.LogKey, params.LogDir, summaryExploreResult)
	log.V2.Info().With(ctx).Str("摘要探索完成").Str("task_id", task.TaskID).Emit()

	return err
}

// PlanExecutor 计划执行器，负责管理计划的执行流程
type PlanExecutor struct {
	taskProcessor *DefaultTaskProcessor
	maxWorkers    int
}

// NewPlanExecutor 创建计划执行器
func NewPlanExecutor(taskProcessor *DefaultTaskProcessor, maxWorkers int) *PlanExecutor {
	return &PlanExecutor{
		taskProcessor: taskProcessor,
		maxWorkers:    maxWorkers,
	}
}

// ExecutePlans 执行所有计划的公共流程
func (e *PlanExecutor) ExecutePlans(ctx context.Context, params *TaskProcessParams) error {
	log.V2.Info().With(ctx).Str("=== ExecutePlans 开始 ===").Str("maxWorkers", fmt.Sprintf("%d", e.maxWorkers)).Emit()

	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		log.V2.Info().With(ctx).Str("=== ExecutePlans 结束 ===").Str("总耗时", duration.String()).Emit()
	}()

	// 初始化通道
	log.V2.Info().With(ctx).Str("初始化通道").Emit()
	taskChan := make(chan *TaskWithPlan, e.maxWorkers)
	resultChan := make(chan struct{}, e.maxWorkers)
	stopChan := make(chan struct{})

	// 启动worker协程
	log.V2.Info().With(ctx).Str("启动worker协程").Emit()
	e.startWorkers(ctx, taskChan, resultChan, stopChan, params)

	// 执行计划调度
	log.V2.Info().With(ctx).Str("开始执行计划调度").Emit()
	err := e.schedulePlans(ctx, params.TempManager.PlanRecordManager, taskChan, resultChan)

	// 停止所有worker
	log.V2.Info().With(ctx).Str("发送停止信号给所有worker").Emit()
	close(stopChan)

	// 等待worker完全停止
	stopTimeout := 180 * time.Second
	select {
	case <-time.After(stopTimeout):
		log.V2.Warn().With(ctx).Str("worker停止超时").Str("timeout", stopTimeout.String()).Emit()
	case <-time.After(5 * time.Second):
		log.V2.Info().With(ctx).Str("worker已停止").Emit()
	}

	return err
}

// startWorkers 启动worker协程
func (e *PlanExecutor) startWorkers(ctx context.Context, taskChan chan *TaskWithPlan, resultChan chan struct{}, stopChan chan struct{}, params *TaskProcessParams) {
	log.V2.Info().With(ctx).Str("--------------------启动worker协程-----------------").Str("workerCount", fmt.Sprintf("%d", e.maxWorkers)).Emit()

	handlers := make([]func() error, 0)
	for i := 0; i < e.maxWorkers; i++ {
		workerID := i
		handlers = append(handlers, func() error {
			log.V2.Info().With(ctx).Str("worker启动").Str("workerID", fmt.Sprintf("%d", workerID)).Emit()

			taskCount := 0
			startTime := time.Now()

			for {
				select {
				case taskWithPlan := <-taskChan:
					taskCount++
					taskStartTime := time.Now()

					log.V2.Debug().With(ctx).Str("worker开始处理任务").Str("workerID", fmt.Sprintf("%d", workerID)).Str("plan_id", taskWithPlan.Plan.PlanID).Str("task_id", taskWithPlan.Task.TaskID).Str("target_path", taskWithPlan.Task.TargetPath).Str("task_count", fmt.Sprintf("%d", taskCount)).Emit()

					err := e.taskProcessor.ProcessTask(ctx, taskWithPlan.Task, taskWithPlan.Plan, params)
					if err != nil {
						log.V2.Error().With(ctx).Str("任务处理失败").Error(err).Str("task_id", taskWithPlan.Task.TaskID).Str("workerID", fmt.Sprintf("%d", workerID)).Emit()
					}

					taskDuration := time.Since(taskStartTime)
					log.V2.Debug().With(ctx).Str("worker任务处理完成").Str("workerID", fmt.Sprintf("%d", workerID)).Str("task_id", taskWithPlan.Task.TaskID).Str("task_duration", taskDuration.String()).Emit()

					resultChan <- struct{}{}

				case <-stopChan:
					totalDuration := time.Since(startTime)
					log.V2.Info().With(ctx).Str("worker收到停止信号").Str("workerID", fmt.Sprintf("%d", workerID)).Str("total_tasks", fmt.Sprintf("%d", taskCount)).Str("total_duration", totalDuration.String()).Emit()
					return nil
				}
			}
		})
	}

	// 使用 group.Go 启动所有 worker
	log.V2.Info().With(ctx).Str("使用group.Go启动所有worker").Emit()
	group.Go(ctx, 0, func(ctx context.Context) {
		log.V2.Info().With(ctx).Str("开始执行worker handlers").Emit()
		err := group.GoAndWait(handlers...)
		if err != nil {
			log.V2.Error().With(ctx).Str("worker执行出错").Error(err).Emit()
		} else {
			log.V2.Info().With(ctx).Str("所有worker执行完成").Emit()
		}
	})
}

// schedulePlans 调度计划执行
func (e *PlanExecutor) schedulePlans(ctx context.Context, planRecordManager *plan.PlanRecordManager, taskChan chan *TaskWithPlan, resultChan chan struct{}) error {
	log.V2.Info().With(ctx).Str("=== schedulePlans 开始 ===").Emit()

	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		log.V2.Info().With(ctx).Str("=== schedulePlans 结束 ===").Str("总耗时", duration.String()).Emit()
	}()

	// 初始化计划队列
	log.V2.Info().With(ctx).Str("初始化计划队列").Emit()
	planQueue, planTaskKeys, completedPlans := e.initializePlanQueue(ctx, planRecordManager)

	log.V2.Info().With(ctx).Str("计划队列初始化完成").Str("plan_count", fmt.Sprintf("%d", len(planQueue))).Emit()

	// 全局任务调度循环
	activeTasks := 0
	loopCount := 0
	lastProgressTime := time.Now()
	lastHealthCheckTime := time.Now()

	for len(planQueue) > 0 || activeTasks > 0 {
		loopCount++
		currentTime := time.Now()

		// 每100次循环或每30秒记录一次进度
		if loopCount%100 == 0 || currentTime.Sub(lastProgressTime) > 30*time.Second {
			log.V2.Info().With(ctx).Str("调度循环进度").Str("loop_count", fmt.Sprintf("%d", loopCount)).Str("plan_queue_size", fmt.Sprintf("%d", len(planQueue))).Str("active_tasks", fmt.Sprintf("%d", activeTasks)).Str("completed_plans", fmt.Sprintf("%d", len(completedPlans))).Emit()
			lastProgressTime = currentTime
		}

		// 每5分钟进行一次健康检查
		if currentTime.Sub(lastHealthCheckTime) > 5*time.Minute {
			monitorSystemHealth(ctx, planQueue, activeTasks, completedPlans)
			lastHealthCheckTime = currentTime
		}

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			log.V2.Error().With(ctx).Str("上下文被取消").Error(ctx.Err()).Emit()
			return ctx.Err()
		default:
		}

		// 动态检查新计划
		e.checkNewPlans(ctx, planRecordManager, &planQueue, &planTaskKeys, completedPlans)

		// 分配任务
		activeTasks = e.assignTasks(ctx, planRecordManager, &planQueue, &planTaskKeys, completedPlans, taskChan, activeTasks)

		// 等待任务完成
		select {
		case <-resultChan:
			activeTasks--
			log.V2.Info().With(ctx).Str("收到任务完成信号").Str("active_tasks", fmt.Sprintf("%d", activeTasks)).Emit()
			e.checkCompletedPlans(ctx, planRecordManager, &planQueue, &planTaskKeys, completedPlans)
		case <-time.After(1000 * time.Millisecond):
			// 超时，继续循环
			if loopCount%1000 == 0 {
				log.V2.Debug().With(ctx).Str("调度循环等待超时").Str("loop_count", fmt.Sprintf("%d", loopCount)).Emit()
			}
		}
		const maxLoopCount = 10000
		// 防止无限循环（安全保护）
		if loopCount > maxLoopCount {
			log.V2.Error().With(ctx).Str("调度循环次数过多，可能存在死循环").Str("loop_count", fmt.Sprintf("%d", loopCount)).Emit()
			// 在退出前进行诊断
			diagnoseStuckIssue(ctx, planQueue, activeTasks)
			return fmt.Errorf("调度循环次数过多，可能存在死循环，loop_count: %d", loopCount)
		}

		// 如果长时间没有进展，进行诊断
		if loopCount%maxLoopCount == 0 && activeTasks == 0 && len(planQueue) > 0 {
			log.V2.Warn().With(ctx).Str("检测到可能的卡住情况，进行诊断").Str("loop_count", fmt.Sprintf("%d", loopCount)).Emit()
			diagnoseStuckIssue(ctx, planQueue, activeTasks)
		}
	}

	log.V2.Info().With(ctx).Str("调度循环完成").Str("total_loops", fmt.Sprintf("%d", loopCount)).Emit()
	return nil
}

// initializePlanQueue 初始化计划队列
func (e *PlanExecutor) initializePlanQueue(ctx context.Context, planRecordManager *plan.PlanRecordManager) ([]*planEntity.PlanRecord, map[string][]string, map[string]bool) {
	completedPlans := make(map[string]bool)
	planQueue := make([]*planEntity.PlanRecord, 0)
	planTaskKeys := make(map[string][]string)

	log.V2.Info().With(ctx).Str("开始初始化计划队列").Emit()

	for {
		plan, err := planRecordManager.GetNextPlan(ctx)
		if err != nil {
			log.V2.Info().With(ctx).Str("没有更多计划需要处理").Error(err).Emit()
			break
		}

		log.V2.Info().With(ctx).Str("成功获取到计划").Str("plan_id", plan.PlanID).Str("plan_state", string(plan.State)).Emit()

		if !completedPlans[plan.PlanID] {
			planQueue = append(planQueue, plan)
			var taskKeys []string
			for taskKey := range plan.Tasks {
				taskKeys = append(taskKeys, taskKey)
			}
			planTaskKeys[plan.PlanID] = taskKeys
			planRecordManager.UpdatePlanState(ctx, plan.PlanID, planEntity.PlanPhasePreparing)
			log.V2.Info().With(ctx).Str("发现新计划").Str("plan_id", plan.PlanID).Str("task_count", fmt.Sprintf("%d", len(taskKeys))).Str("total_plans", fmt.Sprintf("%d", len(planQueue))).Emit()
		} else {
			log.V2.Info().With(ctx).Str("计划已完成，跳过").Str("plan_id", plan.PlanID).Emit()
		}
	}

	return planQueue, planTaskKeys, completedPlans
}

// checkNewPlans 检查新计划
func (e *PlanExecutor) checkNewPlans(ctx context.Context, planRecordManager *plan.PlanRecordManager, planQueue *[]*planEntity.PlanRecord, planTaskKeys *map[string][]string, completedPlans map[string]bool) {
	for {
		plan, err := planRecordManager.GetNextPlan(ctx)
		if err != nil {
			break
		}
		if !completedPlans[plan.PlanID] && !isPlanInQueue(plan.PlanID, *planQueue) {
			*planQueue = append(*planQueue, plan)
			var taskKeys []string
			for taskKey := range plan.Tasks {
				taskKeys = append(taskKeys, taskKey)
			}
			(*planTaskKeys)[plan.PlanID] = taskKeys
			planRecordManager.UpdatePlanState(ctx, plan.PlanID, planEntity.PlanPhasePreparing)
			log.V2.Info().With(ctx).Str("动态发现新计划").Str("plan_id", plan.PlanID).Str("task_count", fmt.Sprintf("%d", len(taskKeys))).Str("total_plans", fmt.Sprintf("%d", len(*planQueue))).Emit()
		}
	}
}

// assignTasks 分配任务
func (e *PlanExecutor) assignTasks(ctx context.Context, planRecordManager *plan.PlanRecordManager, planQueue *[]*planEntity.PlanRecord, planTaskKeys *map[string][]string, completedPlans map[string]bool, taskChan chan *TaskWithPlan, activeTasks int) int {
	log.V2.Debug().With(ctx).Str("assignTasks开始").Str("plan_queue_size", fmt.Sprintf("%d", len(*planQueue))).Str("active_tasks", fmt.Sprintf("%d", activeTasks)).Emit()

	assignedCount := 0
	blockedCount := 0

	for i := 0; i < len(*planQueue); i++ {
		plan := (*planQueue)[i]

		if activeTasks >= e.maxWorkers {
			log.V2.Debug().With(ctx).Str("worker已满，无法分配新任务").Str("active_tasks", fmt.Sprintf("%d", activeTasks)).Str("max_concurrent", fmt.Sprintf("%d", e.maxWorkers)).Emit()
			blockedCount++
			break
		}

		taskKeys := (*planTaskKeys)[plan.PlanID]
		if len(taskKeys) == 0 {
			if e.isPlanCompleted(plan) {
				planRecordManager.CompletePlan(ctx, plan.PlanID)
				completedPlans[plan.PlanID] = true
				*planQueue = append((*planQueue)[:i], (*planQueue)[i+1:]...)
				delete(*planTaskKeys, plan.PlanID)
				i--
				log.V2.Info().With(ctx).Str("计划完成").Str("plan_id", plan.PlanID).Emit()
			}
			continue
		}

		taskKey := taskKeys[0]
		task := plan.Tasks[taskKey]
		(*planTaskKeys)[plan.PlanID] = taskKeys[1:]

		if task.TaskStatus == planEntity.TaskStatusCompleted {
			log.V2.Debug().With(ctx).Str("任务已完成，跳过").Str("task_id", task.TaskID).Emit()
			if e.isPlanCompleted(plan) {
				err := planRecordManager.CompletePlan(ctx, plan.PlanID)
				if err != nil {
					log.V2.Error().With(ctx).Str("完成计划失败").Error(err).Str("plan_id", plan.PlanID).Emit()
				} else {
					log.V2.Info().With(ctx).Str("计划已完成").Str("plan_id", plan.PlanID).Emit()
					completedPlans[plan.PlanID] = true
					*planQueue = append((*planQueue)[:i], (*planQueue)[i+1:]...)
					delete(*planTaskKeys, plan.PlanID)
					i--
				}
			}
			continue
		}

		select {
		case taskChan <- &TaskWithPlan{Task: &task, Plan: plan}:
			activeTasks++
			assignedCount++
			if plan.State == planEntity.PlanPhasePreparing {
				planRecordManager.UpdatePlanState(ctx, plan.PlanID, planEntity.PlanPhaseExecuting)
				log.V2.Info().With(ctx).Str("计划状态设置为执行中").Str("plan_id", plan.PlanID).Emit()
			}
			log.V2.Info().With(ctx).Str("任务已发送到worker").Str("plan_id", plan.PlanID).Str("task_id", task.TaskID).Str("active_tasks", fmt.Sprintf("%d", activeTasks)).Emit()
		default:
			log.V2.Debug().With(ctx).Str("任务通道已满，等待").Str("plan_id", plan.PlanID).Str("task_id", task.TaskID).Emit()
			blockedCount++
			break
		}
	}

	if assignedCount > 0 || blockedCount > 0 {
		log.V2.Debug().With(ctx).Str("assignTasks完成").Str("assigned_count", fmt.Sprintf("%d", assignedCount)).Str("blocked_count", fmt.Sprintf("%d", blockedCount)).Str("final_active_tasks", fmt.Sprintf("%d", activeTasks)).Emit()
	}

	return activeTasks
}

// isPlanCompleted 检查计划是否完成
func (e *PlanExecutor) isPlanCompleted(plan *planEntity.PlanRecord) bool {
	allTasksCompleted := true
	for _, planTask := range plan.Tasks {
		if planTask.TaskStatus != planEntity.TaskStatusCompleted {
			allTasksCompleted = false
			break
		}
	}
	return allTasksCompleted
}

// checkCompletedPlans 检查完成的计划
func (e *PlanExecutor) checkCompletedPlans(ctx context.Context, planRecordManager *plan.PlanRecordManager, planQueue *[]*planEntity.PlanRecord, planTaskKeys *map[string][]string, completedPlans map[string]bool) {
	for i := 0; i < len(*planQueue); i++ {
		plan := (*planQueue)[i]
		latestPlan, err := planRecordManager.GetPlan(ctx, plan.PlanID)
		if err != nil {
			log.V2.Error().With(ctx).Str("获取最新计划状态失败").Error(err).Str("plan_id", plan.PlanID).Emit()
			continue
		}

		if e.isPlanCompleted(latestPlan) {
			err := planRecordManager.CompletePlan(ctx, plan.PlanID)
			if err != nil {
				log.V2.Error().With(ctx).Str("完成计划失败").Error(err).Str("plan_id", plan.PlanID).Emit()
			} else {
				log.V2.Info().With(ctx).Str("计划已完成").Str("plan_id", plan.PlanID).Emit()
				completedPlans[plan.PlanID] = true
				*planQueue = append((*planQueue)[:i], (*planQueue)[i+1:]...)
				delete(*planTaskKeys, plan.PlanID)
				i--
			}
		}
	}
}

// initializeTempManager 初始化临时管理器
func InitializeTempManager(ctx context.Context, request *CreateSummaryRequest) *logic.TempManager {
	tempManager := logic.NewTempManager(ctx)
	fileManager, err := InitFileManager(ctx, request)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------init_file_manager_failed").Error(err).Emit()
		return nil
	}
	tempManager.FileManager = fileManager
	planRecordManager := tempManager.PlanRecordManager
	summaryManager := tempManager.SummaryManager
	namespace := strings.ReplaceAll(request.RepoName, "/", "_") + "_" + request.Branch + "_/"

	// 先初始化存储
	// 使用当前工作目录作为基础路径
	basePath := "."
	summaryManager.InitStorage(basePath, namespace)
	planRecordManager.Init(basePath, namespace)

	// 再创建默认计划
	createDefaultPlanIfNeeded(ctx, tempManager.PlanRecordManager, "/")

	return tempManager
}

/**
前端和后端diff
后端拿到summary对应的merkle的版本，然后去拉对应的merkle树做diff

前端触发summary更新
1、先触发diff
提交当前的merkle树给后台，后台拿到merkle树，和当前的summary所对应的merkle树做比对，会得到一波diff
diff去knowledge上找关联的上下游模块，然后返回给前端需要后续提交的文件
- 如果变更文件父级是module,并且还是其他模块的subModule，那么提取出当前module这一层的所有文件
	- 重新分析模块，更新module的summary和keyFeatures
	- 更新当前模块作为其他子模块的subModule的摘要
- 如果变更文件父级是subModule不是module，那么说明当前是叶子节点，提取该subModule里面的所有内容，还需要提取父级module重新做summary，看keyFeatures
	- 分析子模块复杂度，更新subModule，然后确定subModule是否在变更之后足够复杂，需要新增module

2、再触发update
提交最新的merkle树、变更的文件给后台，后台开始更新summary，拿到上行的文件内容做模块的分析
- 变更文件的父级是module,并且还是其他模块的subModule
	- 拿当前所有的文件和原始summary去做摘要，更新summary、keyFeatures
	- 做一版简单摘要更新parentModule的subModule，分析是否要更新父module的keyFeatures
- 变更文件父级是subModule不是module
	- 拿到变更的文件做复杂度识别
	- 如果是复杂模块需要重新exploreModule然后新增moudule
	- 如果是简单模块，那么直接更新当前module的subModule，分析是否要更新父module的keyFeatures

更新完之后，上传新的summary到tos，summary中包括最新的merkle树id
注意这里要存多份索引key
一份是userKnowledgeId对应的summary
一份是knowledgeId和branchName对应的summary
一份是knowledgebaseId对应的summary
*/

/*
前端带着merkle树来后端查询代码变更
1、通过userId、repoName、branchName、path去下载对应的summaryData数据（也就是指定代码版本的summary）
2、然后通过summaryData中的merkleId去下载merkle树，得到当前summary版本所对应的merkle树
3、summary的merkle树和前端传来的merkle树做diff，得到diffs
4、然后通过diff去获取相关的模块路径和叶子子模块路径
- moduleDirPaths中的内容是文件目录，每次要读取目录下一层级的所有文件信息
- leafDirPaths中的内容叶子节点的目录，要递归的读取目录下所有层间的所有文件信息
5、然后把这个信息返回给前端
*/
func GetSummaryUpdateFiles(ctx context.Context, merkleTreeDownloadKey string) (*codebase.GetSummaryUpdateFilesResponse, error) {
	repoCtx := contexts.GetRepoContext(ctx)
	if repoCtx == nil {
		return nil, fmt.Errorf("GetSummaryUpdateFiles, repoCtx is nil, merkleTreeDownloadKey=%v", merkleTreeDownloadKey)
	}

	log.V2.Info().With(ctx).Str("[GetSummaryUpdateFiles] start ").
		Str("uid: ", repoCtx.Uid).
		Str("repoName: ", repoCtx.RepoName).
		Str("branch: ", repoCtx.Branch).
		Str("repoPath: ", repoCtx.RepoPath).
		Str("did: ", repoCtx.Did).Emit()

	// 使用时间戳和 query 组合作为 logKey
	requestKey := fmt.Sprintf("%s_%s_%s_%s", time.Now().Format("2006-01-02 15:04:05"), repoCtx.Uid, repoCtx.RepoName, repoCtx.Branch)
	requestKey = strings.ReplaceAll(requestKey, "/", "_")
	logDir := "get_summary_related_files"
	logParams := &entity.LogParams{
		RequestKey: requestKey,
		Dir:        logDir,
	}

	var (
		clientTreeBytes       []byte
		clientTreeErr         error
		summaryData           *commonCodebaseEntity.SummaryData
		originUserKnowledgeId string
		summaryDataErr        error
	)

	handlers := make([]func() error, 0)

	handlers = append(handlers, func() error {
		merkleTosManager := tos.NewTosManager()
		merkleTosManager.InitTosClientIfNeeded(config.GetMerkleStorageConfig())
		clientTreeBytes, clientTreeErr = merkleTosManager.DownloadFileFromTos(ctx, merkleTreeDownloadKey)
		return clientTreeErr
	})

	handlers = append(handlers, func() error {
		summaryData, originUserKnowledgeId, summaryDataErr = summaryUpdate.GetSummaryData(ctx)
		return summaryDataErr
	})

	err := group.GoAndWait(handlers...)

	if clientTreeErr != nil {
		log.V2.Error().With(ctx).Str("---------------download_merkle_tree_failed").Error(clientTreeErr).Emit()
		return nil, rpcerr.Wrap(clientTreeErr, codesearch.ErrCode_DownloadFileFailed, fmt.Sprintf("download merkle tree failed, id: %s", merkleTreeDownloadKey))
	}

	if summaryDataErr != nil {
		log.V2.Error().With(ctx).Str("获取摘要数据失败").Error(summaryDataErr).Emit()
		return nil, rpcerr.Wrap(summaryDataErr, codesearch.ErrCode_DownloadFileFailed, fmt.Sprintf("get summary data failed"))
	}
	if err != nil {
		return nil, rpcerr.Wrap(err, codesearch.ErrCode_DownloadFileFailed, fmt.Sprintf("download merkle tree failed, err"))
	}

	// 检查 summaryData 是否为空
	if summaryData == nil {
		log.V2.Error().With(ctx).Str("摘要数据为空，无法进行差异分析").Emit()
		return nil, rpcerr.New(codesearch.ErrCode_ParseFileFailed, "summary data is empty")
	}

	knowledgeManager := summaryManager.NewKnowledgeManager()
	knowledgeManager.UpdateKnowledge(ctx, &summaryData.Knowledge)

	log.V2.Info().With(ctx).Str("get summary data success").Str("server merkleId: ", summaryData.MerkleId).Emit()

	serverTree, err := summaryUpdate.DownloadMerkleTree(ctx, summaryData.MerkleId)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------get summary merkle tree failed").Error(err).Emit()
		return nil, rpcerr.Wrap(err, codesearch.ErrCode_DownloadFileFailed, fmt.Sprintf("get summary merkle tree failed"))
	}
	// 存储debug信息
	// if env.IsPPE() {
	debug.BuildGetRelatedFileInfoContext(ctx, summaryData, &codebase.GetSummaryUpdateFilesRequest{
		MerkleTreeDownloadKey: merkleTreeDownloadKey,
		Uid:                   repoCtx.Uid,
		Did:                   repoCtx.Did,
		RepoPath:              repoCtx.RepoPath,
		RepoName:              repoCtx.RepoName,
		Branch:                repoCtx.Branch,
	}, serverTree, clientTreeBytes, logParams)
	// }
	log.V2.Info().With(ctx).Str("download merkle tree success").Emit()
	clientTree, err := update.AnalysisMerkleTree(ctx, clientTreeBytes)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------summary_marshal_failed").Error(err).Emit()
		return nil, rpcerr.Wrap(err, codesearch.ErrCode_ParseFileFailed, fmt.Sprintf("summary marshal failed"))
	}
	logs.CtxInfo(ctx, "client merkleId: %v", clientTree.Hash)
	diffs := merklet.DiffTrees(serverTree, clientTree)
	if len(diffs) == 0 {
		log.V2.Info().With(ctx).Str("未发现差异").Emit()
	} else {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("发现 %d 个差异", len(diffs))).Emit()
	}

	modifyFilePaths := []string{}
	deleteFilePaths := []string{}
	relatedFilePaths := codebaseEntity.GroupedRelatedPathInfo{}
	for _, diff := range diffs {
		if diff.Type == "delete" {
			deleteFilePaths = append(deleteFilePaths, diff.Path)
		}
		modifyFilePaths = append(modifyFilePaths, diff.Path)
	}
	// 删除文件需要做特化处理，因为删除可能直接删除目录，删除目录的情况下，要把目录也当做变更处理
	deletedDirs := update.DetectDeletedDirectories(clientTree, deleteFilePaths)
	for _, dir := range deletedDirs {
		modifyFilePaths = append(modifyFilePaths, dir)
	}
	tempRelatedFilePaths := update.GetRelatedFileList(ctx, knowledgeManager, &modifyFilePaths)
	relatedFilePaths.LeafGroups = append(relatedFilePaths.LeafGroups, tempRelatedFilePaths.LeafGroups...)
	relatedFilePaths.ModuleGroups = append(relatedFilePaths.ModuleGroups, tempRelatedFilePaths.ModuleGroups...)
	log.V2.Info().With(ctx).Str("tempRelatedFilePaths.LeafGroups: ").Int(len(tempRelatedFilePaths.LeafGroups)).Emit()
	log.V2.Info().With(ctx).Str("tempRelatedFilePaths.ModuleGroups: ").Int(len(tempRelatedFilePaths.ModuleGroups)).Emit()
	groupedRelatedPathInfo := builder.ConvertToGroupedRelatedPathInfo(&relatedFilePaths)
	log.V2.Info().With(ctx).Str("groupedRelatedPathInfo.LeafGroups: ").Int(len(groupedRelatedPathInfo.LeafGroups)).Emit()
	log.V2.Info().With(ctx).Str("groupedRelatedPathInfo.ModuleGroups: ").Int(len(groupedRelatedPathInfo.ModuleGroups)).Emit()

	var diffResults []*codebase.DiffResult_
	for _, diff := range diffs {
		diffResults = append(diffResults, &codebase.DiffResult_{
			Path: diff.Path,
			Type: string(diff.Type),
			Hash: diff.Hash,
		})
	}
	response := &codebase.GetSummaryUpdateFilesResponse{
		GroupedRelatedPathInfo: groupedRelatedPathInfo,
		OriginUserKnowledgeId:  originUserKnowledgeId,
		ServerMerkleId:         summaryData.MerkleId,
		Diffs:                  diffResults,
	}
	return response, nil
}

/**
 * ProcessCodeChangesAndAnalyzeModules 处理代码变更并分析模块
 * 使用并发执行提升性能，支持控制并发数
 * @param ctx - 上下文
 * @param app - 代码搜索应用实例
 * @param tempManager - 临时管理器
 * @param groupedRelatedFileInfo - 分组的相关文件信息
 * @param maxConcurrency - 最大并发数，默认为5
 * @returns error - 错误信息
 */

type ProcessCodeChangesAndAnalyzeModulesParams struct {
	TempManager            *logic.TempManager
	GroupedRelatedFileInfo *codebaseEntity.GroupedRelatedFileInfo
	LogParams              *entity.LogParams
	MaxConcurrency         int
	Uid                    string
}

func ProcessCodeChangesAndAnalyzeModules(ctx context.Context, params *ProcessCodeChangesAndAnalyzeModulesParams) error {
	log.V2.Info().With(ctx).Str("---------------ProcessCodeChangesAndAnalyzeModules start").Emit()

	maxConcurrency := params.MaxConcurrency
	uid := params.Uid
	groupedRelatedFileInfo := params.GroupedRelatedFileInfo
	tempManager := params.TempManager
	logParams := params.LogParams

	// 设置默认并发数
	if maxConcurrency <= 0 {
		maxConcurrency = 5
	}

	// 创建信号量控制并发数
	semaphore := make(chan struct{}, maxConcurrency)

	// 之前模块a 里面有[b,c,d]三个subModule，现在可能新增了一个e目录，那么要看看是否要新增加一个e的submodule
	// 也可以能是直接修改了模块a里面的代码，所以需要更新module的summary、keyFeatures、subModule
	handlers := make([]func() error, 0)
	log.V2.Info().With(ctx).Str("--------start analyze module-------\n").Emit()
	log.V2.Info().With(ctx).Str("ModuleGroups size: ").Int(len(groupedRelatedFileInfo.ModuleGroups)).Emit()
	log.V2.Info().With(ctx).Str("LeafGroups size: ").Int(len(groupedRelatedFileInfo.LeafGroups)).Emit()

	// 进度跟踪变量
	var completedCount int32
	var activeCount int32
	totalHandlers := len(groupedRelatedFileInfo.ModuleGroups) + len(groupedRelatedFileInfo.LeafGroups)

	log.V2.Info().With(ctx).Str("进度初始化").Str("总任务数", fmt.Sprintf("%d", totalHandlers)).Str("最大并发数", fmt.Sprintf("%d", maxConcurrency)).Emit()

	// 收集模块组处理任务
	for _, moduleGroup := range groupedRelatedFileInfo.ModuleGroups {
		group := moduleGroup // 创建副本避免闭包问题
		handlers = append(handlers, func() error {
			// 获取信号量
			semaphore <- struct{}{}
			atomic.AddInt32(&activeCount, 1)
			defer func() {
				<-semaphore
				atomic.AddInt32(&activeCount, -1)
				atomic.AddInt32(&completedCount, 1)
			}()

			log.V2.Info().With(ctx).Str("开始处理模块组").Str("group_path", group.GroupPath).Str("当前进度", fmt.Sprintf("%d/%d", atomic.LoadInt32(&completedCount)+1, totalHandlers)).Str("正在执行", group.GroupPath).Emit()

			module := tempManager.SummaryManager.GetModule(ctx, group.GroupPath)
			if module == nil {
				log.V2.Error().With(ctx).Str("-------no module found--------", group.GroupPath).Emit()
				return nil
			}
			log.V2.Info().With(ctx).Str("---------------module found").Str("module: ", module.Path).Emit()
			// 有一个前置流程根据目录树和变更的文件，判定模块归属，究竟是从属旧的module还是可以独立出一个新的module
			// 如果从属旧的模块，那么就直接ExploreModuleWithKnowledge
			// 如果是可以满足新模块的构造，那么就构造探索计划
			independentModuleAnalyzedResult := &codebaseEntity.IndependentModuleAnalyzedResult{
				IsIndependentModule:   false,
				IndependentModulePath: "",
			}
			if len(group.SubFileInfos) > 0 {
				result, err := update.AnalyzeIndependentModule(ctx, tempManager, &group, logParams, params.Uid)
				if err == nil {
					independentModuleAnalyzedResult = result
				}
			}
			if independentModuleAnalyzedResult.IsIndependentModule {
				log.V2.Info().With(ctx).Str("---------------create plan").Str("plan: ", independentModuleAnalyzedResult.IndependentModulePath).Emit()
				tempManager.PlanRecordManager.CreatePlan(ctx, []*planEntity.TaskRecord{
					{
						TaskID:     "CORE-0",
						Definition: "深入分析当前目录",
						TargetPath: independentModuleAnalyzedResult.IndependentModulePath,
					},
				}, module.Path)
			}
			// 这块后续可以考虑合并
			// 更新一波当前点的summary、keyFeatures、subModule
			update.ExploreModuleWithKnowledge(ctx, tempManager, &group, module, logParams, params.Uid)
			log.V2.Info().With(ctx).Str("---------------explore module success").Str("module: ", module.Path).Emit()
			parentModule := tempManager.SummaryManager.GetModuleBySubModulePath(ctx, group.GroupPath)
			if parentModule != nil {
				// 更新一波父节点的summary、keyFeatures、subModule
				update.ExploreModuleWithKnowledge(ctx, tempManager, &group, parentModule, logParams, params.Uid)
				log.V2.Info().With(ctx).Str("---------------explore parent module success").Str("module: ", parentModule.Path).Emit()
			}

			log.V2.Info().With(ctx).Str("完成处理模块组").Str("group_path", group.GroupPath).Str("当前进度", fmt.Sprintf("%d/%d", atomic.LoadInt32(&completedCount), totalHandlers)).Emit()
			return nil
		})
	}

	log.V2.Info().With(ctx).Str("---------------ProcessCodeChangesAndAnalyzeModules end").Emit()
	// 需要去analysed module，看看模块的复杂度，看看这个模块是否要做为新的module
	// 之前就是subModule，那么看看是否要作为一个独立的模块
	// 如果是复杂模块，那么要用plan做规划，拆分任务，然后做explore
	// 如果不是复杂模块，那么应该带着父级的module的summary分析一下，更新父级的module的summary、keyFeatures、subModule

	// 收集叶子组处理任务
	for _, leafGroup := range groupedRelatedFileInfo.LeafGroups {
		group := leafGroup // 创建副本避免闭包问题
		handlers = append(handlers, func() error {
			// 获取信号量
			semaphore <- struct{}{}
			atomic.AddInt32(&activeCount, 1)
			defer func() {
				<-semaphore
				atomic.AddInt32(&activeCount, -1)
				atomic.AddInt32(&completedCount, 1)
			}()

			log.V2.Info().With(ctx).Str("开始处理叶子组").Str("group_path", group.GroupPath).Str("当前进度", fmt.Sprintf("%d/%d", atomic.LoadInt32(&completedCount)+1, totalHandlers)).Str("正在执行", group.GroupPath).Emit()

			parentModule := tempManager.SummaryManager.GetModuleBySubModulePath(ctx, group.GroupPath)
			if parentModule == nil {
				// 如果叶子节点没有父module，大概率父节点已经被删除了
				log.V2.Warn().With(ctx).Str("-------Leaf: no parent module found--------", group.GroupPath).Emit()
				return nil
			}
			independentModuleAnalyzedResult := &codebaseEntity.IndependentModuleAnalyzedResult{
				IsIndependentModule:   false,
				IndependentModulePath: "",
			}
			if len(group.SubFileInfos) > 0 {
				result, err := update.AnalyzeIndependentModule(ctx, tempManager, &group, logParams, params.Uid)
				if err == nil {
					independentModuleAnalyzedResult = result
				}
			}
			log.V2.Info().With(ctx).Str("---------------analyze leaf module success").Str("module: ", parentModule.Path).Emit()
			// 如果是复杂模块，那么需要重新创建独立的探索任务，然后自动探索函数的子目录子模块
			if independentModuleAnalyzedResult.IsIndependentModule {
				log.V2.Info().With(ctx).Str("---------------create plan").Str("plan: ", independentModuleAnalyzedResult.IndependentModulePath).Emit()
				// 创建探索计划
				tempManager.PlanRecordManager.CreatePlan(ctx, []*planEntity.TaskRecord{
					{
						TaskID:     "CORE-0",
						Definition: "深入分析当前目录",
						TargetPath: independentModuleAnalyzedResult.IndependentModulePath,
						TaskStatus: planEntity.TaskStatusPlanned,
						Priority:   planEntity.TaskPriorityHigh,
					},
				}, parentModule.Path)
			}
			// 如果之前就是叶子节点，且现在还是简单模块，那么只需要更新module的summary、keyFeatures、subModule
			update.ExploreModuleWithKnowledge(ctx, tempManager, &group, parentModule, logParams, params.Uid)
			log.V2.Info().With(ctx).Str("---------------explore leaf module success").Str("module: ", parentModule.Path).Emit()

			log.V2.Info().With(ctx).Str("完成处理叶子组").Str("group_path", group.GroupPath).Str("当前进度", fmt.Sprintf("%d/%d", atomic.LoadInt32(&completedCount), totalHandlers)).Emit()
			return nil
		})
	}

	log.V2.Info().With(ctx).Str("开始并发执行").Str("总任务数", fmt.Sprintf("%d", totalHandlers)).Str("最大并发数", fmt.Sprintf("%d", maxConcurrency)).Emit()

	// 并发执行所有任务
	err := group.GoAndWait(handlers...)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------并发处理中出现错误").Error(err).Emit()
		return err
	}

	log.V2.Info().With(ctx).Str("并发执行完成").Str("总任务数", fmt.Sprintf("%d", totalHandlers)).Str("完成数", fmt.Sprintf("%d", atomic.LoadInt32(&completedCount))).Emit()

	log.V2.Info().With(ctx).Str("---------------execute module exploration start").Emit()
	// 使用重构后的公共流程执行模块探索
	// todo(liboti)
	// 创建了新模块之后，要把新模块加到父级的subModule里面
	err = executeModuleExploration(ctx, tempManager, logParams, maxConcurrency, uid)
	if err != nil {
		log.V2.Error().With(ctx).Str("模块探索失败").Error(err).Emit()
		return err
	}
	return nil
}

// executeModuleExploration 执行模块探索的公共流程
func executeModuleExploration(ctx context.Context,
	tempManager *logic.TempManager,
	localLogParams *entity.LogParams,
	maxConcurrency int,
	uid string) error {
	log.V2.Info().With(ctx).Str("=== executeModuleExploration 开始 ===").Str("maxConcurrency", fmt.Sprintf("%d", maxConcurrency)).Emit()

	// 添加超时监控
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		log.V2.Info().With(ctx).Str("=== executeModuleExploration 结束 ===").Str("总耗时", duration.String()).Emit()
	}()

	// 创建计划执行器
	log.V2.Info().With(ctx).Str("创建计划执行器").Emit()
	taskProcessor := &DefaultTaskProcessor{}
	planExecutor := NewPlanExecutor(taskProcessor, maxConcurrency)

	logDir := "update/explore"
	logKey := "update_summary"

	// 执行计划
	params := &TaskProcessParams{
		Depth:       3,
		LogKey:      logKey,
		LogDir:      localLogParams.Dir + "/" + localLogParams.RequestKey + "/" + logDir,
		TempManager: tempManager,
		Uid:         uid,
	}

	log.V2.Info().With(ctx).Str("开始执行计划").Str("logDir", params.LogDir).Emit()

	// 添加带超时的执行
	done := make(chan error, 1)
	go func() {
		done <- planExecutor.ExecutePlans(ctx, params)
	}()

	// 设置超时时间（根据实际情况调整）
	timeout := 180 * time.Minute
	select {
	case err := <-done:
		if err != nil {
			log.V2.Error().With(ctx).Str("计划执行失败").Error(err).Emit()
		} else {
			log.V2.Info().With(ctx).Str("计划执行成功完成").Emit()
		}
		return err
	case <-time.After(timeout):
		log.V2.Error().With(ctx).Str("计划执行超时").Str("timeout", timeout.String()).Emit()
		return fmt.Errorf("executeModuleExploration 执行超时，超过 %v", timeout)
	case <-ctx.Done():
		log.V2.Error().With(ctx).Str("上下文被取消").Error(ctx.Err()).Emit()
		return ctx.Err()
	}
}

// TaskWithPlan 任务和计划的组合结构 / Task and plan combination structure
type TaskWithPlan struct {
	Task *planEntity.TaskRecord
	Plan *planEntity.PlanRecord
}

func isPlanInQueue(planID string, planQueue []*planEntity.PlanRecord) bool {
	for _, plan := range planQueue {
		if plan.PlanID == planID {
			return true
		}
	}
	return false
}

func createDefaultPlanIfNeeded(ctx context.Context, planRecordManager *plan.PlanRecordManager, path string) {
	_, err := planRecordManager.GetNextPlan(ctx)
	if err == nil {
		log.V2.Info().With(ctx).Str("---------------plan_already_exists").Emit()
		return
	}
	// 如果没有计划，创建一个默认计划
	planRecordManager.CreatePlan(ctx, []*planEntity.TaskRecord{
		{
			TaskID:     "CORE-0",
			Definition: "深入分析当前目录",
			TargetPath: path,
			TaskStatus: planEntity.TaskStatusPlanned,
			Priority:   planEntity.TaskPriorityHigh,
		},
	}, "project_root")
	log.V2.Info().With(ctx).Str("---------------plan_created").Emit()

}

func InitFileManager(ctx context.Context, request *CreateSummaryRequest) (*virtualFile.FileManager, error) {
	addErr := workspace.Workspace.Add(ctx, request.RepoName, request.RepoURL, request.Branch)
	if addErr != nil {
		return nil, addErr
	}
	tempWS := workspace.Workspace.GetTempWorkspace(request.RepoName, request.Branch)
	fileManager := virtualfile.NewLocalFileManager(tempWS.Path)
	return &fileManager, nil
}

func InitTempManager(ctx context.Context, summaryData *commonCodebaseEntity.SummaryData, clientTreeBytes []byte, groupedRelatedFileInfo *codebaseEntity.GroupedRelatedFileInfo) (*logic.TempManager, error) {
	tempManager := logic.NewTempManager(ctx)
	basePath := "."
	tempManager.SummaryManager.InitStorage(basePath, "")
	tempManager.PlanRecordManager.Init(basePath, "")

	// 还原一下knowledge
	tempManager.SummaryManager.UpdateKnowledge(ctx, &summaryData.Knowledge)

	// 分析merkle树
	clientTree, err := update.AnalysisMerkleTree(ctx, clientTreeBytes)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------summary_marshal_failed").Error(err).Emit()
		return nil, err
	}

	// 初始化文件管理器 - 从clientTree构造虚拟仓库
	fileManager, err := initFileManagerFromMerkleTree(ctx, clientTree, groupedRelatedFileInfo)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------init_file_manager_failed").Error(err).Emit()
		return nil, err
	}
	tempManager.FileManager = fileManager
	return tempManager, nil
}

// initFileManagerFromMerkleTree 从Merkle树构造虚拟仓库并初始化文件管理器
func initFileManagerFromMerkleTree(ctx context.Context, clientTree *merklet.TreeNode, groupedRelatedFileInfo *codebaseEntity.GroupedRelatedFileInfo) (*virtualFile.FileManager, error) {
	rootPath := "/"
	virtualTree := update.GetVirtualTreeFromMerkleTree(ctx, clientTree, groupedRelatedFileInfo, []string{})
	// 创建虚拟文件管理器
	fileManager := virtualfile.NewVirtualFileManager(rootPath, virtualTree)
	log.V2.Info().With(ctx).Str("虚拟文件管理器创建完成").Emit()

	return &fileManager, nil
}

// monitorSystemHealth 监控系统健康状态
func monitorSystemHealth(ctx context.Context, planQueue []*planEntity.PlanRecord, activeTasks int, completedPlans map[string]bool) {
	log.V2.Info().With(ctx).Str("=== 系统健康状态监控 ===").Emit()
	log.V2.Info().With(ctx).Str("计划队列大小").Str("plan_queue_size", fmt.Sprintf("%d", len(planQueue))).Emit()
	log.V2.Info().With(ctx).Str("活跃任务数").Str("active_tasks", fmt.Sprintf("%d", activeTasks)).Emit()
	log.V2.Info().With(ctx).Str("已完成计划数").Str("completed_plans", fmt.Sprintf("%d", len(completedPlans))).Emit()

	// 检查计划状态
	for i, plan := range planQueue {
		log.V2.Info().With(ctx).Str("计划状态").Str("index", fmt.Sprintf("%d", i)).Str("plan_id", plan.PlanID).Str("state", string(plan.State)).Str("task_count", fmt.Sprintf("%d", len(plan.Tasks))).Emit()

		// 检查任务状态
		completedTasks := 0
		for _, task := range plan.Tasks {
			if task.TaskStatus == planEntity.TaskStatusCompleted {
				completedTasks++
			}
		}
		log.V2.Info().With(ctx).Str("任务完成情况").Str("plan_id", plan.PlanID).Str("completed_tasks", fmt.Sprintf("%d", completedTasks)).Str("total_tasks", fmt.Sprintf("%d", len(plan.Tasks))).Emit()
	}
}

// diagnoseStuckIssue 诊断卡住问题
func diagnoseStuckIssue(ctx context.Context, planQueue []*planEntity.PlanRecord, activeTasks int) {
	log.V2.Warn().With(ctx).Str("=== 开始诊断卡住问题 ===").Emit()

	// 检查是否有计划但没有活跃任务
	if len(planQueue) > 0 && activeTasks == 0 {
		log.V2.Warn().With(ctx).Str("发现潜在问题：有计划但没有活跃任务").Emit()
		for _, plan := range planQueue {
			log.V2.Warn().With(ctx).Str("待处理计划").Str("plan_id", plan.PlanID).Str("state", string(plan.State)).Emit()
		}
	}

	// 检查是否有长时间运行的任务
	if activeTasks > 0 {
		log.V2.Warn().With(ctx).Str("发现活跃任务").Str("active_tasks", fmt.Sprintf("%d", activeTasks)).Emit()
	}

	// 检查计划管理器状态
	log.V2.Info().With(ctx).Str("计划管理器状态检查完成").Emit()
}

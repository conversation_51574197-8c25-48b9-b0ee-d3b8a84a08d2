package summary_update

import (
	commonUtils "code.byted.org/ies/codin/common/contexts"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"code.byted.org/gopkg/jsonx"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	summaryEntity "code.byted.org/ies/codin/application/codebase/entity"
	merklet "code.byted.org/ies/codin/application/codebase/logic/merklet"
	summaryBuildRecordRepo "code.byted.org/ies/codin/application/codebase/repo/summary_build_record"
	mysql "code.byted.org/ies/codin/application/codesearch/common/mysql"
	"code.byted.org/ies/codin/common/group"
	codebaseEntity "code.byted.org/ies/codin/common/semantic/codebase/entity"
	codeBaseManager "code.byted.org/ies/codin/common/semantic/codebase/manager"
	summaryTos "code.byted.org/ies/codin/common/semantic/tos"
	"code.byted.org/ies/codin/common/semantic/tos/config"
	tos "code.byted.org/ies/codin/common/tos"
	"gorm.io/gorm"
)

/**
 * GetKnowledge 并发下载三个 TOS 文件，然后转换成 SummaryData，按照优先级1，2，3去返回
 * 有1返回1，没1返回2，1，2都没有返回3
 * @param ctx - 上下文信息
 * @param app - 代码搜索应用实例
 * @param request - Merkle 差异请求
 * @returns *SummaryData - 返回的知识库，如果所有下载都失败则返回 nil
 * @returns error - 错误信息
 */
func GetSummaryData(ctx context.Context) (*codebaseEntity.SummaryData, string, error) {
	// 构建三个不同优先级的文件路径
	userKnowledgeId := codeBaseManager.GetSummaryUserKnowledgeIdByCtx(ctx)
	knowledgeBranchId := codeBaseManager.GetSummaryKnowledgeBranchIdByCtx(ctx)
	knowledgebaseId := codeBaseManager.GetSummaryKnowledgebaseIdByCtx(ctx)

	// 定义三个不同优先级的文件路径
	filePaths := map[int]string{
		1: userKnowledgeId,   // 优先级1：用户特定的知识库
		2: knowledgeBranchId, // 优先级2：知识库+分支
		3: knowledgebaseId,   // 优先级3：知识库级别
	}
	logs.CtxInfo(ctx, "GetSummaryData, filePaths=%v", jsonx.ToString(filePaths))

	// 初始化 TOS 管理器
	tosManager := summaryTos.NewSummaryTosManager()
	if err := tosManager.InitTosClientIfNeeded(config.GetSummaryStorageConfig()); err != nil {
		log.V2.Error().With(ctx).Str("初始化TOS客户端失败").Error(err).Emit()
		return nil, "", fmt.Errorf("初始化TOS客户端失败: %w", err)
	}

	// 并发下载三个文件
	results := summaryTos.DownloadFilesConcurrently(ctx, tosManager, filePaths)

	// 按照优先级选择最佳结果并解析
	return summaryTos.SelectBestResultAndParse(results, filePaths)
}

func GetUserSummaryData(ctx context.Context, userKnowledgeId string) (*codebaseEntity.SummaryData, error) {
	tosManager := summaryTos.NewSummaryTosManager()

	// 初始化 TOS 管理器
	if err := tosManager.InitTosClientIfNeeded(config.GetSummaryStorageConfig()); err != nil {
		log.V2.Error().With(ctx).Str("初始化TOS客户端失败").Error(err).Emit()
		return nil, fmt.Errorf("初始化TOS客户端失败: %w", err)
	}

	data, err := tosManager.DownloadFileFromTos(ctx, userKnowledgeId)
	if err != nil {
		log.V2.Error().With(ctx).Str("下载用户知识库失败").Error(err).Emit()
		return nil, fmt.Errorf("下载用户知识库失败: %w", err)
	}

	var summaryData codebaseEntity.SummaryData
	if err := json.Unmarshal(data, &summaryData); err != nil {
		log.V2.Error().With(ctx).Str("解析用户知识库失败").Error(err).Emit()
		return nil, fmt.Errorf("解析用户知识库失败: %w", err)
	}

	return &summaryData, nil
}

// 下载merkleTree
func DownloadMerkleTree(ctx context.Context, merkleId string) (*merklet.TreeNode, error) {
	// 这里需要使用merkleStorage的桶，因为merkleTree的桶是用来存储merkleTree的，而summary的桶是用来存储summary的
	tosManager := tos.NewTosManager()
	// merkle树相关的内容在merkleStorage的桶中
	err := tosManager.InitTosClientIfNeeded(config.GetMerkleStorageConfig())
	if err != nil {
		logs.CtxError(ctx, "get tos client error: %v, keyName: %s", err, merkleId)
		return nil, err
	}
	logs.CtxInfo(ctx, "get tos client success, keyName: %s", merkleId)
	// 1. 下载merkleTree
	merkleTree, err := tosManager.DownloadFileFromTos(ctx, merkleId)
	if err != nil {
		logs.CtxError(ctx, "[GetMerkleDiff] DownloadMerkleTree error: %+v", err)
		return nil, err
	}

	// 2. 解压数据
	uncompressedData, err := merklet.GzipDecompress(merkleTree)
	if err != nil {
		logs.CtxError(ctx, "GzipUncompress error: %v", err)
		return nil, err
	}

	serverTree, err := merklet.DeserializeTree(uncompressedData)
	if err != nil {
		logs.CtxError(ctx, "DeserializeTree error: %v", err)
		return nil, err
	}
	return serverTree, nil
}

func UpdateUserMetaStatus(ctx context.Context, request *codebaseEntity.GetSummaryDataRequest, merkleId string, summaryStatus summaryBuildRecordRepo.SummaryBuildState) error {
	var (
		buildRecordRepo = summaryBuildRecordRepo.NewSummaryBuildRecordRepository(mysql.Get())
		userKnowledgeId = codeBaseManager.GetSummaryUserKnowledgeIdByCtx(ctx)
	)
	logs.CtxInfo(ctx, "UpdateUserMetaStatus userKnowledgeId: %v, merkleId: %v, summaryStatus: %v", userKnowledgeId, merkleId, summaryStatus)
	summaryBuildRecord, getErr := buildRecordRepo.GetBySummaryKey(ctx, userKnowledgeId)
	if getErr != nil {
		if errors.Is(getErr, gorm.ErrRecordNotFound) {
			// 如果记录不存在，创建一个新的记录
			// 没有就创建一条记录
			summaryBuildRecord = &summaryBuildRecordRepo.SummaryBuildRecord{
				SummaryKey:   userKnowledgeId,
				RootMerkleID: merkleId,
				RepoName:     request.RepoName,
				RepoBranch:   request.Branch,
				State:        summaryBuildRecordRepo.SummaryBuildStateBuilding,
				StartTime:    time.Now().Unix(),
				LogId:        commonUtils.GetLogID(ctx),
				BuildParam:   jsonx.ToString(request),
			}
			if createErr := buildRecordRepo.Create(ctx, summaryBuildRecord); createErr != nil {
				logs.CtxError(ctx, "UpdateUserMetaStatus create summary build record error: %v, summaryKey=%v", createErr, userKnowledgeId)
				return createErr
			}
			logs.CtxInfo(ctx, "UpdateUserMetaStatus create summary build record success: %v, summaryKey=%v", summaryBuildRecord, userKnowledgeId)
		} else {
			logs.CtxError(ctx, "UpdateUserMetaStatus get summary build record error: %v, summaryKey=%v", getErr, userKnowledgeId)
			return getErr
		}
	}

	summaryBuildRecord.State = summaryStatus
	summaryBuildRecord.RootMerkleID = merkleId
	if summaryBuildRecord.State == summaryBuildRecordRepo.SummaryBuildStateSucceeded {
		summaryBuildRecord.EndTime = time.Now().Unix()
	}

	saveErr := buildRecordRepo.Save(ctx, summaryBuildRecord)
	if saveErr != nil {
		logs.CtxError(ctx, "UpdateUserMetaStatus save summary build record error: %v, summaryKey=%v", saveErr, userKnowledgeId)
		return saveErr
	}

	return nil
}

func UpdateMetaStatus(ctx context.Context, request *codebaseEntity.GetSummaryDataRequest, merkleId string, summaryStatus summaryBuildRecordRepo.SummaryBuildState) error {
	var (
		buildRecordRepo = summaryBuildRecordRepo.NewSummaryBuildRecordRepository(mysql.Get())
		summaryKeys     = ListSummaryKeyByCtx(ctx)
	)

	for _, summaryKey := range summaryKeys {
		logs.CtxInfo(ctx, "[UpdateMetaStatus] GetSummaryKey: %s", summaryKey)
		summaryBuildRecord, getErr := buildRecordRepo.GetBySummaryKey(ctx, summaryKey)
		if getErr != nil {
			if errors.Is(getErr, gorm.ErrRecordNotFound) {
				// 如果记录不存在，创建一个新的记录
				// 没有就创建一条记录
				summaryBuildRecord = &summaryBuildRecordRepo.SummaryBuildRecord{
					SummaryKey:   summaryKey,
					RootMerkleID: merkleId,
					RepoName:     request.RepoName,
					RepoBranch:   request.Branch,
					State:        summaryBuildRecordRepo.SummaryBuildStateBuilding,
					StartTime:    time.Now().Unix(),
					LogId:        commonUtils.GetLogID(ctx),
					BuildParam:   jsonx.ToString(request),
				}
				if createErr := buildRecordRepo.Create(ctx, summaryBuildRecord); createErr != nil {
					logs.CtxError(ctx, "UpdateMetaStatus create summary build record error: %v, summaryKey=%v", createErr, summaryKey)
					return createErr
				}
			} else {
				logs.CtxError(ctx, "UpdateMetaStatus get summary build record error: %v, summaryKey=%v", getErr, summaryKey)
				return getErr
			}
		}

		summaryBuildRecord.State = summaryStatus
		if summaryBuildRecord.State == summaryBuildRecordRepo.SummaryBuildStateSucceeded {
			summaryBuildRecord.EndTime = time.Now().Unix()
		}
		saveErr := buildRecordRepo.Save(ctx, summaryBuildRecord)
		if saveErr != nil {
			logs.CtxError(ctx, "UpdateMetaStatus save summary build record error: %v, summaryKey=%v", saveErr, summaryKey)
			return saveErr
		}
	}
	return nil
}

func ListSummaryKeyByCtx(ctx context.Context) map[int]string {
	// 每一份 summary 都有三个 key
	userKnowledgeId := codeBaseManager.GetSummaryUserKnowledgeIdByCtx(ctx)
	knowledgeBranchId := codeBaseManager.GetSummaryKnowledgeBranchIdByCtx(ctx)
	knowledgebaseId := codeBaseManager.GetSummaryKnowledgebaseIdByCtx(ctx)

	// 定义三个不同优先级的文件路径
	filePaths := map[int]string{
		1: userKnowledgeId,   // 优先级1：用户特定的知识库
		2: knowledgeBranchId, // 优先级2：知识库+分支
		3: knowledgebaseId,   // 优先级3：知识库级别
	}

	return filePaths
}

/**
 * UploadSummaryData 并发上传三个不同优先级的摘要数据到 TOS
 * @param ctx - 上下文信息
 * @param summaryData - 要上传的摘要数据
 * @param request - 请求参数
 * @returns error - 错误信息
 */
func UploadSummaryData(ctx context.Context, summaryData *codebaseEntity.SummaryData, request *codebaseEntity.GetSummaryDataRequest) error {
	// 定义三个不同优先级的文件路径
	filePaths := ListSummaryKeyByCtx(ctx)
	tosManager := summaryTos.NewSummaryTosManager()

	// 初始化 TOS 客户端
	if err := tosManager.InitTosClientIfNeeded(config.GetSummaryStorageConfig()); err != nil {
		log.V2.Error().With(ctx).Str("初始化TOS客户端失败").Error(err).Emit()
		return fmt.Errorf("初始化TOS客户端失败: %w", err)
	}

	// 序列化摘要数据
	summaryDataJson, err := json.MarshalIndent(summaryData, "", "  ")
	if err != nil {
		log.V2.Error().With(ctx).Str("序列化摘要数据失败").Error(err).Emit()
		return fmt.Errorf("序列化摘要数据失败: %w", err)
	}

	// 并发上传三个文件
	results := uploadFilesConcurrently(ctx, tosManager, summaryDataJson, filePaths)

	// 检查上传结果
	successCount := 0
	for priority, result := range results {
		if result.Error == nil {
			successCount++
			log.V2.Info().With(ctx).Str("成功上传摘要数据").KVs(
				"priority", priority,
				"filePath", filePaths[priority],
			).Emit()
		} else {
			log.V2.Error().With(ctx).Str("上传摘要数据失败").KVs(
				"priority", priority,
				"filePath", filePaths[priority],
			).Error(result.Error).Emit()
		}
	}

	// 如果所有上传都失败，返回错误
	if successCount == 0 {
		return fmt.Errorf("所有优先级的摘要数据上传都失败")
	}

	log.V2.Info().With(ctx).Str("摘要数据上传完成").KVs(
		"successCount", successCount,
		"totalCount", len(filePaths),
	).Emit()

	return nil
}

/**
 * uploadFilesConcurrently 并发上传多个文件
 * @param ctx - 上下文信息
 * @param tosManager - TOS 管理器接口
 * @param fileData - 要上传的文件数据
 * @param filePaths - 文件路径映射，key为优先级，value为文件路径
 * @returns map[int]*UploadResult - 上传结果映射
 */
func uploadFilesConcurrently(ctx context.Context, tosManager *tos.TosManager, fileData []byte, filePaths map[int]string) map[int]*summaryEntity.UploadResult {
	results := make(map[int]*summaryEntity.UploadResult)
	var mu sync.Mutex

	handlers := make([]func() error, 0)
	for priority, filePath := range filePaths {
		p := priority
		path := filePath
		handlers = append(handlers, func() error {
			result := &summaryEntity.UploadResult{
				Priority: p,
			}

			// 上传文件
			_, err := tosManager.UploadFileToTos(ctx, fileData, path)
			if err != nil {
				result.Error = err
			}

			// 线程安全地存储结果
			mu.Lock()
			results[p] = result
			mu.Unlock()
			return nil
		})
	}

	// 使用 group.GoAndWait 并发执行所有上传任务
	group.GoAndWait(handlers...)

	return results
}

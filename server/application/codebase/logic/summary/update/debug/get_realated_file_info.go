package debug

import (
	"context"

	"code.byted.org/gopkg/logs/v2"
	codeEntity "code.byted.org/ies/codin/application/codebase/entity"
	"code.byted.org/ies/codin/application/codebase/logic/merklet"
	codebaseEntity "code.byted.org/ies/codin/common/semantic/codebase/entity"
	"code.byted.org/overpass/capcut_devops_codebase/kitex_gen/codebase"
)

type GetRelatedFileInfoContext struct {
	SummaryData     *codebaseEntity.SummaryData
	Request         *codebase.GetSummaryUpdateFilesRequest
	ServerTreeBytes []byte
	ClientTreeBytes []byte
}

func BuildGetRelatedFileInfoContext(ctx context.Context, summaryData *codebaseEntity.SummaryData, request *codebase.GetSummaryUpdateFilesRequest, serverTree *merklet.TreeNode, clientTreeBytes []byte, logParams *codeEntity.LogParams) {
	serverTreeBytes, err := merklet.SerializeTree(serverTree)
	if err != nil {
		logs.CtxError(ctx, "SerializeTree error: %v", err)
		return
	}
	serverTreeBytes, err = merklet.GzipCompress(serverTreeBytes)
	if err != nil {
		logs.CtxError(ctx, "GzipCompress error: %v", err)
		return
	}
	getRelatedFileInfoContext := &GetRelatedFileInfoContext{
		SummaryData:     summaryData,
		Request:         request,
		ServerTreeBytes: serverTreeBytes,
		ClientTreeBytes: clientTreeBytes,
	}
	UpdateContextToTos(ctx, getRelatedFileInfoContext, logParams.RequestKey, logParams.Dir)
}

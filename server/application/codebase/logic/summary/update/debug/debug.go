package debug

import (
	"context"
	"encoding/json"
	"fmt"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/common/semantic/tos/config"
	"code.byted.org/ies/codin/common/tos"
)

const debugDir = "summary/debug"

func UpdateContextToTos(ctx context.Context, jsonData any, key string, dir string) {
	bytes, err := json.MarshalIndent(jsonData, "", "  ")
	if err != nil {
		fmt.Printf("WriteJsonToFile error: %v\n", err)
		return
	}
	tosManager := tos.NewTosManager()
	if err := tosManager.InitTosClientIfNeeded(config.GetSummaryStorageConfig()); err != nil {
		log.V2.Error().With(ctx).Str("初始化TOS客户端失败").Error(err).Emit()
		return
	}
	tosManager.UploadFileToTos(ctx, bytes, debugDir+"/"+dir+"/"+key)
}

package debug

import (
	"context"

	codeEntity "code.byted.org/ies/codin/application/codebase/entity"
	codebaseEntity "code.byted.org/ies/codin/common/semantic/codebase/entity"
	"code.byted.org/overpass/capcut_devops_codebase/kitex_gen/codebase"
)

type UpdateSummaryContext struct {
	SummaryData                 *codebaseEntity.SummaryData
	Request                     *codebase.UpdateSummaryRequest
	GroupedRelatedFileInfoBytes []byte
	ClientTreeBytes             []byte
}

func BuildUpdateSummaryContext(ctx context.Context, summaryData *codebaseEntity.SummaryData, request *codebase.UpdateSummaryRequest, logParams *codeEntity.LogParams, groupedRelatedFileInfoBytes []byte, clientTreeBytes []byte) {
	updateSummaryContext := &UpdateSummaryContext{
		SummaryData:                 summaryData,
		Request:                     request,
		GroupedRelatedFileInfoBytes: groupedRelatedFileInfoBytes,
		ClientTreeBytes:             clientTreeBytes,
	}
	UpdateContextToTos(ctx, updateSummaryContext, logParams.RequestKey, logParams.Dir)
}

package update

import (
	"context"
	"fmt"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	entity "code.byted.org/ies/codin/application/codebase/entity"
	merklet "code.byted.org/ies/codin/application/codebase/logic/merklet"
	knowledgeManager "code.byted.org/ies/codin/common/semantic/summary"
	knowledgeEntity "code.byted.org/ies/codin/common/semantic/summary/entity"
	commonUtils "code.byted.org/ies/codin/common/utils"
)

/**
 * 带着变更文件，来knowledge中找到影响的模块文件
 * 1、收集模块
 * - a、收集模块
 *   变更文件的父级是module，且有subModule那么说明他是一个模块
 *   变更文件的父级是module，且没有subModule，这里也不把它当做叶子节点，因为他底层也没有其他子模块，会直接去搜底下的路径
 * - b、收集叶子节点
 * - 变更文件的父级是subModule,并且这个subModule不是module，那么说明他是一个叶子节点
 * 2、获取模块的中间层文件夹路径
 * 3、获取叶子节点的路径
 */
func GetRelatedFileList(ctx context.Context, knowledgeManager *knowledgeManager.KnowledgeManager, changedFilePaths *[]string) *entity.GroupedRelatedPathInfo {
	// 使用 map 进行去重，key 为路径，value 为组信息
	moduleGroupsMap := make(map[string]*entity.DirPathGroup)
	leafGroupsMap := make(map[string]*entity.DirPathGroup)

	// 记录已处理的路径，避免重复搜索
	processedPaths := make(map[string]bool)

	// 按module分组收集文件路径
	moduleToFilesMap := make(map[string][]string) // module路径 -> 属于该module的文件路径列表

	for _, filePath := range *changedFilePaths {
		// 如果路径已处理过，跳过
		if processedPaths[filePath] {
			continue
		}

		// 看看当前的目录是否本身就是模块
		module := knowledgeManager.GetModule(ctx, filePath)
		if module != nil {
			moduleToFilesMap[module.Path] = append(moduleToFilesMap[module.Path], filePath)
			continue
		}

		// 找到路径作为subModule的情况
		subModule := getSubModule(ctx, knowledgeManager, filePath)
		if subModule == nil {
			// 如果subModule都没有，有可能是该模块之前太小了，不能被当做一个subModule
			// 这种情况要去找一下module
			parentModule := getParentModule(ctx, knowledgeManager, filePath)
			if parentModule == nil {
				// 文件肯定要挂载一个父module，父module都没有说明异常
				continue
			}
			// 单纯为module，不为subModule
			moduleToFilesMap[parentModule.Path] = append(moduleToFilesMap[parentModule.Path], filePath)
			processedPaths[filePath] = true
			continue
		}

		// 检查是否已经处理过这个subModule路径
		if processedPaths[subModule.Path] {
			continue
		}

		// 看看subModule是否有作为module的情况
		module = knowledgeManager.GetModule(ctx, subModule.Path)
		if module == nil {
			// 单纯为subModule，不为module
			// 如果没有module，那么说明这个subModule是叶子节点
			if _, exists := leafGroupsMap[subModule.Path]; !exists {
				leafGroupsMap[subModule.Path] = &entity.DirPathGroup{
					GroupPath:   subModule.Path,
					SubDirPaths: make([]string, 0),
				}
			}
			// 添加当前文件路径到叶子组
			// leafGroupsMap[subModule.Path].SubDirPaths = append(leafGroupsMap[subModule.Path].SubDirPaths, filePath)
			continue
		}
		// 即为subModule，也是module
		moduleToFilesMap[module.Path] = append(moduleToFilesMap[module.Path], filePath)
		// 不要在这里设置processedPaths[subModule.Path] = true，让collectModuleRelatedCode来处理
	}

	// 处理每个module的所有相关文件路径
	for modulePath, filePaths := range moduleToFilesMap {
		module := knowledgeManager.GetModule(ctx, modulePath)
		if module == nil {
			continue
		}

		// 使用抽取的函数处理 module 相关的代码收集
		collectModuleRelatedCode(module, &filePaths, moduleGroupsMap, processedPaths)
	}

	// 转换为切片格式
	moduleGroups := make([]entity.DirPathGroup, 0, len(moduleGroupsMap))
	for _, group := range moduleGroupsMap {
		moduleGroups = append(moduleGroups, *group)
	}

	leafGroups := make([]entity.DirPathGroup, 0, len(leafGroupsMap))
	for _, group := range leafGroupsMap {
		leafGroups = append(leafGroups, *group)
	}

	result := &entity.GroupedRelatedPathInfo{
		ModuleGroups: moduleGroups,
		LeafGroups:   leafGroups,
	}

	return result
}

/**
 * collectModuleRelatedCode 收集 module 相关的代码路径
 * @param ctx - 上下文信息
 * @param knowledgeManager - 知识库管理器
 * @param module - 模块信息
 * @param moduleFilePaths - 属于该module的文件路径列表
 * @param moduleGroupsMap - 模块组映射
 * @param processedPaths - 已处理路径记录
 */
func collectModuleRelatedCode(module *knowledgeEntity.Module, moduleFilePaths *[]string, moduleGroupsMap map[string]*entity.DirPathGroup, processedPaths map[string]bool) {
	// 检查是否已经处理过这个module路径
	if processedPaths[module.Path] {
		return
	}
	processedPaths[module.Path] = true

	// 如果module不存在于map中，创建新的module组
	if _, exists := moduleGroupsMap[module.Path]; !exists {
		moduleGroupsMap[module.Path] = &entity.DirPathGroup{
			GroupPath:   module.Path,
			SubDirPaths: make([]string, 0),
		}
	}

	// 获取module的中间层文件路径（包括到submodule和到文件的路径）
	intermediatePaths := getModuleIntermediatePaths(module, moduleFilePaths)

	// 添加中间层路径和当前文件路径到module组
	moduleGroupsMap[module.Path].SubDirPaths = append(moduleGroupsMap[module.Path].SubDirPaths, *intermediatePaths...)

	// 去重，保证SubDirPaths唯一
	unique := make(map[string]struct{})
	result := make([]string, 0, len(moduleGroupsMap[module.Path].SubDirPaths))
	for _, path := range moduleGroupsMap[module.Path].SubDirPaths {
		if _, ok := unique[path]; !ok {
			unique[path] = struct{}{}
			result = append(result, path)
		}
	}
	moduleGroupsMap[module.Path].SubDirPaths = result
}

// 可能父级不是module，要继续往上找，直到找到module为止
func getParentModule(ctx context.Context, knowledgeManager *knowledgeManager.KnowledgeManager, filePath string) *knowledgeEntity.Module {
	// 一直往上找，直到没有/为止
	modulePath := getParentPath(filePath)

	for modulePath != "" {
		module := knowledgeManager.GetModule(ctx, modulePath)
		if module == nil {
			modulePath = getParentPath(modulePath)
		} else {
			return module
		}
	}
	return nil
}

func getSubModule(ctx context.Context, knowledgeManager *knowledgeManager.KnowledgeManager, filePath string) *knowledgeEntity.SubModuleInfo {
	modulePath := getParentPath(filePath)

	for modulePath != "" {
		subModule, err := knowledgeManager.GetSubModule(ctx, modulePath)
		if err != nil {
			modulePath = getParentPath(modulePath)
		} else {
			return subModule
		}
	}
	return nil
}

/**
 * getModuleIntermediatePaths 提取出 module 到 submodule 和 module 到文件的中间层路径
 * @param module - 模块信息
 * @param moduleFilePaths - 属于该module的文件路径列表
 * @returns *[]string - 中间层路径列表
 */
func getModuleIntermediatePaths(module *knowledgeEntity.Module, moduleFilePaths *[]string) *[]string {
	intermediatePaths := make([]string, 0)
	// 先加上 module 本身
	intermediatePaths = append(intermediatePaths, commonUtils.NormalizePath(module.Path))

	// 获取 module 到 subModule 的中间层路径
	subModulePaths := getModuleToSubModulePaths(module)
	intermediatePaths = append(intermediatePaths, subModulePaths...)

	// 获取 module 到所有文件的中间层路径
	filePaths := getModuleToAllFilePaths(module, moduleFilePaths)
	intermediatePaths = append(intermediatePaths, filePaths...)

	return &intermediatePaths
}

/**
 * getModuleToSubModulePaths 提取出 module 到 submodule 的中间层路径
 * @param module - 模块信息
 * @returns []string - 中间层路径列表
 */
func getModuleToSubModulePaths(module *knowledgeEntity.Module) []string {
	// 收集 module 到 submodule 的中间层路径
	intermediatePaths := make([]string, 0)

	// 获取 module 路径
	modulePath := commonUtils.NormalizePath(module.Path)

	// 创建 submodule 路径的集合，用于快速查找
	subModulePathSet := make(map[string]bool)
	for _, subModule := range module.SubModules {
		subModulePathSet[commonUtils.NormalizePath(subModule.Path)] = true
	}

	// 遍历所有 submodule，找出中间层路径
	for _, subModule := range module.SubModules {
		subModulePath := commonUtils.NormalizePath(subModule.Path)

		// 如果 submodule 路径不是 module 的子路径，跳过
		expectedPrefix := modulePath + "/"
		if !strings.HasPrefix(subModulePath, expectedPrefix) {
			continue
		}

		// 获取从 module 到 submodule 的所有中间层级
		relativePath := subModulePath[len(modulePath)+1:] // 去掉开头的 "/"
		pathParts := strings.Split(relativePath, "/")

		currentPath := modulePath
		for i := 0; i < len(pathParts)-1; i++ { // 不包含最后一个部分（submodule本身）
			currentPath = currentPath + "/" + pathParts[i]

			// 检查这个路径是否已经作为 submodule 存在
			if !subModulePathSet[currentPath] {
				// 如果不是 submodule，则添加到中间层路径
				intermediatePaths = append(intermediatePaths, currentPath)
			}
		}
	}

	// 去重并返回结果
	uniquePaths := make(map[string]bool)
	for _, path := range intermediatePaths {
		uniquePaths[path] = true
	}

	result := make([]string, 0, len(uniquePaths))
	for path := range uniquePaths {
		result = append(result, path)
	}

	return result
}

/**
 * getModuleToAllFilePaths 提取出 module 到所有文件的中间层路径
 * @param module - 模块信息
 * @param moduleFilePaths - 属于该module的文件路径列表
 * @returns []string - 中间层路径列表
 */
func getModuleToAllFilePaths(module *knowledgeEntity.Module, moduleFilePaths *[]string) []string {
	intermediatePaths := make([]string, 0)

	// 遍历所有属于该module的文件路径
	for _, filePath := range *moduleFilePaths {
		// 获取从 module 到文件的中间层路径
		filePaths := getModuleToFilePathPaths(module, filePath)
		intermediatePaths = append(intermediatePaths, filePaths...)
	}

	// 去重并返回结果
	uniquePaths := make(map[string]bool)
	for _, path := range intermediatePaths {
		uniquePaths[path] = true
	}

	result := make([]string, 0, len(uniquePaths))
	for path := range uniquePaths {
		result = append(result, path)
	}

	return result
}

/**
 * getModuleToFilePathPaths 提取出 module 到文件的中间层路径
 * @param module - 模块信息
 * @param filePath - 文件路径
 * @returns []string - 中间层路径列表
 */
func getModuleToFilePathPaths(module *knowledgeEntity.Module, filePath string) []string {
	intermediatePaths := make([]string, 0)

	// 获取文件的父目录路径
	fileParentPath := commonUtils.NormalizePath(getParentPath(filePath))
	modulePath := commonUtils.NormalizePath(module.Path)

	// 如果文件的父目录就是 module 路径，说明没有中间层
	if fileParentPath == modulePath {
		return intermediatePaths
	}

	// 如果文件父目录不是 module 的子路径，返回空
	if !strings.HasPrefix(fileParentPath, modulePath+"/") {
		return intermediatePaths
	}

	// 获取从 module 到文件父目录的所有中间层级
	relativePath := fileParentPath[len(modulePath)+1:] // 去掉开头的 "/"
	pathParts := strings.Split(relativePath, "/")

	currentPath := modulePath
	for i := 0; i < len(pathParts); i++ {
		currentPath = currentPath + "/" + pathParts[i]

		// 检查这个路径是否已经作为 submodule 存在
		isSubModule := false
		for _, existingSubModule := range module.SubModules {
			if commonUtils.NormalizePath(existingSubModule.Path) == currentPath {
				isSubModule = true
				break
			}
		}

		// 如果不是 submodule，则添加到中间层路径
		if !isSubModule {
			intermediatePaths = append(intermediatePaths, currentPath)
		}
	}

	return intermediatePaths
}

/**
 * getParentPath 获取路径的父目录
 * @param path - 文件路径
 * @returns string - 父目录路径
 */
func getParentPath(path string) string {
	// 移除末尾的斜杠
	cleanPath := strings.TrimSuffix(path, "/")

	// 查找最后一个斜杠的位置
	lastSlashIndex := strings.LastIndex(cleanPath, "/")
	if lastSlashIndex == -1 {
		return ""
	}

	return cleanPath[:lastSlashIndex]
}

/**
 * FilterEmptyModules 剔除空模块，避免处理大量不必要的空文件夹
 * 如果父级目录已经是空目录，那么子目录就不需要处理了，可以直接剔除
 * @param ctx - 上下文
 * @param groupedRelatedFileInfo - 分组的相关文件信息
 * @return 过滤后的分组文件信息
 */
func FilterEmptyModules(ctx context.Context, knowledgeManager *knowledgeManager.KnowledgeManager, groupedRelatedFileInfo *entity.GroupedRelatedFileInfo) *entity.GroupedRelatedFileInfo {
	// 创建空目录路径的集合，用于快速查找
	emptyPaths := make(map[string]bool)

	// 第一遍：收集所有空目录的路径
	for _, moduleGroup := range groupedRelatedFileInfo.ModuleGroups {
		if len(moduleGroup.SubFileInfos) == 0 {
			emptyPaths[moduleGroup.GroupPath] = true
			// log.V2.Debug().With(ctx).Str("发现空模块组").Str("path", moduleGroup.GroupPath).Emit()
		}
	}

	for _, leafGroup := range groupedRelatedFileInfo.LeafGroups {
		if len(leafGroup.SubFileInfos) == 0 {
			emptyPaths[leafGroup.GroupPath] = true
			// log.V2.Debug().With(ctx).Str("发现空叶子组").Str("path", leafGroup.GroupPath).Emit()
		}
	}

	// 第二遍：过滤模块组，剔除那些父级目录为空的子目录
	filteredModuleGroups := make([]entity.FileContentGroup, 0, len(groupedRelatedFileInfo.ModuleGroups))
	for _, moduleGroup := range groupedRelatedFileInfo.ModuleGroups {
		// 检查父目录是否为空
		parentModule := getParentModule(ctx, knowledgeManager, moduleGroup.GroupPath)
		shouldAdd := true
		for parentModule != nil {
			if emptyPaths[parentModule.Path] {
				// log.V2.Debug().With(ctx).Str("模块组被过滤").Str("path", moduleGroup.GroupPath).Str("parent", parentModule.Path).Emit()
				shouldAdd = false
				break
			}
			parentModule = getParentModule(ctx, knowledgeManager, parentModule.Path)
		}
		if shouldAdd {
			filteredModuleGroups = append(filteredModuleGroups, moduleGroup)
		}
	}

	// 第三遍：过滤叶子组，剔除那些父级目录为空的子目录
	filteredLeafGroups := make([]entity.FileContentGroup, 0, len(groupedRelatedFileInfo.LeafGroups))
	for _, leafGroup := range groupedRelatedFileInfo.LeafGroups {
		// 检查父目录是否为空
		parentModule := getParentModule(ctx, knowledgeManager, leafGroup.GroupPath)
		shouldAdd := true
		for parentModule != nil {
			if emptyPaths[parentModule.Path] {
				// log.V2.Debug().With(ctx).Str("模块组被过滤").Str("path", leafGroup.GroupPath).Str("parent", parentModule.Path).Emit()
				shouldAdd = false
				break
			}
			parentModule = getParentModule(ctx, knowledgeManager, parentModule.Path)
		}
		if shouldAdd {
			filteredLeafGroups = append(filteredLeafGroups, leafGroup)
		}
	}

	log.V2.Info().With(ctx).Str("空模块过滤完成").Str("原始模块组数", fmt.Sprintf("%d", len(groupedRelatedFileInfo.ModuleGroups))).Str("过滤后模块组数", fmt.Sprintf("%d", len(filteredModuleGroups))).Str("原始叶子组数", fmt.Sprintf("%d", len(groupedRelatedFileInfo.LeafGroups))).Str("过滤后叶子组数", fmt.Sprintf("%d", len(filteredLeafGroups))).Emit()

	return &entity.GroupedRelatedFileInfo{
		ModuleGroups: filteredModuleGroups,
		LeafGroups:   filteredLeafGroups,
	}
}

/**
 * DetectDeletedDirectories 检测被删除的目录
 * 对每个删除的文件，找到其父级路径中最顶层被删除的文件夹路径，然后去重收集
 * @param clientTree - 客户端文件树
 * @param deleteFilePaths - 删除的文件路径列表
 * @return []string - 被删除的目录路径列表
 */
func DetectDeletedDirectories(clientTree *merklet.TreeNode, deleteFilePaths []string) []string {
	deletedDirs := make(map[string]bool) // 使用map去重

	for _, filePath := range deleteFilePaths {
		// 找到该文件父级路径中最顶层被删除的目录
		topDeletedDir := findTopDeletedDirectory(clientTree, filePath)
		if topDeletedDir != "" {
			deletedDirs[topDeletedDir] = true
		}
	}

	// 转换为切片
	result := make([]string, 0, len(deletedDirs))
	for dirPath := range deletedDirs {
		result = append(result, dirPath)
	}

	return result
}

/**
 * findTopDeletedDirectory 找到文件父级路径中最顶层被删除的目录
 * @param clientTree - 客户端文件树
 * @param filePath - 文件路径
 * @param deleteFilePaths - 删除的文件路径列表
 * @return string - 最顶层被删除的目录路径，如果没有则返回空字符串
 */
func findTopDeletedDirectory(clientTree *merklet.TreeNode, filePath string) string {
	currentPath := getParentPath(filePath)
	lastDeletedPath := ""

	// 向上遍历父级路径
	for currentPath != "" {
		// 检查当前路径是否为目录且被完全删除
		if isDirectoryCompletelyDeleted(clientTree, currentPath) {
			lastDeletedPath = currentPath
			// 继续向上检查，看是否有更上层的目录也被删除
			currentPath = getParentPath(currentPath)
		} else {
			// 当前路径没有被删除，停止向上查找
			break
		}
	}

	return lastDeletedPath
}

/**
 * isDirectoryCompletelyDeleted 检查目录是否被完全删除
 * @param clientTree - 客户端文件树
 * @param dirPath - 目录路径
 * @return bool - 目录是否被完全删除
 */
func isDirectoryCompletelyDeleted(clientTree *merklet.TreeNode, dirPath string) bool {
	// 获取目录
	node := merklet.GetNodeByPath(clientTree, dirPath)

	// 如果目录不存在，说明被删除了
	return node == nil
}

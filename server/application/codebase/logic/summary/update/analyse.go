package update

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	entity "code.byted.org/ies/codin/application/codebase/entity"
	merklet "code.byted.org/ies/codin/application/codebase/logic/merklet"
	virtualFileEntity "code.byted.org/ies/codin/application/codebase/repo/virtualfile/entity"
	commonUtils "code.byted.org/ies/codin/common/utils"
)

/**
 * VirtualTreeStats 虚拟目录树统计信息
 */
type VirtualTreeStats struct {
	totalFiles int
	totalDirs  int
	totalSize  int64
}

// 解析merkle树
func AnalysisMerkleTree(ctx context.Context, merkleTree []byte) (*merklet.TreeNode, error) {
	uncompressedData, err := merklet.GzipDecompress(merkleTree)
	if err != nil {
		logs.CtxError(ctx, "failed to decompress repoTree: %v", err)
		return nil, err
	}

	// 2. 反序列化为树结构
	repoTree, err := merklet.DeserializeTree(uncompressedData)
	if err != nil {
		logs.CtxError(ctx, "failed to deserialize tree: %v", err)
		return nil, err
	}
	return repoTree, nil
}

func GetVirtualTreeFromMerkleTree(ctx context.Context, merkleTree *merklet.TreeNode, groupedRelatedFileInfo *entity.GroupedRelatedFileInfo, deletePaths []string) *virtualFileEntity.VirtualDirectory {
	rootPath := "/"

	// 如果merkleTree为nil，直接返回空的虚拟目录
	if merkleTree == nil {
		log.V2.Info().With(ctx).Str("merkleTree为nil，返回空虚拟目录").Emit()
		return &virtualFileEntity.VirtualDirectory{
			Path:     rootPath,
			Children: make(map[string]*virtualFileEntity.VirtualFile),
		}
	}

	// 从Merkle树中提取所有文件路径
	merkleFilePaths := extractAllFilePathsFromTree(merkleTree)
	log.V2.Info().With(ctx).Str("从Merkle树提取的文件路径数量").Int(len(merkleFilePaths)).Emit()

	// 从groupedRelatedFileInfo中提取所有文件路径
	groupedFilePaths := ExtractAllFilePathsFromGroupedInfo(groupedRelatedFileInfo)
	log.V2.Info().With(ctx).Str("从groupedRelatedFileInfo提取的文件路径数量").Int(len(groupedFilePaths)).Emit()

	// 合并所有文件路径，去重
	allFilePaths := mergeAndDeduplicatePaths(merkleFilePaths, groupedFilePaths)
	log.V2.Info().With(ctx).Str("合并后的文件路径数量").Int(len(allFilePaths)).Emit()

	// 删除指定的文件路径
	filteredFilePaths := removePathsFromList(allFilePaths, deletePaths)
	log.V2.Info().With(ctx).Str("删除指定文件后的路径数量").Int(len(filteredFilePaths)).Emit()

	// 构造虚拟目录树，并合并groupedRelatedFileInfo中的文件内容
	virtualTree := constructVirtualTreeFromPathsAndContent(filteredFilePaths, rootPath, groupedRelatedFileInfo)

	log.V2.Info().With(ctx).Str("虚拟目录树构造完成").Str("root_path", rootPath).Emit()
	return virtualTree
}

// constructVirtualTreeFromPathsAndContent 从文件路径列表和文件内容构造虚拟目录树
func constructVirtualTreeFromPathsAndContent(filePaths []string, rootPath string, groupedRelatedFileInfo *entity.GroupedRelatedFileInfo) *virtualFileEntity.VirtualDirectory {
	virtualTree := &virtualFileEntity.VirtualDirectory{
		Path:     rootPath,
		Children: make(map[string]*virtualFileEntity.VirtualFile),
	}

	// 创建文件内容映射，用于快速查找
	contentMap := make(map[string]string)

	// 从groupedRelatedFileInfo中提取所有文件内容
	extractFileContentFromGroupedInfo(groupedRelatedFileInfo, contentMap)

	// 注意：这里没有ctx，所以不能使用log.V2.Info().With(ctx)
	// 如果需要日志，可以通过其他方式传递或在这里打印

	// 为每个文件路径创建虚拟文件结构
	for _, filePath := range filePaths {
		// 检查是否有对应的文件内容
		content := contentMap[filePath]

		// 创建虚拟文件
		virtualFile := &virtualFileEntity.VirtualFile{
			Path:     filePath,
			Content:  content, // 如果有内容就使用，没有就是空字符串
			IsDir:    false,
			Size:     int64(len(content)),
			Children: nil,
		}

		// 将文件添加到虚拟树中
		addFileToVirtualTree(virtualTree, filePath, virtualFile)
	}

	return virtualTree
}

// extractFileContentFromGroupedInfo 从GroupedRelatedFileInfo中提取文件内容
func extractFileContentFromGroupedInfo(groupedInfo *entity.GroupedRelatedFileInfo, contentMap map[string]string) {
	// 处理模块组
	for _, moduleGroup := range groupedInfo.ModuleGroups {
		for _, fileInfo := range moduleGroup.SubFileInfos {
			contentMap[fileInfo.FilePath] = fileInfo.FileContent
		}
	}

	// 处理叶子组
	for _, leafGroup := range groupedInfo.LeafGroups {
		for _, fileInfo := range leafGroup.SubFileInfos {
			contentMap[fileInfo.FilePath] = fileInfo.FileContent
		}
	}
}

// extractAllFilePathsFromTree 从Merkle树中提取所有文件路径
func extractAllFilePathsFromTree(node *merklet.TreeNode) []string {
	var filePaths []string
	extractFilePathsRecursive(node, &filePaths)
	return filePaths
}

// extractFilePathsRecursive 递归提取文件路径
func extractFilePathsRecursive(node *merklet.TreeNode, filePaths *[]string) {
	if node == nil {
		return
	}

	if node.Type == "file" {
		*filePaths = append(*filePaths, node.Path)
		return
	}

	// 递归处理子节点
	for _, child := range node.Children {
		extractFilePathsRecursive(child, filePaths)
	}
}

// extractAllFilePathsFromGroupedInfo 从GroupedRelatedFileInfo中提取所有文件路径
func ExtractAllFilePathsFromGroupedInfo(groupedInfo *entity.GroupedRelatedFileInfo) []string {
	var filePaths []string

	// 处理模块组
	for _, moduleGroup := range groupedInfo.ModuleGroups {
		for _, fileInfo := range moduleGroup.SubFileInfos {
			filePaths = append(filePaths, fileInfo.FilePath)
		}
	}

	// 处理叶子组
	for _, leafGroup := range groupedInfo.LeafGroups {
		for _, fileInfo := range leafGroup.SubFileInfos {
			filePaths = append(filePaths, fileInfo.FilePath)
		}
	}

	return filePaths
}

// mergeAndDeduplicatePaths 合并两个路径列表并去重
func mergeAndDeduplicatePaths(paths1, paths2 []string) []string {
	pathSet := make(map[string]bool)

	// 添加第一个列表的路径
	for _, path := range paths1 {
		pathSet[path] = true
	}

	// 添加第二个列表的路径
	for _, path := range paths2 {
		pathSet[path] = true
	}

	// 转换回切片
	result := make([]string, 0, len(pathSet))
	for path := range pathSet {
		result = append(result, path)
	}

	return result
}

/**
 * removePathsFromList 从路径列表中删除指定的路径
 * @param sourcePaths 源路径列表
 * @param deletePaths 需要删除的路径列表
 * @return 删除指定路径后的路径列表
 */
func removePathsFromList(sourcePaths []string, deletePaths []string) []string {
	// 创建删除路径的集合，用于快速查找
	deleteSet := make(map[string]bool)
	for _, path := range deletePaths {
		deleteSet[path] = true
	}

	// 过滤掉需要删除的路径
	result := make([]string, 0, len(sourcePaths))
	for _, path := range sourcePaths {
		if !deleteSet[path] {
			result = append(result, path)
		}
	}

	return result
}

// addFileToVirtualTree 将文件添加到虚拟目录树中
func addFileToVirtualTree(tree *virtualFileEntity.VirtualDirectory, filePath string, virtualFile *virtualFileEntity.VirtualFile) {
	// 分割路径
	parts := strings.Split(filePath, "/")
	if len(parts) == 0 {
		return
	}

	// 创建目录结构
	currentDir := tree
	for i := 0; i < len(parts)-1; i++ {
		part := parts[i]
		if part == "" {
			continue
		}

		// 检查目录是否存在
		if existing, exists := currentDir.Children[part]; exists {
			if !existing.IsDir {
				// 如果存在同名文件，跳过
				continue
			}
			// 如果目录不存在，需要创建
			if existing.Children == nil {
				existing.Children = make(map[string]*virtualFileEntity.VirtualFile)
			}
		} else {
			// 创建新目录
			dirPath := filepath.Join(currentDir.Path, part)
			currentDir.Children[part] = &virtualFileEntity.VirtualFile{
				Path:     dirPath,
				IsDir:    true,
				Size:     0,
				Children: make(map[string]*virtualFileEntity.VirtualFile),
			}
		}

		// 移动到子目录
		currentDir = &virtualFileEntity.VirtualDirectory{
			Path:     filepath.Join(currentDir.Path, part),
			Children: currentDir.Children[part].Children,
		}
	}

	// 添加文件
	fileName := parts[len(parts)-1]
	if fileName == "" {
		return
	}

	currentDir.Children[fileName] = virtualFile
}

/**
 * PrintVirtualTree 返回虚拟目录树的结构字符串
 * @param ctx 上下文
 * @param virtualTree 虚拟目录树
 * @param prefix 打印前缀，用于缩进
 * @return 格式化的虚拟目录树字符串
 */
func PrintVirtualTree(ctx context.Context, virtualTree *virtualFileEntity.VirtualDirectory, prefix string) string {
	if virtualTree == nil {
		log.V2.Warn().With(ctx).Str("虚拟目录树为空").Emit()
		return "虚拟目录树为空"
	}

	if prefix == "" {
		prefix = ""
	}

	var result strings.Builder

	// 添加当前目录
	result.WriteString(fmt.Sprintf("%s📁 %s\n", prefix, virtualTree.Path))

	// 递归添加子项
	result.WriteString(printVirtualTreeChildrenString(ctx, virtualTree.Children, prefix+"  "))

	return result.String()
}

/**
 * printVirtualTreeChildrenString 递归返回虚拟目录树子项的字符串
 * @param ctx 上下文
 * @param children 子项映射
 * @param prefix 打印前缀，用于缩进
 * @return 格式化的子项字符串
 */
func printVirtualTreeChildrenString(ctx context.Context, children map[string]*virtualFileEntity.VirtualFile, prefix string) string {
	if children == nil {
		return ""
	}

	var result strings.Builder

	// 先处理目录，再处理文件
	var dirs []string
	var files []string

	for name, child := range children {
		if child.IsDir {
			dirs = append(dirs, name)
		} else {
			files = append(files, name)
		}
	}

	// 添加目录
	for _, dirName := range dirs {
		child := children[dirName]
		// result.WriteString(fmt.Sprintf("%s📁 %s/\n", prefix, dirName))

		// 递归添加子目录的内容
		if child.Children != nil {
			result.WriteString(printVirtualTreeChildrenString(ctx, child.Children, prefix+"  "))
		}
	}

	// 添加文件
	for _, fileName := range files {
		child := children[fileName]
		// contentPreview := ""
		// if len(child.Content) > 50 {
		// 	contentPreview = child.Content[:50] + "..."
		// } else {
		// 	contentPreview = child.Content
		// }

		// result.WriteString(fmt.Sprintf("%s %s (大小: %d bytes)\n", prefix, fileName, child.Size))
		filePath := commonUtils.GetFilePath(fileName, child.Path)
		result.WriteString(filePath + "\n")
		// if contentPreview != "" {
		// 	result.WriteString(fmt.Sprintf("%s   内容预览: %s\n", prefix, contentPreview))
		// }
	}

	return result.String()
}

/**
 * PrintVirtualTreeSummary 返回虚拟目录树的摘要信息字符串
 * @param ctx 上下文
 * @param virtualTree 虚拟目录树
 * @return 格式化的摘要信息字符串
 */
func PrintVirtualTreeSummary(ctx context.Context, virtualTree *virtualFileEntity.VirtualDirectory) string {
	if virtualTree == nil {
		log.V2.Warn().With(ctx).Str("虚拟目录树为空").Emit()
		return "虚拟目录树为空"
	}

	stats := calculateVirtualTreeStats(virtualTree)

	var result strings.Builder
	result.WriteString("🌳 虚拟目录树摘要:\n")
	result.WriteString(fmt.Sprintf("   总文件数: %d\n", stats.totalFiles))
	result.WriteString(fmt.Sprintf("   总目录数: %d\n", stats.totalDirs))
	result.WriteString(fmt.Sprintf("   总大小: %d bytes\n", stats.totalSize))
	result.WriteString(fmt.Sprintf("   根路径: %s\n", virtualTree.Path))

	return result.String()
}

/**
 * calculateVirtualTreeStats 计算虚拟目录树的统计信息
 * @param virtualTree 虚拟目录树
 * @return 统计信息
 */
func calculateVirtualTreeStats(virtualTree *virtualFileEntity.VirtualDirectory) VirtualTreeStats {
	stats := VirtualTreeStats{}
	calculateStatsRecursive(virtualTree, &stats)
	return stats
}

/**
 * calculateStatsRecursive 递归计算统计信息
 * @param virtualTree 虚拟目录树
 * @param stats 统计信息
 */
func calculateStatsRecursive(virtualTree *virtualFileEntity.VirtualDirectory, stats *VirtualTreeStats) {
	if virtualTree == nil || virtualTree.Children == nil {
		return
	}

	for _, child := range virtualTree.Children {
		if child.IsDir {
			stats.totalDirs++
			if child.Children != nil {
				// 递归计算子目录
				calculateStatsRecursive(&virtualFileEntity.VirtualDirectory{
					Path:     child.Path,
					Children: child.Children,
				}, stats)
			}
		} else {
			stats.totalFiles++
			stats.totalSize += child.Size
		}
	}
}

package update

import (
	"code.byted.org/ies/codin/common/contexts"
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	entity "code.byted.org/ies/codin/application/codebase/entity"
	summaryEntity "code.byted.org/ies/codin/application/codebase/entity"
	"code.byted.org/ies/codin/application/codebase/logic"
	"code.byted.org/ies/codin/application/codebase/logic/agent"
	summaryTools "code.byted.org/ies/codin/application/codebase/logic/summary/tools"
	knowledgeTools "code.byted.org/ies/codin/application/codebase/logic/summary/tools/knowledge"
	"code.byted.org/ies/codin/application/codebase/logic/summary/update/config"
	"code.byted.org/ies/codin/application/codebase/logic/tools"
	"code.byted.org/ies/codin/application/codebase/repo/llm"
	"code.byted.org/ies/codin/application/codebase/utils"
	knowledgeEntity "code.byted.org/ies/codin/common/semantic/summary/entity"
	commonUtils "code.byted.org/ies/codin/common/utils"
	conversationproto "code.byted.org/ies/codinmodel/kitex_gen/conversation"
	"code.byted.org/overpass/capcut_devops_expense/kitex_gen/expense"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

const SummaryAnalyzedAgentId = "update_summary_analyzed"
const SummaryPlanAgentId = "update_summary_plan"
const SummaryExploreAgentId = "update_summary_explore"

// 应该是有一个前置流程根据目录树和变更的文件，判定模块归属，究竟是从属旧的module还是新的module
/**
 * AnalyzeIndependentModule 分析模块的复杂度
 * @param {context.Context} ctx - 上下文
 * @param {*logic.CodeSearch} app - 代码搜索应用实例
 * @param {*entity.FileContentGroup} subModuleGroup - 子模块组
 * @return {error} 错误信息
 */
func AnalyzeIndependentModule(ctx context.Context,
	tempManager *logic.TempManager,
	subModuleGroup *summaryEntity.FileContentGroup,
	localLogParams *entity.LogParams,
	uid string,
) (*summaryEntity.IndependentModuleAnalyzedResult, error) {
	log.V2.Info().With(ctx).Str("[AnalyzeIndependentModule]", "uid:", uid)

	repoCtx := contexts.GetRepoContext(ctx)
	if repoCtx == nil {
		return nil, fmt.Errorf("AnalyzeIndependentModule, repoContext is nil")
	}

	userMessage := processAnalyzedMessage(subModuleGroup, repoCtx.RepoName)
	logKey := "independent_module_analyzed_" + commonUtils.GeneratePathKey(subModuleGroup.GroupPath)
	logDir := "update/independent_module_analyzed"
	logParams := &entity.LogParams{
		RequestKey: logKey,
		Dir:        localLogParams.Dir + "/" + localLogParams.RequestKey + "/" + logDir,
	}
	commonUtils.WriteLogToDir(logParams.RequestKey, logParams.Dir, "user message: \n"+userMessage.Content)
	agentTools := []tool.BaseTool{
		tools.GetThinkTool(),
		tools.GetListDirsTool(tempManager.FileManager),
		tools.GetReadDirTool(tempManager.FileManager),
	}
	searchAgent, err := agent.NewSearchAgent(ctx, &agentTools, llm.SummaryScene, SummaryAnalyzedAgentId)
	commonUtils.WriteLogToDir(logParams.RequestKey, logParams.Dir, "onEnd:\n ")
	if err != nil {
		log.V2.Error().With(ctx).Str("create search agent error").Error(err).Emit()
		return nil, err
	}
	resultJsonStr, err := agent.ReActGenerate(ctx, &agent.ReActGenerateParams{
		RawMessage:       userMessage.Content,
		SystemMessage:    config.AnalyzedIndependentModuleSystemPrompt,
		SearchAgent:      searchAgent,
		LogParams:        logParams,
		Uid:              uid,
		ConversationType: conversationproto.ConversationType_CodeSummary,
		ExpenseScene:     expense.Scene_CodeSummary,
	})
	if err != nil {
		log.V2.Error().With(ctx).
			Str("generate response error").
			Str("uid", uid).
			Error(err).Emit()
		return nil, err
	}
	moduleAnalyzedResult, err := parseAnalyzedIndependentModuleResult(ctx, resultJsonStr)
	return moduleAnalyzedResult, err
}

// 这个需要进行增量更新，而不能重新全量的总结覆盖
func ExploreModuleWithKnowledge(ctx context.Context,
	tempManager *logic.TempManager,
	subModuleGroup *summaryEntity.FileContentGroup,
	module *knowledgeEntity.Module,
	localLogParams *entity.LogParams,
	uid string,
) error {
	repoCtx := contexts.GetRepoContext(ctx)
	if repoCtx == nil {
		return fmt.Errorf("ExploreModuleWithKnowledge, repoContext is nil")
	}

	userMessage := processModuleMessageWithKnowledge(subModuleGroup, module, repoCtx.RepoName)
	logKey := "summary_explore_" + commonUtils.GeneratePathKey(subModuleGroup.GroupPath)
	logDir := "update/update_summary"
	logParams := &entity.LogParams{
		RequestKey: logKey,
		Dir:        localLogParams.Dir + "/" + localLogParams.RequestKey + "/" + logDir,
	}
	return exploreAndUpdateModule(ctx, tempManager, userMessage, logParams, uid)
}

// 更新module的summary、doc、keyFeatures、subModule
func exploreAndUpdateModule(ctx context.Context,
	tempManager *logic.TempManager,
	userMessage *schema.Message,
	logParams *entity.LogParams,
	uid string) error {
	agentTools := []tool.BaseTool{
		tools.GetListDirsTool(tempManager.FileManager),
		tools.GetReadFilesTool(tempManager.FileManager),
		tools.GetReadDirTool(tempManager.FileManager),
		tools.GetThinkTool(),
		summaryTools.GetMissingDirectoryCheckTool(tempManager.FileManager),
		knowledgeTools.GetUpdateSubModulesTool(tempManager.SummaryManager),
		knowledgeTools.GetUpdateFeaturesTool(tempManager.SummaryManager),
		knowledgeTools.GetUpdateSummaryTool(tempManager.SummaryManager),
		knowledgeTools.GetDeleteModuleTool(tempManager.SummaryManager),
	}
	logLine := "user message: \n" + userMessage.Content
	commonUtils.WriteLogToDir(logParams.RequestKey, logParams.Dir, logLine)
	searchAgent, err := agent.NewSearchAgent(ctx, &agentTools, llm.SummaryScene, SummaryExploreAgentId)
	if err != nil {
		log.V2.Error().With(ctx).Str("create search agent error").Error(err).Emit()
		return err
	}
	_, err = agent.ReActGenerate(ctx, &agent.ReActGenerateParams{
		RawMessage:    userMessage.Content,
		SystemMessage: config.UpdateModuleSystemPrompt,
		SearchAgent:   searchAgent,
		LogParams:     logParams,
		Uid:           uid,
		ExpenseScene:  expense.Scene_CodeSummary,
	})
	commonUtils.WriteLogToDir(logParams.RequestKey, logParams.Dir, "onEnd:\n ")
	if err != nil {
		log.V2.Error().With(ctx).Str("generate response error").Error(err).Emit()
		return err
	}
	return nil
}

/**
 * processModuleAnalyzedMessage 处理模块分析消息
 * @param {context.Context} ctx - 上下文
 * @param {*logic.CodeSearch} app - 代码搜索应用实例
 * @param {*entity.FileContentGroup} subModuleGroup - 子模块组
 * @return {*schema.Message} 处理后的消息
 */
func processAnalyzedMessage(subModuleGroup *summaryEntity.FileContentGroup, repoName string) *schema.Message {
	builder := strings.Builder{}
	builder.WriteString("<sub_module_group>\n")
	builder.WriteString("<group_path desc='当前发生变化的模块路径'> \n" + subModuleGroup.GroupPath + "\n</group_path>\n")

	// 输出目录树
	builder.WriteString("<file_tree  desc='当前模块到子模块之间的全量文件树，为空则代表所有文件被删除'>\n")
	dirTree := utils.CollectDirectoryTreeWithOutSubModule(subModuleGroup)
	builder.WriteString(dirTree)
	builder.WriteString("</file_tree>\n")
	builder.WriteString("</sub_module_group>\n")
	builder.WriteString("<repo_name desc='当前模块所在的仓库名称'>\n" + repoName + "\n</repo_name>\n")
	builder.WriteString("你必须记住上述<sub_module_group/>、<repo_name/>的信息，不允许忘记这些信息\n")
	// builder.WriteString("**注意：<file_tree/>中的子文件夹为当前模块的独立下游子模块，在判定是否要做模块拆分的时候，不能考虑这些子文件夹**\n")
	return &schema.Message{
		Role:    "user",
		Content: builder.String(),
	}
}

func processModuleMessageWithKnowledge(subModuleGroup *summaryEntity.FileContentGroup, module *knowledgeEntity.Module, repoName string) *schema.Message {
	builder := strings.Builder{}
	builder.WriteString("<origin_module_path desc='当前模块的原始路径'>\n" + module.Path + "\n</origin_module_path>\n")

	builder.WriteString("<origin_module_knowledge desc='当前模块的原始知识信息'>\n")
	knowledgeJson, _ := json.MarshalIndent(module, "", "  ")
	builder.WriteString(string(knowledgeJson))
	builder.WriteString("</origin_module_knowledge>\n")

	builder.WriteString("<sub_module_group desc='当前发生变化的模块'>\n")
	builder.WriteString("<group_path>: \n" + subModuleGroup.GroupPath + "\n</group_path>\n")

	builder.WriteString("<file_tree desc='当前模块到子模块之间的全量文件树，为空则代表所有文件被删除'>\n")
	dirTree := utils.CollectDirectoryTree(subModuleGroup)
	builder.WriteString(dirTree)
	builder.WriteString("</file_tree>\n")

	builder.WriteString("</sub_module_group>\n")
	builder.WriteString("<repo_name desc='当前模块所在的仓库名称'>\n" + repoName + "\n</repo_name>\n")

	builder.WriteString("你必须记住上述<sub_module_group/>、<origin_module_knowledge/>、<origin_module_path/>、<repo_name/>的信息\n")
	builder.WriteString("**任务: 你当前的唯一任务就是更新" + module.Path + "模块的summary、doc、keyFeatures、subModule的信息。你必须时刻记住你的任务。**\n")
	builder.WriteString("**注意：你必须只更新修改的部分，对于没有修改的知识库，你必须保持和之前origin_module_knowledge一致**\n")
	return &schema.Message{
		Role:    "user",
		Content: builder.String(),
	}
}

func parseAnalyzedIndependentModuleResult(ctx context.Context, result string) (*summaryEntity.IndependentModuleAnalyzedResult, error) {
	var moduleAnalyzedResult summaryEntity.IndependentModuleAnalyzedResult
	// 使用JSON提取器从LLM响应中提取有效的JSON内容
	extractedJSON, extractErr := commonUtils.ExtractJSONFromResponse(result)
	if extractErr != nil {
		log.V2.Error().With(ctx).Str("JSON提取失败: " + extractErr.Error()).Emit()
		return nil, extractErr
	}
	err := json.Unmarshal([]byte(extractedJSON), &moduleAnalyzedResult)
	return &moduleAnalyzedResult, err
}

package builder

import (
	"encoding/json"

	"code.byted.org/ies/codin/application/codebase/entity"
	"code.byted.org/ies/codin/application/codebase/logic/merklet"
	"code.byted.org/overpass/capcut_devops_codebase/kitex_gen/codebase"
)

// 转换逻辑
func ConvertToGroupedRelatedPathInfo(groupedRelatedPathInfo *entity.GroupedRelatedPathInfo) *codebase.GroupedRelatedPathInfo {
	leafGroups := make([]*codebase.DirPathGroup, 0)
	moduleGroups := make([]*codebase.DirPathGroup, 0)
	for _, leafGroup := range groupedRelatedPathInfo.LeafGroups {
		leafGroups = append(leafGroups, &codebase.DirPathGroup{
			GroupPath:   leafGroup.GroupPath,
			SubDirPaths: leafGroup.SubDirPaths,
		})
	}
	for _, moduleGroup := range groupedRelatedPathInfo.ModuleGroups {
		moduleGroups = append(moduleGroups, &codebase.DirPathGroup{
			GroupPath:   moduleGroup.GroupPath,
			SubDirPaths: moduleGroup.SubDirPaths,
		})
	}
	return &codebase.GroupedRelatedPathInfo{
		LeafGroups:   leafGroups,
		ModuleGroups: moduleGroups,
	}
}

/**
 * ConvertToGroupedRelatedFileInfo 将 thrift 生成的 GroupedRelatedFileInfo 转换为 entity 中的 GroupedRelatedFileInfo
 * @param groupedRelatedFileInfo thrift 生成的分组文件信息
 * @return entity 中的分组文件信息
 */
func ParseGroupedRelatedFileInfo(groupedRelatedFileInfoBytes *[]byte) (*entity.GroupedRelatedFileInfo, error) {
	groupedRelatedFileInfo := &entity.GroupedRelatedFileInfo{}
	if groupedRelatedFileInfoBytes == nil || len(*groupedRelatedFileInfoBytes) == 0 {
		return groupedRelatedFileInfo, nil
	}
	uncompressedData, err := merklet.GzipDecompress(*groupedRelatedFileInfoBytes)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(uncompressedData, groupedRelatedFileInfo); err != nil {
		return nil, err
	}
	return groupedRelatedFileInfo, nil
}

package summary

import (
	summaryBuildRepo "code.byted.org/ies/codin/application/codebase/repo/summary_build_record"
	"code.byted.org/ies/codin/application/codesearch/common/mysql"
	"code.byted.org/ies/codin/common/contexts"
	codeBaseManager "code.byted.org/ies/codin/common/semantic/codebase/manager"
	summaryTos "code.byted.org/ies/codin/common/semantic/tos"
	"code.byted.org/ies/codin/common/semantic/tos/config"
	"context"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

var testCtx = contexts.WithRepoContext(contexts.WithLogID(context.Background()), &contexts.RepoContext{
	Uid:      "1c1e9d7c",
	Did:      "ef7f6a67-de9e-578b-9394-40f112214b04",
	RepoPath: "/User/qukecheng/code_dev_ut_dir",
	RepoName: "ies/code-index",
	RepoURL:  "https://code.byted.org/ies/code-index",
	Branch:   "master",
	Language: "",
})

// cleanUnitTestData 用于清理测试数据，在本地或 Gitlab 运行时请求的是 BOE
func cleanUnitTestData(ctx context.Context, summaryKey string) error {
	r := summaryBuildRepo.NewSummaryBuildRecordRepository(mysql.Get())
	return r.DeleteBySummaryKey(ctx, summaryKey)
}

func Test_RepoContext(t *testing.T) {
	testCtx = context.Background()
	repoCtx := contexts.GetRepoContext(testCtx)
	assert.Nil(t, repoCtx) // 此时必然是 nil

	testCtx = contexts.WithRepoContext(testCtx, &contexts.RepoContext{})
	repoCtx = contexts.GetRepoContext(testCtx)
	assert.NotNil(t, repoCtx) // 此时就不是 nil 了

}

func Test_summaryHandler_Summary_CreateAndUpdate(t *testing.T) {
	// 三个优先级的 summary key
	summaryKeys := []string{
		codeBaseManager.GetSummaryUserKnowledgeIdByCtx(testCtx),
		codeBaseManager.GetSummaryKnowledgeBranchIdByCtx(testCtx),
		codeBaseManager.GetSummaryKnowledgebaseIdByCtx(testCtx),
	}

	defer func() {
		t.Log("============ 清理测试数据 ============")
		for _, sk := range summaryKeys {
			assert.Nil(t, cleanUnitTestData(testCtx, sk))
		}
	}()

	t.Log("============ 测试创建索引 ============")
	// 构建索引
	rootMerkleID := "10031303946593115898"
	err := SummaryHandler.CreateSummaryIndex(testCtx, rootMerkleID)
	assert.Nil(t, err)

	tosManager := summaryTos.NewSummaryTosManager()
	err = tosManager.InitTosClientIfNeeded(config.GetSummaryStorageConfig())
	assert.Nil(t, err)

	for _, sk := range summaryKeys {
		t.Logf("summary key: %v", sk)
		assert.True(t, lo.IsNotEmpty(sk))

		// TOS 上面必须有 3 个优先级的 summary 数据
		data, tosErr := tosManager.DownloadFileFromTos(testCtx, sk)
		assert.Nil(t, tosErr)
		assert.True(t, len(data) > 0)
		t.Log(cast.ToString(data))

		// DB 里面必须有 3 个优先级的 summary 记录，并且状态是成功的
		summaryRecord, getErr := SummaryHandler.summaryBuildRecordRepo.GetBySummaryKey(testCtx, sk)
		assert.Nil(t, getErr)
		assert.Equal(t, summaryRecord.SummaryKey, sk)
		assert.Equal(t, summaryRecord.RootMerkleID, rootMerkleID)
		assert.Equal(t, summaryRecord.State, summaryBuildRepo.SummaryBuildStateSucceeded)
	}

	// 已有的数据再次创建会立刻返回
	startTime := time.Now()
	err = SummaryHandler.CreateSummaryIndex(testCtx, rootMerkleID)
	assert.Nil(t, err)
	timeCost := time.Since(startTime).Seconds()
	t.Log(timeCost)
	assert.True(t, timeCost <= 1)

	t.Log("============ 测试更新索引 ============")

	baseUserKnowledgeId := codeBaseManager.GetSummaryUserKnowledgeIdByCtx(testCtx)
	clientMerkleId := "10031303946593115898"
	merkleTreeDownloadKey := clientMerkleId
	groupedRelatedFileInfoDownloadKey := "file/5287299671821406655_18316146788427460897"

	// 调用更新 summary，当前是异步调用，所以会很快返回
	err = SummaryHandler.UpdateSummaryIndex(testCtx, baseUserKnowledgeId, clientMerkleId, merkleTreeDownloadKey, groupedRelatedFileInfoDownloadKey)
	assert.Nil(t, err)

	// 设置超时时间为 3min，检查间隔为 1min. 可根据实际情况调整
	repo := summaryBuildRepo.NewSummaryBuildRecordRepository(mysql.Get())
	updateTestOk := false
	timeout := time.After(3 * time.Minute)
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

loop:
	for {
		select {
		case <-timeout:
			// 超时失败
			t.Log("更新摘要超时，3分钟内未完成")
			break loop // 跳出整个循环
		case <-ticker.C:
			successCount := 0
			buildingCount := 0
			failedCount := 0
			for _, sk := range summaryKeys {
				summaryRecord, getErr := repo.GetBySummaryKey(testCtx, sk)
				assert.Nil(t, getErr)
				switch summaryRecord.State {
				case summaryBuildRepo.SummaryBuildStateBuilding:
					buildingCount++
				case summaryBuildRepo.SummaryBuildStateSucceeded:
					successCount++
				case summaryBuildRepo.SummaryBuildStateFailed:
					failedCount++
				}
			}

			assert.True(t, failedCount == 0)      // 有一个失败，则 UT 失败
			if successCount == len(summaryKeys) { // 全部都成功了则跳出去
				t.Log("全部更新成功！")
				updateTestOk = true
				break loop
			}

			t.Logf("update test, successCount=%v, buildingCount=%v, failedCount=%v", successCount, buildingCount, failedCount)
			t.Log("摘要尚未更新完成，继续等待...")
		}
	}

	// 处理未通过的情况
	assert.True(t, updateTestOk)

	t.Log("============ 单测运行完成 ============")
}

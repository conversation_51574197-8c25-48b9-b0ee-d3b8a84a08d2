package summary

import (
	"code.byted.org/gopkg/jsonx"
	"code.byted.org/gopkg/logs/v2"
	codebaseEntity "code.byted.org/ies/codin/application/codebase/entity"
	"code.byted.org/ies/codin/application/codebase/logic/summary/builder"
	"code.byted.org/ies/codin/application/codebase/logic/summary/update"
	"code.byted.org/ies/codin/application/codebase/logic/summary/update/debug"
	summaryUpdate "code.byted.org/ies/codin/application/codebase/logic/summary/update/summary_update"
	summaryBuildRepo "code.byted.org/ies/codin/application/codebase/repo/summary_build_record"
	"code.byted.org/ies/codin/application/codebase/repo/workspace"
	"code.byted.org/ies/codin/application/codesearch/common/mysql"
	"code.byted.org/ies/codin/common/contexts"
	"code.byted.org/ies/codin/common/group"
	"code.byted.org/ies/codin/common/rpcerr"
	commonCodebaseEntity "code.byted.org/ies/codin/common/semantic/codebase/entity"
	commonCodebaseManager "code.byted.org/ies/codin/common/semantic/codebase/manager"
	knowledgeEntity "code.byted.org/ies/codin/common/semantic/summary/entity"
	"code.byted.org/ies/codin/common/semantic/tos/config"
	"code.byted.org/ies/codin/common/tos"
	"code.byted.org/overpass/capcut_devops_codebase/kitex_gen/codebase"
	"context"
	"errors"
	"fmt"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	"strings"
	"time"
)

var SummaryHandler = newSummaryHandler(mysql.Get())

type summaryHandler struct {
	summaryBuildRecordRepo *summaryBuildRepo.SummaryBuildRecordRepository
}

func newSummaryHandler(db *gorm.DB) *summaryHandler {
	return &summaryHandler{
		summaryBuildRecordRepo: summaryBuildRepo.NewSummaryBuildRecordRepository(db),
	}
}

func (s *summaryHandler) CreateSummaryIndex(ctx context.Context, merkleId string) error {
	logs.CtxInfo(ctx, "SummaryHandler.CreateSummaryIndex, start, merkleId=%v", merkleId)

	// repoCtx 必须存在
	repoCtx := contexts.GetRepoContext(ctx)
	if repoCtx == nil {
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, fmt.Sprintf("repoCtx is nil, merkleId=%v", merkleId))
	}
	logs.CtxInfo(ctx, "summaryHandler.CreateSummaryIndex, repoCtx=%v", jsonx.ToString(repoCtx))

	// 拿到当前请求的 summary key（用户级别）
	userKnowledgeId := commonCodebaseManager.GetSummaryUserKnowledgeIdByCtx(ctx)
	logs.CtxInfo(ctx, "summaryHandler.CreateSummaryIndex, userKnowledgeId=%v", userKnowledgeId)

	// 如果已经有了那么就不用创建
	summaryBuildRecordExist, err := s.summaryBuildRecordRepo.Exist(ctx, mysql.NewListQuery().Set("SummaryKey", userKnowledgeId))
	if err != nil {
		logs.CtxError(ctx, "summaryHandler.CreateSummaryIndex, summary build record select error, %v", err)
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, err.Error())
	}
	if summaryBuildRecordExist {
		logs.CtxInfo(ctx, "summaryHandler.CreateSummaryIndex, summary build record already exists, SummaryKey: %s", userKnowledgeId)
		return nil
	}

	// 设置参数结构
	var getSummaryDataRequest = &commonCodebaseEntity.GetSummaryDataRequest{
		Uid:      repoCtx.Uid,
		RepoPath: repoCtx.RepoPath,
		Did:      repoCtx.Did,
		RepoName: repoCtx.RepoName,
		Branch:   repoCtx.Branch,
	}
	logs.CtxInfo(ctx, "summaryHandler.CreateSummaryIndex, getSummaryDataRequest=%v", jsonx.ToString(getSummaryDataRequest))

	// 1. 上传缺省 Summary 数据到 TOS
	if err = summaryUpdate.UploadSummaryData(ctx, &commonCodebaseEntity.SummaryData{
		Knowledge: knowledgeEntity.Knowledge{
			Modules:   []knowledgeEntity.Module{},
			UpdatedAt: time.Now(),
		},
		MerkleId: merkleId,
	}, getSummaryDataRequest); err != nil {
		logs.CtxError(ctx, "summaryHandler.CreateSummaryIndex, summary upload error, %v", err)
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, err.Error())
	}

	// 2. 状态置为构建中
	if err = summaryUpdate.UpdateMetaStatus(ctx, getSummaryDataRequest, merkleId, summaryBuildRepo.SummaryBuildStateBuilding); err != nil {
		logs.CtxError(ctx, "summaryHandler.CreateSummaryIndex, summary UpdateMetaStatus, error, %v", err)
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, err.Error())
	}

	defer func() {
		if err != nil {
			logs.CtxError(ctx, "summaryHandler.CreateSummaryIndex, 创建索引失败, getSummaryDataRequest=%v, merkleId=%v", jsonx.ToString(getSummaryDataRequest), merkleId)
			_ = summaryUpdate.UpdateMetaStatus(ctx, getSummaryDataRequest, merkleId, summaryBuildRepo.SummaryBuildStateFailed)
			return
		}
		logs.CtxInfo(ctx, "summaryHandler.CreateSummaryIndex, 创建索引成功, getSummaryDataRequest=%v, merkleId=%v", jsonx.ToString(getSummaryDataRequest), merkleId)
		_ = summaryUpdate.UpdateMetaStatus(ctx, getSummaryDataRequest, merkleId, summaryBuildRepo.SummaryBuildStateSucceeded)
	}()

	// 3. 开始 summary 过程
	var (
		logKey = "summary_" + repoCtx.RepoName + "_" + repoCtx.Branch
		logDir = repoCtx.RepoName + "_" + repoCtx.Branch + "/summary"
		depth  = 3
	)

	// 初始化业务和workspace
	if err = workspace.Workspace.Add(ctx, repoCtx.RepoName, repoCtx.RepoURL, repoCtx.Branch); err != nil {
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, err.Error())
	}

	// 初始化临时管理器
	tempManager := InitializeTempManager(ctx, &CreateSummaryRequest{
		RepoName: repoCtx.RepoName,
		RepoURL:  repoCtx.RepoURL,
		Branch:   repoCtx.Branch,
		Uid:      repoCtx.Uid,
	})

	// 创建计划执行器
	taskProcessor := &DefaultTaskProcessor{}
	planExecutor := NewPlanExecutor(taskProcessor, 30) // 最大并发数30

	// 执行计划
	params := &TaskProcessParams{
		Depth:       depth,
		LogKey:      logKey,
		LogDir:      logDir,
		TempManager: tempManager,
		Uid:         repoCtx.Uid,
	}
	if err = planExecutor.ExecutePlans(ctx, params); err != nil {
		logs.CtxError(ctx, "summaryHandler.CreateSummaryIndex, planExecutor.ExecutePlans, 执行计划失败, error, %v", err)
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, err.Error())
	}
	logs.CtxDebug(ctx, "summaryHandler.CreateSummaryIndex, ---------------执行计划完成-----------------")

	// 将产出的摘要数据上传到 TOS
	knowledgeData := tempManager.SummaryManager.GetKnowledge(ctx)
	if err = summaryUpdate.UploadSummaryData(ctx, &commonCodebaseEntity.SummaryData{
		Knowledge: lo.FromPtr(knowledgeData),
		MerkleId:  merkleId,
	}, getSummaryDataRequest); err != nil {
		logs.CtxError(ctx, "summaryHandler.CreateSummaryIndex, summary result upload error, %v", err)
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, err.Error())
	}

	logs.CtxDebug(ctx, "summaryHandler.CreateSummaryIndex, ---------------执行构建完成-----------------")
	return nil
}

/*
前端返回模块的全部文件内容，然后后端做summary更新
1、先上传前端传来的merkle树
2、然后下载summaryData
3、通过summary中的数据还原knowledge，通过前端的merkle树和前端的groupedRelatedFileInfo还原虚拟文件系统
4、然后根据groupedRelatedFileInfo开始分module，更新summary
这里有两块，一块是module，一块是leaf，我们不区分删除和变更，统一都当做变更处理
  - 对于module，我们每次拿到module到subModule中间这一层的全量信息，所以每次是module整体还原分析summary，然后基于原始的summary和变更的文件做融合分析，更新summary、keyFeatures、subModule
    我们不需要关注删除的情况，删除也是当做代码变更，因为每次不只带增量，每次都会带上全量的文件，所以删除的情况下，自然就会少了那个被删除的文件，然后重新分析
  - 对于叶子节点，我们每次会拿到叶子模块路径下面的全部的所有文件路径和文件内容，然后做分析，更新模块的summary、keyFeatures、subModule
  - 在分析流程，我们会分析这次的全量模块文件，是否足够拆分出另外的独立的子模块，如果可以拆出子模块，则会继续分析（处理一个超大的代码变更，改动1万个文件，那么我们就会拆分成一个独立的模块）
    （如果复杂度超过阈值，那么就新增一个module，如果没那么复杂就更新一下原有的Module的summary）

5、分析结束之后，把当前最新的summary带上merkleId更新到云端

注意：ProcessCodeChangesAndAnalyzeModules 是长耗时任务，现在改为异步执行，请求会立即返回。
状态说明：
- Status "0": 更新中
- Status "1": 更新完成
- Status "2": 更新失败
*/
func (s *summaryHandler) UpdateSummaryIndex(ctx context.Context, baseUserKnowledgeId, clientMerkleId, merkleTreeDownloadKey, groupedRelatedFileInfoDownloadKey string) error {
	logs.CtxInfo(ctx, "SummaryHandler.UpdateSummaryIndex, start, baseUserKnowledgeId=%v, clientMerkleId=%v, merkleTreeDownloadKey=%v, groupedRelatedFileInfoDownloadKey=%v",
		baseUserKnowledgeId, clientMerkleId, merkleTreeDownloadKey, groupedRelatedFileInfoDownloadKey)

	// repoCtx 必须存在
	repoCtx := contexts.GetRepoContext(ctx)
	if repoCtx == nil {
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, fmt.Sprintf("repoCtx is nil, clientMerkleId=%v", clientMerkleId))
	}
	logs.CtxInfo(ctx, "summaryHandler.UpdateSummaryIndex, repoCtx=%v", jsonx.ToString(repoCtx))

	var (
		requestKey = strings.ReplaceAll(fmt.Sprintf("%s_%s_%s_%s", time.Now().Format(time.DateTime), repoCtx.Uid, repoCtx.RepoName, repoCtx.Branch), "/", "_")
		logDir     = "update_summary"
		logParams  = &codebaseEntity.LogParams{
			RequestKey: requestKey,
			Dir:        logDir,
		}
	)

	// 获取当前 用户级别的 summary key
	summaryUserKnowledgeId := commonCodebaseManager.GetSummaryUserKnowledgeIdByCtx(ctx)
	logs.CtxInfo(ctx, "summaryHandler.UpdateSummaryIndex, summaryUserKnowledgeId=[%v]", summaryUserKnowledgeId)

	// 如果当前这个分支索引已经在更新中了，那么就直接返回. 由前端控制等待逻辑
	summaryBuildRecord, err := s.summaryBuildRecordRepo.GetBySummaryKey(ctx, summaryUserKnowledgeId)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("当前索引尚未被构建, summaryKey=%v, err=%v", summaryUserKnowledgeId, err)
		}
		summaryBuildRecord = &summaryBuildRepo.SummaryBuildRecord{}
	}
	if summaryBuildRecord.State == summaryBuildRepo.SummaryBuildStateBuilding {
		logs.CtxInfo(ctx, "--------------- summary_is_building")
		return nil
	}

	// 先准备一下数据，并发下载 baseSummaryData、clientTree、groupedRelatedFileInfoBytes
	baseSummaryData, clientTree, groupedRelatedFileInfoBytes, err := s.prepareUpdateData(ctx, baseUserKnowledgeId, merkleTreeDownloadKey, groupedRelatedFileInfoDownloadKey)
	if err != nil {
		logs.CtxError(ctx, "summaryHandler.UpdateSummaryIndex, prepareUpdateData failed, err=%v", err)
		return err
	}
	logs.CtxInfo(ctx, "summaryHandler.UpdateSummaryIndex, 准备数据完成, baseSummaryData=%v, clientTree=%v, groupedRelatedFileInfoBytes=%v", baseUserKnowledgeId, merkleTreeDownloadKey, groupedRelatedFileInfoDownloadKey)

	var updateSummaryRequest = &codebase.UpdateSummaryRequest{
		MerkleTreeDownloadKey:             merkleTreeDownloadKey,
		Uid:                               repoCtx.Uid,
		RepoName:                          repoCtx.RepoName,
		Branch:                            repoCtx.Branch,
		RepoPath:                          repoCtx.RepoPath,
		Did:                               repoCtx.Did,
		GroupedRelatedFileInfoDownloadKey: lo.ToPtr(groupedRelatedFileInfoDownloadKey),
		BaseUserKnowledgeId:               baseUserKnowledgeId,
		ClientMerkleId:                    clientMerkleId,
	}
	go debug.BuildUpdateSummaryContext(ctx, baseSummaryData, updateSummaryRequest, logParams, groupedRelatedFileInfoBytes, clientTree)

	// 将 groupedRelatedFileInfoBytes 转换结构
	groupedRelatedFileInfo, err := builder.ParseGroupedRelatedFileInfo(&groupedRelatedFileInfoBytes)
	if err != nil {
		logs.CtxError(ctx, "summaryHandler.UpdateSummaryIndex, ParseGroupedRelatedFileInfo failed, err=%v", err)
		go debug.BuildUpdateSummaryContext(ctx, baseSummaryData, updateSummaryRequest, logParams, groupedRelatedFileInfoBytes, clientTree)
		return rpcerr.Wrap(err, codebase.ErrCode_ParseFileFailed, fmt.Sprintf("summary marshal failed"))
	}

	// 初始化 TempManager
	merkleTosManager := tos.NewTosManager()
	if err = merkleTosManager.InitTosClientIfNeeded(config.GetMerkleStorageConfig()); err != nil {
		logs.CtxError(ctx, "summaryHandler.UpdateSummaryIndex, init tos failed, err=%v", err)
		go debug.BuildUpdateSummaryContext(ctx, baseSummaryData, updateSummaryRequest, logParams, groupedRelatedFileInfoBytes, clientTree)
		return rpcerr.New(codebase.ErrCode_InitTosClientFailed, err.Error())
	}
	tempManager, err := InitTempManager(ctx, baseSummaryData, clientTree, groupedRelatedFileInfo)
	if err != nil {
		logs.CtxError(ctx, "summaryHandler.UpdateSummaryIndex, init temp manager failed, err=%v", err)
		return rpcerr.Wrap(err, codebase.ErrCode_DownloadFileFailed, fmt.Sprintf("init temp manager failed"))
	}
	logs.CtxInfo(ctx, "summaryHandler.UpdateSummaryIndex, prepare info success, merkleId=%v", clientMerkleId)

	var getSummaryDataRequest = &commonCodebaseEntity.GetSummaryDataRequest{
		Uid:      repoCtx.Uid,
		RepoPath: repoCtx.RepoPath,
		Did:      repoCtx.Did,
		RepoName: repoCtx.RepoName,
		Branch:   repoCtx.Branch,
	}

	// 剔除空模块，避免处理大量不必要的空文件夹
	groupedRelatedFileInfo = update.FilterEmptyModules(ctx, tempManager.SummaryManager, groupedRelatedFileInfo)

	// 更新索引预备，此时需要先将 [用户级别的摘要] 状态设置为 building
	if err = summaryUpdate.UpdateUserMetaStatus(ctx, getSummaryDataRequest, clientMerkleId, summaryBuildRepo.SummaryBuildStateBuilding); err != nil {
		logs.CtxError(ctx, "summaryHandler.UpdateSummaryIndex, UpdateUserMetaStatus failed, err=%v", err)
		return err
	}

	// 异步执行 summary 分析更新，设置 300 分钟超时.
	// TODO: 目前博提做了异步任务，后续等异步任务正式运行了，这部分理论上不会在 TCE 运行，而是去 CronJob，那时候这里就不可以放在协程里，不然会有问题
	ctx = group.BuildAsyncCtx(ctx)
	_ = group.Go(ctx, 300*time.Minute, func(ctx context.Context) {
		logs.CtxInfo(ctx, "summaryHandler.UpdateSummaryIndex, 开始更新 summary 数据, baseUserKnowledgeId=%v", baseUserKnowledgeId)
		repoCtx = contexts.GetRepoContext(ctx)
		// 只要运行完了有 err 就把用户级别的摘要记录变成失败状态；没有 err 则把三个级别的摘要数据全部设置为成功状态
		defer func() {
			if err != nil {
				logs.CtxError(ctx, "summaryHandler.UpdateSummaryIndex, 更新索引失败, getSummaryDataRequest=%v, merkleId=%v", jsonx.ToString(getSummaryDataRequest), clientMerkleId)
				_ = summaryUpdate.UpdateUserMetaStatus(ctx, getSummaryDataRequest, clientMerkleId, summaryBuildRepo.SummaryBuildStateFailed)
				return
			}

			// 如果最终索引更新成功了，那么需要把userKnowledgeId, knowledgeBranchId, knowledgebaseId 的summary都更新
			logs.CtxInfo(ctx, "summaryHandler.UpdateSummaryIndex, 更新索引成功, getSummaryDataRequest=%v, merkleId=%v", jsonx.ToString(getSummaryDataRequest), clientMerkleId)
			_ = summaryUpdate.UpdateMetaStatus(ctx, getSummaryDataRequest, clientMerkleId, summaryBuildRepo.SummaryBuildStateSucceeded)
		}()

		// 更新摘要
		err = ProcessCodeChangesAndAnalyzeModules(ctx, &ProcessCodeChangesAndAnalyzeModulesParams{
			TempManager:            tempManager,
			GroupedRelatedFileInfo: groupedRelatedFileInfo,
			LogParams:              logParams,
			MaxConcurrency:         20,
			Uid:                    repoCtx.Uid,
		})
		if err != nil {
			logs.CtxInfo(ctx, "summaryHandler.UpdateSummaryIndex, ProcessCodeChangesAndAnalyzeModules failed, err=%v", err)
			return
		}

		// 上传最新的摘要数据
		err = summaryUpdate.UploadSummaryData(ctx, &commonCodebaseEntity.SummaryData{
			Knowledge: *tempManager.SummaryManager.GetKnowledge(ctx),
			MerkleId:  clientMerkleId,
		}, getSummaryDataRequest)
		if err != nil {
			logs.CtxError(ctx, "summaryHandler.UpdateSummaryIndex, UploadSummaryData failed, err=%v", err)
			return
		}

		logs.CtxInfo(ctx, "summaryHandler.UpdateSummaryIndex, DONE, repoCtx=%v", jsonx.ToString(repoCtx))
	})
	return nil
}

func (s *summaryHandler) prepareUpdateData(ctx context.Context, baseKnowledgeId, merkleTreeDownloadKey, groupedRelatedFileInfoDownloadKey string) (baseSummaryData *commonCodebaseEntity.SummaryData, clientTree, groupedRelatedFileInfoBytes []byte, err error) {
	logs.CtxInfo(ctx, "summaryHandler.prepareUpdateData, baseKnowledgeId=%v", baseKnowledgeId)
	logs.CtxInfo(ctx, "summaryHandler.prepareUpdateData, merkleTreeDownloadKey=%v", merkleTreeDownloadKey)
	logs.CtxInfo(ctx, "summaryHandler.prepareUpdateData, groupedRelatedFileInfoDownloadKey=%v", groupedRelatedFileInfoDownloadKey)

	var baseSummaryDataErr, clientTreeErr, groupedRelatedFileInfoErr error
	workers := errgroup.Group{}
	workers.SetLimit(5)

	// 获取基准摘要数据
	workers.Go(func() error {
		baseSummaryData, baseSummaryDataErr = summaryUpdate.GetUserSummaryData(ctx, baseKnowledgeId)
		return baseSummaryDataErr
	})

	// 获取客户端 MerkleTree
	workers.Go(func() error {
		merkleTosManager := tos.NewTosManager()
		if initTosErr := merkleTosManager.InitTosClientIfNeeded(config.GetMerkleStorageConfig()); initTosErr != nil {
			return rpcerr.New(codebase.ErrCode_InitTosClientFailed, initTosErr.Error())
		}
		clientTree, clientTreeErr = merkleTosManager.DownloadFileFromTos(ctx, merkleTreeDownloadKey)
		return clientTreeErr
	})

	// 获取 groupedRelatedFileInfoBytes
	if groupedRelatedFileInfoDownloadKey != "" {
		workers.Go(func() error {
			summaryTosManager := tos.NewTosManager()
			if initTosErr := summaryTosManager.InitTosClientIfNeeded(config.GetSummaryStorageConfig()); initTosErr != nil {
				return rpcerr.New(codebase.ErrCode_InitTosClientFailed, initTosErr.Error())
			}
			groupedRelatedFileInfoBytes, groupedRelatedFileInfoErr = summaryTosManager.DownloadFileFromTos(ctx, groupedRelatedFileInfoDownloadKey)
			return groupedRelatedFileInfoErr
		})
	}

	// 等待并行跑完
	if waitErr := workers.Wait(); waitErr != nil {
		logs.CtxError(ctx, "summaryHandler.prepareUpdateData, waitErr=%v", waitErr)
	}

	// 处理并行过程里的错误
	if baseSummaryDataErr != nil {
		return nil, nil, nil,
			rpcerr.Wrap(baseSummaryDataErr,
				codebase.ErrCode_DownloadFileFailed,
				fmt.Sprintf("get summary data failed, baseKnowledgeId=%v, merkleTreeDownloadKey=%v, groupedRelatedFileInfoDownloadKey=%v", baseKnowledgeId, merkleTreeDownloadKey, groupedRelatedFileInfoDownloadKey))
	}

	if clientTreeErr != nil {
		return nil, nil, nil,
			rpcerr.Wrap(clientTreeErr,
				codebase.ErrCode_DownloadFileFailed,
				fmt.Sprintf("download merkle tree failed, baseKnowledgeId=%v, merkleTreeDownloadKey=%v, groupedRelatedFileInfoDownloadKey=%v", baseKnowledgeId, merkleTreeDownloadKey, groupedRelatedFileInfoDownloadKey))
	}

	if groupedRelatedFileInfoErr != nil {
		return nil, nil, nil,
			rpcerr.Wrap(groupedRelatedFileInfoErr,
				codebase.ErrCode_DownloadFileFailed,
				fmt.Sprintf("download grouped related file info failed, baseKnowledgeId=%v, merkleTreeDownloadKey=%v, groupedRelatedFileInfoDownloadKey=%v", baseKnowledgeId, merkleTreeDownloadKey, groupedRelatedFileInfoDownloadKey))
	}

	return baseSummaryData, clientTree, groupedRelatedFileInfoBytes, nil
}

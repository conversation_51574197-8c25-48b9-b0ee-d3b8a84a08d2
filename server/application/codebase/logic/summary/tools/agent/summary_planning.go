package agentTools

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	codeBaseEntity "code.byted.org/ies/codin/application/codebase/entity"
	"code.byted.org/ies/codin/application/codebase/logic"
	agent "code.byted.org/ies/codin/application/codebase/logic/agent"
	prompt "code.byted.org/ies/codin/application/codebase/logic/summary/prompt"
	summaryTools "code.byted.org/ies/codin/application/codebase/logic/summary/tools"
	"code.byted.org/ies/codin/application/codebase/logic/tools"
	planTools "code.byted.org/ies/codin/application/codebase/logic/tools/plan"
	"code.byted.org/ies/codin/application/codebase/repo/llm"
	"code.byted.org/ies/codin/common/utils"
	conversationproto "code.byted.org/ies/codinmodel/kitex_gen/conversation"
	"code.byted.org/overpass/capcut_devops_expense/kitex_gen/expense"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type SummaryPlanningParams struct {
	Path     string `json:"path"`
	Depth    int    `json:"depth"`
	Strategy string `json:"strategy"`
	RepoName string `json:"repo_name"`
	Uid      string `json:"uid"`
}

const SummaryPlanningAgentId = "summary_planning"

func ExecuteSummaryPlanning(ctx context.Context, tempManager *logic.TempManager, p *SummaryPlanningParams, logDir string) (string, error) {
	module := tempManager.SummaryManager.GetModule(ctx, p.Path)
	if module != nil {
		return "该模块已经规划分析过了，不需要重复规划探索", nil
	}
	if tempManager.FileManager == nil {
		log.V2.Error().With(ctx).Str("file manager is nil").Emit()
		return "文件管理器为空", fmt.Errorf("file manager is nil")
	}
	logKey := utils.GeneratePathKey(p.Path)
	dir := logDir + "/code_summary_plan"
	logParams := &codeBaseEntity.LogParams{
		RequestKey: logKey,
		Dir:        dir,
	}
	userMessage := processPlanningMessage(ctx, p)

	agentTools := []tool.BaseTool{
		tools.GetListDirsTool((*tempManager).FileManager),
		tools.GetThinkTool(),
		tools.GetReadDirTool((*tempManager).FileManager),
		// 任务规划和执行相关
		planTools.GetCreatePlanTool(tempManager),
		summaryTools.GetMissingDirectoryCheckTool((*tempManager).FileManager),
	}
	searchAgent, err := agent.NewSearchAgent(ctx, &agentTools, llm.SummaryScene, SummaryPlanningAgentId)
	if err != nil {
		log.V2.Error().With(ctx).Str("create search agent error").Error(err).Emit()
		return "创建规划Agent失败" + err.Error(), err
	}
	utils.WriteLogToDir(logParams.RequestKey, logParams.Dir, "userMessage:\n "+userMessage.Content)
	resultJsonStr, err := agent.ReActGenerate(ctx, &agent.ReActGenerateParams{
		RawMessage:       userMessage.Content,
		SystemMessage:    prompt.SummaryPlanningSystemPrompt,
		SearchAgent:      searchAgent,
		LogParams:        logParams,
		Uid:              p.Uid,
		ConversationType: conversationproto.ConversationType_CodeSummary,
		ExpenseScene:     expense.Scene_CodeSummary,
	})
	utils.WriteLogToDir(logParams.RequestKey, logParams.Dir, "onEnd:\n ")
	if err != nil {
		log.V2.Error().With(ctx).Str("generate response error").Error(err).Emit()
		return "规划Agent生成失败" + err.Error(), err
	}
	return resultJsonStr, nil
}

func processPlanningMessage(ctx context.Context, params *SummaryPlanningParams) *schema.Message {
	builder := strings.Builder{}
	builder.WriteString("扫描指定路径之下的代码，并制定详细的分析计划，必须严格按照路径扫描，不能扫描上层路径\n")
	builder.WriteString("<config>\n")
	builder.WriteString("path: " + params.Path + "\n")
	builder.WriteString("depth: " + strconv.Itoa(params.Depth) + "\n")
	builder.WriteString("repo_name: " + params.RepoName + "\n")
	builder.WriteString("</config>\n")
	builder.WriteString("你必须记住上述<config/>配置信息，不允许忘记这些信息\n")
	return &schema.Message{
		Role:    "user",
		Content: builder.String(),
	}
}

package agentTools

import (
	"context"
	"fmt"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codebase/entity"
	"code.byted.org/ies/codin/application/codebase/logic"
	"code.byted.org/ies/codin/application/codebase/logic/agent"
	prompt "code.byted.org/ies/codin/application/codebase/logic/summary/prompt"
	summaryTools "code.byted.org/ies/codin/application/codebase/logic/summary/tools"
	knowledgeTools "code.byted.org/ies/codin/application/codebase/logic/summary/tools/knowledge"
	"code.byted.org/ies/codin/application/codebase/logic/tools"
	"code.byted.org/ies/codin/application/codebase/repo/llm"
	commonUtils "code.byted.org/ies/codin/common/utils"
	conversationproto "code.byted.org/ies/codinmodel/kitex_gen/conversation"
	"code.byted.org/overpass/capcut_devops_expense/kitex_gen/expense"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type SummaryExploreParams struct {
	Path            string `json:"path"`
	RepoName        string `json:"repo_name"`
	Depth           int    `json:"depth"`
	IsComplexModule bool   `json:"is_complex_module"`
	Task            string `json:"task"`
	Uid             string `json:"uid"`
}

const SummaryExploreAgentId = "summary_explore"

func ExecuteSummaryExplore(ctx context.Context, tempManager *logic.TempManager, p *SummaryExploreParams, logDir string) (string, error) {
	module := tempManager.SummaryManager.GetModule(ctx, p.Path)
	if module != nil {
		return "该模块已经规划分析过了，不需要重复规划探索", nil
	}
	if tempManager.FileManager == nil {
		log.V2.Error().With(ctx).Str("file manager is nil").Emit()
		return "文件管理器为空", fmt.Errorf("file manager is nil")
	}
	// 生成日志键 / Generate log key
	logKey := commonUtils.GeneratePathKey(p.Path)
	dir := logDir + "/code_summary_explore"
	logParams := &entity.LogParams{
		RequestKey: logKey,
		Dir:        dir,
	}
	userMessage := processExploreMessage(ctx, p)

	agentTools := []tool.BaseTool{
		tools.GetListDirsTool((*tempManager).FileManager),
		tools.GetThinkTool(),
		tools.GetReadFilesTool((*tempManager).FileManager),
		tools.GetReadDirTool((*tempManager).FileManager),
		summaryTools.GetMissingDirectoryCheckTool((*tempManager).FileManager),
		knowledgeTools.GetKnowledgeRecordTool(tempManager.SummaryManager),
		summaryTools.GetCheckModuleAnalysedStatusTool(tempManager.SummaryManager),
	}

	searchAgent, err := agent.NewSearchAgent(ctx,
		&agentTools,
		llm.SummaryScene,
		SummaryExploreAgentId,
	)

	if err != nil {
		log.V2.Error().With(ctx).Str("create search agent error").Error(err).Emit()
		return "创建规划Agent失败" + err.Error(), err
	}
	commonUtils.WriteLogToDir(logParams.RequestKey, logParams.Dir, "userMessage:\n "+userMessage.Content)

	resultStr, err := agent.ReActGenerate(ctx, &agent.ReActGenerateParams{
		RawMessage:       userMessage.Content,
		SystemMessage:    prompt.ExploreSystemPrompt,
		SearchAgent:      searchAgent,
		LogParams:        logParams,
		Uid:              p.Uid,
		ConversationType: conversationproto.ConversationType_CodeSummary,
		ExpenseScene:     expense.Scene_CodeSummary,
	})
	commonUtils.WriteLogToDir(logParams.RequestKey, logParams.Dir, "onEnd:\n ")
	if err != nil {
		log.V2.Error().With(ctx).Str("summary planning agent generate error").Error(err).Emit()
		return "探索目录失败" + err.Error(), err
	}

	logLine := "onToolEnd: \n summary explore\n"
	commonUtils.WriteLogToDir(logParams.RequestKey, logParams.Dir, logLine)
	commonUtils.WriteLogToDir(logParams.RequestKey, "explore_result", resultStr)
	return resultStr, nil
}

func processExploreMessage(ctx context.Context, params *SummaryExploreParams) *schema.Message {
	builder := strings.Builder{}
	builder.WriteString("<config>\n")
	builder.WriteString(fmt.Sprintf("path: %s\n", params.Path))
	builder.WriteString(fmt.Sprintf("repo_name: %s\n", params.RepoName))
	builder.WriteString(fmt.Sprintf("depth: %d\n", params.Depth))
	builder.WriteString("</config>\n")
	builder.WriteString("你必须记住上述<config/>配置信息，不允许忘记这些信息\n")
	if params.IsComplexModule {
		builder.WriteString("当前模块是复杂模块，会有另外的分析任务去分析内部的子模块，你只需要对当前模块的这一层做非常细致的探索，不要遗漏任何文件，然后对下层的每个模块快速扫一眼即可")
	} else {
		builder.WriteString("当前模块是常规模块，你需要对当前模块内的所有子文件做非常细致的探索，不放过目录内的每一个文件每一个角落")
	}
	return &schema.Message{
		Role:    "user",
		Content: builder.String(),
	}
}

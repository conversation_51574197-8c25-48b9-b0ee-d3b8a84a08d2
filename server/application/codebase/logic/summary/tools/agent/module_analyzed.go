package agentTools

import (
	"context"
	"fmt"
	"strings"

	"code.byted.org/gopkg/jsonx"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/overpass/capcut_devops_expense/kitex_gen/expense"

	"code.byted.org/ies/codin/application/codebase/entity"
	"code.byted.org/ies/codin/application/codebase/logic"
	"code.byted.org/ies/codin/application/codebase/logic/agent"
	"code.byted.org/ies/codin/application/codebase/logic/tools"
	"code.byted.org/ies/codin/application/codebase/repo/llm"
	"code.byted.org/ies/codin/application/codesearch/repo/prompt"
	"code.byted.org/ies/codin/common/utils"
)

func ExecuteModuleAnalyze(ctx context.Context, tempManager *logic.TempManager, path string, repoName string, logDir string, uid string) (*entity.ModuleAnalyzedResult, error) {
	logKey := utils.GeneratePathKey(path)
	logParams := &entity.LogParams{
		RequestKey: logKey,
		Dir:        logDir + "/code_module_analyzed",
	}
	if tempManager.FileManager == nil {
		return nil, fmt.Errorf("ExecuteModuleAnalyze, file manager is nil")
	}

	// 1. 列出目标路径下的内容
	listDirParam := &tools.ListDirParams{
		PathList: []string{path},
		Depth:    3,
		RepoName: repoName,
	}
	logs.CtxInfo(ctx, "ExecuteModuleAnalyze, path = %v, repoName = %v, listDirParam = %v", path, repoName, jsonx.ToString(listDirParam))
	pathContent := tools.ListDir(ctx, tempManager.FileManager, listDirParam)
	pathContent = strings.TrimSpace(pathContent)
	logs.CtxInfo(ctx, "ExecuteModuleAnalyze, path = %v, repoName = %v, pathContent = %v", path, repoName, pathContent)

	// 2. 匹配复杂度判断的硬控策略
	if hint, IsComplexModule := matchComplexCondition(path, pathContent); hint {
		return &entity.ModuleAnalyzedResult{IsComplexModule: IsComplexModule}, nil
	}

	// 3. 格式化模型输入消息
	systemPrompt, userPrompt, _ := prompt.Get(ctx, "capcut.codesearch.complexity_analyze", map[string]any{
		"path":         path,
		"repo_name":    repoName,
		"path_content": pathContent,
	})
	utils.WriteJsonToDir(logParams.RequestKey, logParams.Dir, systemPrompt)
	utils.WriteJsonToDir(logParams.RequestKey, logParams.Dir, userPrompt)

	// 4. 执行模型预测
	out, err := agent.Generate(ctx, &agent.GenerateParams{
		Uid:           uid,
		RawMessage:    userPrompt,
		SystemMessage: systemPrompt,
		LogParams:     logParams,
		LLMScene:      llm.ThinkingScene,
		ExpenseScene:  expense.Scene_CodeSummary,
	})
	if err != nil {
		logs.CtxError(ctx, "ExecuteModuleAnalyze, agent.generate error, err = %v", err)
		return nil, err
	}
	logs.CtxInfo(ctx, "ExecuteModuleAnalyze, agent.generate, output = %v", jsonx.ToString(out))
	utils.WriteLogToDir(logParams.RequestKey, logParams.Dir, "LLM Thinking:\n "+llm.GetArkReasoningContent(out))
	utils.WriteLogToDir(logParams.RequestKey, logParams.Dir, "LLM Output:\n "+out.Content)

	return &entity.ModuleAnalyzedResult{
		IsComplexModule: strings.Contains(strings.ToLower(out.Content), "true"),
	}, nil
}

func matchComplexCondition(path, pathContent string) (hint, isComplexModule bool) {
	if strings.TrimSpace(path) == "/" { // 在真实软件开发里，Repo ROOT 下面必然复杂，则无需跑一次模型预测
		return true, true
	}

	if len(pathContent) == 0 { // 路径下面内容是空，必然不算作复杂模块
		return true, false
	}

	return false, false
}

package tools

import (
	"code.byted.org/ies/codin/common/semantic/repo"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	virtualfile "code.byted.org/ies/codin/application/codebase/repo/virtualfile"
	"code.byted.org/ies/codin/common/utils"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

// MissingDirectoryCheckTool 缺失目录检查工具
// 用于检查指定目录下是否存在未被记录的目录路径，支持目录合并逻辑
type MissingDirectoryCheckTool struct {
	fileManager *virtualfile.FileManager
}

// MissingDirectoryCheckParams 缺失目录检查参数
type MissingDirectoryCheckParams struct {
	Path                   string   `json:"path"`                    // 要检查的目录绝对路径
	NecessaryDirectories   []string `json:"necessary_directories"`   // 已记录的已纳入探索计划的目录列表，绝对路径
	UnnecessaryDirectories []string `json:"unnecessary_directories"` // 已记录的未被纳入探索计划的目录列表，绝对路径
	RepoName               string   `json:"repo_name"`               // 仓库名称
	Reason                 string   `json:"reason"`                  // 调用该工具的原因
}

// MissingDirectoryCheckResult 缺失目录检查结果
type MissingDirectoryCheckResult struct {
	MissingDirectories []string `json:"missing_directories"` // 未被记录的目录列表
}

// GetMissingDirectoryCheckTool 获取缺失目录检查工具实例
// @param app CodeSearch应用实例
// @param fileManager 文件管理器
// @return tool.InvokableTool 可调用的工具实例
func GetMissingDirectoryCheckTool(fileManager *virtualfile.FileManager) tool.InvokableTool {
	return &MissingDirectoryCheckTool{
		fileManager: fileManager,
	}
}

// Info 返回工具信息
// @param ctx 上下文
// @return *schema.ToolInfo 工具信息
// @return error 错误信息
func (t *MissingDirectoryCheckTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "missing_directory_check",
		Desc: "检查指定目录下是否存在未被记录的目录路径",
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"path": {
				Type:     "string",
				Desc:     "要检查的目录绝对路径",
				Required: true,
			},
			"necessary_directories": {
				Type:     "array",
				Desc:     "已记录的已纳入探索计划的目录列表，绝对路径",
				Required: true,
			},
			"unnecessary_directories": {
				Type:     "array",
				Desc:     "已记录的未被纳入探索计划的目录列表，绝对路径",
				Required: true,
			},
			"reason": {
				Type:     "string",
				Desc:     "调用该工具的原因",
				Required: true,
			},
			"repo_name": {
				Type:     "string",
				Desc:     "仓库名称",
				Required: true,
			},
		}),
	}, nil
}

// InvokableRun 执行缺失目录检查
// @param ctx 上下文
// @param argumentsInJSON JSON格式的参数
// @param opts 工具选项
// @return string 检查结果描述
// @return error 错误信息
func (t *MissingDirectoryCheckTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON", argumentsInJSON).Emit()

	// 解析参数
	p := &MissingDirectoryCheckParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), p)
	if err != nil {
		return "解析参数失败" + err.Error(), nil
	}
	// 根目录先简单处理，不用检查
	if p.Path == "/" {
		return "所有路径均已记录", nil
	}
	missingDirs, err := GetMissingDirectoryList(ctx, p, t.fileManager)
	if err != nil {
		return "", err
	}
	if len(missingDirs) == 0 {
		return "所有路径均已记录", nil
	}

	builder := strings.Builder{}
	builder.WriteString("未被记录的目录路径：\n")
	for _, dir := range missingDirs {
		builder.WriteString(dir)
		builder.WriteString("\n")
	}
	builder.WriteString("你需要重新思考是否要对未被记录的目录路径做梳理：")

	return builder.String(), nil
}

// GetMissingDirectoryList 获取缺失目录列表
// 返回最小集合的未被记录的子目录列表，如果子目录本身没有被完全记录，则返回子目录本身
// 支持目录合并逻辑：当所有子目录都被记录时，父目录不被视为缺失
// @param ctx 上下文
// @param p 检查参数
// @param app CodeSearch应用实例
// @param fileManager 文件管理器
// @return []string 缺失目录列表
// @return error 错误信息
func GetMissingDirectoryList(ctx context.Context, p *MissingDirectoryCheckParams, fileManager *virtualfile.FileManager) ([]string, error) {
	log.V2.Info().With(ctx).Str("path", p.Path).Str("repo_name", p.RepoName).Emit()

	if fileManager == nil {
		return nil, errors.New("文件管理器为空")
	}

	// 检查要检查的目录是否存在
	if !(*fileManager).Exists(ctx, p.Path) {
		return nil, fmt.Errorf("check path not found: %s", p.Path)
	}

	// 获取业务仓库信息
	repoItem, ok := repo.Repo.GetRepoItem(ctx, p.RepoName)
	if !ok {
		log.V2.Error().With(ctx).Str("get repo item error").Emit()
		return nil, errors.New("获取仓库信息失败")
	}

	// 获取需要检查的文件路径列表
	shouldCheckFiles := []string{}
	for _, realPath := range repoItem.PathList {
		businessPath := utils.GetFilePath(p.Path, realPath)
		exists := (*fileManager).Exists(ctx, businessPath)
		if !exists {
			continue
		}
		shouldCheckFiles = append(shouldCheckFiles, businessPath)
	}

	log.V2.Info().With(ctx).Str("repoItem.PathList", strings.Join(repoItem.PathList, ", ")).Emit()
	log.V2.Info().With(ctx).Str("shouldCheckFiles", strings.Join(shouldCheckFiles, ", ")).Emit()

	// 合并已记录的目录列表，并进行路径标准化
	recordedDirs := make(map[string]bool)

	for _, dir := range p.NecessaryDirectories {
		// 标准化路径：移除开头斜杠，统一分隔符
		normalizedDir := utils.NormalizePath(dir)
		recordedDirs[normalizedDir] = true
	}

	for _, dir := range p.UnnecessaryDirectories {
		// 标准化路径：移除开头斜杠，统一分隔符
		normalizedDir := utils.NormalizePath(dir)
		recordedDirs[normalizedDir] = true
	}

	log.V2.Info().With(ctx).Str("recordedDirs", strings.Join(func() []string {
		var keys []string
		for k := range recordedDirs {
			keys = append(keys, k)
		}
		return keys
	}(), ", ")).Emit()

	// 递归检查目录
	missingDirs := []string{}
	for _, path := range shouldCheckFiles {
		dirs, err := checkDirectory(ctx, path, path, recordedDirs, fileManager)
		if err != nil {
			return nil, err
		}
		missingDirs = append(missingDirs, dirs...)
	}

	// 返回完整路径，不转换为相对路径
	var resultMissingDirs []string
	for _, dir := range missingDirs {
		// 跳过当前目录
		if dir == p.Path {
			continue
		}
		resultMissingDirs = append(resultMissingDirs, dir)
	}

	// 检测已记录目录中的路径重叠
	allRecordedDirs := append(p.NecessaryDirectories, p.UnnecessaryDirectories...)
	overlaps := detectPathOverlaps(allRecordedDirs)
	log.V2.Info().With(ctx).Str("overlaps count", fmt.Sprintf("%d", len(overlaps))).Emit()
	if len(overlaps) > 0 {
		log.V2.Warn().With(ctx).Str("path overlaps detected", strings.Join(overlaps, ", ")).Emit()
		// 将重叠信息添加到返回结果中
		resultMissingDirs = append(resultMissingDirs, "=== 存在路径重叠 ===")
		resultMissingDirs = append(resultMissingDirs, overlaps...)
		resultMissingDirs = append(resultMissingDirs, "不允许出现路径重叠的情况，你需要删除存在重叠的路径")
	}
	return resultMissingDirs, nil
}

// detectPathOverlaps 检测路径列表中的重叠路径
// 检查是否存在父子路径关系，避免路径重叠导致的逻辑错误
// @param paths 路径列表
// @return []string 重叠路径信息列表
func detectPathOverlaps(paths []string) []string {
	var overlaps []string
	pathMap := make(map[string]bool)

	// 将路径按长度排序，确保父路径在子路径之前检查
	sortedPaths := make([]string, len(paths))
	copy(sortedPaths, paths)

	// 简单的长度排序，更长的路径（更深的目录）排在后面
	for i := 0; i < len(sortedPaths)-1; i++ {
		for j := i + 1; j < len(sortedPaths); j++ {
			if len(sortedPaths[i]) > len(sortedPaths[j]) {
				sortedPaths[i], sortedPaths[j] = sortedPaths[j], sortedPaths[i]
			}
		}
	}

	for _, path := range sortedPaths {
		// 检查当前路径是否是其他路径的父路径
		for existingPath := range pathMap {
			if isParentPath(existingPath, path) {
				overlapInfo := fmt.Sprintf("重叠: %s 是 %s 的父路径", existingPath, path)
				overlaps = append(overlaps, overlapInfo)
			} else if isParentPath(path, existingPath) {
				overlapInfo := fmt.Sprintf("重叠: %s 是 %s 的父路径", path, existingPath)
				overlaps = append(overlaps, overlapInfo)
			}
		}
		pathMap[path] = true
	}

	return overlaps
}

// isParentPath 检查path1是否是path2的父路径
// 用于检测路径重叠关系，确保路径逻辑的正确性
// @param path1 第一个路径
// @param path2 第二个路径
// @return bool 如果path1是path2的父路径则返回true
func isParentPath(path1, path2 string) bool {
	// 标准化路径分隔符
	path1 = strings.ReplaceAll(path1, "\\", "/")
	path2 = strings.ReplaceAll(path2, "\\", "/")

	// 移除开头的斜杠以便统一处理
	path1 = strings.TrimPrefix(path1, "/")
	path2 = strings.TrimPrefix(path2, "/")

	// 如果路径相同，不是父子关系
	if path1 == path2 {
		return false
	}

	// 如果path1为空，则path1是根目录，是任何路径的父路径
	if path1 == "" {
		return true
	}

	// 如果path2为空，则path2是根目录，不可能是其他路径的子路径
	if path2 == "" {
		return false
	}

	// 检查path2是否以path1开头，并且下一个字符是路径分隔符
	prefix := path1 + "/"
	return strings.HasPrefix(path2, prefix)
}

// checkDirectory 递归检查目录缺失情况
// 核心逻辑：只要所有子目录都被记录（且有子目录），无论是否根目录，都直接返回空
// 支持目录合并：当父目录的所有子目录都被记录时，父目录不被视为缺失
// @param ctx 上下文
// @param dirPath 当前检查的目录路径
// @param checkPath 要检查的根目录路径
// @param recordedDirs 已记录的目录映射（已标准化）
// @param fileManager 文件管理器
// @return []string 未被记录的目录列表
// @return error 错误信息
func checkDirectory(ctx context.Context, dirPath string, checkPath string, recordedDirs map[string]bool, fileManager *virtualfile.FileManager) ([]string, error) {
	log.V2.Info().With(ctx).Str("dirPath", dirPath).Emit()
	entries, err := (*fileManager).ReadDir(ctx, dirPath)
	if err != nil {
		return nil, err
	}

	log.V2.Info().With(ctx).Str("entries", strings.Join(func() []string {
		var names []string
		for _, entry := range entries {
			names = append(names, entry.Name)
		}
		return names
	}(), ", ")).Emit()

	log.V2.Info().With(ctx).Str("recordedDirs", strings.Join(func() []string {
		var keys []string
		for k := range recordedDirs {
			keys = append(keys, k)
		}
		return keys
	}(), ", ")).Emit()

	var missingDirs []string
	var hasSubDir bool
	var allChildrenRecorded = true

	for _, entry := range entries {
		if !entry.IsDir {
			continue // 只处理目录，跳过文件
		}
		childPath := filepath.Join(dirPath, entry.Name)
		hasSubDir = true

		// 检查是否有更细粒度的记录（子目录被记录）
		hasDetailedRecord := false
		for recordedPath := range recordedDirs {
			if strings.HasPrefix(recordedPath, childPath+"/") {
				hasDetailedRecord = true
				break
			}
		}

		if recordedDirs[childPath] && !hasDetailedRecord {
			continue // 目录本身已被完整记录，且没有更细粒度的记录
		}

		if !recordedDirs[childPath] && !hasDetailedRecord {
			missingDirs = append(missingDirs, childPath)
			allChildrenRecorded = false
		} else {
			// 目录本身未被记录，或者有更细粒度的记录，递归检查
			childMissingDirs, err := checkDirectory(ctx, childPath, checkPath, recordedDirs, fileManager)
			if err != nil {
				return nil, err
			}
			if len(childMissingDirs) > 0 {
				missingDirs = append(missingDirs, childMissingDirs...)
				allChildrenRecorded = false
			}
			// 如果子目录检查结果为空，说明子目录被完全记录，不需要把当前目录加入 missingDirs
		}
	}

	// 只要所有子目录都被记录（且有子目录），无论是否根目录，都直接返回空
	if allChildrenRecorded && hasSubDir {
		return []string{}, nil
	}
	if len(missingDirs) > 0 {
		return missingDirs, nil
	}
	// 叶子目录未被记录
	if !hasSubDir {
		return []string{dirPath}, nil
	}
	return []string{}, nil
}

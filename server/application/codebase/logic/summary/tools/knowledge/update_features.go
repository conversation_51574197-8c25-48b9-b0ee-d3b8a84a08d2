package knowledgeTools

import (
	"context"
	"encoding/json"
	"fmt"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/common/semantic/summary"
	"code.byted.org/ies/codin/common/semantic/summary/entity"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

// UpdateFeaturesTool 更新功能特性工具
// 用于更新模块的功能特性，支持添加和删除关键功能
type UpdateFeaturesTool struct {
	knowledgeManager *summary.KnowledgeManager
}

// ActionType 动作类型枚举
type UpdateFeatureActionType string

const (
	AddFeature    UpdateFeatureActionType = "add"    // 添加功能
	DeleteFeature UpdateFeatureActionType = "delete" // 删除功能
	UpdateFeature UpdateFeatureActionType = "update" // 更新功能
)

// UpdateFeatureAction 更新功能动作
type UpdateFeatureAction struct {
	ActionType UpdateFeatureActionType `json:"action_type"` // 动作类型
	KeyFeature entity.KeyFeatureInfo   `json:"key_feature"` // 关键功能
}

// UpdateFeaturesParams 知识记录参数结构
type UpdateFeaturesParams struct {
	// 基础信息
	ModulePath           string                `json:"module_path"`            // 模块路径
	UpdateFeatureActions []UpdateFeatureAction `json:"update_feature_actions"` // 动作列表
}

// GetUpdateFeaturesTool 获取更新功能特性工具实例
// @param app CodeSearch应用实例
// @param knowledgeManager 知识管理器
// @return tool.InvokableTool 可调用的工具实例
func GetUpdateFeaturesTool(knowledgeManager *summary.KnowledgeManager) tool.InvokableTool {
	return &UpdateFeaturesTool{
		knowledgeManager: knowledgeManager,
	}
}

// Info 返回工具信息
// @param ctx 上下文
// @return *schema.ToolInfo 工具信息
// @return error 错误信息
func (t *UpdateFeaturesTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "update_features",
		Desc: `更新模块的功能特性`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"module_path": {
				Type:     "string",
				Desc:     "模块的绝对路径，比如 /path/to/module",
				Required: true,
			},
			"update_feature_actions": {
				Type:     "array",
				Desc:     "更新功能的动作列表，会严格按照顺序执行",
				Required: true,
				SubParams: map[string]*schema.ParameterInfo{
					"action_type": {
						Type:     "string",
						Desc:     "动作类型",
						Required: true,
						Enum:     []string{string(AddFeature), string(DeleteFeature), string(UpdateFeature)},
					},
					"key_feature": {
						Type:     "object",
						Desc:     "关键功能",
						Required: true,
						SubParams: map[string]*schema.ParameterInfo{
							"name": {
								Type:     "string",
								Desc:     "关键功能名称",
								Required: true,
							},
							"description": {
								Type:     "string",
								Desc:     "关键功能描述",
								Required: true,
							},
							"call_method": {
								Type:     "string",
								Desc:     "调用方式的具体例子指引",
								Required: true,
							},
						},
					},
				},
			},
		}),
	}, nil
}

// InvokableRun 执行更新功能特性
// @param ctx 上下文
// @param argumentsInJSON JSON格式的参数
// @param opts 工具选项
// @return string 执行结果描述
// @return error 错误信息
func (t *UpdateFeaturesTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON", argumentsInJSON).Emit()

	// 解析参数
	params := &UpdateFeaturesParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), params)
	if err != nil {
		return "参数解析失败: " + err.Error(), nil
	}

	// 验证必填参数
	if params.ModulePath == "" {
		return "模块路径不能为空", nil
	}

	if len(params.UpdateFeatureActions) == 0 {
		return "动作列表不能为空", nil
	}

	// 执行更新操作
	result, err := UpdateFeatures(ctx, t.knowledgeManager, params)
	if err != nil {
		return "更新特征失败: " + err.Error(), nil
	}

	return result, nil
}

// UpdateFeatures 更新模块的功能特性
// 根据动作列表执行添加或删除关键功能的操作
// @param ctx 上下文
// @param knowledgeManager 知识管理器
// @param params 更新参数
// @return string 执行结果描述
// @return error 错误信息
func UpdateFeatures(ctx context.Context, knowledgeManager *summary.KnowledgeManager, params *UpdateFeaturesParams) (string, error) {
	// 获取模块信息
	module := knowledgeManager.GetModule(ctx, params.ModulePath)
	if module == nil {
		return "模块不存在: " + params.ModulePath, nil
	}

	// 记录操作数量
	addCount := 0
	deleteCount := 0

	// 按顺序执行动作列表
	for _, action := range params.UpdateFeatureActions {
		switch action.ActionType {
		case AddFeature:
			// 检查是否已存在相同名称的功能，如果存在则覆盖
			if containsKeyFeature(module.KeyFeatures, action.KeyFeature.Name) {
				// 删除已存在的功能，然后添加新的
				module.KeyFeatures = removeKeyFeature(module.KeyFeatures, action.KeyFeature.Name)
				log.V2.Info().With(ctx).Str("覆盖功能", action.KeyFeature.Name).Emit()
			}
			module.KeyFeatures = append(module.KeyFeatures, action.KeyFeature)
			addCount++
			log.V2.Info().With(ctx).Str("添加功能", action.KeyFeature.Name).Emit()
		case UpdateFeature:
			// 检查是否已存在相同名称的功能，如果存在则覆盖
			if containsKeyFeature(module.KeyFeatures, action.KeyFeature.Name) {
				// 删除已存在的功能，然后添加新的
				module.KeyFeatures = removeKeyFeature(module.KeyFeatures, action.KeyFeature.Name)
				log.V2.Info().With(ctx).Str("覆盖功能", action.KeyFeature.Name).Emit()
			}
			module.KeyFeatures = append(module.KeyFeatures, action.KeyFeature)
			addCount++
			log.V2.Info().With(ctx).Str("更新功能", action.KeyFeature.Name).Emit()
		case DeleteFeature:
			// 删除指定名称的功能
			originalCount := len(module.KeyFeatures)
			module.KeyFeatures = removeKeyFeature(module.KeyFeatures, action.KeyFeature.Name)
			if len(module.KeyFeatures) < originalCount {
				deleteCount++
				log.V2.Info().With(ctx).Str("删除功能", action.KeyFeature.Name).Emit()
			} else {
				log.V2.Warn().With(ctx).Str("功能不存在", action.KeyFeature.Name).Emit()
			}
		default:
			log.V2.Warn().With(ctx).Str("未知动作类型", string(action.ActionType)).Emit()
		}
	}

	// 更新模块信息
	knowledgeManager.UpdateModule(ctx, module)

	// 返回执行结果
	result := fmt.Sprintf("更新特征成功: 添加%d个功能，删除%d个功能", addCount, deleteCount)
	return result, nil
}

// removeKeyFeature 从关键功能列表中删除指定名称的功能
// @param features 关键功能列表
// @param name 要删除的功能名称
// @return []entity.KeyFeatureInfo 删除后的功能列表
func removeKeyFeature(features []entity.KeyFeatureInfo, name string) []entity.KeyFeatureInfo {
	var result []entity.KeyFeatureInfo
	for _, feature := range features {
		if feature.Name != name {
			result = append(result, feature)
		}
	}
	return result
}

// containsKeyFeature 检查关键功能列表中是否包含指定名称的功能
// @param features 关键功能列表
// @param name 要检查的功能名称
// @return bool 如果包含则返回true
func containsKeyFeature(features []entity.KeyFeatureInfo, name string) bool {
	for _, feature := range features {
		if feature.Name == name {
			return true
		}
	}
	return false
}

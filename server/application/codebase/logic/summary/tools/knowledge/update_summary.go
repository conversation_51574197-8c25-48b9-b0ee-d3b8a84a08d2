package knowledgeTools

import (
	"context"
	"encoding/json"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/common/semantic/summary"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

// UpdateSummaryTool 更新模块总结工具
// 用于更新模块的总结
type UpdateSummaryTool struct {
	knowledgeManager *summary.KnowledgeManager
}

// UpdateSummaryParams 知识记录参数结构
type UpdateSummaryParams struct {
	// 基础信息
	ModulePath    string  `json:"module_path"`    // 模块路径
	ModuleName    string  `json:"module_name"`    // 模块名称
	ModuleSummary *string `json:"module_summary"` // 总结
	ModuleDoc     *string `json:"module_doc"`     // 详细文档
}

// GetUpdateFeaturesTool 获取更新功能特性工具实例
// @param app CodeSearch应用实例
// @param knowledgeManager 知识管理器
// @return tool.InvokableTool 可调用的工具实例
func GetUpdateSummaryTool(knowledgeManager *summary.KnowledgeManager) tool.InvokableTool {
	return &UpdateSummaryTool{
		knowledgeManager: knowledgeManager,
	}
}

// Info 返回工具信息
// @param ctx 上下文
// @return *schema.ToolInfo 工具信息
// @return error 错误信息
func (t *UpdateSummaryTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "update_doc",
		Desc: `更新模块的总结摘要`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"module_path": {
				Type:     "string",
				Desc:     "模块的绝对路径，比如 /path/to/module",
				Required: true,
			},
			"module_name": {
				Type:     "string",
				Desc:     "模块的功能名称，根据当前模块的能力进行的一个总结名称，比如：用户管理、订单管理、支付管理",
				Required: true,
			},
			"module_summary": {
				Type:     "string",
				Desc:     "模块的总结，包括模块的主要功能、主要职责、主要流程",
				Required: false,
			},
			"module_doc": {
				Type:     "string",
				Required: false,
				Desc: `深度且详细的记录当前模块的文档
				1. 模块概述
				- 模块的主要目的和价值
				- 在整体架构中的位置和作用：使用Mermaid渲染架构关系图，并强调该模块的位置和作用
				- 核心特性和优势
				- 适用场景和使用限制

				2. 详细架构设计
				- 整体架构图（包含组件关系）
				- 使用Mermaid渲染架构关系图
				- 使用Mermaid时序图描述关键交互流程
				- 每个核心组件的详细职责，给出关键代码示例，并添加注释摘要
				- 状态管理机制，给出关键代码示例，并添加注释摘要

				3. 核心功能详解
				- 每个功能点的详细说明，给出关键代码示例，并添加注释摘要
				- 功能间的依赖关系
				- 配置项和参数说明
				- 核心功能描述上需要配上代码示例，需要有代码示例的注释

				4. 接口文档
				- 公共API详细说明，包括接口参数类型和返回值类型，给出关键代码示例，并添加注释摘要
				- 错误码定义
				- 调用示例和最佳实践，给出关键代码示例，并添加注释摘要
				- 使用Mermaid类图展示关键数据结构

				5. 实现细节
				- 使用Mermaid流程图描述功能细节
				- 关键算法说明,给出关键代码示例
				- 数据结构介绍,给出关键代码示例
				- 缓存策略,给出关键代码示例
				- 并发处理，给出关键代码示例
				- 资源管理，给出关键代码示例

				6. 扩展机制
				- 插件系统设计,给出关键代码示例，使用Mermaid渲染架构关系图
				- 自定义扩展点,给出关键代码示例
				- 扩展开发指南,给出关键代码示例
				- 扩展示例代码
        `,
			},
		}),
	}, nil
}

// InvokableRun 执行更新功能特性
// @param ctx 上下文
// @param argumentsInJSON JSON格式的参数
// @param opts 工具选项
// @return string 执行结果描述
// @return error 错误信息
func (t *UpdateSummaryTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON", argumentsInJSON).Emit()

	// 解析参数
	params := &UpdateSummaryParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), params)
	if err != nil {
		return "参数解析失败: " + err.Error(), nil
	}

	// 验证必填参数
	if params.ModulePath == "" {
		return "模块路径不能为空", nil
	}

	// 执行更新操作
	result, err := UpdateSummary(ctx, t.knowledgeManager, params)
	if err != nil {
		return "更新总结失败: " + err.Error(), nil
	}

	return result, nil
}

// UpdateFeatures 更新模块的功能特性
// 根据动作列表执行添加或删除关键功能的操作
// @param ctx 上下文
// @param knowledgeManager 知识管理器
// @param params 更新参数
// @return string 执行结果描述
// @return error 错误信息
func UpdateSummary(ctx context.Context, knowledgeManager *summary.KnowledgeManager, params *UpdateSummaryParams) (string, error) {
	// 获取模块信息
	module := knowledgeManager.GetModule(ctx, params.ModulePath)
	if module == nil {
		return "模块不存在: " + params.ModulePath, nil
	}

	// 更新总结
	if params.ModuleSummary != nil {
		module.Summary = *params.ModuleSummary
	}
	if params.ModuleDoc != nil {
		module.Doc = *params.ModuleDoc
	}
	module.Name = params.ModuleName

	// 更新模块信息
	knowledgeManager.UpdateModule(ctx, module)

	return "更新总结成功", nil
}

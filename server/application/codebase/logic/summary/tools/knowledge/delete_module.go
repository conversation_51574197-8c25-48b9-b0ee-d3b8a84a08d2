package knowledgeTools

import (
	"context"
	"encoding/json"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/common/semantic/summary"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

// DeleteModuleTool 删除模块工具
// 用于删除指定的模块及其相关信息
type DeleteModuleTool struct {
	knowledgeManager *summary.KnowledgeManager
}

// DeleteModuleParams 删除模块参数结构
type DeleteModuleParams struct {
	// 基础信息
	ModulePath string `json:"module_path"` // 模块路径
	Reason     string `json:"reason"`      // 删除原因
}

// GetDeleteModuleTool 获取删除模块工具实例
// @param app CodeSearch应用实例
// @param knowledgeManager 知识管理器
// @return tool.InvokableTool 可调用的工具实例
func GetDeleteModuleTool(knowledgeManager *summary.KnowledgeManager) tool.InvokableTool {
	return &DeleteModuleTool{
		knowledgeManager: knowledgeManager,
	}
}

// Info 返回工具信息
// @param ctx 上下文
// @return *schema.ToolInfo 工具信息
// @return error 错误信息
func (t *DeleteModuleTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "delete_module",
		Desc: `删除指定的模块及其相关信息
		注意:
		当你确定某个模块已经被完全删除或不再需要时，**你必须调用该工具进行删除记录，这非常重要**
		`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"module_path": {
				Type:     "string",
				Desc:     "要删除的模块的绝对路径，比如 /path/to/module",
				Required: true,
			},
			"reason": {
				Type:     "string",
				Desc:     "删除模块的原因，比如：模块已被完全删除、功能已被重构、不再需要等",
				Required: true,
			},
		}),
	}, nil
}

// InvokableRun 执行删除模块操作
// @param ctx 上下文
// @param argumentsInJSON JSON格式的参数
// @param opts 工具选项
// @return string 执行结果描述
// @return error 错误信息
func (t *DeleteModuleTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON", argumentsInJSON).Emit()

	// 解析参数
	params := &DeleteModuleParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), params)
	if err != nil {
		return "参数解析失败: " + err.Error(), nil
	}

	// 验证必填参数
	if params.ModulePath == "" {
		return "模块路径不能为空", nil
	}
	if params.Reason == "" {
		return "删除原因不能为空", nil
	}

	// 执行删除操作
	result, err := DeleteModule(ctx, t.knowledgeManager, params)
	if err != nil {
		return "删除模块失败: " + err.Error(), nil
	}

	return result, nil
}

// DeleteModule 删除指定的模块
// @param ctx 上下文
// @param knowledgeManager 知识管理器
// @param params 删除参数
// @return string 执行结果描述
// @return error 错误信息
func DeleteModule(ctx context.Context, knowledgeManager *summary.KnowledgeManager, params *DeleteModuleParams) (string, error) {
	// 获取模块信息
	module := knowledgeManager.GetModule(ctx, params.ModulePath)
	if module == nil {
		return "模块不存在: " + params.ModulePath, nil
	}

	// 记录删除信息
	log.V2.Info().With(ctx).Str("删除模块").Str("module_path", params.ModulePath).Str("reason", params.Reason).Emit()

	// 删除模块
	(*knowledgeManager).DeleteModule(ctx, params.ModulePath)

	result := map[string]interface{}{
		"success":     true,
		"module_path": params.ModulePath,
		"reason":      params.Reason,
		"message":     "模块删除成功",
	}
	resultJSON, _ := json.Marshal(result)
	return string(resultJSON), nil
}

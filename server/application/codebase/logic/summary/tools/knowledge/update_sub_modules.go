package knowledgeTools

import (
	"context"
	"encoding/json"
	"fmt"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/common/semantic/summary"
	"code.byted.org/ies/codin/common/semantic/summary/entity"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type UpdateSubModulesTool struct {
	knowledgeManager *summary.KnowledgeManager
}

type UpdateSubModuleActionType string

const (
	AddSubModule    UpdateSubModuleActionType = "add"    // 添加子模块
	DeleteSubModule UpdateSubModuleActionType = "delete" // 删除子模块
	UpdateSubModule UpdateSubModuleActionType = "update" // 更新子模块
)

type UpdateSubModuleAction struct {
	ActionType UpdateSubModuleActionType `json:"action_type"` // 动作类型 / Action type
	SubModule  entity.SubModuleInfo      `json:"sub_module"`  // 子模块 / Sub-module
}

// UpdateFeaturesParams 知识记录参数结构 / Knowledge record parameters structure
type UpdateSubModulesParams struct {
	// 基础信息 / Basic Information
	ModulePath             string                  `json:"module_path"`               // 模块路径 / Module path
	UpdateSubModuleActions []UpdateSubModuleAction `json:"update_sub_module_actions"` // 动作列表 / Actions list
}

func GetUpdateSubModulesTool(knowledgeManager *summary.KnowledgeManager) tool.InvokableTool {
	return &UpdateSubModulesTool{
		knowledgeManager: knowledgeManager,
	}
}

func (t *UpdateSubModulesTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "update_sub_modules",
		Desc: `更新子模块的功能描述`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"module_path": {
				Type:     "string",
				Desc:     "模块的绝对路径，比如 /path/to/module",
				Required: true,
			},
			"update_sub_module_actions": {
				Type:     "array",
				Desc:     "更新子模块的动作列表，会严格按照顺序执行",
				Required: true,
				SubParams: map[string]*schema.ParameterInfo{
					"action_type": {
						Type:     "string",
						Desc:     "动作类型",
						Required: true,
						Enum:     []string{string(AddSubModule), string(DeleteSubModule), string(UpdateSubModule)},
					},
					"sub_module": {
						Type:     "object",
						Desc:     "子模块必须满足核心模块的定义，必须在系统中扮演重要角色，必须对系统整体功能的影响程度较大，必须是有完整逻辑的，相关联的文件超过200行的才能被认为是子模块，不能是零散的、独立的功能点",
						Required: true,
						SubParams: map[string]*schema.ParameterInfo{
							"name": {
								Type:     "string",
								Desc:     "子模块的功能名称",
								Required: true,
							},
							"reason": {
								Type:     "string",
								Desc:     "记录知识的原因",
								Required: true,
							},
							"description": {
								Type:     "string",
								Desc:     "关键功能描述",
								Required: true,
							},
							"path": {
								Type:     "string",
								Desc:     "子模块绝对路径",
								Required: true,
							},
						},
					},
				},
			},
		}),
	}, nil
}

func (t *UpdateSubModulesTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON", argumentsInJSON).Emit()

	// 解析参数 / Parse parameters
	params := &UpdateSubModulesParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), params)
	if err != nil {
		return "参数解析失败: " + err.Error(), nil
	}
	// 验证必填参数 / Validate required parameters
	if params.ModulePath == "" {
		return "模块路径不能为空", nil
	}
	result, err := UpdateSubModules(ctx, t.knowledgeManager, params)
	if err != nil {
		return "更新子模块失败: " + err.Error(), nil
	}
	return result, nil
}

// UpdateSubModules 更新模块的子模块
// 根据动作列表执行添加或删除子模块的操作
// @param ctx 上下文
// @param knowledgeManager 知识管理器
// @param params 更新参数
// @return string 执行结果描述
// @return error 错误信息
func UpdateSubModules(ctx context.Context, knowledgeManager *summary.KnowledgeManager, params *UpdateSubModulesParams) (string, error) {
	module := knowledgeManager.GetModule(ctx, params.ModulePath)
	if module == nil {
		return "模块不存在: " + params.ModulePath, nil
	}

	addCount := 0
	deleteCount := 0

	for _, action := range params.UpdateSubModuleActions {
		switch action.ActionType {
		case AddSubModule:
			// 检查是否已存在相同路径的子模块，如果存在则覆盖
			if containsSubModule(module.SubModules, action.SubModule.Path) {
				// 删除已存在的子模块，然后添加新的
				module.SubModules = removeSubModule(module.SubModules, action.SubModule.Path)
				log.V2.Info().With(ctx).Str("覆盖子模块", action.SubModule.Path).Emit()
			}
			module.SubModules = append(module.SubModules, action.SubModule)
			addCount++
			log.V2.Info().With(ctx).Str("添加子模块", action.SubModule.Path).Emit()
		case UpdateSubModule:
			// 检查是否已存在相同路径的子模块，如果存在则覆盖
			if containsSubModule(module.SubModules, action.SubModule.Path) {
				// 删除已存在的子模块，然后添加新的
				module.SubModules = removeSubModule(module.SubModules, action.SubModule.Path)
				log.V2.Info().With(ctx).Str("覆盖子模块", action.SubModule.Path).Emit()
			}
			module.SubModules = append(module.SubModules, action.SubModule)
			addCount++
			log.V2.Info().With(ctx).Str("更新子模块", action.SubModule.Path).Emit()
		case DeleteSubModule:
			originalCount := len(module.SubModules)
			module.SubModules = removeSubModule(module.SubModules, action.SubModule.Path)
			if len(module.SubModules) < originalCount {
				deleteCount++
				log.V2.Info().With(ctx).Str("删除子模块", action.SubModule.Path).Emit()
			} else {
				log.V2.Warn().With(ctx).Str("子模块不存在", action.SubModule.Path).Emit()
			}
		default:
			log.V2.Warn().With(ctx).Str("未知动作类型", string(action.ActionType)).Emit()
		}
	}

	knowledgeManager.UpdateModule(ctx, module)

	result := fmt.Sprintf("更新子模块成功: 添加%d个，删除%d个", addCount, deleteCount)
	return result, nil
}

// removeSubModule 从子模块列表中删除指定路径的子模块
func removeSubModule(subModules []entity.SubModuleInfo, path string) []entity.SubModuleInfo {
	var result []entity.SubModuleInfo
	for _, sub := range subModules {
		if sub.Path != path {
			result = append(result, sub)
		}
	}
	return result
}

// containsSubModule 检查子模块列表中是否包含指定路径的子模块
func containsSubModule(subModules []entity.SubModuleInfo, path string) bool {
	for _, sub := range subModules {
		if sub.Path == path {
			return true
		}
	}
	return false
}

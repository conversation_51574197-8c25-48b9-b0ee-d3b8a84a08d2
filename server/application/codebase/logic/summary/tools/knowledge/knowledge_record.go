package knowledgeTools

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/common/semantic/summary"
	"code.byted.org/ies/codin/common/semantic/summary/entity"
	"code.byted.org/ies/codin/common/utils"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type KnowledgeRecordTool struct {
	knowledgeManager *summary.KnowledgeManager
}

// KnowledgeRecordParams 知识记录参数结构 / Knowledge record parameters structure
type KnowledgeRecordParams struct {
	// 基础信息 / Basic Information
	ModulePath    string                  `json:"module_path"`    // 模块路径 / Module path
	ModuleName    string                  `json:"module_name"`    // 模块名称 / Module name
	ModuleSummary string                  `json:"module_summary"` // 模块的总结 / Summary
	ModuleDoc     string                  `json:"module_doc"`     // 模块的详细文档 / Summary
	Reason        string                  `json:"reason"`         // 记录知识的原因 / Reason for recording knowledge
	KeyFeatures   []entity.KeyFeatureInfo `json:"key_features"`   // 关键功能列表和详细描述 / Key features list
	SubModules    []entity.SubModuleInfo  `json:"sub_modules"`    // 重要的子模块功能列表 / important Sub-modules list
}

func GetKnowledgeRecordTool(knowledgeManager *summary.KnowledgeManager) tool.InvokableTool {
	return &KnowledgeRecordTool{
		knowledgeManager: knowledgeManager,
	}
}

func (t *KnowledgeRecordTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "knowledge_record",
		Desc: `
		深度且详细的记录当前模块的架构、功能、子模块等详细信息，构建项目知识图谱
		注意:
		当你确定分析完了一个模块，明确了架构、功能列表、子功能模块之后，**你必须调用该工具进行记录，这非常重要**
		`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"module_path": {
				Type:     "string",
				Desc:     "模块的绝对路径，比如 /path/to/module",
				Required: true,
			},
			"module_name": {
				Type:     "string",
				Desc:     "模块的功能名称，根据当前模块的能力进行的一个总结名称，比如：用户管理、订单管理、支付管理",
				Required: true,
			},
			"module_summary": {
				Type:     "string",
				Desc:     "模块的总结，包括模块的主要功能、主要职责、主要流程",
				Required: true,
			},
			"module_doc": {
				Type: "string",
				Desc: `深度且详细的记录当前模块的文档
				1. 模块概述
				- 模块的主要目的和价值
				- 在整体架构中的位置和作用：使用Mermaid渲染架构关系图，并强调该模块的位置和作用
				- 核心特性和优势
				- 适用场景和使用限制

				2. 详细架构设计
				- 整体架构图（包含组件关系）
				- 使用Mermaid渲染架构关系图
				- 使用Mermaid时序图描述关键交互流程
				- 每个核心组件的详细职责，给出关键代码示例，并添加注释摘要
				- 状态管理机制，给出关键代码示例，并添加注释摘要

				3. 核心功能详解
				- 每个功能点的详细说明，给出关键代码示例，并添加注释摘要
				- 功能间的依赖关系
				- 配置项和参数说明
				- 核心功能描述上需要配上代码示例，需要有代码示例的注释

				4. 接口文档
				- 公共API详细说明，包括接口参数类型和返回值类型，给出关键代码示例，并添加注释摘要
				- 错误码定义
				- 调用示例和最佳实践，给出关键代码示例，并添加注释摘要
				- 使用Mermaid类图展示关键数据结构

				5. 实现细节
				- 使用Mermaid流程图描述功能细节
				- 关键算法说明,给出关键代码示例
				- 数据结构介绍,给出关键代码示例
				- 缓存策略,给出关键代码示例
				- 并发处理，给出关键代码示例
				- 资源管理，给出关键代码示例

				6. 扩展机制
				- 插件系统设计,给出关键代码示例，使用Mermaid渲染架构关系图
				- 自定义扩展点,给出关键代码示例
				- 扩展开发指南,给出关键代码示例
				- 扩展示例代码
        `,
				Required: true,
			},
			"reason": {
				Type:     "string",
				Desc:     "当前的模块为什么要记录在parent_module_id下，父模块和子模块直接是否有很强的父子关系",
				Required: true,
			},
			"key_features": {
				Type:     "array",
				Desc:     "模块的关键功能特性列表",
				Required: true,
				SubParams: map[string]*schema.ParameterInfo{
					"name": {
						Type:     "string",
						Desc:     "关键功能名称",
						Required: true,
					},
					"description": {
						Type:     "string",
						Desc:     "关键功能描述",
						Required: true,
					},
					"call_method": {
						Type:     "string",
						Desc:     "调用方式的具体例子指引",
						Required: true,
					},
				},
			},
			"sub_modules": {
				Type:     "array",
				Desc:     "子模块必须满足核心模块的定义，必须在系统中扮演重要角色，必须对系统整体功能的影响程度较大，必须是有完整逻辑的，相关联的文件超过200行的才能被认为是子模块，不能是零散的、独立的功能点",
				Required: true,
				SubParams: map[string]*schema.ParameterInfo{
					"name": {
						Type:     "string",
						Desc:     "子模块的功能名称",
						Required: true,
					},
					"reason": {
						Type:     "string",
						Desc:     "记录知识的原因",
						Required: true,
					},
					"description": {
						Type:     "string",
						Desc:     "关键功能描述",
						Required: true,
					},
					"path": {
						Type:     "string",
						Desc:     "子模块绝对路径",
						Required: true,
					},
				},
			},
		}),
	}, nil
}

func (t *KnowledgeRecordTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON", argumentsInJSON).Emit()

	// 解析参数 / Parse parameters
	params := &KnowledgeRecordParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), params)
	if err != nil {
		return "参数解析失败: " + err.Error(), nil
	}

	// 验证必填参数 / Validate required parameters
	if params.ModulePath == "" {
		return "模块路径不能为空", nil
	}
	if params.ModuleName == "" {
		return "模块名称不能为空", nil
	}
	if params.ModuleDoc == "" {
		return "模块文档不能为空", nil
	}
	if len(params.KeyFeatures) == 0 {
		return "关键功能列表不能为空", nil
	}
	moduleId, err := RecordKnowledge(ctx, t.knowledgeManager, params)
	logKey := fmt.Sprintf("%s_%s.md", time.Now().Format("2006-01-02 15:04:05"), params.ModuleName)
	utils.WriteFile(logKey, "explore_module_result", params.ModuleDoc)
	if err != nil {
		return "保存知识记录失败: " + err.Error(), nil
	}

	result := map[string]interface{}{
		"success":  true,
		"moduleId": moduleId,
	}
	resultJSON, _ := json.Marshal(result)
	return string(resultJSON), nil
}

func RecordKnowledge(ctx context.Context, knowledgeManager *summary.KnowledgeManager, params *KnowledgeRecordParams) (string, error) {
	// 转换参数格式 / Convert parameter format
	moduleParams := &entity.ModuleParams{
		ModulePath:    params.ModulePath,
		ModuleName:    params.ModuleName,
		ModuleSummary: params.ModuleSummary,
		ModuleDoc:     params.ModuleDoc,
		KeyFeatures:   convertKeyFeatures(params.KeyFeatures),
		SubModules:    convertSubModules(params.SubModules),
	}

	// 保存模块 / Save module
	moduleId, err := knowledgeManager.SaveModule(ctx, moduleParams)
	if err != nil {
		return "", err
	}
	return moduleId, nil
}

// convertKeyFeatures 转换关键功能格式 / Convert key features format
func convertKeyFeatures(features []entity.KeyFeatureInfo) []entity.KeyFeatureInfo {
	var converted []entity.KeyFeatureInfo
	for _, feature := range features {
		converted = append(converted, entity.KeyFeatureInfo{
			Name:        feature.Name,
			Description: feature.Description,
			CallMethod:  feature.CallMethod,
		})
	}
	return converted
}

// convertSubModules 转换子模块格式 / Convert sub-modules format
func convertSubModules(subModules []entity.SubModuleInfo) []entity.SubModuleInfo {
	var converted []entity.SubModuleInfo
	for _, subModule := range subModules {
		converted = append(converted, entity.SubModuleInfo{
			Name:        subModule.Name,
			Path:        subModule.Path,
			Reason:      subModule.Reason,
			Description: subModule.Description,
		})
	}
	return converted
}

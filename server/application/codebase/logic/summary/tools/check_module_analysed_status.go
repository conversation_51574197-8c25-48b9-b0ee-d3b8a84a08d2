package tools

import (
	"context"
	"encoding/json"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/common/semantic/summary"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type CheckModuleAnalysedStatusTool struct {
	knowledgeManager *summary.KnowledgeManager
}

// CheckModuleAnalysedStatusParams 知识记录参数结构 / Knowledge record parameters structure
type CheckModuleAnalysedStatusParams struct {
	// 基础信息 / Basic Information
	ModulePath string `json:"module_path"` // 模块路径 / Module path
}

func GetCheckModuleAnalysedStatusTool(knowledgeManager *summary.KnowledgeManager) tool.InvokableTool {
	return &CheckModuleAnalysedStatusTool{
		knowledgeManager: knowledgeManager,
	}
}

func (t *CheckModuleAnalysedStatusTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "check_module_analyzed_status",
		Desc: `判断模块的解析状态，有些模块可能已经分析过了`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"module_path": {
				Type:     "string",
				Desc:     "模块的绝对路径，比如 /path/to/module",
				Required: true,
			},
		}),
	}, nil
}

func (t *CheckModuleAnalysedStatusTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON", argumentsInJSON).Emit()

	// 解析参数 / Parse parameters
	params := &CheckModuleAnalysedStatusParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), params)
	if err != nil {
		return "参数解析失败: " + err.Error(), nil
	}
	// 验证必填参数 / Validate required parameters
	if params.ModulePath == "" {
		return "模块路径不能为空", nil
	}
	module := t.knowledgeManager.GetModule(ctx, params.ModulePath)
	builder := strings.Builder{}
	if module != nil {
		builder.WriteString("模块已经解析过了\n")
		builder.WriteString("模块文档如下：\n")
		builder.WriteString(module.Doc + "\n")
		return builder.String(), nil
	}

	return "模块暂未解析", nil
}

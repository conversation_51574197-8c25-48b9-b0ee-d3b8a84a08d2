package tools

import (
	"context"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type ThinkTool struct{}

type ThinkParams struct {
	Thinking string `json:"thinking"`
	Reason   string `json:"reason"`
}

func GetThinkTool() tool.InvokableTool {
	return &ThinkTool{}
}

func (t *ThinkTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "think",
		Desc: `
		描述：**自由描述和反思你目前所知的内容，你尝试过的事情，以及你的重要发现**，这些如何与你的目标和用户的意图保持一致。你可以尝试不同的场景，权衡选项，并推理可能的下一步。用户不会看到你在这里的任何想法，所以你可以自由思考。这个思考工具充当一个草稿本，你可以在其中自由地突出显示你在上下文中看到的观察结果，对它们进行推理，并得出结论，用中文输出。
		注意：
		(1) 当你从搜索代码过渡到实际理解代码时。你必须执行<think/>工具，你应该问自己是否已经收集了所有必要的上下文，找到了所有需要理解的位置，检查了引用、类型、相关定义等
		(2) 当你多次调用工具，并且结果不符合预期时，你必须执行<think/>工具，，重新进行整体的思考和规划，从根目录开始从头进行目录查看和搜索
		(3) 如果发现问题和当前的搜索内容完全无关，你需要决定如何处理。在这种情况下，最好先退一步，从大局思考你到目前为止所做的工作以及问题可能真正来自哪里，而不是直接进入代码的查看
		(4) 如果不清楚你是否在正确的目录上搜索，需要通过推理你到目前为止所知道的内容来确保你选择正确的目录
		(5) 如果你处于规划模式并搜索文件但没有找到任何匹配项，你应该思考你尚未尝试的其他可能的搜索词
		(6) **不允许连续调用<think/>工具，你应该在调用think工具的时候，输出你的所有规划，这非常重要**
		你可以自由思考并反思你到目前为止所知的内容以及下一步该做什么。你可以单独使用此命令，而不使用任何其他命令。
		`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"thinking": {
				Type:     "string",
				Desc:     "描述你当前的思考和规划，需要写在同一行",
				Required: true,
			},
			"reason": {
				Type:     "string",
				Desc:     "描述你当前的思考和规划的原因，需要写在同一行",
				Required: true,
			},
		}),
	}, nil
}

func (t *ThinkTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON").Str(argumentsInJSON).Emit()
	return argumentsInJSON, nil
}

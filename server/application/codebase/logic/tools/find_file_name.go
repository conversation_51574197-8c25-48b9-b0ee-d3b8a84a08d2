package tools

import (
	"context"
	"encoding/json"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codebase/repo/virtualfile"
	"code.byted.org/ies/codin/application/codebase/repo/virtualfile/utils"
	commonUtils "code.byted.org/ies/codin/common/utils"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type FindFileNameTool struct {
	fileManager *virtualfile.FileManager
}

type FindFileNameParams struct {
	Path   string `json:"path"`
	Glob   string `json:"glob"`
	Reason string `json:"reason"`
}

func GetFindFileNameTool(fileManager *virtualfile.FileManager) tool.InvokableTool {
	return &FindFileNameTool{
		fileManager: fileManager,
	}
}

func (t *FindFileNameTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "find_file_name",
		Desc: `
		描述：递归的去搜索，当文件名与提供的正则表达式匹配时，则返回该文件名。
		注意：
		- 该工具一般用于文件名的模糊搜索，可以在指定目录内，提取到文件名相关的内容
		- **永远不要使用 glob 模式搜索整个项目，而是使用更具体的 glob 模式搜索。**
		- 始终使用此命令而不是内置的"find"，因为此命令针对你的机器进行了优化。
		- **带后缀名搜索的时候，注意项目存在不同后缀文件，比方说main.js/main.ts/main.tsx，不推荐带具体的后缀进行搜索，这非常重要。**
		- **必须注意path路径的完整性，使用绝对路径，这非常重要**
		`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"path": {
				Type:     "string",
				Desc:     "要搜索的目录的绝对路径。",
				Required: true,
			},
			"reason": {
				Type:     "string",
				Desc:     "列出执行该命令的原因，为什么当前可以进行文件搜索了，不需要继续查看目录树了",
				Required: true,
			},
			"glob": {
				Type:     "string",
				Desc:     "在提供的路径处的文件名中搜索的模式。需要使用多个 glob 模式搜索增加效率，用分号分隔它们，这里搜索文件名应该用纯英文",
				Required: true,
			},
		}),
	}, nil
}

func (t *FindFileNameTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON").Str(argumentsInJSON).Emit()
	// 解析参数
	p := &FindFileNameParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), p)
	if err != nil {
		log.V2.Warn().With(ctx).Str("unmarshal error: " + err.Error()).Emit()
		return "输出的数据结构不符合预期" + err.Error(), nil
	}
	content := FindFileName(ctx, t.fileManager, p)
	return content, nil
}

/**
 * @description 根据文件名模式搜索文件
 * @param ctx 上下文
 * @param app CodeSearch应用实例
 * @param fileManager 文件管理器
 * @param p 搜索参数
 * @return string 搜索结果
 */
func FindFileName(ctx context.Context, fileManager *virtualfile.FileManager, p *FindFileNameParams) string {
	if fileManager == nil {
		return "文件管理器为空"
	}

	var result strings.Builder
	patterns := strings.Split(p.Glob, ";")

	// 使用文件管理器检查路径是否存在
	exists := (*fileManager).Exists(ctx, p.Path)
	if !exists {
		log.V2.Error().With(ctx).Str("rootPath not exist").Str("rootPath: ", p.Path).Emit()
		return "指定的文件路径不存在: " + p.Path
	}

	// 使用文件管理器进行文件名搜索
	for _, pattern := range patterns {
		pattern = strings.TrimSpace(pattern)
		if pattern == "" {
			continue
		}
		// 使用文件管理器搜索匹配的文件
		matches, err := utils.FindFileNameWithFileManager(ctx, *fileManager, p.Path, pattern)
		if err != nil {
			log.V2.Warn().With(ctx).Str("pattern", pattern).Str("error", err.Error()).Emit()
			continue
		}

		for _, match := range matches {
			relPath := commonUtils.GetFilePath(match, p.Path)
			result.WriteString(relPath)
			result.WriteString("\n")
		}
	}
	content := result.String()
	if len(content) == 0 {
		return "未找到任何文件"
	}
	return content
}

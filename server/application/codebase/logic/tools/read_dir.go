package tools

import (
	"code.byted.org/ies/codin/common/semantic/repo"
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codebase/repo/virtualfile"
	virtualFileEntity "code.byted.org/ies/codin/application/codebase/repo/virtualfile/entity"
	commonUtils "code.byted.org/ies/codin/common/utils"
	utils "code.byted.org/ies/codin/common/utils"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type ReadDirTool struct {
	fileManager *virtualfile.FileManager
}

type ReadDirParams struct {
	Path     string `json:"path"`
	Reason   string `json:"reason"`
	RepoName string `json:"repo_name"`
}

func GetReadDirTool(fileManager *virtualfile.FileManager) tool.InvokableTool {
	return &ReadDirTool{
		fileManager: fileManager,
	}
}

func (t *ReadDirTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "read_dir",
		Desc: `
		描述： 读取指定目录内的一级子文件和子目录信息，并返回目录结构和子文件文件内容。
		注意：
		- **必须注意path路径的完整性，使用绝对路径，这非常重要**
		- 只会读取当前目录层级，不会递归读取深层目录
		- 自动跳过单层目录，如果目录下只有一个子目录，会继续读取直到遇到多个文件/目录
		`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"path": {
				Type:     "string",
				Desc:     "目录的绝对路径，确保目录真实存在，不要随便捏造不存在的路径",
				Required: true,
			},
			"reason": {
				Type:     "string",
				Desc:     "列出执行该命令的原因，为什么当前需要查看该文件，他对你的任务有什么帮助",
				Required: true,
			},
			"repo_name": {
				Type:     "string",
				Desc:     "仓库名称，指定要查询的仓库。读取上下文的 repo_name 信息",
				Required: true,
			},
		}),
	}, nil
}

func (t *ReadDirTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON").Str(argumentsInJSON).Emit()
	// 解析参数
	p := &ReadDirParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), p)
	if err != nil {
		log.V2.Warn().With(ctx).Str("unmarshal error: " + err.Error()).Emit()
		return "输出的数据结构不符合预期" + err.Error(), nil
	}
	content := ReadDir(ctx, t.fileManager, p)
	return content, nil
}

/**
 * @description 自动跳过单层目录，直到遇到多个文件/目录或单个文件
 * @param ctx 上下文
 * @param fileManager 文件管理器
 * @param basePath 基础路径
 * @param dirPath 目录路径
 * @return string 最终的有效路径
 * @return []filemanager.DirectoryEntry 目录条目列表
 * @return error 错误信息
 */
func readDirWithAutoSkip(ctx context.Context, fileManager *virtualfile.FileManager, basePath, dirPath string) (string, []virtualFileEntity.DirectoryEntry, error) {
	currentPath := utils.GetFilePath(basePath, dirPath)
	maxDepth := 10 // 防止无限递归，最多跳过10层

	for depth := 0; depth < maxDepth; depth++ {
		exists := (*fileManager).Exists(ctx, currentPath)
		if !exists {
			log.V2.Warn().With(ctx).Str("read_dir path not exists: " + currentPath).Emit()
			return currentPath, nil, nil
		}

		dirEntries, err := (*fileManager).ReadDir(ctx, currentPath)
		if err != nil {
			log.V2.Warn().With(ctx).Str("read directory error: " + err.Error()).Emit()
			return currentPath, nil, fmt.Errorf("读取目录内容出错: %w", err)
		}

		// 如果目录为空，直接返回
		if len(dirEntries) == 0 {
			return currentPath, dirEntries, nil
		}

		// 如果只有一个条目且是目录，继续向下读取
		if len(dirEntries) == 1 && dirEntries[0].IsDir {
			currentPath = filepath.Join(currentPath, dirEntries[0].Name)
			log.V2.Info().With(ctx).Str("自动跳过单层目录: " + currentPath).Emit()
			continue
		}

		// 如果有多于一个条目，或者唯一条目是文件，则停止并返回
		return currentPath, dirEntries, nil
	}

	// 达到最大深度限制
	log.V2.Warn().With(ctx).Str("到达最大深度限制: " + currentPath).Emit()
	dirEntries, err := (*fileManager).ReadDir(ctx, currentPath)
	return currentPath, dirEntries, err
}

/**
 * @description 读取指定目录的所有文件和子目录信息
 * @param ctx 上下文
 * @param app CodeSearch应用实例
 * @param fileManager 文件管理器
 * @param p 读取目录参数
 * @return string 目录结构信息
 */
func ReadDir(ctx context.Context, fileManager *virtualfile.FileManager, p *ReadDirParams) string {
	if fileManager == nil {
		return "文件管理器为空"
	}

	repoItem, ok := repo.Repo.GetRepoItem(ctx, p.RepoName)
	if !ok {
		log.V2.Error().With(ctx).Str("get repo item error").Emit()
		return "获取仓库信息失败"
	}

	entries := []virtualFileEntity.DirectoryEntry{}
	effectivePaths := []string{} // 记录实际读取的路径

	for _, dirPath := range repoItem.PathList {
		effectivePath, dirEntries, err := readDirWithAutoSkip(ctx, fileManager, p.Path, dirPath)
		if err != nil {
			log.V2.Warn().With(ctx).Str("read directory with auto skip error: " + err.Error()).Emit()
			return "读取目录内容出错: " + err.Error()
		}

		if len(dirEntries) > 0 {
			entries = append(entries, dirEntries...)
			effectivePaths = append(effectivePaths, effectivePath)
		}
	}

	// 构建目录结构信息
	var result strings.Builder

	// 显示实际读取的路径信息
	if len(effectivePaths) > 0 {
		result.WriteString("实际读取路径：\n")
		for _, path := range effectivePaths {
			result.WriteString(fmt.Sprintf("- %s\n", path))
		}
		result.WriteString("\n")
	}

	// 分别处理文件和目录
	var files []string
	result.WriteString("目录结构：\n")
	for _, entry := range entries {
		if entry.IsDir {
			result.WriteString(fmt.Sprintf("文件夹： %s\n", entry.Path))
		} else {
			files = append(files, entry.Name)
			result.WriteString(fmt.Sprintf("文件： %s\n", entry.Path))
		}
	}
	result.WriteString("\n文件内容结构：\n")
	// 输出文件信息
	for _, file := range files {
		filePath := filepath.Join(p.Path, file)
		filePath = commonUtils.NormalizePath(filePath)
		fileContent, err := (*fileManager).ReadFile(ctx, filePath)
		if err != nil {
			result.WriteString(fmt.Sprintf("文件路径： %s\n", filePath))
			result.WriteString(fmt.Sprintf("(读取失败: %s)\n", err.Error()))
		} else {
			result.WriteString(fmt.Sprintf("文件路径： %s\n", filePath))
			if fileContent.Readable {
				content := fileContent.Content
				if len(content) > 5000 {
					content = content[:5000] + "......文件内容过大，已截断"
				}
				result.WriteString(fmt.Sprintf("文件内容： %s\n", content))
			} else {
				result.WriteString(fmt.Sprintf("(文件不可读: %s)\n", fileContent.Err))
			}
		}
	}

	return result.String()
}

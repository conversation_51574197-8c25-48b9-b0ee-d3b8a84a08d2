package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2/log"
	summaryUpdate "code.byted.org/ies/codin/application/codebase/logic/summary/update/summary_update"
	codeBaseEntity "code.byted.org/ies/codin/common/semantic/codebase/entity"
	codeBaseManager "code.byted.org/ies/codin/common/semantic/codebase/manager"
	summary "code.byted.org/ies/codin/common/semantic/summary"
	summaryEntity "code.byted.org/ies/codin/common/semantic/summary/entity"
	"code.byted.org/ies/codinmodel/kitex_gen/agentserver"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type SummaryTool struct {
	repoInfo *agentserver.RepoInfo
	uid      string
	did      string
}

func GetSummaryTool(repoInfo *agentserver.RepoInfo, uid string, did string) tool.InvokableTool {
	return &SummaryTool{
		repoInfo: repoInfo,
		uid:      uid,
		did:      did,
	}
}

func (t *SummaryTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "get_summary",
		Desc: `This tool provides the directory structure of code repository, helping you understand the project holistically.`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"explanation": {
				Type:     "string",
				Desc:     "One sentence explanation as to why this tool is being used, and how it contributes to the goal.",
				Required: false,
			},
			"repository_name": {
				Type:     "string",
				Desc:     "The name of the repository to search in. <system_info> will provide the list of repositories in the workspace. If the repository list is not provided in <system_info>, you are not allowed to use this tool.",
				Required: true,
			},
			"requirement_content": {
				Type:     "string",
				Desc:     "Complete, unaltered original requirement content. You MUST give the complete user's original requirement. If the original requirement has been optimized by requirement content optimization tools, give the optimized complete requirement content.",
				Required: true,
			},
		}),
	}, nil
}

func (t *SummaryTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON").
		Str(argumentsInJSON).
		Str("repo").
		Obj(*t.repoInfo).
		Str("uid").
		Str(t.uid).
		Str("did").
		Str(t.did).Emit()

	// 解析参数
	request := &codeBaseEntity.GetSummaryDataRequest{
		RepoName: *t.repoInfo.RepoName,
		RepoPath: *t.repoInfo.Path,
		Branch:   *t.repoInfo.Branch,
		Uid:      t.uid,
		Did:      t.did,
	}
	summary, err := GetSummary(ctx, request)
	if err != nil {
		return "获取summary失败" + err.Error(), nil
	}

	return summary, nil
}

func GetSummary(ctx context.Context, req *codeBaseEntity.GetSummaryDataRequest) (string, error) {
	summaryStr := strings.Builder{}
	summaryStr.WriteString("<project_summary> \n")
	if env.IsPPE() || env.IsProduct() {
		summary, err := getSummaryProd(ctx, req)
		summaryStr.WriteString(summary)
		if err != nil {
			log.V2.Error().With(ctx).Str("---------------get_summary_failed").Error(err).Emit()
		}
	} else {
		// 在非生产环境下使用本地mockdata
		summary, err := getSummaryStrLocal(ctx, req)
		summaryStr.WriteString(summary)
		if err != nil {
			log.V2.Error().With(ctx).Str("---------------get_summary_local_failed").Error(err).Emit()
		}
	}
	summaryStr.WriteString("</project_summary> \n")
	return summaryStr.String(), nil
}

// 从生产的tos里面拉到summary
func getSummaryProd(ctx context.Context, req *codeBaseEntity.GetSummaryDataRequest) (string, error) {
	userKnowledgeId := codeBaseManager.GetSummaryUserKnowledgeId(&codeBaseEntity.GetSummaryDataRequest{
		Uid:      req.Uid,
		RepoPath: req.RepoPath,
		Did:      req.Did,
		RepoName: req.RepoName,
		Branch:   req.Branch,
	})
	summaryData, err := summaryUpdate.GetUserSummaryData(ctx, userKnowledgeId)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------get_summaryData_failed").Error(err).Emit()
		return "", err
	}
	formattedTree := summary.FormatKnowledgeTree(&summaryData.Knowledge)
	summaryStr := strings.Builder{}
	summaryStr.WriteString(formattedTree)
	return summaryStr.String(), nil
}

/**
 * getSummaryStrLocal 从本地mockdata目录读取项目摘要信息
 */
func getSummaryStrLocal(ctx context.Context, req *codeBaseEntity.GetSummaryDataRequest) (string, error) {
	// 构建mockdata文件路径
	mockdataDir := "application/codesearch/logic/code/builder/mockdata"
	filePath := mockdataDir + "/" + req.RepoName
	fmt.Println("filePath: ", filePath)
	// 读取mockdata文件
	data, err := os.ReadFile(filePath)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------read_mockdata_failed").Error(err).Str("file_path", filePath).Emit()
		return "", err
	}

	// 解析JSON数据
	knowledgeData := summaryEntity.Knowledge{}
	err = json.Unmarshal(data, &knowledgeData)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------unmarshal_failed").Error(err).Emit()
		return "", err
	}

	// 格式化知识库树
	formattedTree := summary.FormatKnowledgeTree(&knowledgeData)
	summaryStr := strings.Builder{}
	summaryStr.WriteString(formattedTree)
	return summaryStr.String(), nil
}

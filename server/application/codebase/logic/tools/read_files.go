package tools

import (
	"code.byted.org/ies/codin/common/semantic/repo"
	"context"
	"encoding/json"
	"errors"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codebase/repo/virtualfile"
	utils "code.byted.org/ies/codin/common/utils"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type ReadFileTool struct {
	fileManager *virtualfile.FileManager
}

type FileInfo struct {
	Path    string `json:"path"`
	Content string `json:"content"`
}

type ReadFileParams struct {
	PathList  []string `json:"path_list"`
	StartLine int      `json:"start_line"`
	EndLine   int      `json:"end_line"`
	Reason    string   `json:"reason"`
	// NeedSummary bool   `json:"need_summary"`
	RepoName string `json:"repo_name"`
}

func GetReadFilesTool(fileManager *virtualfile.FileManager) tool.InvokableTool {
	return &ReadFileTool{
		fileManager: fileManager,
	}
}

func (t *ReadFileTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "read_files",
		Desc: `
		描述：读取多个文件的内容。此工具调用的输出将是从 start_line 到 end_line 的索引文件内容。你需要充分的评估文件路径的语义和用户提问的关联，确保真的需要读这个文件。
		注意：
		- **必须注意path_list路径的完整性，使用绝对路径，这非常重要**
		- 需要根据当前查看的文件名做出复杂度的评估，来决定查看代码的行数，最小应该50行，最多可以查看 250 行。
		- 使用此工具收集信息时，你有责任确保你有完整的上下文。具体来说，每次调用此命令时，你应该：
			(1) 评估你查看的内容是否足以继续你的任务。
			(2) 注意哪些行没有显示。
			(3) 如果你查看的文件内容不足，并且你怀疑它们可能在未显示的行中，主动再次调用工具查看这些行。
			(4) 如有疑问，再次调用此工具以收集更多信息。记住，部分文件视图可能会错过关键的依赖项、导入或功能。
			(5) 如果之前上下文已经阅读过文件，那么不应该重复阅读
		`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"path_list": {
				Type:     "array",
				Desc:     "要读取内容的文件列表，需要提供文件的绝对路径，确保目录真实存在，你必须一次性传入多个路径，增加效率",
				Required: true,
			},
			"start_line": {
				Type:     "integer",
				Desc:     "要读取的开始行，从1开始索引",
				Required: true,
			},
			"end_line": {
				Type:     "integer",
				Desc:     "要读取的结束行，**单次查看一般为200行**",
				Required: true,
			},
			"reason": {
				Type:     "string",
				Desc:     "列出执行该命令的原因，为什么当前需要查看该文件，他对你的任务有什么帮助",
				Required: true,
			},
			"repo_name": {
				Type:     "string",
				Desc:     "仓库名称，指定要查询的仓库。读取上下文的 repo_name 信息",
				Required: true,
			},
		}),
	}, nil
}

func (t *ReadFileTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON").Str(argumentsInJSON).Emit()
	// 解析参数
	p := &ReadFileParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), p)
	if err != nil {
		log.V2.Warn().With(ctx).Str("unmarshal error: " + err.Error()).Emit()
		return "输出的数据结构不符合预期" + err.Error(), nil
	}
	fileInfoList, err := ReadFile(ctx, t.fileManager, p)
	if err != nil {
		return err.Error(), nil
	}
	var result strings.Builder
	for _, fileInfo := range fileInfoList {
		result.WriteString("文件路径：\n")
		result.WriteString(fileInfo.Path)
		result.WriteString("\n")
		result.WriteString("文件内容：\n")
		result.WriteString(fileInfo.Content)
	}
	return result.String(), nil
}

/**
 * @description 读取多个文件的内容
 * @param ctx 上下文
 * @param app CodeSearch应用实例
 * @param fileManager 文件管理器
 * @param p 读取文件参数
 * @return []*FileInfo 文件信息列表
 * @return error 错误信息
 */
func ReadFile(ctx context.Context, fileManager *virtualfile.FileManager, p *ReadFileParams) ([]*FileInfo, error) {
	if fileManager == nil {
		return nil, errors.New("文件管理器为空")
	}

	var result []*FileInfo
	repoItem, ok := repo.Repo.GetRepoItem(ctx, p.RepoName)
	if !ok {
		log.V2.Warn().With(ctx).Str("get repo item error").Emit()
		return nil, errors.New("获取仓库信息失败")
	}
	shouldReadFiles := []string{}
	for _, path := range p.PathList {
		for _, dirPath := range repoItem.PathList {
			realPath := utils.GetFilePath(path, dirPath)
			exists := (*fileManager).Exists(ctx, realPath)
			if !exists {
				continue
			}
			shouldReadFiles = append(shouldReadFiles, realPath)
		}
	}
	for _, path := range shouldReadFiles {
		// 标准化路径
		// normalizedPath := utils.GetFilePath(path, workspace.Path)

		// 使用文件管理器读取文件
		fileContent, err := (*fileManager).ReadFile(ctx, path)
		if err != nil {
			log.V2.Warn().With(ctx).Str("find file content error: " + err.Error()).Emit()
			result = append(result, &FileInfo{
				Path:    path,
				Content: "读取内容出错" + err.Error() + "\n",
			})
			continue
		}

		if !fileContent.Readable {
			errorMsg := "文件不可读"
			if fileContent.Err != nil {
				errorMsg += ": " + fileContent.Err.Error()
			}
			result = append(result, &FileInfo{
				Path:    path,
				Content: errorMsg,
			})
			continue
		}

		// 按行截取内容
		selectedContent := utils.GetLinesByRange(fileContent.Content, p.StartLine, p.EndLine)
		result = append(result, &FileInfo{
			Path:    path,
			Content: selectedContent,
		})
	}
	return result, nil
}

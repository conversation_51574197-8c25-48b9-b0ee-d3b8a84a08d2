package tools

import (
	semanticCodebase "code.byted.org/ies/codin/common/semantic/codebase"
	"context"
	"encoding/json"

	"code.byted.org/gopkg/logs/v2/log"
	codeEntity "code.byted.org/ies/codin/common/semantic/codebase/entity"
	commonUtils "code.byted.org/ies/codin/common/utils"
	"code.byted.org/ies/codinmodel/kitex_gen/agentserver"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type GoToDependencyTool struct {
	repoInfo *agentserver.RepoInfo
	uid      string
	did      string
}

type GoToDependencyParams struct {
	ChunkID         string `json:"chunk_id"`
	RelationType    string `json:"relation_type"`
	RelationSubType string `json:"relation_sub_type"`
	Reason          string `json:"reason"`
}

// GetGoToDependencyTool creates a new instance of GoToDefinitionTool.
func GetGoToDependencyTool() tool.InvokableTool {
	return &GoToDependencyTool{}
}

// Info returns the tool's information.
func (t *GoToDependencyTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "go_to_definition",
		Desc: "获取代码片段的依赖项，此方法将获得某个代码片段的所有依赖项",
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"chunk_id": {
				Type:     "string",
				Desc:     "代码片段的唯一标识符",
				Required: true,
			},
			"relation_type": {
				Type: "string",
				Desc: "依赖关系类型",
				Enum: []string{"Invoke", "Contain", "Inherit", "Import"},
			},
			"relation_sub_Type": {
				Type: "string",
				Desc: "依赖关系子类型",
				Enum: []string{"InterfaceToClass", "MethodToClass", "MethodToMethod", "MethodToProperty", "PropertyToMethod"},
			},
			"reason": {
				Type:     "string",
				Desc:     "列出执行该命令的原因，为什么需要获取依赖项",
				Required: true,
			},
		}),
	}, nil
}

// InvokableRun executes the GoToDefinitionTool.
func (t *GoToDependencyTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON").Str(argumentsInJSON).Emit()
	// 解析参数
	p := &GoToDependencyParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), p)
	if err != nil {
		log.V2.Error().With(ctx).Str("unmarshal error").Error(err).Emit()
		return "unmarshal error", err
	}
	content := DependencySearch(ctx, p)
	return content, nil
}

func DependencySearch(ctx context.Context, params *GoToDependencyParams) string {
	result, err := semanticCodebase.SemanticManager.GetCodeDependency(ctx, &codeEntity.GetCodeDependencyRequest{
		ChunkId:         params.ChunkID,
		RelationType:    codeEntity.RelationType(params.RelationType),
		RelationSubType: codeEntity.RelationSubType(params.RelationSubType),
		UserKnowledgeId: "",
	})
	if err != nil {
		log.V2.Error().With(ctx).Str("SearchCodeDependency error").Error(err).Emit()
		return "SearchCodeDependency error"
	}
	codeRelationSnippets := commonUtils.ConvertDependencyToCodeRelationSnippets(result)
	data, err := json.Marshal(codeRelationSnippets)
	if err != nil {
		log.V2.Error().With(ctx).Str("Marshal error").Error(err).Emit()
		return "Marshal error"
	}
	return string(data)
}

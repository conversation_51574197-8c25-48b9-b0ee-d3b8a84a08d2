package tools

import (
	"context"
	"encoding/json"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codebase/repo/virtualfile"
	"code.byted.org/ies/codin/application/codebase/repo/virtualfile/utils"
	commonUtils "code.byted.org/ies/codin/common/utils"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type FindFileNameByContentTool struct {
	fileManager *virtualfile.FileManager
}

type FindFileNameByContentParams struct {
	Path   string `json:"path"`
	Regex  string `json:"regex"`
	Depth  int    `json:"depth"`
	Reason string `json:"reason"`
}

func GetFindFileNameByContentTool(fileManager *virtualfile.FileManager) tool.InvokableTool {
	return &FindFileNameByContentTool{
		fileManager: fileManager,
	}
}

func (t *FindFileNameByContentTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "find_file_name_by_content",
		Desc: `
		递归的去搜索，当文件内容与提供的正则表达式匹配时，则返回该文件名。
		注意：
		- 该工具一般用于文件内容的模糊搜索，可以在指定目录内，提取到目录文件里面和关键字相关的文件
		- 永远不要使用 grep，而是使用此命令，因为它针对你的机器进行了优化。
		- 查看的目录深度范围在1-4之间，一般可以考虑depth为2
		- **必须注意path路径的完整性，使用绝对路径，这非常重要**
		`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"path": {
				Type:     "string",
				Desc:     "文件或目录的绝对路径",
				Required: true,
			},
			"depth": {
				Type:     "int",
				Desc:     "要搜索的目录的深度，0表示全量递归，>0表示限制层级，只在指定的层级进行搜索",
				Required: false,
			},
			"reason": {
				Type:     "string",
				Desc:     "列出执行该命令的原因，为什么当前可以进行文件搜索了，不需要继续查看目录树了",
				Required: true,
			},
			"regex": {
				Type:     "string",
				Desc:     "**输出正则表达式优先考虑英文单词，英文单词必须遵守下划线或中横线或驼峰(generate_image/generate-image/generateImage)**",
				Required: false,
			},
		}),
	}, nil
}

func (t *FindFileNameByContentTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON").Str(argumentsInJSON).Emit()
	// businessManager := business.NewBusinessManager(ctx)
	// 解析参数
	p := &FindFileNameByContentParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), p)
	if err != nil {
		log.V2.Warn().With(ctx).Str("unmarshal error: " + err.Error()).Emit()
		return "输出的数据结构不符合预期" + err.Error(), nil
	}
	content := FindFileNameByContent(ctx, t.fileManager, p)
	return content, nil
}

/**
 * @description 根据文件内容搜索文件名
 * @param ctx 上下文
 * @param app CodeSearch应用实例
 * @param fileManager 文件管理器
 * @param p 搜索参数
 * @return string 搜索结果
 */
func FindFileNameByContent(ctx context.Context, fileManager *virtualfile.FileManager, p *FindFileNameByContentParams) string {
	if fileManager == nil {
		return "文件管理器为空"
	}

	// 使用文件管理器进行内容搜索
	contentMap, err := utils.FindFileContentWithFileManager(ctx, *fileManager, p.Path, p.Regex, commonUtils.CollectFilePathsOptions{
		Depth:       p.Depth,
		CollectFile: true,
		CollectDir:  false,
	})
	if err != nil {
		log.V2.Warn().With(ctx).Str("find file content error: " + err.Error()).Emit()
		return "搜索出错" + err.Error()
	}
	var result strings.Builder
	for path, _ := range contentMap {
		result.WriteString(path)
		result.WriteString("\n")
	}
	content := result.String()
	if len(content) == 0 {
		return "未找到任何文件"
	}
	return content
}

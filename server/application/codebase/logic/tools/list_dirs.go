package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"code.byted.org/gopkg/jsonx"
	"code.byted.org/gopkg/logs/v2"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codebase/repo/virtualfile"
	virtualfileUtils "code.byted.org/ies/codin/application/codebase/repo/virtualfile/utils"
	repo "code.byted.org/ies/codin/common/semantic/repo"
	utils "code.byted.org/ies/codin/common/utils"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type ListDirTool struct {
	fileManager *virtualfile.FileManager
}

type ListDirParams struct {
	PathList []string `json:"path_list"`
	Depth    int      `json:"depth"`
	Reason   string   `json:"reason"`
	RepoName string   `json:"repo_name"`
}

func GetListDirsTool(fileManager *virtualfile.FileManager) tool.InvokableTool {
	return &ListDirTool{
		fileManager: fileManager,
	}
}

func (t *ListDirTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "list_dirs",
		Desc: `
		列出目录的内容。结果将包含子项的名称。如果名称以 / 结尾，它是一个文件夹，否则是一个文件
		注意：
		- 该工具一般用于目录树的查看，可以比较快速的对整体仓库结构有一个认知，但是大范围的查看目录可能存在一些成本以及引入一些不相关的目录
		- **查看的目录深度范围必须在3-4之间**
		- **必须注意path路径的完整性，使用绝对路径，这非常重要**
		`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"path_list": {
				Type:     "array",
				Desc:     "要列出的目录的绝对路径列表。必须确保目录的准确，确保目录真实存在，你必须一次性传入多个路径，增加效率",
				Required: true,
			},
			"depth": {
				Type:     "int",
				Desc:     "要列出的目录的深度",
				Required: true,
			},
			"reason": {
				Type:     "string",
				Desc:     "列出执行该命令的原因，为什么当前需要继续查看目录树，而不是直接进行搜索",
				Required: true,
			},
			"repo_name": {
				Type:     "string",
				Desc:     "仓库名称，指定要查询的仓库。读取上下文的 repo_name 信息",
				Required: true,
			},
		}),
	}, nil
}

func (t *ListDirTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON").Str(argumentsInJSON).Emit()
	// 解析参数
	p := &ListDirParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), p)
	if err != nil {
		log.V2.Warn().With(ctx).Str("unmarshal error: " + err.Error()).Emit()
		return "输出的数据结构不符合预期" + err.Error(), nil
	}
	content := ListDir(ctx, t.fileManager, p)
	return content, nil
}

func ListDir(ctx context.Context, fileManager *virtualfile.FileManager, p *ListDirParams) string {
	filePathList := []string{}
	repoItem, ok := repo.Repo.GetRepoItem(ctx, p.RepoName)

	if !ok {
		log.V2.Warn().With(ctx).Str("get business repo item error").Emit()
		return "获取业务仓库信息失败"
	}
	logs.CtxInfo(ctx, "ListDir, repoItem = %v", jsonx.ToString(repoItem))

	// 遍历业务路径列表，收集文件路径
	for _, path := range p.PathList {
		hasPath := false
		for _, realPath := range repoItem.PathList {
			businessPath := utils.GetFilePath(path, realPath)
			logs.CtxInfo(ctx, "ListDir, businessPath = %v, path = %v, realPath = %v, repoName = %v", businessPath, path, realPath, p.RepoName)

			exists := (*fileManager).Exists(ctx, businessPath)
			if !exists {
				continue
			}
			hasPath = true
			filePaths, err := virtualfileUtils.CollectFilePathsWithFileManager(ctx, *fileManager, businessPath, p.Depth)
			if err != nil {
				log.V2.Warn().With(ctx).Str("collect file paths error: " + err.Error()).Emit()
				logLine := fmt.Sprint("collect file paths error", err, "\n")
				filePathList = append(filePathList, logLine)
			}
			filePathList = append(filePathList, filePaths...)
		}
		if !hasPath {
			log.V2.Warn().With(ctx).Str("list_dirs path not exists: ").Str(path).Emit()
		}
	}

	content := strings.Join(filePathList, "\n")
	return content
}

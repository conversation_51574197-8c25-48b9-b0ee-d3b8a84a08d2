package planTools

import (
	"context"
	"encoding/json"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codebase/logic"
	"code.byted.org/ies/codin/application/codebase/repo/plan/entity"
	plan "code.byted.org/ies/codin/application/codebase/repo/plan/manager"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type CreatePlanTool struct {
	planRecordManager *plan.PlanRecordManager
}

type CreatePlanParams struct {
	Tasks    []*TaskRecord `json:"tasks,omitempty"`
	PlanPath string        `json:"plan_path"`
}

type TaskRecord struct {
	TaskID     string              `json:"task_id"`
	Definition string              `json:"definition"`
	TargetPath string              `json:"target_path"`
	Priority   entity.TaskPriority `json:"priority"`
	Reason     string              `json:"reason"`
}

func GetCreatePlanTool(tempManager *logic.TempManager) tool.InvokableTool {
	return &CreatePlanTool{
		planRecordManager: tempManager.PlanRecordManager,
	}
}

// plan
func (t *CreatePlanTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "create_plan",
		Desc: "创建分析计划",
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"tasks": {
				Type:     "array",
				Desc:     "任务列表，创建探索计划时需要，一个task必须是一个独立模块的探索，不能是单独的文件",
				Required: false,
				SubParams: map[string]*schema.ParameterInfo{
					"task_id": {
						Type:     "string",
						Desc:     "任务ID",
						Required: true,
					},
					"definition": {
						Type: "string",
						Desc: `
						"任务详细描述"
						比如：
						1. 深入分析 absolute/path/to/directory 目录，重点关注组件架构
						2. 深入分析 absolute/path/to/directory 目录，理解核心机制与数据流向
						3. 深入分析 imageManager 模块，掌握关键功能和模块调用方式
						4. 分析 API 接口层，重点关注调用方式、数据流和上下游依赖链路
						5. 一个任务应该只做一件事情，只做一个目录的探索，不要把多个任务合并成一个任务
						`,
						Required: true,
					},
					"target_path": {
						Type:     "string",
						Desc:     "创建的任务里，要分析的模块路径，必须为绝对路径",
						Required: true,
					},
					"priority": {
						Type:     "string",
						Desc:     "任务优先级",
						Required: true,
						Enum:     []string{"high", "medium", "low"},
					},
					"reason": {
						Type:     "string",
						Desc:     "任务执行原因,为什么要执行这个任务，是因为目录语义还是发现了文件群语义",
						Required: true,
					},
				},
			},
			"plan_path": {
				Type:     "string",
				Desc:     "当前计划的父路径，所有task必须基于这个路径之下做分析，不能超出这个路径",
				Required: true,
			},
		}),
	}, nil
}

func (t *CreatePlanTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON").Str(argumentsInJSON).Emit()

	// 解析参数
	p := &CreatePlanParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), p)
	if err != nil {
		log.V2.Error().With(ctx).Str("unmarshal error").Error(err).Emit()
		return "输出的数据结构不符合预期" + err.Error(), err
	}

	return CreatePlan(ctx, t.planRecordManager, p)
}

// 处理创建计划
func CreatePlan(ctx context.Context, planRecordManager *plan.PlanRecordManager, p *CreatePlanParams) (string, error) {
	// 解析任务列表
	var tasks []*entity.TaskRecord
	for _, task := range p.Tasks {
		taskRecord := entity.TaskRecord{
			TaskID:     task.TaskID + time.Now().Format("20060102150405"),
			Definition: task.Definition,
			TaskStatus: entity.TaskStatusPlanned,
			TargetPath: task.TargetPath,
			Priority:   task.Priority,
			UpdatedAt:  time.Now(),
		}
		tasks = append(tasks, &taskRecord)
	}

	planID := planRecordManager.CreatePlan(ctx, tasks, p.PlanPath)

	result := map[string]interface{}{
		"success": true,
		"plan_id": planID,
	}

	resultJSON, _ := json.Marshal(result)
	return string(resultJSON), nil
}

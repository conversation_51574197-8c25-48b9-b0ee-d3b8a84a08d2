package summary

import (
	"code.byted.org/ies/codin/common/contexts"
	"context"
	"github.com/samber/lo"

	"encoding/json"

	"code.byted.org/gopkg/logs"
	"code.byted.org/ies/codin/application/codebase/logic/merklet"
	summary "code.byted.org/ies/codin/application/codebase/logic/summary"
	consumeType "code.byted.org/ies/codin/application/codebase/repo/consume/interface"
	"code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
	"code.byted.org/ies/codin/application/codebase/repo/workspace"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/ies/codin/common/semantic/repo"
	"code.byted.org/ies/codin/common/semantic/tos/config"
	"code.byted.org/ies/codin/common/tos"
	"code.byted.org/overpass/capcut_devops_codebase/kitex_gen/codebase"
)

// SummaryConsumer 摘要消费者
type SummaryConsumer struct{}

// NewSummaryConsumer 创建摘要消费者
func NewSummaryConsumer() consumeType.Consumer {
	return &SummaryConsumer{}
}

// GetType 获取消费者类型
func (c *SummaryConsumer) GetType() entity.IndexType {
	return entity.IndexTypeSummary
}

// ShouldConsume 判断是否应该消费该任务
func (c *SummaryConsumer) ShouldConsume(task *entity.IndexTask) bool {
	return task != nil && task.IndexType == entity.IndexTypeSummary
}

// Consume 消费任务
func (c *SummaryConsumer) Consume(ctx context.Context, task *entity.IndexTask) {
	// 根据任务类型执行不同的消费逻辑
	switch task.TaskType {
	case entity.IndexTaskTypeBuild:
		if task.BuildParams.SummaryBuildInfo == nil {
			return
		}
		c.consumeBuildTask(ctx, task)
	case entity.IndexTaskTypeUpdate:
		if task.BuildParams.SummaryUpdateInfo == nil {
			return
		}
		c.consumeUpdateTask(ctx, task)
	default:
		logs.CtxError(ctx, "unknown task type: %v", task.TaskType)
	}
}

// consumeBuildTask 消费构建任务
func (c *SummaryConsumer) consumeBuildTask(ctx context.Context, task *entity.IndexTask) {
	// 1. 解析构建参数
	buildInfo := task.BuildParams.SummaryBuildInfo

	// 2. 执行构建逻辑
	c.executeBuildTask(ctx, buildInfo)

	// 3. 更新任务状态
	task.Status = entity.IndexTaskStatusCompleted
}

// consumeUpdateTask 消费更新任务
func (c *SummaryConsumer) consumeUpdateTask(ctx context.Context, task *entity.IndexTask) {
	// 1. 解析更新参数
	updateInfo := task.BuildParams.SummaryUpdateInfo

	// 2. 执行更新逻辑
	c.executeUpdateTask(ctx, updateInfo)

	// 3. 更新任务状态
	task.Status = entity.IndexTaskStatusCompleted
}

// executeBuildTask 执行构建任务
func (c *SummaryConsumer) executeBuildTask(ctx context.Context, info *entity.SummaryBuildIndexTaskInfo) error {
	logs.CtxInfo(ctx, "[ExecuteBuildTask] request: %v", info)

	// ctx 表示 go 语言一次压栈的生命周期信息，因此对于一次请求中不会改变的信息，可以用 ctx 作为会话上下文携带
	ctx = contexts.WithRepoContext(ctx, &contexts.RepoContext{
		Uid:      info.Uid,
		Did:      "mac",
		RepoPath: "user",
		RepoName: info.RepoName,
		RepoURL:  info.RepoURL,
		Branch:   info.Branch,
		Language: info.Language,
	})

	logs.CtxInfo(ctx, "[ExecuteBuildTask] request: %v", info)

	// 获取仓库配置
	repoPathList, ok := repo.Repo.GetRepoPathList(ctx, info.RepoName)
	if !ok {
		logs.CtxError(ctx, "get repo item error: %v", info.RepoName)
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, "get repo item error")
	}

	tosManager := tos.NewTosManager()
	tosManager.InitTosClientIfNeeded(config.GetMerkleStorageConfig())

	// 初始化仓库
	err := workspace.Workspace.Add(ctx, info.RepoName, info.RepoURL, info.Branch)
	if err != nil {
		logs.CtxError(ctx, "init workspace error: %v", err)
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, "init workspace error")
	}

	// 构建 merkle tree
	treeFileMap, err := merklet.BuildMerkleTree(ctx, info.RepoName, info.Branch, repoPathList)
	if err != nil {
		logs.CtxError(ctx, "build merkle tree error: %v", err)
		return rpcerr.New(codebase.ErrCode_ParseFileFailed, "build merkle tree error")
	}

	treeFileBytes, err := json.Marshal(treeFileMap)
	if err != nil {
		logs.CtxError(ctx, "marshal treeFileMap error: %v", err)
		return rpcerr.New(codebase.ErrCode_ParseFileFailed, "marshal treeFileMap error")
	}

	treeFile, err := merklet.GzipCompress(treeFileBytes)
	if err != nil {
		logs.CtxError(ctx, "gzip compress error: %v", err)
		return rpcerr.New(codebase.ErrCode_ParseFileFailed, "gzip compress error")
	}
	rootMerkleId := (*treeFileMap)["hash"].(string)

	// 先上传merkle tree
	_, err = tosManager.UploadFileToTos(ctx, treeFile, rootMerkleId)
	if err != nil {
		logs.CtxError(ctx, "upload merkle tree error: %v", err)
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, "upload merkle tree error")
	}

	if err = summary.SummaryHandler.CreateSummaryIndex(ctx, rootMerkleId); err != nil {
		logs.CtxError(ctx, "build index if needed error: %v", err)
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, "build index if needed error")
	}
	logs.CtxInfo(ctx, "build index success")
	return nil

}

// executeUpdateTask 执行更新任务
func (c *SummaryConsumer) executeUpdateTask(ctx context.Context, info *entity.SummaryUpdateIndexTaskInfo) {
	repoURL, _ := repo.Repo.GetRepoURL(ctx, info.RepoName)
	ctx = contexts.WithRepoContext(ctx, &contexts.RepoContext{
		Uid:      info.Uid,
		Did:      info.Did,
		RepoPath: info.RepoPath,
		RepoName: info.RepoName,
		RepoURL:  repoURL,
		Branch:   info.Branch,
		Language: "",
	})

	err := summary.SummaryHandler.UpdateSummaryIndex(ctx,
		info.BaseUserKnowledgeId,
		info.ClientMerkleId,
		info.MerkleTreeDownloadKey,
		lo.FromPtrOr(info.GroupedRelatedFileInfoDownloadKey, ""))
	if err != nil {
		logs.CtxError(ctx, "update summary error: %v", err)
		return
	}
}

package consume

import (
	"context"

	"code.byted.org/ies/codin/application/codebase/logic/consume/summary"
	"code.byted.org/ies/codin/application/codebase/logic/consume/vector"
	"code.byted.org/ies/codin/application/codebase/repo/consume/factory"
	repository "code.byted.org/ies/codin/application/codebase/repo/taskrepo/repository"
)

func Consume(ctx context.Context) error {
	// 创建任务仓库
	repo := repository.NewMemoryTaskRepository()

	// 创建工厂和管理器
	consumerFactory := factory.NewConsumerFactory()
	consumerManager := consumerFactory.CreateManager(repo)

	// 注册消费者
	consumerManager.RegisterConsumer(summary.NewSummaryConsumer())
	consumerManager.RegisterConsumer(vector.NewVectorConsumer())

	// 开始消费任务
	consumerManager.ConsumeTask(ctx)
	return nil
}

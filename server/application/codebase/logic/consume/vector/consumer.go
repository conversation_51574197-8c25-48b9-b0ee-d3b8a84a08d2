package vector

import (
	"context"
	"encoding/json"
	"fmt"

	"code.byted.org/gopkg/logs"
	"code.byted.org/ies/codin/application/codebase/logic/merklet"
	semantic "code.byted.org/ies/codin/application/codebase/logic/semantic"
	consumeType "code.byted.org/ies/codin/application/codebase/repo/consume/interface"
	"code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
	"code.byted.org/ies/codin/application/codebase/repo/workspace"
	code "code.byted.org/ies/codin/common/semantic/codebase/entity"

	"code.byted.org/ies/codin/common/group"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/ies/codin/common/semantic/repo"
	"code.byted.org/ies/codin/common/semantic/tos/config"
	"code.byted.org/ies/codin/common/tos"
	"code.byted.org/overpass/capcut_devops_codebase/kitex_gen/codebase"
)

// VectorConsumer 向量消费者
type VectorConsumer struct {
	// 可以添加所需的依赖
}

// NewVectorConsumer 创建向量消费者
func NewVectorConsumer() consumeType.Consumer {
	return &VectorConsumer{}
}

// GetType 获取消费者类型
func (c *VectorConsumer) GetType() entity.IndexType {
	return entity.IndexTypeVector
}

// ShouldConsume 判断是否应该消费该任务
func (c *VectorConsumer) ShouldConsume(task *entity.IndexTask) bool {
	return task != nil && task.IndexType == entity.IndexTypeVector
}

// Consume 消费任务
func (c *VectorConsumer) Consume(ctx context.Context, task *entity.IndexTask) {
	// 根据任务类型执行不同的消费逻辑
	switch task.TaskType {
	case entity.IndexTaskTypeBuild:
		if task.BuildParams.VectorBuildInfo == nil {
			return
		}
		c.consumeBuildTask(ctx, task)
	case entity.IndexTaskTypeUpdate:
		if task.BuildParams.VectorUpdateInfo == nil {
			return
		}
		c.consumeUpdateTask(ctx, task)
	default:
		logs.CtxError(ctx, "unknown task type: %v", task.TaskType)
	}

}

// consumeBuildTask 消费构建任务
func (c *VectorConsumer) consumeBuildTask(ctx context.Context, task *entity.IndexTask) {
	// 1. 解析构建参数
	buildInfo := task.BuildParams.VectorBuildInfo

	// 2. 执行构建逻辑
	c.executeBuildTask(ctx, buildInfo)

	// 3. 更新任务状态
	task.Status = entity.IndexTaskStatusCompleted
}

// consumeUpdateTask 消费更新任务
func (c *VectorConsumer) consumeUpdateTask(ctx context.Context, task *entity.IndexTask) {
	// 1. 解析更新参数
	updateInfo := task.BuildParams.VectorUpdateInfo

	// 2. 执行更新逻辑
	c.executeUpdateTask(ctx, updateInfo)

	// 3. 更新任务状态
	task.Status = entity.IndexTaskStatusCompleted
}

// executeBuildTask 执行构建任务
func (c *VectorConsumer) executeBuildTask(ctx context.Context, info *entity.VectorBuildIndexTaskInfo) error {
	logs.CtxInfo(ctx, "[ExecuteBuildTask] request: %v", info)

	// 获取仓库配置
	repoConfig, ok := repo.Repo.GetRepoItem(ctx, info.RepoName)
	if !ok {
		logs.CtxError(ctx, "get repo item error: %v", info.RepoName)
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, fmt.Sprintf("get repo item error: %v", info.RepoName))
	}

	tosManager := tos.NewTosManager()
	tosManager.InitTosClientIfNeeded(config.GetMerkleStorageConfig())
	logs.CtxInfo(ctx, "get repo item success: %v", info.RepoName)
	// 初始化仓库
	err := workspace.Workspace.Add(ctx, info.RepoName, info.RepoURL, info.Branch)
	if err != nil {
		logs.CtxError(ctx, "init workspace error: %v", err)
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, fmt.Sprintf("init workspace error: %v", err))
	}
	logs.CtxInfo(ctx, "init workspace success: %v", info.RepoName)

	// 构建 merkle tree
	treeFileMap, err := merklet.BuildMerkleTree(ctx, info.RepoName, info.Branch, repoConfig.PathList)
	if err != nil {
		logs.CtxError(ctx, "build merkle tree error: %v", err)
		return rpcerr.New(codebase.ErrCode_ParseFileFailed, "build merkle tree error")
	}
	treeFileBytes, err := json.Marshal(treeFileMap)
	if err != nil {
		logs.CtxError(ctx, "marshal treeFileMap error: %v", err)
		return rpcerr.New(codebase.ErrCode_ParseFileFailed, "marshal treeFileMap error")
	}
	treeFile, err := merklet.GzipCompress(treeFileBytes)
	if err != nil {
		logs.CtxError(ctx, "gzip compress error: %v", err)
		return rpcerr.New(codebase.ErrCode_ParseFileFailed, "gzip compress error")
	}
	logs.CtxInfo(ctx, "build merkle tree success")
	rootMerkleId := (*treeFileMap)["hash"].(string)
	// 先上传merkle tree
	_, err = tosManager.UploadFileToTos(ctx, treeFile, rootMerkleId)
	if err != nil {
		logs.CtxError(ctx, "upload merkle tree error: %v", err)
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, "upload merkle tree error")
	}

	_, err = semantic.Semantic.BuildIndexIfNeeded(ctx, &semantic.BuildIndexParams{
		RepoName: info.RepoName,
		RepoURL:  info.RepoURL,
		Branch:   info.Branch,
		Language: info.Language,
		Uid:      info.Uid,
		RepoPath: info.RepoPath,
		Did:      "mac",
	}, rootMerkleId, repoConfig.PathList)
	if err != nil {
		logs.CtxError(ctx, "build index if needed error: %v", err)
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, "build index if needed error")
	}
	logs.CtxInfo(ctx, "build index success")
	return nil
}

// executeUpdateTask 执行更新任务
func (c *VectorConsumer) executeUpdateTask(ctx context.Context, info *entity.VectorUpdateIndexTaskInfo) error {
	logs.CtxInfo(ctx, "[UploadMerkleTree] start request, uid: %v, repoName: %v, branch: %v, repoPath: %v, did: %v, baseUserKnowledgeId: %v, clientMerkleId: %v, deleteFileIds: %v, ", info.Uid, info.RepoName, info.Branch, info.RepoPath, info.Did, info.BaseUserKnowledgeId, info.ClientMerkleId, info.DeleteFileIds)

	if info.BaseUserKnowledgeId == "" {
		logs.CtxError(ctx, "empty originUserKnowledgeId")
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, "empty originUserKnowledgeId")
	}

	// download files from tos
	tosManager := tos.NewTosManager()
	tosManager.InitTosClientIfNeeded(config.GetMerkleStorageConfig())
	var (
		chunkFileBytes     []byte
		chunkFileErr       error
		relationsFileBytes []byte
		relationsErr       error
	)

	handlers := make([]func() error, 0)

	if info.ChunkFileDownloadKey != "" {
		handlers = append(handlers, func() error {
			chunkFileCompressData, err := tosManager.DownloadFileFromTos(ctx, info.ChunkFileDownloadKey)
			if err != nil {
				chunkFileErr = rpcerr.New(codebase.ErrCode_DownloadFileFailed, fmt.Sprintf("download chunk file error, id: %s, err: %v", info.ChunkFileDownloadKey, err))
				return chunkFileErr
			}
			chunkFileData, err := merklet.GzipDecompress(chunkFileCompressData)
			if err != nil {
				chunkFileErr = rpcerr.New(codebase.ErrCode_ParseFileFailed, fmt.Sprintf("parse chunk file error, id: %s, err: %v", info.ChunkFileDownloadKey, err))
				return chunkFileErr
			}
			chunkFileBytes = chunkFileData
			return nil
		})
	}

	if info.RelationsFileDownloadKey != "" {
		handlers = append(handlers, func() error {
			relationsFileCompressData, err := tosManager.DownloadFileFromTos(ctx, info.RelationsFileDownloadKey)
			if err != nil {
				relationsErr = rpcerr.New(codebase.ErrCode_DownloadFileFailed, fmt.Sprintf("download relations file error, id: %s, err: %v", info.RelationsFileDownloadKey, err))
				return relationsErr
			}
			relationsFileData, err := merklet.GzipDecompress(relationsFileCompressData)
			if err != nil {
				relationsErr = rpcerr.New(codebase.ErrCode_ParseFileFailed, fmt.Sprintf("parse relations file error, id: %s, err: %v", info.RelationsFileDownloadKey, err))
				return relationsErr
			}
			relationsFileBytes = relationsFileData
			return nil
		})
	}

	if len(handlers) > 0 {
		group.GoAndWait(handlers...)
	}

	if chunkFileErr != nil {
		logs.CtxError(ctx, "[MerkleUpload] get chunkFile error: %v", chunkFileErr)
		return chunkFileErr
	}
	if relationsErr != nil {
		logs.CtxError(ctx, "[MerkleUpload] get relationsFile error: %v", relationsErr)
		return relationsErr
	}
	logs.CtxInfo(ctx, "download files success")
	resp, err := semantic.Semantic.BuildIndexWithUploadChunk(ctx, &code.UploadChunkRequest{
		Uid:                   info.Uid,
		RepoName:              info.RepoName,
		DeletedFileIds:        info.DeleteFileIds,
		Branch:                info.Branch,
		RepoPath:              info.RepoPath,
		Did:                   info.Did,
		Content:               chunkFileBytes,
		ChunkRelationContent:  relationsFileBytes,
		RootMerkleId:          info.ClientMerkleId,
		OriginUserKnowledgeId: info.BaseUserKnowledgeId,
	})
	if err != nil {
		logs.CtxError(ctx, "[UploadMerkleTree] UploadChunk error: %v", err)
		return rpcerr.New(codebase.ErrCode_BuildIndexFailed, fmt.Sprintf("build index error, id: %s, err: %v", info.BaseUserKnowledgeId, err))
	}
	logs.CtxInfo(ctx, "[UploadMerkleTree] resp: %v", resp)
	return nil
}

package logic

import (
	"context"

	plan "code.byted.org/ies/codin/application/codebase/repo/plan/manager"
	filemanager "code.byted.org/ies/codin/application/codebase/repo/virtualfile"
	summary "code.byted.org/ies/codin/common/semantic/summary"
)

// 跟随request生命周期走的mgr，request结束就释放
type TempManager struct {
	PlanRecordManager *plan.PlanRecordManager
	SummaryManager    *summary.KnowledgeManager
	FileManager       *filemanager.FileManager
}

func NewTempManager(ctx context.Context) *TempManager {
	return &TempManager{
		PlanRecordManager: plan.NewPlanRecordManager(),
		SummaryManager:    summary.NewKnowledgeManager(),
		FileManager:       nil,
	}
}

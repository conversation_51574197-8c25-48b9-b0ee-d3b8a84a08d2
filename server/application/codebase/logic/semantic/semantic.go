package semantic

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"sync"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codebase/repo/workspace"
	codebaseType "code.byted.org/ies/codin/common/semantic/codebase"
	semanticCodebase "code.byted.org/ies/codin/common/semantic/codebase"
	code "code.byted.org/ies/codin/common/semantic/codebase/entity"
	codeBaseManager "code.byted.org/ies/codin/common/semantic/codebase/manager"
	tosConfig "code.byted.org/ies/codin/common/semantic/tos/config"
	"code.byted.org/ies/codin/common/tos"
	commonUtils "code.byted.org/ies/codin/common/utils"
	"code.byted.org/overpass/capcut_devops_codebase/kitex_gen/codebase"
)

var Semantic *semanticSearchManager
var once sync.Once

func init() {
	once.Do(func() {
		Semantic = newSemanticSearchManager()
	})
}

// SearchManager 搜索管理器
type semanticSearchManager struct {
	codeBase codebaseType.CodeBase
}

type BuildIndexParams struct {
	RepoName string
	RepoURL  string
	Branch   string
	Language string
	Uid      string
	RepoPath string
	Did      string
}

func newSemanticSearchManager() *semanticSearchManager {
	return &semanticSearchManager{
		codeBase: &semanticCodebase.SemanticManager,
	}
}

// 上传chunk
func (m *semanticSearchManager) BuildIndexWithUploadChunk(ctx context.Context, req *code.UploadChunkRequest) (*codebase.UploadMerkleTreeResponse, error) {
	// 检查 merkle tree 在 tos 是否已经存在，不存在则中断
	tosManager := tos.NewTosManager()
	tosManager.InitTosClientIfNeeded(tosConfig.GetMerkleStorageConfig())
	exist, _ := tosManager.IsObjectExist(ctx, req.RootMerkleId)
	if !exist {
		logs.CtxError(ctx, "merkle tree not exist")
		return nil, errors.New("merkle tree not exist")
	}
	logs.CtxInfo(ctx, "start build index with upload chunk")

	// 上传chunk relation
	userKnowledgeId, err := m.codeBase.BuildIndexWithUploadChunk(ctx, req)
	if err != nil {
		log.V2.Error().With(ctx).Str("索引构建失败").Error(err).Emit()
		return nil, errors.New("索引构建失败")
	}
	logs.CtxInfo(ctx, "索引构建成功")
	return &codebase.UploadMerkleTreeResponse{
		Id: userKnowledgeId,
	}, nil
}

func (m *semanticSearchManager) BuildIndexIfNeeded(ctx context.Context, req *BuildIndexParams, rootMerkleId string, pathList []string) (string, error) {
	userKnowledgeId := codeBaseManager.GetDbUserKnowledgeId(req.RepoName, req.Uid, req.RepoPath, req.Did, req.Branch)
	knowledgebaseId := codeBaseManager.GetDbKnowledgebaseId(req.RepoName)
	exists, err := m.codeBase.ExistsBuildRecord(ctx, &code.FuzzyQueryBuildRecordRequest{
		UserKnowledgeId: userKnowledgeId,
		Branch:          req.Branch,
		KnowledgebaseId: knowledgebaseId,
	})
	if err != nil {
		logs.CtxError(ctx, "fuzzy query build record error: %v", err)
		return "", err
	}
	if exists {
		logs.CtxInfo(ctx, "build index already exists, userKnowledgeId: %s", userKnowledgeId)
		return userKnowledgeId, nil
	}
	logs.CtxInfo(ctx, "repoConfig: %v", req)

	logs.CtxInfo(ctx, "执行 codin-indexer 命令，repoName：%s, 语言：%s", req.RepoName, req.Language)
	jsonPathStr, err := json.Marshal(pathList)
	if err != nil {
		// 如果解析报错就所有路径都要用
		jsonPathStr = []byte(`[""]`)
	}

	tempWS := workspace.Workspace.GetTempWorkspace(req.RepoName, req.Branch)
	wsPath := tempWS.Path
	logs.CtxInfo(ctx, "wsPath: %s", wsPath)
	cmd1 := exec.Command("npx", "@byted-image/codin-indexer", "-r", wsPath, "-l", req.Language, "-w", string(jsonPathStr))
	var stderr1 bytes.Buffer
	cmd1.Stderr = &stderr1
	logs.CtxInfo(ctx, "执行命令: %s\n", cmd1.String())
	err = cmd1.Run()
	if err != nil {
		logs.CtxError(ctx, "codin-indexer 命令执行失败: %v\n错误输出:\n%s", err, stderr1.String())
		return "", err
	}
	logs.CtxInfo(ctx, "codin-indexer 命令执行成功")

	fileName := commonUtils.GetFileName(wsPath)
	chunkFilePath := filepath.Join(wsPath, fileName+".json")
	chunkFile, err := os.ReadFile(chunkFilePath)
	if err != nil {
		logs.CtxError(ctx, "read chunk.json error: %v", err)
		return "", err
	}

	chunkRelationPath := filepath.Join(wsPath, fileName+"_relationship.json")
	chunkRelationFile, err := os.ReadFile(chunkRelationPath)
	if err != nil {
		logs.CtxError(ctx, "read chunk_relationship.json error: %v", err)
		return "", err
	}

	OriginUserKnowledgeId := fmt.Sprintf("root/user/mac/code.byted.org:%s/tree/master", req.RepoName)
	_, err = m.BuildIndexWithUploadChunk(ctx, &code.UploadChunkRequest{
		Uid:                   "root",
		RepoName:              req.RepoName,
		DeletedFileIds:        []string{},
		Branch:                req.Branch,
		RepoPath:              req.RepoPath,
		Did:                   req.Did,
		Content:               chunkFile,
		ChunkRelationContent:  chunkRelationFile,
		RootMerkleId:          rootMerkleId,
		OriginUserKnowledgeId: OriginUserKnowledgeId,
	})
	if err != nil {
		logs.CtxError(ctx, "build index with upload chunk error: %v", err)
		return "", err
	}
	return userKnowledgeId, nil
}

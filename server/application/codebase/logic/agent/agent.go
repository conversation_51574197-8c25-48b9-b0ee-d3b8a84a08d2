package agent

import (
	"context"

	"code.byted.org/flow/eino-byted-ext/callbacks/fornax"
	"code.byted.org/gopkg/jsonx"
	"code.byted.org/gopkg/logs/v2"
	fornaxcommon "code.byted.org/ies/codin/common/fornax"
	"code.byted.org/overpass/capcut_devops_expense/kitex_gen/expense"

	"time"

	"errors"
	"fmt"
	"io"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codebase/entity"
	"code.byted.org/ies/codin/application/codebase/repo/llm"
	agentsdk "code.byted.org/ies/codin/common/agentsdk/core"
	"code.byted.org/ies/codin/common/agentsdk/einox"
	"code.byted.org/ies/codin/common/agentsdk/tokencalculator"

	// "code.byted.org/ies/codin/common/semantic/code/llm"
	"code.byted.org/ies/codin/common/utils"
	conversationproto "code.byted.org/ies/codinmodel/kitex_gen/conversation"
	"github.com/cloudwego/eino/callbacks"
	einoModel "github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/schema"
	utilsCallbacks "github.com/cloudwego/eino/utils/callbacks"
)

func NewSearchAgent(
	ctx context.Context,
	tools *[]tool.BaseTool,
	scene llm.Scene,
	agentId string,
) (einox.Agent, error) {
	chatModel, err := llm.GetChatModel(ctx, scene)
	if err != nil {
		log.V2.Error().With(ctx).Str("创建chat model失败").Error(err).Emit()
		return nil, err
	}

	ragent, err := einox.NewReActAgent(ctx, &einox.ReActConfig{
		ID:    agentId,
		Model: chatModel,
		ToolsConfig: compose.ToolsNodeConfig{
			Tools: *tools,
		},
		MaxStep: 150,
	})

	if err != nil {
		log.V2.Error().With(ctx).Str("创建agent失败").Error(err).Emit()
		return nil, err
	}

	return ragent, nil
}

type ReActGenerateParams struct {
	RawMessage       string
	SystemMessage    string
	SearchAgent      einox.Agent
	LogParams        *entity.LogParams
	Uid              string
	ConversationType conversationproto.ConversationType
	ExpenseScene     expense.Scene
}

func ReActGenerate(ctx context.Context, params *ReActGenerateParams) (string, error) {
	startTime := time.Now()
	rawMessage := params.RawMessage
	systemMessage := params.SystemMessage
	searchAgent := params.SearchAgent
	logParams := params.LogParams

	handler := utilsCallbacks.NewHandlerHelper().ChatModel(&utilsCallbacks.ModelCallbackHandler{
		OnError: func(ctx context.Context, runInfo *callbacks.RunInfo, err error) context.Context {
			logLine := "OnError: \n" + err.Error()
			utils.WriteLogToDir(logParams.RequestKey, logParams.Dir, logLine)
			log.V2.Error().With(ctx).Str(logLine).Emit()
			return ctx
		},
		OnEndWithStreamOutput: func(ctx context.Context, runInfo *callbacks.RunInfo, output *schema.StreamReader[*einoModel.CallbackOutput]) context.Context {
			defer func() {
				if output != nil {
					output.Close()
				}
			}()
			endTime := time.Now()
			duration := endTime.Sub(startTime)
			var description string
			for {
				contentChunk, err := output.Recv()
				if errors.Is(err, io.EOF) {
					break
				}
				if err != nil {
					break
				}
				description += contentChunk.Message.Content
			}
			logLine := "onEnd: \n" + runInfo.Name + "\n" + description + " \n duration:" + fmt.Sprintf("%v", duration)
			utils.WriteLogToDir(logParams.RequestKey, logParams.Dir, logLine)
			log.V2.Debug().With(ctx).Str(logLine).Emit()
			return ctx
		},
	}).Tool(&utilsCallbacks.ToolCallbackHandler{
		OnStart: func(ctx context.Context, info *callbacks.RunInfo, input *tool.CallbackInput) context.Context {
			// log.V2.Info().With(ctx).Str("OnStart").Str(info.Name).Str(input.ArgumentsInJSON).Emit()
			logLine := "OnToolStart: \n" + info.Name + " " + input.ArgumentsInJSON
			utils.WriteLogToDir(logParams.RequestKey, logParams.Dir, logLine)
			log.V2.Debug().With(ctx).Str(logLine).Emit()
			return ctx
		},
		OnEnd: func(ctx context.Context, info *callbacks.RunInfo, output *tool.CallbackOutput) context.Context {
			// log.V2.Info().With(ctx).Str("OnEnd").Str(info.Name).Str(output.Response).Emit()
			endTime := time.Now()
			duration := endTime.Sub(startTime)
			logLine := "OnToolEnd: \n" + info.Name + " " + output.Response + " \n duration:" + fmt.Sprintf("%v", duration)
			utils.WriteLogToDir(logParams.RequestKey, logParams.Dir, logLine)
			log.V2.Debug().With(ctx).Str(logLine).Emit()
			return ctx
		},
	}).Handler()
	result := ""

	cid, err := llm.AgentSDK.CreateConversation(ctx, params.Uid, params.ConversationType)
	if err != nil {
		log.V2.Error().With(ctx).Str("创建会话失败").Error(err).Emit()
		return "", err
	}

	log.V2.Info().With(ctx).
		Str("[ReActGenerate]InferenceSync").
		Str("uid: ", params.Uid).
		Str("cid: ", cid).
		Emit()
	streamResult, err := llm.AgentSDK.InferenceSync(
		ctx,
		&agentsdk.ConversationOptions{
			ConversationId: cid,
			Uid:            params.Uid,
			RawMessage:     rawMessage,
		},
		&agentsdk.StreamOptions{
			StreamStrategy: agentsdk.StreamStrategy_None,
		},
		&agentsdk.AgentOptions{
			SystemPrompt: systemMessage,
			Agent:        searchAgent,
			TokenCalculator: tokencalculator.NewUserCalculator(ctx, &tokencalculator.UserConfig{
				Uid:    params.Uid,
				ConvId: cid,
				Scene:  params.ExpenseScene,
				Extra:  map[string]string{"source": "codebase/logic/agent/ReActGenerate"},
			}),
			Callbacks: []callbacks.Handler{handler},
		},
	)

	if err != nil {
		log.V2.Error().With(ctx).Str("searchAgent.Generate error").Error(err).Emit()
		utils.WriteLogToDir(logParams.RequestKey, logParams.Dir, "searchAgent.Generate error: "+err.Error())
		return "", err
	}

	defer func() {
		if streamResult != nil {
			streamResult.Close()
		}
	}()

	for {
		contentChunk, err := streamResult.Recv()
		if errors.Is(err, io.EOF) {
			break
		}
		if err != nil {
			break
		}
		result += contentChunk.Content
	}

	return result, nil
}

type GenerateParams struct {
	Uid           string
	RawMessage    string
	SystemMessage string
	LogParams     *entity.LogParams
	LLMScene      llm.Scene
	ExpenseScene  expense.Scene
}

func Generate(ctx context.Context, params *GenerateParams) (outMessage *schema.Message, err error) {
	rawMessage := params.RawMessage
	systemMessage := params.SystemMessage
	logParams := params.LogParams

	chatModel, err := llm.GetChatModel(ctx, params.LLMScene)
	if err != nil {
		log.V2.Error().With(ctx).Str("创建chat model失败").Error(err).Emit()
		return nil, err
	}

	// 上报 Fornax trace
	fornaxHandler := fornax.NewDefaultCallbackHandler(fornaxcommon.GetFornaxClient())
	newCtx := callbacks.InitCallbacks(ctx, nil, fornaxHandler)

	cid, err := llm.AgentSDK.CreateConversation(ctx, params.Uid, conversationproto.ConversationType_CodeSearch)
	if err != nil {
		log.V2.Error().With(ctx).Str("创建会话失败").Error(err).Emit()
		return nil, err
	}

	outMessage, err = llm.AgentSDK.Ask(newCtx,
		&agentsdk.ConversationOptions{
			ConversationId: cid,
			Uid:            params.Uid,
			RawMessage:     rawMessage,
		}, &agentsdk.ChatOptions{
			SystemPrompt: systemMessage,
			ChatModel:    chatModel,
			TokenCalculator: tokencalculator.NewUserCalculator(ctx, &tokencalculator.UserConfig{
				Uid:    params.Uid,
				ConvId: cid,
				Scene:  params.ExpenseScene,
				Extra:  map[string]string{"source": "codebase/logic/agent/Generate"},
			}),
		})
	if err != nil {
		logs.CtxError(ctx, "Generate error, err = %v", err)
		utils.WriteLogToDir(logParams.RequestKey, logParams.Dir, "Generate error: "+err.Error())
		return nil, err
	}

	utils.WriteLogToDir(logParams.RequestKey, logParams.Dir, "result: \n"+jsonx.ToString(outMessage))
	return outMessage, nil
}

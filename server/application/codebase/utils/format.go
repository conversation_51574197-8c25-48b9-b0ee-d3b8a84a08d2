package utils

import (
	"sort"
	"strings"

	summaryEntity "code.byted.org/ies/codin/application/codebase/entity"
)

func GetDirectoryPathTree(subModuleGroup *summaryEntity.FileContentGroup) string {
	builder := strings.Builder{}
	builder.WriteString("目录结构: " + subModuleGroup.GroupPath)
	for _, fileInfo := range subModuleGroup.SubFileInfos {
		builder.WriteString("\n" + fileInfo.FilePath)
	}

	return builder.String()
}

/**
 * collectDirectoryTree 收集目录树结构
 * @param {*entity.FileContentGroup} subModuleGroup - 子模块组
 * @return {string} 目录树字符串
 */
func CollectDirectoryTree(subModuleGroup *summaryEntity.FileContentGroup) string {
	if subModuleGroup == nil || len(subModuleGroup.SubFileInfos) == 0 {
		return ""
	}

	// 直接收集所有文件路径
	filePaths := make([]string, 0, len(subModuleGroup.SubFileInfos))
	for _, fileInfo := range subModuleGroup.SubFileInfos {
		filePaths = append(filePaths, fileInfo.FilePath)
	}

	// 排序
	sort.Strings(filePaths)

	// 构建输出
	var builder strings.Builder
	builder.WriteString("目录结构:\n")
	for _, filePath := range filePaths {
		builder.WriteString(filePath + "\n")
	}

	return builder.String()
}

func CollectDirectoryTreeWithOutSubModule(subModuleGroup *summaryEntity.FileContentGroup) string {
	if subModuleGroup == nil || len(subModuleGroup.SubFileInfos) == 0 {
		return ""
	}

	// 直接收集所有文件路径
	filePaths := make([]string, 0, len(subModuleGroup.SubFileInfos))
	for _, fileInfo := range subModuleGroup.SubFileInfos {
		// 如果末尾是/，且文件内容为空，则认为是子subModule，那么忽略
		if strings.HasSuffix(fileInfo.FilePath, "/") && strings.TrimSpace(fileInfo.FileContent) == "" {
			continue
		}
		filePaths = append(filePaths, fileInfo.FilePath)
	}

	// 排序
	sort.Strings(filePaths)

	// 构建输出
	var builder strings.Builder
	builder.WriteString("目录结构:\n")
	for _, filePath := range filePaths {
		builder.WriteString(filePath + "\n")
	}

	return builder.String()
}

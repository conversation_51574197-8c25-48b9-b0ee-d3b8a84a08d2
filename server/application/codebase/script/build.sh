#!/usr/bin/env bash
set -e

RUN_NAME="capcut.devops.codesearch"

NPM_PKGS=("@byted-image/codin-indexer" "@byted-image/merkle")  # 必须安装的npm包

# 1. 强制安装 Node.js（如果未安装）
install_node() {
    if ! command -v node &> /dev/null; then
        echo "正在安装 Node.js..."
        # Linux/Mac (使用 nvm)
        if curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.5/install.sh | bash; then
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
            nvm install 18  # 安装 Node.js 18 LTS
            nvm use 18
        else
            # 备用方案：直接下载二进制包
            curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
            sudo apt-get install -y nodejs  # Ubuntu/Debian
            # Mac 可用 brew（需提前安装 Homebrew）
            # brew install node@18
        fi
    fi
}

# 2. 强制安装npm包
install_npm_pkgs() {
    echo "配置npm镜像源..."
    npm config set registry https://bnpm.byted.org
    
    for pkg in "${NPM_PKGS[@]}"; do
        echo "正在强制安装 ${pkg}..."
        
        # 尝试安装（带重试机制）
        for i in {1..3}; do
            if npm install -g --force "${pkg}"; then
                echo "✓ ${pkg}安装成功: $(npm list -g ${pkg} --depth=0 | grep ${pkg})"
                break
            else
                echo "⚠️ 第${i}次安装失败，清理缓存后重试..."
                npm cache clean --force
                sleep 2
                
                if [ $i -eq 3 ]; then
                    echo "❌ 无法安装 ${pkg}，请手动检查！"
                    exit 1
                fi
            fi
        done
    done
}

install_node
install_npm_pkgs

cd server
mkdir -p output/bin
mkdir -p output/configs
cp -r application/codesearch/script/* output/

chmod +x output/bootstrap.sh

if [ "$IS_SYSTEM_TEST_ENV" != "1" ]; then
    go build -o output/bin/${RUN_NAME} application/codesearch/main.go
else
    go test -c -covermode=set -o output/bin/${RUN_NAME} -coverpkg=./...
fi

# #! /usr/bin/env bash
# set -ex

# #### golang

# # install go
# wget https://dl.google.com/go/go1.24.2.linux-amd64.tar.gz
# tar -C /usr/local -xzf go1.24.2.linux-amd64.tar.gz
# export PATH=$PATH:/usr/local/go/bin:/root/go/bin
# echo 'export PATH=$PATH:/usr/local/go/bin:/root/go/bin' >> ~/.bashrc

# # config go env
# go env -w GO111MODULE=on
# go env -w GOPROXY="https://go-mod-proxy.byted.org,https://goproxy.cn,https://proxy.golang.org,direct"
# go env -w GOPRIVATE="*.byted.org,*.everphoto.cn,git.smartisan.com"
# go env -w GOSUMDB="sum.golang.google.cn"

# # install gopls
# go install golang.org/x/tools/gopls@latest


# #### typescript

# # install nodejs
# wget https://nodejs.org/dist/v22.16.0/node-v22.16.0-linux-x64.tar.gz
# tar -C /usr/local -xzf node-v22.16.0-linux-x64.tar.gz && mv /usr/local/node-v22.16.0-linux-x64 /usr/local/node
# export PATH=$PATH:/usr/local/node/bin
# echo 'export PATH=$PATH:/usr/local/node/bin' >> ~/.bashrc

# # install typescript & typescript-language-server
# npm install -g typescript typescript-language-server

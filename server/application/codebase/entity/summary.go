package entity

type FileInfo struct {
	FilePath    string `json:"file_path"`
	FileContent string `json:"file_content"`
}

/**
 * CodeStructure 表示完整的文档结构
 * @property {[]CodeItem} Items - 文档项目列表
 */
type CodeStructure struct {
	Items      []CodeItem `json:"items"`
	Technology string     `json:"technology"`
}

/**
 * CodeItem 表示文档结构中的一个项目
 * @property {string} Title - 项目标题（英文标识符）
 * @property {string} Name - 项目名称（显示名称）
 * @property {[]string} DependentFile - 依赖的文件列表
 * @property {string} Prompt - 生成提示词
 * @property {[]CodeItem} Children - 子项目列表
 */
type CodeItem struct {
	Title         string     `json:"title"`
	Name          string     `json:"name"`
	DependentFile []string   `json:"dependent_file"`
	Children      []CodeItem `json:"children,omitempty"`
}

type ModuleSummary struct {
	ModuleName    string `json:"name"`
	ModuleSummary string `json:"file_summary"`
	// ChildrenModuleSummary []*ModuleSummary `json:"children_module_summary"`
}

type EvalutionResult struct {
	AnalysisReason string   `json:"analysis_reason"`
	ImportantFiles []string `json:"important_files"`
	ShouldSearch   bool     `json:"should_search"`
}

// type PathInfoContent struct {
// 	Path        string     `json:"path"`
// 	ContentList []FileInfo `json:"content_list"`
// }

type RelatedFilePaths struct {
	ModuleFilePaths []string
	LeafFilePaths   []string
}

// FileGroup 文件组结构，包含组路径和所有子文件路径
type DirPathGroup struct {
	GroupPath   string   `json:"group_path"`     // 组路径（module或subModule路径）
	SubDirPaths []string `json:"sub_file_paths"` // 该组下的所有子文件路径
}

// GroupedRelatedPathInfo 分组后的相关文件路径
type GroupedRelatedPathInfo struct {
	ModuleGroups []DirPathGroup `json:"module_groups"` // 模块组列表
	LeafGroups   []DirPathGroup `json:"leaf_groups"`   // 叶子节点组列表
}

type FileContentGroup struct {
	GroupPath    string     `json:"group_path"`     // 组路径（module或subModule路径）
	SubFileInfos []FileInfo `json:"sub_file_infos"` // 该组下的所有子文件路径
}

type GroupedRelatedFileInfo struct {
	ModuleGroups []FileContentGroup `json:"module_groups"` // 模块组列表
	LeafGroups   []FileContentGroup `json:"leaf_groups"`   // 叶子节点组列表
}

type ModuleAnalyzedResult struct {
	IsComplexModule bool `json:"is_complex_module"`
}

type IndependentModuleAnalyzedResult struct {
	IsIndependentModule   bool   `json:"is_independent_module"`
	IndependentModulePath string `json:"independent_module_path"`
}

// UploadResult 表示上传结果的结构体
type UploadResult struct {
	Priority int   // 优先级：1=最高，2=中等，3=最低
	Error    error // 上传错误
}

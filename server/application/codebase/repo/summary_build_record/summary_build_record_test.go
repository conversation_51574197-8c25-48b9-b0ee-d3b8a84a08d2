package summary_build_record

import (
	"code.byted.org/gopkg/jsonx"
	"code.byted.org/ies/codin/application/codesearch/common/mysql"
	"code.byted.org/ies/codin/common/contexts"
	"context"
	"errors"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"
)

func TestCodeSearchSummaryBuildRecordRepository(t *testing.T) {
	var (
		ctx            = contexts.WithLogID(context.Background())
		testSummaryKey = "123321_qukecheng_hello_codin333"
	)

	repo := NewSummaryBuildRecordRepository(mysql.Get())

	logID := contexts.GetLogID(ctx)
	t.Logf("LogID: %v", logID)

	// 看看是不是存在
	record, getErr := repo.GetBySummaryKey(ctx, testSummaryKey)
	if !errors.Is(getErr, gorm.ErrRecordNotFound) {
		assert.Nil(t, getErr)
	}

	if record != nil {
		t.Log("--- 删除已存在的测试数据")
		deleteErr := repo.DeleteById(ctx, record.Id)
		assert.Nil(t, deleteErr)
	}

	// 创建
	t.Log("--- 创建测试数据")
	createErr := repo.Create(ctx, &SummaryBuildRecord{
		SummaryKey: testSummaryKey,
		LogId:      logID,
	})
	assert.Nil(t, createErr)

	// 测试直接获取
	t.Log("--- 测试获取数据")
	record, getErr = repo.GetBySummaryKey(ctx, testSummaryKey)
	assert.Nil(t, getErr)
	assert.Equal(t, testSummaryKey, record.SummaryKey)
	t.Log(jsonx.ToString(record))

	// 测试批量查询
	t.Log("--- 测试批量查询")
	list, selErr := repo.Select(ctx, mysql.NewListQuery().Set("UserKnowledgeID", testSummaryKey))
	assert.Nil(t, selErr)
	assert.Equal(t, 1, len(list))
	t.Log(jsonx.ToString(list))

	// 是否存在
	t.Log("--- 测试是否存在")
	exist, existErr := repo.Exist(ctx, mysql.NewListQuery().Set("UserKnowledgeID", testSummaryKey))
	assert.Nil(t, existErr)
	assert.True(t, exist)
}

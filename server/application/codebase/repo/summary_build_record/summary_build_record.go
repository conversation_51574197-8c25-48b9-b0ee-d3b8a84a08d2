package summary_build_record

import (
	"context"

	. "code.byted.org/ies/codin/application/codesearch/common/mysql"
	"gorm.io/gorm"
)

type SummaryBuildState = string

const (
	SummaryBuildStateNone      SummaryBuildState = ""
	SummaryBuildStateBuilding  SummaryBuildState = "0"
	SummaryBuildStateSucceeded SummaryBuildState = "1"
	SummaryBuildStateFailed    SummaryBuildState = "2"
)

type SummaryBuildRecord struct {
	Id           int64             `gorm:"id"`
	SummaryKey   string            `gorm:"summary_key"`
	RootMerkleID string            `gorm:"root_merkle_id"`
	RepoName     string            `gorm:"repo_name"`
	RepoBranch   string            `gorm:"repo_branch"`
	State        SummaryBuildState `gorm:"state"`
	StartTime    int64             `gorm:"start_time"`
	EndTime      int64             `gorm:"end_time"`
	LogId        string            `gorm:"log_id"`
	BuildParam   string            `gorm:"build_param"`
}

func (c *SummaryBuildRecord) TableName() string {
	return "codesearch_summary_build_record"
}

type SummaryBuildRecordRepository struct {
	QueryEngine
}

func (c *SummaryBuildRecordRepository) prepare(query *ListQuery) *gorm.DB {
	return Where(c.Db().Model(&SummaryBuildRecord{}), query,
		Test(func() bool {
			val, ok := query.Get("SummaryKey")
			return ok && len(val.(string)) >= 0
		}, " and summary_key = @SummaryKey"),
	)
}

func NewSummaryBuildRecordRepository(db *gorm.DB) *SummaryBuildRecordRepository {
	return &SummaryBuildRecordRepository{
		NewQueryEngine(db),
	}
}

func (c *SummaryBuildRecordRepository) GetBySummaryKey(ctx context.Context, summaryKey string) (*SummaryBuildRecord, error) {
	var record = new(SummaryBuildRecord)
	err := c.Tx(ctx).Where("summary_key = ?", summaryKey).Take(record).Error
	return record, err
}

func (c *SummaryBuildRecordRepository) Select(ctx context.Context, query *ListQuery) ([]*SummaryBuildRecord, error) {
	var list = make([]*SummaryBuildRecord, 0)
	err := c.prepare(query).WithContext(ctx).Find(&list).Error
	return list, err
}

func (c *SummaryBuildRecordRepository) Create(ctx context.Context, record *SummaryBuildRecord) error {
	return c.Tx(ctx).Create(&record).Error
}

func (c *SummaryBuildRecordRepository) Exist(ctx context.Context, query *ListQuery) (exist bool, err error) {
	var list = make([]*SummaryBuildRecord, 0)
	err = c.prepare(query).WithContext(ctx).Find(&list).Error
	if err != nil {
		return false, err
	}
	return len(list) > 0, err
}

func (c *SummaryBuildRecordRepository) DeleteById(ctx context.Context, id int64) error {
	return c.Tx(ctx).Where("id = ?", id).Delete(&SummaryBuildRecord{}).Error
}

func (c *SummaryBuildRecordRepository) DeleteBySummaryKey(ctx context.Context, summaryKey string) error {
	return c.Tx(ctx).Where("summary_key = ?", summaryKey).Delete(&SummaryBuildRecord{}).Error
}

func (c *SummaryBuildRecordRepository) Save(ctx context.Context, record *SummaryBuildRecord) error {
	return c.Tx(ctx).Save(record).Error
}

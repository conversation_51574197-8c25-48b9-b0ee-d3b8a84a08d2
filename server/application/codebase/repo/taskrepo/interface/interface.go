package taskrepo

import (
	"context"
	"time"

	entity "code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
)

// TaskRepository 任务存储接口
// 定义了索引任务的持久化操作
type TaskRepository interface {
	// CreateTask 创建任务
	// @param ctx 上下文
	// @param task 任务实体
	// @return error 创建过程中的错误
	SubmitTask(ctx context.Context, task *entity.IndexTask) error

	// GetTask 获取任务
	// @param ctx 上下文
	// @param taskID 任务ID
	// @return *entity.IndexTask 任务实体
	// @return error 获取过程中的错误
	GetTask(ctx context.Context, taskID string) (*entity.IndexTask, error)

	// GetNextTask 获取下一个任务
	// @param ctx 上下文
	// @param indexType 索引类型
	// @return *entity.IndexTask 任务实体
	// @return error 获取过程中的错误
	GetNextTask(ctx context.Context) (*entity.IndexTask, error)

	// UpdateTaskStatus 更新任务状态
	// @param ctx 上下文
	// @param taskID 任务ID
	// @param status 新状态
	// @param errorMessage 错误信息（可选）
	// @return error 更新过程中的错误
	UpdateTaskStatus(ctx context.Context, taskID string, status entity.IndexTaskStatus, errorMessage string) error

	// GetTasksByRepo 根据仓库信息获取任务
	// @param ctx 上下文
	// @param repoName 仓库名称
	// @param branch 分支（可选）
	// @param uid 用户ID（可选）
	// @return []*entity.IndexTask 任务列表
	// @return error 获取过程中的错误
	GetTasksByRepo(ctx context.Context, repoName string, branch string, uid string) ([]*entity.IndexTask, error)

	// CleanupExpiredTasks 清理过期的任务
	// @param ctx 上下文
	// @param timeout 超时时间
	// @return error 清理过程中的错误
	CleanupExpiredTasks(ctx context.Context, timeout time.Duration) error
}

package repository

import (
	"context"
	"errors"
	"time"

	entity "code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
)

// MemoryTaskRepository 内存任务存储实现
// 用于测试和开发环境
type MemoryTaskRepository struct {
	tasks map[string]*entity.IndexTask
}

// NewMemoryTaskRepository 创建内存任务存储实例
func NewMemoryTaskRepository() *MemoryTaskRepository {
	return &MemoryTaskRepository{
		tasks: make(map[string]*entity.IndexTask),
	}
}

// CreateTask 创建任务
func (r *MemoryTaskRepository) SubmitTask(ctx context.Context, task *entity.IndexTask) error {
	if task.TaskID == "" {
		return errors.New("invalid task id")
	}

	if _, exists := r.tasks[task.TaskID]; exists {
		return errors.New("task already exists")
	}

	// 设置创建时间和更新时间
	now := time.Now()
	task.CreatedAt = now
	task.UpdatedAt = now

	r.tasks[task.TaskID] = task
	return nil
}

// GetTask 获取任务
func (r *MemoryTaskRepository) GetTask(ctx context.Context, taskID string) (*entity.IndexTask, error) {
	if taskID == "" {
		return nil, errors.New("invalid task id")
	}

	task, exists := r.tasks[taskID]
	if !exists {
		return nil, entity.ErrTaskNotFound
	}

	return task, nil
}

func (r *MemoryTaskRepository) GetNextTask(ctx context.Context) (*entity.IndexTask, error) {
	for _, task := range r.tasks {
		if task.Status == entity.IndexTaskStatusPending {
			return task, nil
		}
	}
	return nil, nil
}

// UpdateTaskStatus 更新任务状态
func (r *MemoryTaskRepository) UpdateTaskStatus(ctx context.Context, taskID string, status entity.IndexTaskStatus, errorMessage string) error {
	if taskID == "" {
		return errors.New("invalid task id")
	}

	task, exists := r.tasks[taskID]
	if !exists {
		return entity.ErrTaskNotFound
	}

	task.Status = status
	task.UpdatedAt = time.Now()

	return nil
}

// GetPendingTasks 获取待处理的任务
func (r *MemoryTaskRepository) GetPendingTasks(ctx context.Context, indexType entity.IndexType, limit int) ([]*entity.IndexTask, error) {
	var tasks []*entity.IndexTask

	for _, task := range r.tasks {
		if task.Status == entity.IndexTaskStatusPending {
			if indexType == "" || task.IndexType == indexType {
				tasks = append(tasks, task)
				if len(tasks) >= limit {
					break
				}
			}
		}
	}

	return tasks, nil
}

// GetRunningTasks 获取运行中的任务
func (r *MemoryTaskRepository) GetRunningTasks(ctx context.Context, indexType entity.IndexType) ([]*entity.IndexTask, error) {
	var tasks []*entity.IndexTask

	for _, task := range r.tasks {
		if task.Status == entity.IndexTaskStatusRunning {
			if indexType == "" || task.IndexType == indexType {
				tasks = append(tasks, task)
			}
		}
	}

	return tasks, nil
}

// GetTasksByRepo 根据仓库信息获取任务
func (r *MemoryTaskRepository) GetTasksByRepo(ctx context.Context, repoName string, branch string, uid string) ([]*entity.IndexTask, error) {
	var tasks []*entity.IndexTask

	for _, task := range r.tasks {
		if task.BuildParams.VectorBuildInfo.RepoName == repoName {
			if branch == "" || task.BuildParams.VectorBuildInfo.Branch == branch {
				if uid == "" || task.BuildParams.VectorBuildInfo.Uid == uid {
					tasks = append(tasks, task)
				}
			}
		}
	}

	return tasks, nil
}

// CleanupExpiredTasks 清理过期的任务
func (r *MemoryTaskRepository) CleanupExpiredTasks(ctx context.Context, timeout time.Duration) error {
	now := time.Now()

	for taskID, task := range r.tasks {
		if now.Sub(task.CreatedAt) > timeout {
			delete(r.tasks, taskID)
		}
	}

	return nil
}

func (r *MemoryTaskRepository) GetAllTasks(ctx context.Context) ([]*entity.IndexTask, error) {
	tasks := make([]*entity.IndexTask, 0, len(r.tasks))
	for _, task := range r.tasks {
		tasks = append(tasks, task)
	}
	return tasks, nil
}

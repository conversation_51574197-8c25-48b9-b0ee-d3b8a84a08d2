package entity

import "time"

// IndexTaskStatus 任务状态
type IndexTaskStatus string

const (
	IndexTaskStatusPending   IndexTaskStatus = "pending"   // 待处理
	IndexTaskStatusRunning   IndexTaskStatus = "running"   // 运行中
	IndexTaskStatusCompleted IndexTaskStatus = "completed" // 已完成
	IndexTaskStatusFailed    IndexTaskStatus = "failed"    // 失败
)

// IndexType 索引类型
type IndexType string

const (
	IndexTypeVector  IndexType = "vector"  // 向量索引
	IndexTypeSummary IndexType = "summary" // 摘要索引
)

// IndexTaskType 任务类型
type IndexTaskType string

const (
	IndexTaskTypeBuild  IndexTaskType = "build"  // 构建任务
	IndexTaskTypeUpdate IndexTaskType = "update" // 更新任务
)

// VectorBuildIndexTaskInfo 向量构建任务信息
type VectorBuildIndexTaskInfo struct {
	Uid      string // 用户id
	RepoPath string // 仓库路径
	RepoName string // 仓库名
	RepoURL  string // 仓库地址
	Branch   string // 仓库分支
	Language string // 仓库语言
}

// VectorUpdateIndexTaskInfo 向量更新任务信息
type VectorUpdateIndexTaskInfo struct {
	MerkleTreeDownloadKey    string   // Gzip压缩后的仓库树数据
	BaseUserKnowledgeId      string   // 索引构建userKnowledgeId id
	Uid                      string   // 用户
	RepoName                 string   // 仓库
	Branch                   string   // 分支
	RepoPath                 string   // 路径
	Did                      string   // 设备id
	ChunkFileDownloadKey     string   // 新增 chunk 文件
	RelationsFileDownloadKey string   // 新增 relations 文件
	DeleteFileIds            []string // 需要删除的文件hash
	ClientMerkleId           string   // 客户端merkle树id
}

// SummaryBuildIndexTaskInfo 摘要构建任务信息
type SummaryBuildIndexTaskInfo struct {
	Uid      string // 用户id
	RepoPath string // 仓库路径
	RepoName string // 仓库名
	RepoURL  string // 仓库地址
	Branch   string // 仓库分支
	Language string // 仓库语言
}

// SummaryUpdateIndexTaskInfo 摘要更新任务信息
type SummaryUpdateIndexTaskInfo struct {
	MerkleTreeDownloadKey             string  // 客户端merkle树id
	Uid                               string  // 用户
	RepoName                          string  // 仓库
	Branch                            string  // 分支
	RepoPath                          string  // 路径
	Did                               string  // 设备id
	GroupedRelatedFileInfoDownloadKey *string // 相关文件信息下载key
	BaseUserKnowledgeId               string  // 根据这个基础版本构建新的summary
	ClientMerkleId                    string  // 客户端merkle树id
}

// TaskInfo 任务信息
type TaskInfo struct {
	IndexType         IndexType                   // 索引类型：vector/summary
	TaskType          IndexTaskType               // 任务类型：build/update
	VectorBuildInfo   *VectorBuildIndexTaskInfo   // 向量构建任务信息
	VectorUpdateInfo  *VectorUpdateIndexTaskInfo  // 向量更新任务信息
	SummaryBuildInfo  *SummaryBuildIndexTaskInfo  // 摘要构建任务信息
	SummaryUpdateInfo *SummaryUpdateIndexTaskInfo // 摘要更新任务信息
}

// IndexTask 索引任务
type IndexTask struct {
	TaskID    string          // 任务ID
	TaskType  IndexTaskType   // 任务类型
	IndexType IndexType       // 索引类型
	Status    IndexTaskStatus // 任务状态

	BuildParams TaskInfo  // 构建参数，里面会存储TaskInfo
	CreatedAt   time.Time // 创建时间
	UpdatedAt   time.Time // 更新时间
}

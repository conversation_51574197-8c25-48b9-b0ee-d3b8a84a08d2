package virtualfile

import (
	"context"
	"path/filepath"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codebase/repo/virtualfile/entity"
)

// FileManagerType 文件管理器类型
type FileManagerType string

const (
	// LocalFileManagerType 本地文件管理器
	LocalFileManagerType FileManagerType = "local"
	// VirtualFileManagerType 虚拟文件管理器
	VirtualFileManagerType FileManagerType = "virtual"
	// HybridFileManagerType 混合文件管理器（本地+虚拟）
	HybridFileManagerType FileManagerType = "hybrid"
)

// FileManagerConfig 文件管理器配置
type FileManagerConfig struct {
	Type        FileManagerType          `json:"type"`
	RootPath    string                   `json:"root_path"`
	VirtualTree *entity.VirtualDirectory `json:"virtual_tree,omitempty"`
}

// FileManagerFactory 文件管理器工厂
type FileManagerFactory struct{}

// NewFileManagerFactory 创建文件管理器工厂
func NewFileManagerFactory() *FileManagerFactory {
	return &FileManagerFactory{}
}

// CreateFileManager 创建文件管理器
func (f *FileManagerFactory) CreateFileManager(ctx context.Context, config *FileManagerConfig) (FileManager, error) {
	log.V2.Info().With(ctx).Str("type", string(config.Type)).Str("root_path", config.RootPath).Emit()

	// 标准化根路径
	rootPath, err := filepath.Abs(config.RootPath)
	if err != nil {
		log.V2.Error().With(ctx).Str("abs path error").Error(err).Emit()
		return nil, err
	}

	switch config.Type {
	case LocalFileManagerType:
		return f.createLocalFileManager(rootPath), nil
	case VirtualFileManagerType:
		return f.createVirtualFileManager(rootPath, config.VirtualTree), nil
	case HybridFileManagerType:
		return f.createHybridFileManager(rootPath, config.VirtualTree), nil
	default:
		log.V2.Error().With(ctx).Str("unknown file manager type").Str("type", string(config.Type)).Emit()
		return nil, entity.ErrUnknownFileManagerType
	}
}

// createLocalFileManager 创建本地文件管理器
func (f *FileManagerFactory) createLocalFileManager(rootPath string) FileManager {
	return NewLocalFileManager(rootPath)
}

// createVirtualFileManager 创建虚拟文件管理器
func (f *FileManagerFactory) createVirtualFileManager(rootPath string, virtualTree *entity.VirtualDirectory) FileManager {
	return NewVirtualFileManager(rootPath, virtualTree)
}

// createHybridFileManager 创建混合文件管理器
func (f *FileManagerFactory) createHybridFileManager(rootPath string, virtualTree *entity.VirtualDirectory) FileManager {
	// 混合文件管理器使用虚拟提供者，它会优先从本地读取，本地没有的从虚拟树读取
	return NewVirtualFileManager(rootPath, virtualTree)
}

// CreateLocalFileManager 创建本地文件管理器（便捷方法）
func (f *FileManagerFactory) CreateLocalFileManager(ctx context.Context, rootPath string) (FileManager, error) {
	config := &FileManagerConfig{
		Type:     LocalFileManagerType,
		RootPath: rootPath,
	}
	return f.CreateFileManager(ctx, config)
}

// CreateVirtualFileManager 创建虚拟文件管理器（便捷方法）
func (f *FileManagerFactory) CreateVirtualFileManager(ctx context.Context, rootPath string, virtualTree *entity.VirtualDirectory) (FileManager, error) {
	config := &FileManagerConfig{
		Type:        VirtualFileManagerType,
		RootPath:    rootPath,
		VirtualTree: virtualTree,
	}
	return f.CreateFileManager(ctx, config)
}

// CreateHybridFileManager 创建混合文件管理器（便捷方法）
func (f *FileManagerFactory) CreateHybridFileManager(ctx context.Context, rootPath string, virtualTree *entity.VirtualDirectory) (FileManager, error) {
	config := &FileManagerConfig{
		Type:        HybridFileManagerType,
		RootPath:    rootPath,
		VirtualTree: virtualTree,
	}
	return f.CreateFileManager(ctx, config)
}

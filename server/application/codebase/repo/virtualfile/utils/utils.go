package utils

import (
	"context"
	"errors"
	"fmt"
	"path/filepath"
	"regexp"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codebase/repo/virtualfile"
	"code.byted.org/ies/codin/common/utils"
	"github.com/bmatcuk/doublestar/v4"
)

/**
 * @description 使用文件管理器收集文件和目录路径
 * @param ctx 上下文
 * @param fileManager 文件管理器
 * @param basePath 基础路径
 * @param depth 深度
 * @return []string 文件和目录路径列表（目录以 / 结尾）
 * @return error 错误信息
 */
func CollectFilePathsWithFileManager(ctx context.Context, fileManager virtualfile.FileManager, basePath string, depth int) ([]string, error) {
	if fileManager == nil {
		return nil, errors.New("workspace is not initialized")
	}
	var result []string

	// 如果深度为0，直接返回
	if depth <= 0 {
		return result, nil
	}

	// 读取目录
	entries, err := fileManager.ReadDir(ctx, basePath)
	if err != nil {
		log.V2.Error().With(ctx).Str("read_dir_error", err.Error()).Str("base_path", basePath).Emit()
		return result, err
	}

	// 处理每个条目
	for _, entry := range entries {
		entryName := entry.Name
		entryPath := filepath.Join(basePath, entryName)

		if entry.IsDir {
			// 目录以 / 结尾，方便区分
			dirPath := entryPath + "/"
			result = append(result, dirPath)

			// 如果是目录，递归处理
			if depth > 1 {
				subPaths, err := CollectFilePathsWithFileManager(ctx, fileManager, entryPath, depth-1)
				if err != nil {
					log.V2.Error().With(ctx).Str("recursive collect error").Error(err).Emit()
					continue
				}
				result = append(result, subPaths...)
			}
		} else {
			// 如果是文件，添加到结果中
			result = append(result, entryPath)
		}
	}

	return result, nil
}

/**
 * @description 使用文件管理器根据内容搜索文件
 * @param ctx 上下文
 * @param fileManager 文件管理器
 * @param path 要搜索的目录
 * @param rootPath 根路径
 * @param searchPattern 搜索模式（支持|分隔的多关键词）
 * @param options 收集选项
 * @return map[string]string 文件路径到文件内容的映射
 * @return error 错误信息
 */
func FindFileContentWithFileManager(ctx context.Context, fileManager virtualfile.FileManager, path string, searchPattern string, options utils.CollectFilePathsOptions) (map[string]string, error) {
	if fileManager == nil {
		return nil, errors.New("文件管理器为空")
	}

	// 存储结果
	results := make(map[string]string)

	// 获取文件路径列表
	filePaths, err := CollectFilePathsWithFileManager(ctx, fileManager, path, options.Depth)
	if err != nil {
		return nil, err
	}
	utils.WriteFile("filePaths", "codesearch_logs/test", utils.FormatPathsToTree(filePaths))

	// 遍历收集到的文件路径
	for _, relPath := range filePaths {
		// 跳过目录（以 / 结尾的路径）
		if strings.HasSuffix(relPath, "/") {
			continue
		}

		// 使用文件管理器读取文件内容
		fileContent, err := fileManager.ReadFile(ctx, relPath)
		if err != nil {
			log.V2.Warn().With(ctx).Str("read file error").Error(err).Emit()
			continue
		}

		content := fileContent.Content

		// 如果提供了搜索模式，检查文件内容是否匹配（支持多关键词）
		if searchPattern != "" {
			if !utils.MatchAny(content, searchPattern) {
				continue
			}
		}

		// 将文件内容添加到结果中
		results[relPath] = content
	}

	return results, nil
}

/**
 * @description 使用文件管理器根据文件名模式搜索文件
 * @param ctx 上下文
 * @param fileManager 文件管理器
 * @param rootPath 根路径
 * @param pattern 文件名模式（支持 glob 模式）
 * @return []string 匹配的文件路径列表
 * @return error 错误信息
 */
func FindFileNameWithFileManager(ctx context.Context, fileManager virtualfile.FileManager, rootPath string, pattern string) ([]string, error) {
	if fileManager == nil {
		return nil, errors.New("文件管理器为空")
	}

	var matches []string

	// 自动加递归前缀
	if !strings.Contains(pattern, "/") {
		pattern = "**/" + pattern
	}

	// 使用 doublestar.Glob 进行模式匹配
	// 由于 FileManager 现在实现了 fs.FS 接口，可以直接使用
	matchedPaths, err := doublestar.Glob(fileManager, pattern)
	if err != nil {
		log.V2.Error().With(ctx).Str("doublestar glob error").Error(err).Str("pattern", pattern).Emit()
		return nil, err
	}

	// 将匹配的路径转换为绝对路径
	for _, matchedPath := range matchedPaths {
		// 检查是否为文件（不是目录）
		info, err := fileManager.Stat(ctx, matchedPath)
		if err != nil {
			log.V2.Warn().With(ctx).Str("stat error").Error(err).Str("path", matchedPath).Emit()
			continue
		}

		if !info.IsDir {
			// 构建绝对路径
			absPath := utils.GetFilePath(matchedPath, rootPath)
			log.V2.Info().With(ctx).Str("matched file", absPath).Str("pattern", pattern).Emit()
			matches = append(matches, absPath)
		}
	}

	return matches, nil
}

/**
 * @description 简单的 glob 模式匹配函数
 * @param path 文件路径
 * @param pattern glob 模式
 * @return bool 是否匹配
 */
func matchGlobPattern(path, pattern string) bool {
	// 将路径转换为使用斜杠分隔符
	path = filepath.ToSlash(path)
	pattern = filepath.ToSlash(pattern)

	// 处理 ** 模式（递归匹配）
	if strings.Contains(pattern, "**") {
		// 简单的 ** 处理：将 ** 替换为 .* 进行正则匹配
		regexPattern := strings.ReplaceAll(pattern, "**", ".*")
		regexPattern = strings.ReplaceAll(regexPattern, "*", "[^/]*")
		regexPattern = "^" + regexPattern + "$"

		matched, err := regexp.MatchString(regexPattern, path)
		if err != nil {
			return false
		}
		return matched
	}

	// 处理简单的 * 模式
	if strings.Contains(pattern, "*") {
		regexPattern := strings.ReplaceAll(pattern, "*", "[^/]*")
		regexPattern = "^" + regexPattern + "$"

		matched, err := regexp.MatchString(regexPattern, path)
		if err != nil {
			return false
		}

		// 添加调试日志
		if strings.Contains(path, "text-enhancement") {
			log.V2.Info().Str("pattern", pattern).Str("path", path).Str("regexPattern", regexPattern).Str("matched", fmt.Sprintf("%v", matched)).Emit()
		}

		return matched
	}

	// 精确匹配
	return path == pattern
}

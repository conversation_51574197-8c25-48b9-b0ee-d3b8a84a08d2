package virtualfile

import (
	"context"
	"io/fs"
	"os"

	"code.byted.org/ies/codin/application/codebase/repo/virtualfile/entity"
)

// FileManager 文件管理器接口
type FileManager interface {
	// ReadDir 读取目录内容
	ReadDir(ctx context.Context, path string) ([]entity.DirectoryEntry, error)

	// ReadFile 读取文件内容
	ReadFile(ctx context.Context, path string) (entity.FileContent, error)

	// Stat 获取文件信息
	Stat(ctx context.Context, path string) (entity.FileInfo, error)

	// Exists 检查文件或目录是否存在
	Exists(ctx context.Context, path string) bool

	// IsDir 检查是否为目录
	IsDir(ctx context.Context, path string) bool

	// GetRootPath 获取根路径
	GetRootPath() string

	// 实现 fs.FS 接口，用于与 doublestar.Glob 兼容
	fs.FS
}

// FileSystemProvider 文件系统提供者接口
type FileSystemProvider interface {
	// ReadDir 读取目录
	ReadDir(path string) ([]os.DirEntry, error)

	// ReadFile 读取文件
	ReadFile(path string) ([]byte, error)

	// Stat 获取文件信息
	Stat(path string) (os.FileInfo, error)

	// Exists 检查文件是否存在
	Exists(path string) bool
}

package virtualfile

import (
	"context"
	"os"
	"path/filepath"
	"strings"

	"io/fs"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/application/codebase/repo/virtualfile/entity"
	"code.byted.org/ies/codin/application/codebase/repo/virtualfile/local"
	"code.byted.org/ies/codin/application/codebase/repo/virtualfile/virtual"
	"code.byted.org/ies/codin/common/utils"
)

// FileManagerImpl 文件管理器实现
type FileManagerImpl struct {
	provider FileSystemProvider
	rootPath string
}

// NewFileManager 创建文件管理器
func NewFileManager(provider FileSystemProvider, rootPath string) FileManager {
	return &FileManagerImpl{
		provider: provider,
		rootPath: rootPath,
	}
}

// NewLocalFileManager 创建本地文件管理器
func NewLocalFileManager(rootPath string) FileManager {
	provider := local.NewLocalFileSystemProvider(rootPath)
	return NewFileManager(provider, rootPath)
}

// NewVirtualFileManager 创建虚拟文件管理器
func NewVirtualFileManager(rootPath string, virtualTree *entity.VirtualDirectory) FileManager {
	provider := virtual.NewVirtualFileSystemProvider(rootPath, virtualTree)
	return NewFileManager(provider, rootPath)
}

// ReadDir 读取目录内容
func (m *FileManagerImpl) ReadDir(ctx context.Context, path string) ([]entity.DirectoryEntry, error) {
	// log.V2.Info().With(ctx).Str("path", path).Emit()

	// 标准化路径
	normalizedPath := m.normalizePath(path)

	// 读取目录
	entries, err := m.provider.ReadDir(normalizedPath)
	if err != nil {
		logs.CtxWarn(ctx, "read dir error: %v", err)
		return nil, err
	}

	// 转换为DirectoryEntry接口
	result := make([]entity.DirectoryEntry, 0, len(entries))
	for _, entry := range entries {
		// 跳过隐藏文件
		if strings.HasPrefix(entry.Name(), ".") {
			continue
		}

		entryPath := utils.GetFilePath(entry.Name(), normalizedPath)
		result = append(result, entity.DirectoryEntry{
			Name:  entry.Name(),
			IsDir: entry.IsDir(),
			Path:  entryPath,
		})
	}

	return result, nil
}

// ReadFile 读取文件内容
func (m *FileManagerImpl) ReadFile(ctx context.Context, path string) (entity.FileContent, error) {
	// log.V2.Info().With(ctx).Str("path", path).Emit()

	// 标准化路径
	normalizedPath := m.normalizePath(path)

	// 检查文件是否存在
	if !m.provider.Exists(normalizedPath) {
		logs.CtxWarn(ctx, "file not exists: %s", normalizedPath)
		return entity.FileContent{
			Content:  "",
			Size:     0,
			Readable: false,
			Err:      os.ErrNotExist,
		}, os.ErrNotExist
	}

	// 读取文件
	content, err := m.provider.ReadFile(normalizedPath)
	if err != nil {
		logs.CtxWarn(ctx, "read file error: %v", err)
		return entity.FileContent{
			Content:  "",
			Size:     0,
			Readable: false,
			Err:      err,
		}, err
	}

	// 限制文件内容长度
	contentStr := string(content)
	if len(contentStr) > 10000 {
		contentStr = contentStr[:10000] + "...(内容已截断)"
	}

	return entity.FileContent{
		Content:  contentStr,
		Size:     int64(len(content)),
		Readable: true,
		Err:      nil,
	}, nil
}

// Stat 获取文件信息
func (m *FileManagerImpl) Stat(ctx context.Context, path string) (entity.FileInfo, error) {
	// log.V2.Info().With(ctx).Str("path", path).Emit()

	// 标准化路径
	normalizedPath := m.normalizePath(path)

	// 获取文件信息
	info, err := m.provider.Stat(normalizedPath)
	if err != nil {
		logs.CtxWarn(ctx, "stat error: %v", err)
		return entity.FileInfo{}, err
	}

	return entity.FileInfo{
		Name:    info.Name(),
		Size:    info.Size(),
		Mode:    info.Mode(),
		ModTime: info.ModTime(),
		IsDir:   info.IsDir(),
	}, nil
}

// Exists 检查文件或目录是否存在
func (m *FileManagerImpl) Exists(ctx context.Context, path string) bool {
	normalizedPath := m.normalizePath(path)
	return m.provider.Exists(normalizedPath)
}

// IsDir 检查是否为目录
func (m *FileManagerImpl) IsDir(ctx context.Context, path string) bool {
	normalizedPath := m.normalizePath(path)

	info, err := m.provider.Stat(normalizedPath)
	if err != nil {
		return false
	}

	return info.IsDir()
}

// GetRootPath 获取根路径
func (m *FileManagerImpl) GetRootPath() string {
	return m.rootPath
}

// Open 实现 fs.FS 接口，用于与 doublestar.Glob 兼容
func (m *FileManagerImpl) Open(name string) (fs.File, error) {
	// 标准化路径
	normalizedPath := m.normalizePath(name)

	// 检查文件是否存在
	if !m.provider.Exists(normalizedPath) {
		return nil, &fs.PathError{
			Op:   "open",
			Path: name,
			Err:  fs.ErrNotExist,
		}
	}

	// 获取文件信息
	info, err := m.provider.Stat(normalizedPath)
	if err != nil {
		return nil, &fs.PathError{
			Op:   "open",
			Path: name,
			Err:  err,
		}
	}

	// 如果是目录，返回目录文件
	if info.IsDir() {
		return &fileManagerDir{
			fileManager: m,
			path:        normalizedPath,
			info:        info,
		}, nil
	}

	// 如果是文件，返回文件
	return &fileManagerFile{
		fileManager: m,
		path:        normalizedPath,
		info:        info,
	}, nil
}

// normalizePath 标准化路径
func (m *FileManagerImpl) normalizePath(path string) string {
	// 如果路径是绝对路径，转换为相对路径
	if filepath.IsAbs(path) {
		relPath, err := filepath.Rel(m.rootPath, path)
		if err != nil {
			return path
		}
		return relPath
	}

	return path
}

// fileManagerFile 实现 fs.File 接口的文件
type fileManagerFile struct {
	fileManager *FileManagerImpl
	path        string
	info        os.FileInfo
	content     []byte
	readPos     int64
}

func (f *fileManagerFile) Stat() (fs.FileInfo, error) {
	return f.info, nil
}

func (f *fileManagerFile) Read(p []byte) (n int, err error) {
	// 懒加载文件内容
	if f.content == nil {
		content, err := f.fileManager.provider.ReadFile(f.path)
		if err != nil {
			return 0, err
		}
		f.content = content
	}

	if f.readPos >= int64(len(f.content)) {
		return 0, fs.ErrClosed
	}

	n = copy(p, f.content[f.readPos:])
	f.readPos += int64(n)
	return n, nil
}

func (f *fileManagerFile) Close() error {
	return nil
}

// fileManagerDir 实现 fs.File 接口的目录
type fileManagerDir struct {
	fileManager *FileManagerImpl
	path        string
	info        os.FileInfo
	entries     []os.DirEntry
	readPos     int
}

func (d *fileManagerDir) Stat() (fs.FileInfo, error) {
	return d.info, nil
}

func (d *fileManagerDir) Read(p []byte) (n int, err error) {
	return 0, &fs.PathError{
		Op:   "read",
		Path: d.path,
		Err:  fs.ErrInvalid,
	}
}

func (d *fileManagerDir) Close() error {
	return nil
}

func (d *fileManagerDir) ReadDir(n int) ([]fs.DirEntry, error) {
	// 懒加载目录条目
	if d.entries == nil {
		entries, err := d.fileManager.provider.ReadDir(d.path)
		if err != nil {
			return nil, err
		}
		d.entries = entries
	}

	if d.readPos >= len(d.entries) {
		return nil, fs.ErrClosed
	}

	var result []fs.DirEntry
	if n <= 0 {
		result = d.entries[d.readPos:]
		d.readPos = len(d.entries)
	} else {
		end := d.readPos + n
		if end > len(d.entries) {
			end = len(d.entries)
		}
		result = d.entries[d.readPos:end]
		d.readPos = end
	}

	return result, nil
}

package virtual

import (
	"path/filepath"
	"strings"

	"code.byted.org/ies/codin/application/codebase/repo/virtualfile/entity"
	"code.byted.org/ies/codin/common/utils"
)

// VirtualTreeBuilder 虚拟目录树构建器
type VirtualTreeBuilder struct {
	root *entity.VirtualDirectory
}

// NewVirtualTreeBuilder 创建虚拟目录树构建器
func NewVirtualTreeBuilder(rootPath string) *VirtualTreeBuilder {
	return &VirtualTreeBuilder{
		root: &entity.VirtualDirectory{
			Path:     rootPath,
			Children: make(map[string]*entity.VirtualFile),
		},
	}
}

// AddFile 添加文件到虚拟树
func (b *VirtualTreeBuilder) AddFile(path, content string) error {
	// 标准化路径
	normalizedPath := filepath.Clean(path)

	// 分割路径
	parts := strings.Split(normalizedPath, "/")
	if len(parts) == 0 {
		return entity.ErrInvalidPath
	}

	// 创建目录结构
	currentDir := b.root
	for i := 0; i < len(parts)-1; i++ {
		part := parts[i]
		if part == "" {
			continue
		}

		// 检查目录是否存在
		if existing, exists := currentDir.Children[part]; exists {
			if !existing.IsDir {
				return entity.ErrInvalidPath // 路径冲突
			}
			// 如果目录不存在，需要创建
			if existing.Children == nil {
				existing.Children = make(map[string]*entity.VirtualFile)
			}
		} else {
			// 创建新目录
			dirPath := utils.GetFilePath(part, currentDir.Path)
			currentDir.Children[part] = &entity.VirtualFile{
				Path:     dirPath,
				IsDir:    true,
				Size:     0,
				Children: make(map[string]*entity.VirtualFile),
			}
		}

		// 移动到子目录
		currentDir = &entity.VirtualDirectory{
			Path:     utils.GetFilePath(part, currentDir.Path),
			Children: currentDir.Children[part].Children,
		}
	}

	// 添加文件
	fileName := parts[len(parts)-1]
	if fileName == "" {
		return entity.ErrInvalidPath
	}

	currentDir.Children[fileName] = &entity.VirtualFile{
		Path:    path,
		Content: content,
		IsDir:   false,
		Size:    int64(len(content)),
	}

	return nil
}

// AddDirectory 添加目录到虚拟树
func (b *VirtualTreeBuilder) AddDirectory(path string) error {
	// 标准化路径
	normalizedPath := filepath.Clean(path)

	// 分割路径
	parts := strings.Split(normalizedPath, "/")
	if len(parts) == 0 {
		return entity.ErrInvalidPath
	}

	// 创建目录结构
	currentDir := b.root
	for _, part := range parts {
		if part == "" {
			continue
		}

		// 检查目录是否存在
		if existing, exists := currentDir.Children[part]; exists {
			if !existing.IsDir {
				return entity.ErrInvalidPath // 路径冲突
			}
			// 如果目录不存在，需要创建
			if existing.Children == nil {
				existing.Children = make(map[string]*entity.VirtualFile)
			}
		} else {
			// 创建新目录
			dirPath := utils.GetFilePath(part, currentDir.Path)
			currentDir.Children[part] = &entity.VirtualFile{
				Path:     dirPath,
				IsDir:    true,
				Size:     0,
				Children: make(map[string]*entity.VirtualFile),
			}
		}

		// 移动到子目录
		currentDir = &entity.VirtualDirectory{
			Path:     utils.GetFilePath(part, currentDir.Path),
			Children: currentDir.Children[part].Children,
		}
	}

	return nil
}

// AddFiles 批量添加文件
func (b *VirtualTreeBuilder) AddFiles(files map[string]string) error {
	for path, content := range files {
		if err := b.AddFile(path, content); err != nil {
			return err
		}
	}
	return nil
}

// AddDirectories 批量添加目录
func (b *VirtualTreeBuilder) AddDirectories(directories []string) error {
	for _, path := range directories {
		if err := b.AddDirectory(path); err != nil {
			return err
		}
	}
	return nil
}

// Build 构建虚拟目录树
func (b *VirtualTreeBuilder) Build() *entity.VirtualDirectory {
	return b.root
}

// GetFile 获取文件
func (b *VirtualTreeBuilder) GetFile(path string) (*entity.VirtualFile, error) {
	normalizedPath := filepath.Clean(path)
	parts := strings.Split(normalizedPath, "/")

	currentDir := b.root
	for i := 0; i < len(parts)-1; i++ {
		part := parts[i]
		if part == "" {
			continue
		}

		if child, exists := currentDir.Children[part]; exists && child.IsDir {
			if child.Children == nil {
				return nil, entity.ErrDirectoryNotFound
			}
			currentDir = &entity.VirtualDirectory{
				Path:     child.Path,
				Children: child.Children,
			}
		} else {
			return nil, entity.ErrDirectoryNotFound
		}
	}

	fileName := parts[len(parts)-1]
	if fileName == "" {
		return nil, entity.ErrInvalidPath
	}

	if file, exists := currentDir.Children[fileName]; exists && !file.IsDir {
		return file, nil
	}

	return nil, entity.ErrFileNotFound
}

// GetDirectory 获取目录
func (b *VirtualTreeBuilder) GetDirectory(path string) (*entity.VirtualDirectory, error) {
	normalizedPath := filepath.Clean(path)
	parts := strings.Split(normalizedPath, "/")

	currentDir := b.root
	for _, part := range parts {
		if part == "" {
			continue
		}

		if child, exists := currentDir.Children[part]; exists && child.IsDir {
			if child.Children == nil {
				return nil, entity.ErrDirectoryNotFound
			}
			currentDir = &entity.VirtualDirectory{
				Path:     child.Path,
				Children: child.Children,
			}
		} else {
			return nil, entity.ErrDirectoryNotFound
		}
	}

	return currentDir, nil
}

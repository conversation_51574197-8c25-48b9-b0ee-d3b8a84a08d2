package virtual

import (
	"os"
	"path/filepath"
	"strings"
	"time"

	"code.byted.org/ies/codin/application/codebase/repo/virtualfile/entity"
	"code.byted.org/ies/codin/common/utils"
)

// VirtualFileSystemProvider 虚拟文件系统提供者
type VirtualFileSystemProvider struct {
	rootPath    string
	virtualTree *entity.VirtualDirectory
}

// NewVirtualFileSystemProvider 创建虚拟文件系统提供者
func NewVirtualFileSystemProvider(rootPath string, virtualTree *entity.VirtualDirectory) *VirtualFileSystemProvider {
	return &VirtualFileSystemProvider{
		rootPath:    rootPath,
		virtualTree: virtualTree,
	}
}

// ReadDir 读取目录
func (p *VirtualFileSystemProvider) ReadDir(path string) ([]os.DirEntry, error) {
	// // 首先尝试从本地文件系统读取
	// if p.existsInLocal(path) {
	// 	return p.readFromLocal(path)
	// }

	// 如果本地不存在，从虚拟树中读取
	return p.readFromVirtual(path)
}

// ReadFile 读取文件
func (p *VirtualFileSystemProvider) ReadFile(path string) ([]byte, error) {
	// // 首先尝试从本地文件系统读取
	// if p.existsInLocal(path) {
	// 	return p.readFileFromLocal(path)
	// }

	// 如果本地不存在，从虚拟树中读取
	return p.readFileFromVirtual(path)
}

// Stat 获取文件信息
func (p *VirtualFileSystemProvider) Stat(path string) (os.FileInfo, error) {
	// // 首先尝试从本地文件系统获取
	// if p.existsInLocal(path) {
	// 	return p.statFromLocal(path)
	// }

	// 如果本地不存在，从虚拟树中获取
	return p.statFromVirtual(path)
}

// Exists 检查文件是否存在
func (p *VirtualFileSystemProvider) Exists(path string) bool {
	// return p.existsInLocal(path) || p.existsInVirtual(path)
	return p.existsInVirtual(path)
}

// existsInLocal 检查本地文件是否存在
func (p *VirtualFileSystemProvider) existsInLocal(path string) bool {
	fullPath := utils.GetFilePath(path, p.rootPath)
	_, err := os.Stat(fullPath)
	return err == nil
}

// existsInVirtual 检查虚拟文件是否存在
func (p *VirtualFileSystemProvider) existsInVirtual(path string) bool {
	return p.findInVirtualTree(path) != nil
}

// readFromLocal 从本地读取目录
func (p *VirtualFileSystemProvider) readFromLocal(path string) ([]os.DirEntry, error) {
	fullPath := utils.GetFilePath(path, p.rootPath)
	return os.ReadDir(fullPath)
}

// readFromVirtual 从虚拟树读取目录
func (p *VirtualFileSystemProvider) readFromVirtual(path string) ([]os.DirEntry, error) {
	virtualFile := p.findInVirtualTree(path)
	if virtualFile == nil || !virtualFile.IsDir {
		return nil, os.ErrNotExist
	}

	// 从虚拟目录中获取子项
	children := p.getVirtualChildren(path)
	entries := make([]os.DirEntry, 0, len(children))

	for _, child := range children {
		entries = append(entries, &virtualDirEntry{
			name:  filepath.Base(child.Path),
			isDir: child.IsDir,
		})
	}

	return entries, nil
}

// readFileFromLocal 从本地读取文件
func (p *VirtualFileSystemProvider) readFileFromLocal(path string) ([]byte, error) {
	fullPath := utils.GetFilePath(path, p.rootPath)
	return os.ReadFile(fullPath)
}

// readFileFromVirtual 从虚拟树读取文件
func (p *VirtualFileSystemProvider) readFileFromVirtual(path string) ([]byte, error) {
	virtualFile := p.findInVirtualTree(path)
	if virtualFile == nil || virtualFile.IsDir {
		return nil, os.ErrNotExist
	}
	if virtualFile.Content == "" {
		return []byte("**文件内容隐藏**"), nil
	}

	return []byte(virtualFile.Content), nil
}

// statFromLocal 从本地获取文件信息
func (p *VirtualFileSystemProvider) statFromLocal(path string) (os.FileInfo, error) {
	fullPath := utils.GetFilePath(path, p.rootPath)
	return os.Stat(fullPath)
}

// statFromVirtual 从虚拟树获取文件信息
func (p *VirtualFileSystemProvider) statFromVirtual(path string) (os.FileInfo, error) {
	virtualFile := p.findInVirtualTree(path)
	if virtualFile == nil {
		return nil, os.ErrNotExist
	}

	return &virtualFileInfo{
		name:  filepath.Base(path),
		size:  virtualFile.Size,
		isDir: virtualFile.IsDir,
	}, nil
}

// findInVirtualTree 在虚拟树中查找文件
func (p *VirtualFileSystemProvider) findInVirtualTree(path string) *entity.VirtualFile {
	if p.virtualTree == nil {
		return nil
	}

	// 将路径转换为相对路径
	relPath := strings.TrimPrefix(path, p.rootPath)
	relPath = strings.TrimPrefix(relPath, "/")
	// 移除末尾的斜杠
	relPath = strings.TrimSuffix(relPath, "/")

	// 在虚拟树中查找
	return p.findInVirtualDirectory(p.virtualTree, relPath)
}

// findInVirtualDirectory 在虚拟目录中递归查找
func (p *VirtualFileSystemProvider) findInVirtualDirectory(dir *entity.VirtualDirectory, path string) *entity.VirtualFile {
	if dir == nil || dir.Children == nil {
		return nil
	}

	// 如果路径为空，返回目录本身
	if path == "" {
		return &entity.VirtualFile{
			Path:     dir.Path,
			IsDir:    true,
			Children: dir.Children,
		}
	}

	// 分割路径
	parts := strings.Split(path, "/")
	if len(parts) == 0 {
		return nil
	}

	// 查找第一个部分
	child, exists := dir.Children[parts[0]]
	if !exists {
		return nil
	}

	// 如果只有一个部分，返回找到的文件
	if len(parts) == 1 {
		return child
	}

	// 如果是目录，继续递归查找
	if child.IsDir && child.Children != nil {
		// 构建剩余路径
		remainingPath := strings.Join(parts[1:], "/")
		return p.findInVirtualDirectory(&entity.VirtualDirectory{
			Path:     child.Path,
			Children: child.Children,
		}, remainingPath)
	}

	return nil
}

// getVirtualChildren 获取虚拟目录的子项
func (p *VirtualFileSystemProvider) getVirtualChildren(path string) []*entity.VirtualFile {
	virtualFile := p.findInVirtualTree(path)
	if virtualFile == nil || !virtualFile.IsDir || virtualFile.Children == nil {
		return []*entity.VirtualFile{}
	}

	children := make([]*entity.VirtualFile, 0, len(virtualFile.Children))
	for _, child := range virtualFile.Children {
		children = append(children, child)
	}

	return children
}

// virtualDirEntry 虚拟目录条目
type virtualDirEntry struct {
	name  string
	isDir bool
}

func (v *virtualDirEntry) Name() string {
	return v.name
}

func (v *virtualDirEntry) IsDir() bool {
	return v.isDir
}

func (v *virtualDirEntry) Type() os.FileMode {
	if v.isDir {
		return os.ModeDir
	}
	return 0
}

func (v *virtualDirEntry) Info() (os.FileInfo, error) {
	return &virtualFileInfo{
		name:  v.name,
		isDir: v.isDir,
	}, nil
}

// virtualFileInfo 虚拟文件信息
type virtualFileInfo struct {
	name    string
	size    int64
	isDir   bool
	modTime int64
}

func (v *virtualFileInfo) Name() string {
	return v.name
}

func (v *virtualFileInfo) Size() int64 {
	return v.size
}

func (v *virtualFileInfo) Mode() os.FileMode {
	if v.isDir {
		return os.ModeDir | 0755
	}
	return 0644
}

func (v *virtualFileInfo) ModTime() time.Time {
	if v.modTime == 0 {
		return time.Now()
	}
	return time.Unix(v.modTime, 0)
}

func (v *virtualFileInfo) IsDir() bool {
	return v.isDir
}

func (v *virtualFileInfo) Sys() interface{} {
	return nil
}

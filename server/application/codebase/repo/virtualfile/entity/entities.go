package entity

import (
	"io/fs"
	"time"
)

// fileInfo 文件信息实现
type FileInfo struct {
	Name    string      `json:"name"`
	Size    int64       `json:"size"`
	Mode    fs.FileMode `json:"mode"`
	ModTime time.Time   `json:"mod_time"`
	IsDir   bool        `json:"is_dir"`
}

// fileContent 文件内容实现
type FileContent struct {
	Content  string `json:"content"`
	Size     int64  `json:"size"`
	Readable bool   `json:"readable"`
	Err      error  `json:"err"`
}

// directoryEntry 目录条目实现
type DirectoryEntry struct {
	Name  string `json:"name"`
	IsDir bool   `json:"is_dir"`
	Path  string `json:"path"`
}

// VirtualFile 虚拟文件结构
type VirtualFile struct {
	Path     string                  `json:"path"`
	Content  string                  `json:"content"`
	IsDir    bool                    `json:"is_dir"`
	Size     int64                   `json:"size"`
	Children map[string]*VirtualFile `json:"children,omitempty"`
}

// VirtualDirectory 虚拟目录结构
type VirtualDirectory struct {
	Path     string                  `json:"path"`
	Children map[string]*VirtualFile `json:"children"`
}

package entity

import "errors"

// 文件管理器相关错误
var (
	// ErrUnknownFileManagerType 未知的文件管理器类型
	ErrUnknownFileManagerType = errors.New("unknown file manager type")

	// ErrFileNotFound 文件未找到
	ErrFileNotFound = errors.New("file not found")

	// ErrDirectoryNotFound 目录未找到
	ErrDirectoryNotFound = errors.New("directory not found")

	// ErrFileNotReadable 文件不可读
	ErrFileNotReadable = errors.New("file not readable")

	// ErrInvalidPath 无效路径
	ErrInvalidPath = errors.New("invalid path")

	// ErrVirtualFileNotAvailable 虚拟文件不可用
	ErrVirtualFileNotAvailable = errors.New("virtual file not available")
)

package local

import (
	"os"

	"code.byted.org/ies/codin/common/utils"
)

// LocalFileSystemProvider 本地文件系统提供者
type LocalFileSystemProvider struct {
	rootPath string
}

// NewLocalFileSystemProvider 创建本地文件系统提供者
func NewLocalFileSystemProvider(rootPath string) *LocalFileSystemProvider {
	return &LocalFileSystemProvider{
		rootPath: rootPath,
	}
}

// ReadDir 读取目录
func (p *LocalFileSystemProvider) ReadDir(path string) ([]os.DirEntry, error) {
	fullPath := utils.GetFilePath(path, p.rootPath)
	return os.ReadDir(fullPath)
}

// ReadFile 读取文件
func (p *LocalFileSystemProvider) ReadFile(path string) ([]byte, error) {
	fullPath := utils.GetFilePath(path, p.rootPath)
	return os.ReadFile(fullPath)
}

// Stat 获取文件信息
func (p *LocalFileSystemProvider) Stat(path string) (os.FileInfo, error) {
	fullPath := utils.GetFilePath(path, p.rootPath)
	return os.Stat(fullPath)
}

// Exists 检查文件是否存在
func (p *LocalFileSystemProvider) Exists(path string) bool {
	fullPath := utils.GetFilePath(path, p.rootPath)
	_, err := os.Stat(fullPath)
	return err == nil
}

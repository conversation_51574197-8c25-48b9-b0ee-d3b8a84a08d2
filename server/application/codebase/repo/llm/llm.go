package llm

import (
	"context"
	"fmt"
	"github.com/cloudwego/eino-ext/components/model/ark"
	"github.com/cloudwego/eino/schema"

	llmcommon "code.byted.org/ies/codin/common/llm"
)

func GetChatModel(ctx context.Context, scene Scene) (*llmcommon.ChatModelWithName, error) {
	switch scene {
	case SummaryScene:
		return CreateSeed16ForCodeSummary(ctx)
	case ThinkingScene:
		return CreateSeed16Thinking(ctx)
	}

	return nil, fmt.Errorf("unknown scene: %s", scene)
}

func ToSchemaMessage(systemPrompt, userPrompt string, messages ...*schema.Message) []*schema.Message {
	ret := make([]*schema.Message, 0)
	ret = append(ret, &schema.Message{Role: schema.System, Content: systemPrompt})
	ret = append(ret, messages...)
	ret = append(ret, &schema.Message{Role: schema.User, Content: userPrompt})
	return ret
}

func GetArkReasoningContent(message *schema.Message) string {
	reasoningContent, _ := ark.GetReasoningContent(message)
	return reasoningContent
}

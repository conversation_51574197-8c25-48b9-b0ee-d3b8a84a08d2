package llm

import (
	"context"
	"errors"
	"os"

	"code.byted.org/gopkg/env"
	"code.byted.org/ies/codin/common/llm"
	"code.byted.org/ies/codin/common/llm/ark"
	"code.byted.org/ies/codin/common/tcc"
)

func CreateSeed16ForCodeSummary(ctx context.Context) (*llm.ChatModelWithName, error) {
	if env.IsPPE() || env.IsProduct() {
		return codesummaryWithTce(ctx, false)
	}
	return codesummaryWithLocal(ctx, false)
}

func codesummaryWithTce(ctx context.Context, thinking bool) (*llm.ChatModelWithName, error) {
	apiKey, err := tcc.GetTccReader().GetArkApiKey(ctx)
	if err != nil {
		return nil, err
	}
	return ark.CreateSeed16_Pro(ctx, apiKey, seed16Pro, thinking)
}

func codesummaryWithLocal(ctx context.Context, thinking bool) (*llm.ChatModelWithName, error) {
	apiKey := os.Getenv("ARK_KEY")
	model := os.Getenv("CODESUMMARY_SEED16_MODEL")
	if apiKey == "" || model == "" {
		return nil, errors.New("ARK_KEY or CODESUMMARY_SEED16_MODEL is not set")
	}
	return ark.CreateSeed16_Pro(ctx, apiKey, model, thinking)
}

func CreateSeed16Thinking(ctx context.Context) (*llm.ChatModelWithName, error) {
	if env.IsPPE() || env.IsProduct() {
		return codesummaryWithTce(ctx, true)
	}
	return codesummaryWithLocal(ctx, true)
}

package workspace

import (
	"context"
	"testing"

	"code.byted.org/gopkg/jsonx"
	"github.com/stretchr/testify/assert"
)

func Test_workspace_Add(t *testing.T) {
	var (
		ctx      = context.Background()
		repoName = "ies/qukecheng_lvweb"
		repoURL  = "https://code.byted.org/ies/qukecheng_lvweb"
		branch   = "feat/qkc"
	)

	addErr := Workspace.Add(ctx, repoName, repoURL, branch)
	assert.Nil(t, addErr)

	tempWorkspace := Workspace.GetTempWorkspace(repoName, branch)
	t.Logf("tempWorkspace: %v", jsonx.ToString(tempWorkspace))
}

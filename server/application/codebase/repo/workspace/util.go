package workspace

import (
	"context"
	"fmt"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
)

func cloneRepositoryUseApi(ctx context.Context, repoName, repoURL, branch, repoPath string) (string, error) {
	logs.CtxInfo(ctx, "拉取代码仓库, repoName=%v, repoURL=%v, branch=%v, repoPath=%v", repoName, repoURL, branch, repoPath)

	// 创建目标目录
	if _, err := os.Stat(repoPath); err == nil {
		if removeErr := os.RemoveAll(repoPath); removeErr != nil {
			return "", fmt.Errorf("无法删除已存在的仓库目录: %w", removeErr)
		}
	}

	// 构建API URL
	originRepoName := repoName
	repoName = url.QueryEscape(repoName)
	apiURL := fmt.Sprintf("https://code.byted.org/api/v4/projects/%s/repository/archive", repoName)
	if branch != "" {
		apiURL += fmt.Sprintf("?sha=%s", branch)
	}
	log.V2.Info().With(ctx).Str("使用API下载仓库").Str(apiURL).Emit()

	// 设置最大重试次数
	maxRetries := 3
	var err error
	var output []byte
	success := false

	// 使用重试循环进行下载和解压
	for attempt := 1; attempt <= maxRetries; attempt++ {
		log.V2.Info().With(ctx).Str("尝试下载和解压，第").Int(attempt).Str("次，共").Int(maxRetries).Str("次").Emit()

		// 创建临时文件用于保存下载的压缩包
		archiveRepoName := strings.ReplaceAll(originRepoName, "/", "-")
		archiveBranch := strings.ReplaceAll(branch, "/", "-")
		archivePath := filepath.Join(os.TempDir(), fmt.Sprintf("%s_%s_%s.tar.gz", archiveRepoName, archiveBranch, time.Now().Format("20060102")))
		logs.CtxInfo(ctx, "archivePath=%v", archivePath)

		// 尝试清理可能存在的旧文件
		_ = os.Remove(archivePath)

		// 使用固定的私有令牌进行认证
		privateToken := "5ub5aRjgax8ke4heTLEH"

		// 使用更可靠的curl命令下载
		cmd := exec.Command(
			"curl",
			"-L",
			"-H", fmt.Sprintf("private-token: %s", privateToken),
			"--fail",       // 服务器错误时使curl返回非零状态码
			"--retry", "3", // 失败时重试3次
			"--retry-delay", "2", // 重试间隔2秒
			"--connect-timeout", "30", // 连接超时30秒
			"--max-time", "300", // 总下载时间不超过5分钟
			"-o", archivePath,
			apiURL,
		)
		logs.CtxInfo(ctx, "执行下载命令: %v", cmd.String())
		logs.CtxInfo(ctx, "使用增强的下载参数")

		// 执行下载
		output, err = cmd.CombinedOutput()
		if err != nil {
			logs.CtxError(ctx, "下载失败，尝试 %v 次，错误: %v, 输出: %v", attempt, err, string(output))
			if attempt == maxRetries {
				return "", fmt.Errorf("多次尝试下载仓库均失败: %w, 输出: %s", err, string(output))
			}
			time.Sleep(5 * time.Second) // 重试前等待
			continue
		}

		// 添加短暂延迟，确保文件系统同步完成
		log.V2.Info().With(ctx).Str("等待文件系统同步").Emit()
		time.Sleep(2 * time.Second)

		// 检查下载的文件大小，确认下载成功
		fileInfo, err := os.Stat(archivePath)
		if err != nil {
			log.V2.Error().With(ctx).Str("获取文件信息失败，尝试").Int(attempt).Str("错误").Str(err.Error()).Emit()
			if attempt == maxRetries {
				return "", fmt.Errorf("多次尝试后无法获取下载文件信息: %w", err)
			}
			time.Sleep(3 * time.Second)
			continue
		}

		if fileInfo.Size() == 0 {
			err = fmt.Errorf("下载的文件为空，可能是API访问被拒绝或仓库不存在")
			log.V2.Error().With(ctx).Str("文件为空，尝试").Int(attempt).Emit()
			if attempt == maxRetries {
				return "", err
			}
			time.Sleep(3 * time.Second)
			continue
		}

		// 检查文件大小是否合理（至少应该有一定的大小）
		minExpectedSize := int64(1024) // 至少1KB
		if fileInfo.Size() < minExpectedSize {
			err = fmt.Errorf("下载的文件过小 (%d bytes)，可能下载不完整", fileInfo.Size())
			log.V2.Error().With(ctx).Str("文件过小，尝试").Int(attempt).Int64(fileInfo.Size()).Emit()
			if attempt == maxRetries {
				return "", err
			}
			time.Sleep(3 * time.Second)
			continue
		}

		log.V2.Info().With(ctx).Str("下载完成，文件大小").Int64(fileInfo.Size()).Emit()

		// 验证tar.gz文件是否完整
		validateCmd := exec.Command("gzip", "-t", archivePath)
		validateOutput, validateErr := validateCmd.CombinedOutput()
		if validateErr != nil {
			log.V2.Error().With(ctx).Str("压缩包验证失败，尝试").Int(attempt).Str(string(validateOutput)).Emit()
			if attempt == maxRetries {
				return "", fmt.Errorf("多次尝试后压缩包仍然不完整或已损坏: %w", validateErr)
			}
			time.Sleep(3 * time.Second)
			continue
		}
		log.V2.Info().With(ctx).Str("压缩包验证成功").Emit()

		// 验证文件没有被占用
		file, err := os.Open(archivePath)
		if err != nil {
			log.V2.Error().With(ctx).Str("无法打开归档文件，尝试").Int(attempt).Str(err.Error()).Emit()
			if attempt == maxRetries {
				return "", fmt.Errorf("多次尝试后仍无法打开归档文件: %w", err)
			}
			time.Sleep(3 * time.Second)
			continue
		}
		file.Close()

		// 确保目标目录存在
		if err = os.MkdirAll(repoPath, 0755); err != nil {
			log.V2.Error().With(ctx).Str("创建解压目录失败，尝试").Int(attempt).Str(err.Error()).Emit()
			if attempt == maxRetries {
				return "", fmt.Errorf("多次尝试后仍无法创建解压目录: %w", err)
			}
			time.Sleep(2 * time.Second)
			continue
		}

		// 解压前先等待确保文件系统缓存已刷新
		log.V2.Info().With(ctx).Str("等待文件系统缓存刷新").Emit()
		time.Sleep(1 * time.Second)

		// 解压缩归档文件，使用-v选项增加详细输出
		log.V2.Info().With(ctx).Str("解压代码仓库").Str(archivePath).Emit()
		cmd = exec.Command("tar", "-xzvf", archivePath, "-C", repoPath, "--strip-components=1")
		output, err = cmd.CombinedOutput()
		if err != nil {
			// 记录更详细的错误信息
			log.V2.Error().With(ctx).Str("解压失败，尝试").Int(attempt).Str(string(output)).Emit()

			// 尝试列出归档文件内容以进行诊断
			listCmd := exec.Command("tar", "-tvf", archivePath)
			listOutput, _ := listCmd.CombinedOutput()
			log.V2.Info().With(ctx).Str("归档文件内容").Str(string(listOutput)).Emit()

			if attempt == maxRetries {
				return "", fmt.Errorf("多次尝试后仍无法解压仓库: %w, 输出: %s", err, string(output))
			}

			// 清理可能部分解压的文件
			os.RemoveAll(repoPath)
			if err = os.MkdirAll(repoPath, 0755); err != nil {
				return "", fmt.Errorf("清理后无法重新创建解压目录: %w", err)
			}

			time.Sleep(5 * time.Second)
			continue
		}

		// 解压后等待文件系统操作完成
		log.V2.Info().With(ctx).Str("等待解压操作完成").Emit()
		time.Sleep(2 * time.Second)

		// 验证解压结果
		files, err := os.ReadDir(repoPath)
		if err != nil {
			log.V2.Error().With(ctx).Str("无法读取解压后的目录，尝试").Int(attempt).Str(err.Error()).Emit()
			if attempt == maxRetries {
				return "", fmt.Errorf("多次尝试后仍无法读取解压后的目录: %w", err)
			}
			time.Sleep(3 * time.Second)
			continue
		}

		if len(files) == 0 {
			err = fmt.Errorf("解压后目录为空，可能解压失败")
			log.V2.Error().With(ctx).Str("解压后目录为空，尝试").Int(attempt).Emit()
			if attempt == maxRetries {
				return "", err
			}
			time.Sleep(3 * time.Second)
			continue
		}

		// 列出解压后的文件结构以便调试
		log.V2.Info().With(ctx).Str("解压成功，目录内容数量").Int(len(files)).Emit()
		for i, f := range files {
			if i < 10 { // 只记录前10个文件，避免日志过大
				log.V2.Info().With(ctx).Str("文件/目录").Str(f.Name()).Emit()
			}
		}

		// 输出解压结果
		log.V2.Info().With(ctx).Str("解压成功，清理归档文件").Str(archivePath).Emit()

		// 清理下载的压缩包
		os.Remove(archivePath)

		success = true
		// 成功跳出循环
		break
	}

	if !success {
		return "", fmt.Errorf("经过%d次尝试后，仍无法成功下载和解压仓库", maxRetries)
	}

	return repoPath, nil
}

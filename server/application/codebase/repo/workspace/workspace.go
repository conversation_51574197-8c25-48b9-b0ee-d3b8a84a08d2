package workspace

import (
	"code.byted.org/gopkg/jsonx"
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
)

var Workspace *workspace
var once sync.Once

func init() {
	once.Do(func() {
		Workspace = newWorkspace()
	})
}

// TempWorkspace 临时工作空间结构 @todo LRU 逐出逻辑，防止重入时使用旧的仓库
type TempWorkspace struct {
	Path       string // 工作空间路径
	RepoURL    string // 仓库URL
	RepoName   string // 仓库名称
	RepoBranch string // 仓库分支
}

func getWorkspaceDir() string {
	if env.IsPPE() || env.IsProduct() {
		return "codesearch_workspaces"
	}
	return "application/codesearch/codesearch_workspaces"
}

type workspace struct {
	repoKeyToTempWorkspace map[string]*TempWorkspace // 工作空间映射表
	mu                     sync.RWMutex              // 读写锁
	basePath               string                    // 基础路径
}

// NewWorkspace 创建新的工作空间管理器
func newWorkspace() *workspace {
	basePath := os.Getenv("CODIN_CODESEARCH_WORKSPACE")
	basePath = filepath.Join(basePath, getWorkspaceDir())

	// 确保基础目录存在
	if err := os.MkdirAll(basePath, 0777); err != nil {
		log.V2.Error().Str("创建基础目录失败").Error(err).Emit()
		return nil
	}

	return &workspace{
		repoKeyToTempWorkspace: make(map[string]*TempWorkspace),
		basePath:               basePath,
	}
}

func (m *workspace) getRepoKey(repoName, branch string) string {
	return repoName + "_" + branch
}

func (m *workspace) getRepoPath(repoName, branch string) string {
	relativePath := filepath.Join(m.basePath, repoName, branch)
	absPath, _ := filepath.Abs(relativePath)
	return absPath
}

// Add 添加新的仓库到工作空间
func (m *workspace) Add(ctx context.Context, repoName, repoURL, branch string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	addSucceed := false

	repoKey := m.getRepoKey(repoName, branch)
	repoPath := m.getRepoPath(repoName, branch)

	logs.CtxInfo(ctx, "WorkspaceManager.Add, repoName=%s, repoURL=%s, branch=%s", repoName, repoURL, branch)
	logs.CtxInfo(ctx, "WorkspaceManager.Add, repoKey=%s, repoPath=%s", repoKey, repoPath)

	// 1. 先检查内存map
	if ws, exists := m.repoKeyToTempWorkspace[repoKey]; exists {
		logs.CtxInfo(ctx, "WorkspaceManager.Add, repoKey=%s, already exists in %s", repoKey, ws.Path)
		return nil
	}

	ws := &TempWorkspace{
		Path:       repoPath,
		RepoURL:    repoURL,
		RepoName:   repoName,
		RepoBranch: branch,
	}
	logs.CtxInfo(ctx, "WorkspaceManager.Add, ws=%s", jsonx.ToString(ws))

	// 2. 检查本地是否已有该repo目录（支持多级路径）
	if info, err := os.Stat(repoPath); err == nil && info.IsDir() {
		// 找到已有目录，直接加载
		m.repoKeyToTempWorkspace[repoKey] = ws
		logs.CtxInfo(ctx, "WorkspaceManager.Add, repo=%s loaded from disk at %s", repoName, ws.Path)
		return nil
	}

	// 注释：如果磁盘读取失败，直接忽略，走后续clone逻辑
	// 3. 创建新的工作空间
	if mkdirErr := os.MkdirAll(repoPath, 0755); mkdirErr != nil {
		return fmt.Errorf("WorkspaceManager.Add, mkdir failed err=%v", mkdirErr)
	}

	// 清理失败的工作空间
	defer func() {
		if !addSucceed {
			_ = os.RemoveAll(repoPath)
		}
	}()

	// 4. 克隆仓库
	if rp, cloneErr := cloneRepositoryUseApi(ctx, repoName, repoURL, branch, repoPath); cloneErr != nil {
		logs.CtxError(ctx, "WorkspaceManager.Add, repo=%s, rp=%v, cloneErr=%v", repoName, rp, cloneErr)
		return cloneErr
	}

	// 5. 创建并保存工作空间信息
	m.repoKeyToTempWorkspace[repoKey] = ws
	addSucceed = true
	return nil
}

func (m *workspace) GetTempWorkspace(repoName, branch string) *TempWorkspace {
	m.mu.RLock()
	defer m.mu.RUnlock()
	repoKey := m.getRepoKey(repoName, branch)
	if ws, exists := m.repoKeyToTempWorkspace[repoKey]; exists {
		return ws
	}
	return nil
}

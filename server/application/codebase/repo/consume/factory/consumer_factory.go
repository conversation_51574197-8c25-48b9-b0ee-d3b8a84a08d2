package factory

import (
	"code.byted.org/ies/codin/application/codebase/repo/consume/manager"
	taskrepo "code.byted.org/ies/codin/application/codebase/repo/taskrepo/interface"
)

// ConsumerFactory 消费者工厂
type ConsumerFactory struct {
	// 可以添加配置、依赖等
}

// NewConsumerFactory 创建消费者工厂
func NewConsumerFactory() *ConsumerFactory {
	return &ConsumerFactory{}
}

// CreateManager 创建消费管理器
func (f *ConsumerFactory) CreateManager(taskRepo taskrepo.TaskRepository) *manager.ConsumerManager {
	return manager.NewConsumerManager(taskRepo)
}

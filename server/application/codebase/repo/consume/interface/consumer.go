package _interface

import (
	"context"

	"code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
)

// Consumer 消费者接口
type Consumer interface {
	// GetType 获取消费者类型
	GetType() entity.IndexType
	// ShouldConsume 判断是否应该消费该任务
	ShouldConsume(task *entity.IndexTask) bool
	// Consume 消费任务，确保一次执行成功
	Consume(ctx context.Context, task *entity.IndexTask)
}

// Registry 消费者注册中心
type Registry interface {
	// Register 注册消费者
	Register(consumer Consumer)
	// GetConsumers 获取所有已注册的消费者
	GetConsumers() []Consumer
	// GetConsumer 获取指定类型的消费者
	GetConsumer(taskType entity.IndexType) Consumer
}

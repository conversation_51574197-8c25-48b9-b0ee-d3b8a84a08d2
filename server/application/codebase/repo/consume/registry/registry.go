package registry

import (
	"sync"

	_interface "code.byted.org/ies/codin/application/codebase/repo/consume/interface"
	"code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
)

// ConsumerRegistry 消费者注册中心实现
type ConsumerRegistry struct {
	consumers map[entity.IndexType]_interface.Consumer
	mu        sync.RWMutex
}

// NewConsumerRegistry 创建消费者注册中心
func NewConsumerRegistry() *ConsumerRegistry {
	return &ConsumerRegistry{
		consumers: make(map[entity.IndexType]_interface.Consumer),
	}
}

// Register 注册消费者
func (r *ConsumerRegistry) Register(consumer _interface.Consumer) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.consumers[consumer.GetType()] = consumer
}

// GetConsumers 获取所有已注册的消费者
func (r *ConsumerRegistry) GetConsumers() []_interface.Consumer {
	r.mu.RLock()
	defer r.mu.RUnlock()
	consumers := make([]_interface.Consumer, 0, len(r.consumers))
	for _, consumer := range r.consumers {
		consumers = append(consumers, consumer)
	}
	return consumers
}

// GetConsumer 获取指定类型的消费者
func (r *ConsumerRegistry) GetConsumer(taskType entity.IndexType) _interface.Consumer {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.consumers[taskType]
}

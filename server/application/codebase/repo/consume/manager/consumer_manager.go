package manager

import (
	"context"

	"code.byted.org/gopkg/logs"
	_interface "code.byted.org/ies/codin/application/codebase/repo/consume/interface"
	"code.byted.org/ies/codin/application/codebase/repo/consume/registry"
	"code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
	taskrepo "code.byted.org/ies/codin/application/codebase/repo/taskrepo/interface"
)

// ConsumerManager 消费管理器
type ConsumerManager struct {
	registry *registry.ConsumerRegistry
	taskRepo taskrepo.TaskRepository
}

// NewConsumerManager 创建消费管理器
func NewConsumerManager(taskRepo taskrepo.TaskRepository) *ConsumerManager {
	return &ConsumerManager{
		registry: registry.NewConsumerRegistry(),
		taskRepo: taskRepo,
	}
}

// RegisterConsumer 注册消费者
func (m *ConsumerManager) RegisterConsumer(consumer _interface.Consumer) {
	m.registry.Register(consumer)
}

// ConsumeTask 消费任务
func (m *ConsumerManager) ConsumeTask(ctx context.Context) {

	logs.CtxInfo(ctx, "start ConsumeTask")
	task, err := m.taskRepo.GetNextTask(ctx)
	if err != nil {
		logs.CtxError(ctx, "get next task failed: %v", err)
		return
	}
	logs.CtxInfo(ctx, "get next task success: %+v", task)
	if task.Status != entity.IndexTaskStatusPending {
		logs.CtxInfo(ctx, "task status is not pending: %+v", task)
		return
	}

	// 获取对应的消费者
	consumer := m.registry.GetConsumer(task.IndexType)
	if consumer == nil {
		logs.CtxError(ctx, "get consumer failed: %+v", task)
		return
	}

	// 检查是否应该消费
	if !consumer.ShouldConsume(task) {
		logs.CtxError(ctx, "should not consume: %+v", task)
		return
	}

	// 更新任务状态为运行中
	task.Status = entity.IndexTaskStatusRunning
	logs.CtxInfo(ctx, "start consume: %+v", task)

	// 执行消费
	consumer.Consume(ctx, task)
}

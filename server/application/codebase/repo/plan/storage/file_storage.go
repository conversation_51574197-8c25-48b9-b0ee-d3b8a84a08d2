package planStorage

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"sort"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codebase/repo/plan/entity"
)

// ===== 文件存储实现 / File Storage Implementation =====

// FileStorage 文件存储实现 / File storage implementation
// 遵循依赖反转原则 / Follow Dependency Inversion Principle
type FileStorage struct {
	filePath  string // 存储文件路径 / Storage file path
	namespace string // 命名空间，用于隔离不同请求的计划数据 / Namespace for isolating plan data from different requests
}

// PlanData 计划数据集合 / Plan data collection
// 用于在单个文件中存储多个计划 / Used to store multiple plans in a single file
type PlanData struct {
	Plans     map[string]entity.PlanRecord `json:"plans"`      // 计划映射表 / Plan mapping
	UpdatedAt time.Time                    `json:"updated_at"` // 最后更新时间 / Last updated time
}

// NewFileStorage 创建文件存储实例 / Create file storage instance
// @param basePath 存储根目录路径 / Base storage directory path
// @param namespace 命名空间，用于隔离不同请求的计划数据 / Namespace for isolating plan data from different requests
// @return PlanStorage 计划存储接口 / Plan storage interface
func NewFileStorage(basePath string, namespace string) PlanStorage {
	// 使用命名空间作为文件名的一部分，确保不同命名空间存储在不同文件中
	// Use namespace as part of filename to ensure different namespaces store in different files
	escapedNamespace := url.QueryEscape(namespace)
	fileName := fmt.Sprintf("plans_%s.json", escapedNamespace)
	filePath := filepath.Join(basePath, fileName)

	// 确保目录存在 / Ensure directory exists
	if err := os.MkdirAll(basePath, 0755); err != nil {
		log.V2.Error().Str("error", err.Error()).Emit()
	}

	return &FileStorage{
		filePath:  filePath,
		namespace: namespace,
	}
}

// SaveAllPlans 批量保存所有计划到文件 / Batch save all plans to file
// @param ctx 上下文信息 / Context information
// @param plans 所有计划数据 / All plan data
// @return error 错误信息 / Error information
func (fs *FileStorage) SaveAllPlans(ctx context.Context, plans map[string]entity.PlanRecord) error {
	// 构建计划数据集合 / Build plan data collection
	planData := &PlanData{
		Plans:     plans,
		UpdatedAt: time.Now(),
	}

	// 保存到文件 / Save to file
	return fs.savePlanData(ctx, planData)
}

// LoadAllPlans 从文件加载所有计划 / Load all plans from file
func (fs *FileStorage) LoadAllPlans(ctx context.Context) (map[string]entity.PlanRecord, error) {
	// 加载计划数据 / Load plan data
	planData, err := fs.loadPlanData(ctx)
	if err != nil {
		log.V2.Error().With(ctx).Str("namespace: ", fs.namespace).Str("load plan data error").Error(err).Emit()
		return nil, fmt.Errorf("加载计划数据失败 / failed to load plan data: %w", err)
	}

	log.V2.Info().With(ctx).Str("namespace: ", fs.namespace).Str("file_path", fs.filePath).Str("plan_count", fmt.Sprintf("%d", len(planData.Plans))).Emit()
	return planData.Plans, nil
}

// DeletePlan 删除计划 / Delete plan
func (fs *FileStorage) DeletePlan(ctx context.Context, planID string) error {
	// 加载计划数据 / Load plan data
	planData, err := fs.loadPlanData(ctx)
	if err != nil {
		log.V2.Error().With(ctx).Str("namespace: ", fs.namespace).Str("load plan data error").Error(err).Emit()
		return fmt.Errorf("加载计划数据失败 / failed to load plan data: %w", err)
	}

	// 删除指定计划 / Delete specified plan
	if _, exists := planData.Plans[planID]; !exists {
		log.V2.Error().With(ctx).Str("namespace: ", fs.namespace).Str("plan not found").Str("plan_id", planID).Emit()
		return fmt.Errorf("计划不存在 / plan not found: %s", planID)
	}

	delete(planData.Plans, planID)
	planData.UpdatedAt = time.Now()

	// 保存更新后的数据 / Save updated data
	return fs.savePlanData(ctx, planData)
}

// ListPlans 列出所有计划ID / List all plan IDs
func (fs *FileStorage) ListPlans(ctx context.Context) ([]string, error) {
	// 加载计划数据 / Load plan data
	planData, err := fs.loadPlanData(ctx)
	if err != nil {
		log.V2.Error().With(ctx).Str("namespace: ", fs.namespace).Str("load plan data error").Error(err).Emit()
		return nil, fmt.Errorf("加载计划数据失败 / failed to load plan data: %w", err)
	}

	// 提取计划ID / Extract plan IDs
	planIDs := make([]string, 0, len(planData.Plans))
	for planID := range planData.Plans {
		planIDs = append(planIDs, planID)
	}

	// 排序计划ID / Sort plan IDs
	sort.Strings(planIDs)

	log.V2.Info().With(ctx).Str("namespace: ", fs.namespace).Str("plan_count", fmt.Sprintf("%d", len(planIDs))).Emit()
	return planIDs, nil
}

// loadPlanData 从文件加载计划数据 / Load plan data from file
func (fs *FileStorage) loadPlanData(ctx context.Context) (*PlanData, error) {
	if _, err := os.Stat(fs.filePath); os.IsNotExist(err) {
		log.V2.Info().With(ctx).Str("namespace: ", fs.namespace).Str("plan file not exist, create new").Emit()
		// 文件不存在，返回空数据 / File doesn't exist, return empty data
		return &PlanData{
			Plans:     map[string]entity.PlanRecord{},
			UpdatedAt: time.Now(),
		}, nil
	}

	// 读取文件 / Read file
	data, err := os.ReadFile(fs.filePath)
	if err != nil {
		log.V2.Error().With(ctx).Str("namespace: ", fs.namespace).Str("read plan file error").Error(err).Emit()
		return nil, fmt.Errorf("读取计划文件失败 / failed to read plan file: %w", err)
	}

	// 反序列化数据 / Deserialize data
	var planData PlanData
	if err := json.Unmarshal(data, &planData); err != nil {
		log.V2.Error().With(ctx).Str("namespace: ", fs.namespace).Str("deserialize plan data error").Error(err).Emit()
		return nil, fmt.Errorf("反序列化计划数据失败 / failed to deserialize plan data: %w", err)
	}

	log.V2.Info().With(ctx).Str("namespace: ", fs.namespace).Str("plan file exists").Str("plan_count", fmt.Sprintf("%d", len(planData.Plans))).Emit()
	return &planData, nil
}

// savePlanData 保存计划数据到文件 / Save plan data to file
func (fs *FileStorage) savePlanData(ctx context.Context, planData *PlanData) error {
	// 序列化数据 / Serialize data
	data, err := json.MarshalIndent(planData, "", "  ")
	if err != nil {
		log.V2.Error().With(ctx).Str("namespace: ", fs.namespace).Str("serialize plan data error").Error(err).Emit()
		return fmt.Errorf("序列化计划数据失败 / failed to serialize plan data: %w", err)
	}

	// 写入文件 / Write to file
	if err := os.WriteFile(fs.filePath, data, 0644); err != nil {
		log.V2.Error().With(ctx).Str("namespace: ", fs.namespace).Str("write plan file error").Error(err).Emit()
		return fmt.Errorf("写入计划文件失败 / failed to write plan file: %w", err)
	}

	log.V2.Info().With(ctx).Str("namespace: ", fs.namespace).Str("file_path", fs.filePath).Str("plan_count", fmt.Sprintf("%d", len(planData.Plans))).Emit()
	return nil
}

// GetNamespace 获取当前命名空间 / Get current namespace
// @return string 当前命名空间 / Current namespace
func (fs *FileStorage) GetNamespace() string {
	return fs.namespace
}

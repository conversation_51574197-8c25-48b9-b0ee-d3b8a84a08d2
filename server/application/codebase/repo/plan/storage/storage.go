package planStorage

import (
	"context"

	"code.byted.org/ies/codin/application/codebase/repo/plan/entity"
)

// PlanStorage 计划存储接口 / Plan storage interface
// 定义计划数据的持久化操作抽象，遵循依赖倒置原则
// Defines abstraction for plan data persistence operations, following dependency inversion principle
type PlanStorage interface {
	// SaveAllPlans 批量保存所有计划到存储系统 / Batch save all plans to storage system
	// @param ctx 上下文信息 / Context information
	// @param plans 所有计划数据 / All plan data
	// @return error 保存失败时的错误信息 / Error information on save failure
	SaveAllPlans(ctx context.Context, plans map[string]entity.PlanRecord) error

	// LoadAllPlans 从存储系统加载所有计划 / Load all plans from storage system
	// @param ctx 上下文信息 / Context information
	// @return (map[string]entity.PlanRecord, error) 计划记录映射和错误信息 / Plan record mapping and error information
	LoadAllPlans(ctx context.Context) (map[string]entity.PlanRecord, error)

	// DeletePlan 从存储系统删除计划 / Delete plan from storage system
	// @param ctx 上下文信息 / Context information
	// @param planID 计划唯一标识 / Plan unique identifier
	// @return error 删除失败时的错误信息 / Error information on delete failure
	DeletePlan(ctx context.Context, planID string) error

	// ListPlans 列出所有计划的ID / List all plan IDs
	// @param ctx 上下文信息 / Context information
	// @return ([]string, error) 计划ID列表和错误信息 / Plan ID list and error information
	ListPlans(ctx context.Context) ([]string, error)

	// GetNamespace 获取当前命名空间 / Get current namespace
	// @return string 当前命名空间 / Current namespace
	GetNamespace() string
}

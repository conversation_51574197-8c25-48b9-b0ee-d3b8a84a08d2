package service

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codebase/repo/plan/entity"
	planStorage "code.byted.org/ies/codin/application/codebase/repo/plan/storage"
	"code.byted.org/ies/codin/common/utils"
)

// PlanRecordManager 计划记录管理器 / Plan record manager
// 负责管理计划的生命周期，包括创建、执行、监控等核心业务逻辑
type PlanRecordManager struct {
	namespace string                        // 命名空间，用于隔离不同请求的计划 / Namespace for isolating plans from different requests
	plans     map[string]*entity.PlanRecord // 内存中的计划缓存 / In-memory plan cache
	mutex     sync.RWMutex                  // 读写锁保护并发访问 / Read-write lock for concurrent access protection
	storage   *planStorage.PlanStorage      // 存储接口实现 / Storage interface implementation
}

// NewPlanRecordManager 创建计划记录管理器实例 / Create plan record manager instance
// @param basePath 基础路径 / Base path
// @param namespace 命名空间，用于隔离不同请求的计划 / Namespace for isolating plans from different requests
// @return *PlanRecordManager 管理器实例 / Manager instance
func NewPlanRecordManager() *PlanRecordManager {
	manager := &PlanRecordManager{
		plans: make(map[string]*entity.PlanRecord),
	}

	return manager
}

func (prm *PlanRecordManager) Init(basePath string, namespace string) {
	prm.mutex.Lock()
	defer prm.mutex.Unlock()
	storage := planStorage.NewFileStorage(basePath, namespace)
	prm.storage = &storage
	prm.namespace = namespace // 设置命名空间 / Set namespace

	// 加载现有计划 / Load existing plans
	// if plans, err := storage.LoadAllPlans(context.Background()); err == nil {
	// 	// 只保留当前命名空间的计划 / Only keep plans from current namespace
	// 	cleanedPlans := prm.cleanupPlans(plans)
	// 	prm.plans = cleanedPlans
	// 	log.V2.Info().Str("namespace", namespace).Str("loaded_plans", fmt.Sprintf("%d", len(plans))).Str("cleaned_plans", fmt.Sprintf("%d", len(cleanedPlans))).Emit()
	// } else {
	// 	// 如果加载失败，创建空计划缓存 / If loading fails, create empty plan cache
	// 	prm.plans = make(map[string]*entity.PlanRecord)
	// 	log.V2.Warn().Str("namespace", namespace).Str("load_plans_failed", err.Error()).Emit()
	// }
}

// ===== 核心业务方法 / Core Business Methods =====

// CreatePlan 创建新的计划 / Create new plan
// @param ctx 上下文信息 / Context information
// @param tasks 任务列表 / Task list
// @param planPath 计划路径 / Plan path
// @return (string, error) 计划ID和错误信息 / Plan ID and error information
func (prm *PlanRecordManager) CreatePlan(ctx context.Context, tasks []*entity.TaskRecord, planPath string) string {
	prm.mutex.Lock()
	defer prm.mutex.Unlock()

	// 标准化路径 / Normalize path
	normalizedPath := utils.NormalizePath(planPath)

	// 检查是否已存在相同路径的计划 / Check if plan with same path already exists
	if existingPlanID := prm.findExistingPlanByPath(normalizedPath); existingPlanID != nil {
		log.V2.Info().With(ctx).Str("namespace", prm.namespace).Str("plan_path", planPath).Str("normalized_path", normalizedPath).Str("existing_plan_id", *existingPlanID).Str("plan already exists").Emit()
		return *existingPlanID
	}

	// 生成唯一计划ID，包含命名空间前缀 / Generate unique plan ID with namespace prefix
	planID := fmt.Sprintf("%s_plan_%d", prm.namespace, time.Now().UnixNano())

	// 构建任务映射表 / Build task mapping
	taskMap := map[string]entity.TaskRecord{}
	for _, task := range tasks {
		taskMap[task.TaskID] = *task
	}

	// 创建计划记录 / Create plan record
	plan := entity.PlanRecord{
		PlanID:    planID,
		Tasks:     taskMap,
		PlanPath:  normalizedPath, // 使用标准化后的路径 / Use normalized path
		UpdatedAt: time.Now(),
		State:     entity.PlanPhaseInitial,
	}

	// 缓存到内存 / Cache in memory
	prm.plans[planID] = &plan

	// 持久化存储 / Persist to storage
	prm.savePlanUnsafe(ctx)

	log.V2.Info().With(ctx).Str("namespace", prm.namespace).Str("plan_id", planID).Str("plan_path", planPath).Str("normalized_path", normalizedPath).Str("task_count", fmt.Sprintf("%d", len(tasks))).Str("new plan created").Emit()
	return planID
}

// GetNextTask 获取下一个可执行任务 / Get next executable task
// @param ctx 上下文信息 / Context information
// @param planID 计划ID / Plan ID
// @return (*entity.TaskRecord, error) 任务记录和错误信息 / Task record and error information
func (prm *PlanRecordManager) GetNextTask(ctx context.Context, planID string) (*entity.TaskRecord, error) {
	prm.mutex.RLock()
	defer prm.mutex.RUnlock()

	tasks, err := prm.getTaskListUnsafe(ctx, planID)
	if err != nil {
		return nil, err
	}
	// 按优先级排序并返回最高优先级任务 / Sort by priority and return highest priority task
	return prm.selectHighestPriorityTask(tasks), nil
}

// GetTaskList 获取计划中的所有任务 / Get all tasks in plan
// @param ctx 上下文信息 / Context information
// @param planID 计划ID / Plan ID
// @return ([]*entity.TaskRecord, error) 任务列表和错误信息 / Task list and error information
func (prm *PlanRecordManager) GetTaskList(ctx context.Context, planID string) ([]*entity.TaskRecord, error) {
	prm.mutex.RLock()
	defer prm.mutex.RUnlock()
	return prm.getTaskListUnsafe(ctx, planID)
}

func (prm *PlanRecordManager) getTaskListUnsafe(ctx context.Context, planID string) ([]*entity.TaskRecord, error) {
	// 获取计划 / Get plan
	plan, err := prm.getPlanFromCache(ctx, planID)
	if plan == nil || err != nil {
		log.V2.Error().With(ctx).Str("namespace", prm.namespace).Str("get plan error").Error(err).Emit()
		return nil, err
	}
	log.V2.Info().With(ctx).Str("namespace", prm.namespace).Str("get plan success").Str("plan_id", plan.PlanID).Emit()

	// 查找所有依赖已满足的未完成任务 / Find all incomplete tasks with satisfied dependencies
	var tasks []*entity.TaskRecord
	for _, task := range plan.Tasks {
		if task.TaskStatus == entity.TaskStatusPlanned {
			tasks = append(tasks, &task)
		}
	}

	if len(tasks) == 0 {
		return nil, fmt.Errorf("没有可执行的任务") // 没有可执行的任务 / No executable tasks
	}
	log.V2.Info().With(ctx).Str("namespace", prm.namespace).Str("ready_tasks_count", fmt.Sprintf("%d", len(tasks))).Emit()

	// 按优先级排序并返回最高优先级任务 / Sort by priority and return highest priority task
	sort.Slice(tasks, func(i, j int) bool {
		return tasks[i].Priority < tasks[j].Priority
	})
	return tasks, nil
}

// StartTask 开始执行任务 / Start task execution
// @param ctx 上下文信息 / Context information
// @param planID 计划ID / Plan ID
// @param taskID 任务ID / Task ID
// @return error 错误信息 / Error information
func (prm *PlanRecordManager) StartTask(ctx context.Context, planID string, taskID string) error {
	prm.mutex.Lock()
	defer prm.mutex.Unlock()

	// 获取计划 / Get plan
	plan, err := prm.getPlanFromCache(ctx, planID)
	if err != nil {
		return err
	}

	// 检查任务是否存在 / Check if task exists
	task, exists := plan.Tasks[taskID]
	if !exists {
		return fmt.Errorf("任务不存在 / task not found: %s", taskID)
	}

	// 直接更新计划中的任务状态 / Directly update task status in plan
	now := time.Now()
	task.TaskStatus = entity.TaskStatusInProgress
	task.UpdatedAt = now
	plan.Tasks[taskID] = task // 将修改后的任务写回计划中 / Write modified task back to plan
	plan.UpdatedAt = now

	// 持久化存储 / Persist to storage
	prm.savePlanUnsafe(ctx)

	log.V2.Info().With(ctx).Str("namespace", prm.namespace).Str("plan_id", planID).Str("task_id", taskID).Str("task_status", string(task.TaskStatus)).Emit()
	return nil
}

// CompleteTask 完成任务 / Complete task
// @param ctx 上下文信息 / Context information
// @param planID 计划ID / Plan ID
// @param taskID 任务ID / Task ID
// @param results 任务执行结果 / Task execution results
// @return error 错误信息 / Error information
func (prm *PlanRecordManager) CompleteTask(ctx context.Context, planID string, taskID string) error {
	prm.mutex.Lock()
	defer prm.mutex.Unlock()

	// 获取计划 / Get plan
	plan, err := prm.getPlanFromCache(ctx, planID)
	if err != nil {
		return err
	}

	// 检查任务是否存在 / Check if task exists
	task, exists := plan.Tasks[taskID]
	if !exists {
		return fmt.Errorf("任务不存在 / task not found: %s", taskID)
	}

	// 直接更新计划中的任务状态 / Directly update task status in plan
	now := time.Now()
	task.TaskStatus = entity.TaskStatusCompleted
	task.UpdatedAt = now
	plan.Tasks[taskID] = task // 将修改后的任务写回计划中 / Write modified task back to plan

	// 更新计划状态 / Update plan state
	plan.UpdatedAt = now

	// 持久化存储 / Persist to storage
	prm.savePlanUnsafe(ctx)

	log.V2.Info().With(ctx).Str("namespace", prm.namespace).Str("plan_id", planID).Str("task_id", taskID).Str("task_status", string(task.TaskStatus)).Emit()
	return nil
}

func (prm *PlanRecordManager) CleanPlan(ctx context.Context) {
	prm.mutex.Lock()
	defer prm.mutex.Unlock()
	prm.plans = make(map[string]*entity.PlanRecord)
}

// GetNextPlan 获取下一个没有完成的计划 / Get next incomplete plan
// @param ctx 上下文信息 / Context information
// @return (*entity.PlanRecord, error) 计划记录和错误信息 / Plan record and error information
func (prm *PlanRecordManager) GetNextPlan(ctx context.Context) (*entity.PlanRecord, error) {
	prm.mutex.RLock()
	defer prm.mutex.RUnlock()

	// 遍历所有缓存的计划，查找未完成的计划 / Iterate through all cached plans to find incomplete ones
	for planID, plan := range prm.plans {
		// 只返回初始状态的计划
		if plan.State != entity.PlanPhaseInitial {
			continue
		}
		// 只在真正找到有效计划时打印日志
		log.V2.Info().With(ctx).Str("namespace", prm.namespace).Str("plan_id", planID).Str("plan_state", string(plan.State)).Str("found incomplete plan").Emit()
		return plan, nil
	}

	return nil, fmt.Errorf("没有找到未完成的计划 / no incomplete plans found")
}

// CompletePlan 完成计划 / Complete plan
// @param ctx 上下文信息 / Context information
// @param planID 计划ID / Plan ID
// @return error 错误信息 / Error information
func (prm *PlanRecordManager) CompletePlan(ctx context.Context, planID string) error {
	prm.mutex.Lock()
	defer prm.mutex.Unlock()

	// 获取计划 / Get plan
	plan, err := prm.getPlanFromCache(ctx, planID)
	if err != nil {
		return err
	}

	// 检查计划是否已经完成 / Check if plan is already completed
	if plan.State == entity.PlanPhaseCompleted {
		log.V2.Info().With(ctx).Str("namespace", prm.namespace).Str("plan_id", planID).Str("plan already completed").Emit()
		return nil
	}

	// 检查所有任务是否都已完成
	hasPlannedTasks := false
	for taskID, task := range plan.Tasks {
		if task.TaskStatus == entity.TaskStatusCompleted {
			continue
		}
		if task.TaskStatus == entity.TaskStatusInProgress {
			// 强制完成in_progress的任务
			log.V2.Warn().With(ctx).Str("namespace", prm.namespace).Str("plan_id", planID).Str("task_id", taskID).Str("force completing in_progress task").Emit()
			task.TaskStatus = entity.TaskStatusCompleted
			task.UpdatedAt = time.Now()
			plan.Tasks[taskID] = task // 将修改后的任务写回计划中 / Write modified task back to plan
		} else if task.TaskStatus == entity.TaskStatusPlanned {
			// 如果有planned状态的任务，标记为有未完成任务
			hasPlannedTasks = true
		}
	}

	// 如果有planned状态的任务，返回错误
	if hasPlannedTasks {
		return fmt.Errorf("计划中还有未开始的任务 / plan has planned tasks")
	}

	// 更新计划状态为完成 / Update plan state to completed
	now := time.Now()
	plan.State = entity.PlanPhaseCompleted
	plan.UpdatedAt = now

	// 持久化存储 / Persist to storage
	prm.savePlanUnsafe(ctx)

	log.V2.Info().With(ctx).Str("namespace", prm.namespace).Str("plan_id", planID).Str("plan completed successfully").Emit()
	return nil
}

// UpdatePlanState 更新计划状态 / Update plan state
// @param ctx 上下文信息 / Context information
// @param planID 计划ID / Plan ID
// @param state 新状态 / New state
// @return error 错误信息 / Error information
func (prm *PlanRecordManager) UpdatePlanState(ctx context.Context, planID string, state entity.PlanStatus) error {
	prm.mutex.Lock()
	defer prm.mutex.Unlock()

	// 获取计划 / Get plan
	plan, err := prm.getPlanFromCache(ctx, planID)
	if err != nil {
		return err
	}

	// 更新计划状态 / Update plan state
	now := time.Now()
	plan.State = state
	plan.UpdatedAt = now

	// 持久化存储 / Persist to storage
	prm.savePlanUnsafe(ctx)

	log.V2.Info().With(ctx).Str("namespace", prm.namespace).Str("plan_id", planID).Str("state", string(state)).Str("plan state updated").Emit()
	return nil
}

// GetTask 获取指定计划中的指定任务 / Get specified task from specified plan
// @param ctx 上下文信息 / Context information
// @param planID 计划ID / Plan ID
// @param taskID 任务ID / Task ID
// @return (*entity.TaskRecord, error) 任务记录和错误信息 / Task record and error information
func (prm *PlanRecordManager) GetTask(ctx context.Context, planID string, taskID string) (*entity.TaskRecord, error) {
	prm.mutex.RLock()
	defer prm.mutex.RUnlock()

	// 获取计划 / Get plan
	plan, err := prm.getPlanFromCache(ctx, planID)
	if err != nil {
		return nil, err
	}

	// 获取任务 / Get task
	task, exists := plan.Tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("任务不存在 / task not found: %s", taskID)
	}

	return &task, nil
}

// GetPlan 获取计划，优先从缓存获取，缓存不存在时从存储加载 / Get plan, preferring cache, loading from storage if not cached
func (prm *PlanRecordManager) GetPlan(ctx context.Context, planID string) (*entity.PlanRecord, error) {
	prm.mutex.RLock()
	defer prm.mutex.RUnlock()

	return prm.getPlanFromCache(ctx, planID)
}

// GetNamespace 获取当前命名空间 / Get current namespace
// @return string 当前命名空间 / Current namespace
func (prm *PlanRecordManager) GetNamespace() string {
	prm.mutex.RLock()
	defer prm.mutex.RUnlock()
	return prm.namespace
}

// ===== 私有辅助方法 / Private Helper Methods =====

func (prm *PlanRecordManager) getPlanFromCache(ctx context.Context, planID string) (*entity.PlanRecord, error) {
	// 从缓存获取 / Get from cache
	plan, exists := prm.plans[planID]
	if !exists {
		return nil, fmt.Errorf("计划不存在 / plan not found: %s", planID)
	}
	return plan, nil
}

func (prm *PlanRecordManager) savePlanUnsafe(ctx context.Context) {
	// 检查存储是否已初始化 / Check if storage is initialized
	if prm.storage == nil {
		log.V2.Warn().With(ctx).Str("namespace", prm.namespace).Str("storage not initialized, skipping save").Emit()
		return
	}

	// 将指针map转换为值map用于存储 / Convert pointer map to value map for storage
	plansForStorage := make(map[string]entity.PlanRecord)
	for planID, plan := range prm.plans {
		plansForStorage[planID] = *plan
	}

	// 使用批量保存方法 / Use batch save method
	if err := (*prm.storage).SaveAllPlans(ctx, plansForStorage); err != nil {
		log.V2.Error().With(ctx).Str("namespace", prm.namespace).Str("save all plans error").Error(err).Emit()
	}
}

func (prm *PlanRecordManager) selectHighestPriorityTask(readyTasks []*entity.TaskRecord) *entity.TaskRecord {
	var highPriorityTasks []*entity.TaskRecord
	var mediumPriorityTasks []*entity.TaskRecord
	var lowPriorityTasks []*entity.TaskRecord
	// 按优先级分类 / Classify by priority
	for _, task := range readyTasks {
		switch task.Priority {
		case entity.TaskPriorityHigh:
			highPriorityTasks = append(highPriorityTasks, task)
		case entity.TaskPriorityMedium:
			mediumPriorityTasks = append(mediumPriorityTasks, task)
		case entity.TaskPriorityLow:
			lowPriorityTasks = append(lowPriorityTasks, task)
		}
	}

	// 返回最高优先级的任务 / Return highest priority task
	if len(highPriorityTasks) > 0 {
		return highPriorityTasks[0]
	}
	if len(mediumPriorityTasks) > 0 {
		return mediumPriorityTasks[0]
	}
	if len(lowPriorityTasks) > 0 {
		return lowPriorityTasks[0]
	}

	return nil
}

// findExistingPlanByPath 根据标准化路径查找已存在的计划 / Find existing plan by normalized path
// @param normalizedPath 标准化路径 / Normalized path
// @return string 已存在计划的ID，如果不存在则返回空字符串 / Existing plan ID, empty string if not found
func (prm *PlanRecordManager) findExistingPlanByPath(normalizedPath string) *string {
	for planID, plan := range prm.plans {
		// 跳过已完成的计划 / Skip completed plans
		if plan.State == entity.PlanPhaseCompleted {
			continue
		}

		// 比较标准化后的路径 / Compare normalized paths
		if utils.NormalizePath(plan.PlanPath) == normalizedPath {
			return &planID
		}
	}
	return nil
}

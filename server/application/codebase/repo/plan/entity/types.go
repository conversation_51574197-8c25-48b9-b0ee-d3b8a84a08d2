package entity

// ===== 任务状态枚举 =====
// TaskStatus 任务执行状态枚举 / Task execution status enumeration
type TaskStatus string

const (
	TaskStatusPlanned    TaskStatus = "planned"     // 已计划 / Planned
	TaskStatusInProgress TaskStatus = "in_progress" // 执行中 / In Progress
	TaskStatusCompleted  TaskStatus = "completed"   // 已完成 / Completed
)

// ===== 任务优先级枚举 =====
// TaskPriority 任务优先级枚举 / Task priority enumeration
type TaskPriority string

const (
	TaskPriorityHigh   TaskPriority = "high"   // 高优先级 / High priority
	TaskPriorityMedium TaskPriority = "medium" // 中等优先级 / Medium priority
	TaskPriorityLow    TaskPriority = "low"    // 低优先级 / Low priority
)

// ===== 计划阶段常量 =====
// Plan phase constants / 计划阶段常量
type PlanStatus string

const (
	PlanPhaseInitial   PlanStatus = "initial"   // 初始阶段 / Initial phase
	PlanPhasePreparing PlanStatus = "preparing" // 准备阶段 / Preparing phase
	PlanPhaseExecuting PlanStatus = "executing" // 执行阶段 / Executing phase
	PlanPhaseCompleted PlanStatus = "completed" // 完成阶段 / Completed phase
)

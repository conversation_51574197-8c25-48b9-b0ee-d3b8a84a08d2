package entity

import "time"

// ===== 任务记录实体 =====

// TaskRecord 任务记录实体 / Task record entity
// 表示单个任务的完整信息，包括定义、执行状态、依赖关系等
// Represents complete information of a single task including definition, execution status, dependencies, etc.
type TaskRecord struct {
	TaskID string `json:"task_id"` // 任务唯一标识 / Task unique identifier

	// 任务定义 / Task definition
	Definition string `json:"definition"`

	// 执行状态 / Execution state
	TaskStatus TaskStatus `json:"task_status"`

	// 要分析的目录
	TargetPath string `json:"target_path"`

	// 所属计划ID / Plan ID that this task belongs to
	PlanID string `json:"plan_id,omitempty"`

	// 任务优先级 / Task priority
	Priority TaskPriority `json:"priority"`

	// 执行时间 / Execution time
	UpdatedAt time.Time `json:"updated_at"`
}

package entity

import "time"

// ===== 计划记录实体 =====

// PlanRecord 计划记录实体 / Plan record entity
// 表示一个完整的分析计划，包含多个任务和执行状态
// Represents a complete analysis plan containing multiple tasks and execution state
type PlanRecord struct {
	PlanID    string                `json:"plan_id"`    // 计划唯一标识 / Plan unique identifier
	PlanPath  string                `json:"plan_path"`  // 计划路径 / Plan path
	Tasks     map[string]TaskRecord `json:"tasks"`      // 任务映射表 / Task mapping
	State     PlanStatus            `json:"state"`      // 计划执行状态 / Plan execution state
	UpdatedAt time.Time             `json:"updated_at"` // 最后更新时间 / Last updated time
}

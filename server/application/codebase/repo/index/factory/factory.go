package factory

import (
	"fmt"

	"code.byted.org/gopkg/logs/v2"
	index "code.byted.org/ies/codin/application/codebase/repo/index/interface"
	"code.byted.org/ies/codin/application/codebase/repo/index/summary"
	"code.byted.org/ies/codin/application/codebase/repo/index/vector"
	taskRepoEntity "code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
	taskRepo "code.byted.org/ies/codin/application/codebase/repo/taskrepo/interface"
)

// IndexManagerFactory 索引管理器工厂
// 负责创建和管理不同类型的索引管理器
type IndexManagerFactory struct {
	managers   map[taskRepoEntity.IndexType]index.IndexManager
	repository taskRepo.TaskRepository
}

// NewIndexManagerFactory 创建索引管理器工厂
// @param repository 任务存储库
// @return *IndexManagerFactory 索引管理器工厂实例
func NewIndexManagerFactory(repository taskRepo.TaskRepository) *IndexManagerFactory {
	factory := &IndexManagerFactory{
		managers:   make(map[taskRepoEntity.IndexType]index.IndexManager),
		repository: repository,
	}

	// 初始化默认的索引管理器
	factory.initDefaultManagers()

	return factory
}

// initDefaultManagers 初始化默认的索引管理器
func (f *IndexManagerFactory) initDefaultManagers() {
	// 创建向量索引管理器
	vectorManager := vector.NewVectorIndexManager(f.repository)
	f.managers[taskRepoEntity.IndexTypeVector] = vectorManager

	// 创建摘要索引管理器
	summaryManager := summary.NewSummaryIndexManager(f.repository)
	f.managers[taskRepoEntity.IndexTypeSummary] = summaryManager
}

// GetManager 获取指定类型的索引管理器
// @param indexType 索引类型
// @return IndexManager 索引管理器
// @return error 获取过程中的错误
func (f *IndexManagerFactory) GetManager(indexType taskRepoEntity.IndexType) (index.IndexManager, error) {
	manager, exists := f.managers[indexType]
	if !exists {
		return nil, fmt.Errorf("index manager not found for type: %s", indexType)
	}

	return manager, nil
}

// GetAllManagers 获取所有索引管理器
// @return map[entity.IndexType]IndexManager 索引管理器映射
func (f *IndexManagerFactory) GetAllManagers() map[taskRepoEntity.IndexType]index.IndexManager {
	return f.managers
}

// RegisterManager 注册索引管理器
// @param indexType 索引类型
// @param manager 索引管理器
func (f *IndexManagerFactory) RegisterManager(indexType taskRepoEntity.IndexType, manager index.IndexManager) {
	f.managers[indexType] = manager
	logs.Info("Registered index manager: %s for type: %s", manager.Name(), indexType)
}

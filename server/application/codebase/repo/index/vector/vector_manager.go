package vector

import (
	"context"
	"fmt"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/application/codebase/repo/index/manager"
	"code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
	taskRepoEntity "code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
	taskRepo "code.byted.org/ies/codin/application/codebase/repo/taskrepo/interface"
)

// VectorIndexManager 向量索引管理器
// 负责向量索引（图谱索引）的构建和更新
type VectorIndexManager struct {
	*manager.BaseIndexManager
}

// NewVectorIndexManager 创建向量索引管理器
// @param repository 任务存储库
// @return *VectorIndexManager 向量索引管理器实例
func NewVectorIndexManager(repository taskRepo.TaskRepository) *VectorIndexManager {
	baseManager := manager.NewBaseIndexManager(
		taskRepoEntity.IndexTypeVector,
		"VectorIndexManager",
		repository,
	)

	return &VectorIndexManager{
		BaseIndexManager: baseManager,
	}
}

// Name 返回索引管理器的名称标识
func (v *VectorIndexManager) Name() string {
	return "VectorIndexManager"
}

// Build 构建向量索引
// @param ctx 上下文
// @param taskInfo 任务信息
// @return error 构建过程中的错误
func (v *VectorIndexManager) Build(ctx context.Context, taskInfo *entity.TaskInfo) error {
	logs.CtxInfo(ctx, "[%s] Build vector index start", v.Name())

	if err := v.ValidateTask(taskInfo); err != nil {
		return err
	}

	// 调用基础实现创建任务
	err := v.BaseIndexManager.Build(ctx, taskInfo)
	if err != nil {
		logs.CtxError(ctx, "[%s] Build vector index failed: %v", v.Name(), err)
		return fmt.Errorf("build vector index failed: %w", err)
	}

	logs.CtxInfo(ctx, "[%s] Build vector index task submitted successfully", v.Name())
	return nil
}

// Update 更新向量索引
// @param ctx 上下文
// @param taskInfo 任务信息
// @return error 更新过程中的错误
func (v *VectorIndexManager) Update(ctx context.Context, taskInfo *entity.TaskInfo) error {
	logs.CtxInfo(ctx, "[%s] Update vector index start", v.Name())

	if err := v.ValidateTask(taskInfo); err != nil {
		return err
	}

	// 调用基础实现创建任务
	err := v.BaseIndexManager.Update(ctx, taskInfo)
	if err != nil {
		logs.CtxError(ctx, "[%s] Update vector index failed: %v", v.Name(), err)
		return fmt.Errorf("update vector index failed: %w", err)
	}

	logs.CtxInfo(ctx, "[%s] Update vector index task submitted successfully", v.Name())
	return nil
}

// ValidateTask 验证向量索引任务参数
// @param task 任务实体
// @return error 验证过程中的错误
func (v *VectorIndexManager) ValidateTask(task *entity.TaskInfo) error {
	// 根据任务类型进行相应的验证
	switch task.TaskType {
	case entity.IndexTaskTypeBuild:
		return v.ValidateBuildTask(task)
	case entity.IndexTaskTypeUpdate:
		return v.ValidateUpdateTask(task)
	default:
		return taskRepoEntity.ErrInvalidBuildParams
	}
}

func (v *VectorIndexManager) ValidateBuildTask(task *entity.TaskInfo) error {
	if task.VectorBuildInfo == nil {
		return taskRepoEntity.ErrInvalidBuildParams
	}

	return nil
}

func (v *VectorIndexManager) ValidateUpdateTask(task *entity.TaskInfo) error {
	if task.VectorUpdateInfo == nil {
		return taskRepoEntity.ErrInvalidBuildParams
	}

	return nil
}

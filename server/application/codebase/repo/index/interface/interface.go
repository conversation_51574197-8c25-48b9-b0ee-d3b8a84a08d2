package index

import (
	"context"

	"code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
)

// IndexManager 索引管理器接口
// 定义了索引构建和更新的抽象能力
type IndexManager interface {
	// Name 返回索引管理器的名称标识
	Name() string

	// Build 构建索引
	// @param ctx 上下文
	// @param taskInfo 任务信息
	// @return error 构建过程中的错误
	Build(ctx context.Context, taskInfo *entity.TaskInfo) error

	// Update 更新索引
	// @param ctx 上下文
	// @param taskInfo 任务信息
	// @return error 更新过程中的错误
	Update(ctx context.Context, taskInfo *entity.TaskInfo) error
}

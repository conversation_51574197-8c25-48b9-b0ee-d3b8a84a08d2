package manager

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
	taskRepoEntity "code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
	taskRepo "code.byted.org/ies/codin/application/codebase/repo/taskrepo/interface"
)

// BaseIndexManager 基础索引管理器
// 提供了索引管理器的通用功能实现
type BaseIndexManager struct {
	indexType  taskRepoEntity.IndexType
	name       string
	repository taskRepo.TaskRepository
}

// NewBaseIndexManager 创建基础索引管理器
// @param indexType 索引类型
// @param name 管理器名称
// @param repository 任务存储库
// @return *BaseIndexManager 基础索引管理器实例
func NewBaseIndexManager(indexType taskRepoEntity.IndexType, name string, repository taskRepo.TaskRepository) *BaseIndexManager {
	return &BaseIndexManager{
		indexType:  indexType,
		name:       name,
		repository: repository,
	}
}

// Name 返回索引管理器的名称标识
func (m *BaseIndexManager) Name() string {
	return m.name
}

// Build 构建索引的基础实现
// @param ctx 上下文
// @param taskInfo 任务信息
// @return error 构建过程中的错误
func (m *BaseIndexManager) Build(ctx context.Context, taskInfo *entity.TaskInfo) error {
	logs.CtxInfo(ctx, "[%s] Build index start", m.name)
	if taskInfo == nil {
		return fmt.Errorf("taskInfo is nil")
	}

	// 创建任务
	task, err := m.createTask(ctx, taskRepoEntity.IndexTaskTypeBuild, taskInfo)
	if err != nil {
		logs.CtxError(ctx, "[%s] Create build task failed: %v", m.name, err)
		return fmt.Errorf("create build task failed: %w", err)
	}

	// 提交任务到存储库
	err = m.repository.SubmitTask(ctx, task)
	if err != nil {
		logs.CtxError(ctx, "[%s] Submit build task failed: %v", m.name, err)
		return fmt.Errorf("submit build task failed: %w", err)
	}

	logs.CtxInfo(ctx, "[%s] Build task submitted successfully, taskID: %s", m.name, task.TaskID)
	return nil
}

// Update 更新索引的基础实现
// @param ctx 上下文
// @param taskInfo 任务信息
// @return error 更新过程中的错误
func (m *BaseIndexManager) Update(ctx context.Context, taskInfo *entity.TaskInfo) error {
	logs.CtxInfo(ctx, "[%s] Update index start", m.name)
	if taskInfo == nil {
		return fmt.Errorf("taskInfo is nil")
	}

	// 创建任务
	task, err := m.createTask(ctx, taskRepoEntity.IndexTaskTypeUpdate, taskInfo)
	if err != nil {
		logs.CtxError(ctx, "[%s] Create update task failed: %v", m.name, err)
		return fmt.Errorf("create update task failed: %w", err)
	}

	// 提交任务到存储库
	err = m.repository.SubmitTask(ctx, task)
	if err != nil {
		logs.CtxError(ctx, "[%s] Submit update task failed: %v", m.name, err)
		return fmt.Errorf("submit update task failed: %w", err)
	}

	logs.CtxInfo(ctx, "[%s] Update task submitted successfully, taskID: %s", m.name, task.TaskID)
	return nil
}

// createTask 创建任务
// @param ctx 上下文
// @param taskType 任务类型
// @param taskInfo 任务信息
// @return *entity.IndexTask 任务实体
// @return error 创建过程中的错误
func (m *BaseIndexManager) createTask(ctx context.Context, taskType taskRepoEntity.IndexTaskType, taskInfo *entity.TaskInfo) (*taskRepoEntity.IndexTask, error) {

	// 创建基础任务
	task := &taskRepoEntity.IndexTask{
		TaskID:      m.generateTaskID(taskType),
		TaskType:    taskType,
		IndexType:   m.indexType,
		Status:      taskRepoEntity.IndexTaskStatusPending,
		BuildParams: *taskInfo,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	return task, nil
}

// generateTaskID 生成任务ID
// @param taskType 任务类型
// @return string 任务ID
func (m *BaseIndexManager) generateTaskID(taskType taskRepoEntity.IndexTaskType) string {
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("%s_%s_%d", m.indexType, taskType, timestamp)
}

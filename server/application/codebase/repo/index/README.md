# 索引管理模块

## 简介

索引管理模块负责管理代码仓库的索引构建和更新任务。该模块遵循SOLID设计原则，提供了清晰的接口定义和可扩展的实现。

主要功能：
- 提交索引构建任务
- 提交索引更新任务
- 管理任务状态
- 提供任务查询接口

注意：本模块只负责任务的提交和管理，不负责具体的索引构建实现。索引构建的具体实现由专门的消费模块处理。

## 目录结构

```
index/
├── README.md           # 本文档
├── interface.go        # 核心接口定义
├── entity/            # 实体定义
│   ├── entities.go    # 任务实体和相关类型
│   └── errors.go      # 错误定义
├── manager/           # 管理器实现
│   └── base_manager.go # 基础管理器实现
├── vector/            # 向量索引管理器
│   └── vector_manager.go
├── summary/           # 摘要索引管理器
│   └── summary_manager.go
├── factory/           # 工厂实现
│   └── factory.go     # 索引管理器工厂
├── health/            # 健康检查
│   └── checker.go     # 任务健康检查器
└── utils/             # 工具类
    ├── repository.go  # 内存存储实现（用于测试）
    └── example.go     # 使用示例
```

## 设计原则

### 1. 单一职责原则 (SRP)
- 每个类都有明确的职责
- 任务管理和任务执行分离
- 不同类型的索引有独立的管理器

### 2. 开闭原则 (OCP)
- 基础管理器提供通用实现
- 可以通过继承和重写方法扩展功能
- 新增索引类型不需要修改现有代码

### 3. 里氏替换原则 (LSP)
- 具体管理器可以替换基础管理器
- 保持接口的语义一致性

### 4. 接口隔离原则 (ISP)
- 接口精简且职责单一
- 客户端不需要依赖它不使用的方法

### 5. 依赖倒置原则 (DIP)
- 依赖抽象而非具体实现
- 通过接口进行解耦
- 使用依赖注入管理组件关系

## 核心接口

### IndexManager
```go
type IndexManager interface {
    Name() string
    Build(ctx context.Context, opts ...entity.Option) error
    Update(ctx context.Context, opts ...entity.Option) error
}
```

### TaskRepository
```go
type TaskRepository interface {
    CreateTask(ctx context.Context, task *entity.IndexTask) error
    GetTask(ctx context.Context, taskID string) (*entity.IndexTask, error)
    UpdateTaskStatus(ctx context.Context, taskID string, status entity.IndexTaskStatus, errorMessage string) error
    GetTasksByRepo(ctx context.Context, repoName string, branch string, uid string) ([]*entity.IndexTask, error)
    CleanupExpiredTasks(ctx context.Context, timeout time.Duration) error
}
```

## 使用示例

```go
// 创建任务存储库
repository := utils.NewMemoryTaskRepository()

// 创建索引管理器工厂
factory := factory.NewIndexManagerFactory(repository)

// 获取向量索引管理器
vectorManager, err := factory.GetManager(entity.IndexTypeVector)
if err != nil {
    return err
}

// 构建向量索引
err = vectorManager.Build(ctx,
    entity.WithRepoName("example-repo"),
    entity.WithRepoURL("https://github.com/example/repo"),
    entity.WithBranch("main"),
    entity.WithLanguage("typescript"),
    entity.WithUid("user123"),
)
if err != nil {
    return err
}

// 获取任务状态
task, err := repository.GetTask(ctx, taskID)
if err != nil {
    return err
}
fmt.Printf("Task status: %s\n", task.Status)
```

## 错误处理

所有错误都定义在 `entity/errors.go` 中：

```go
var (
    ErrInvalidBuildParams = errors.New("invalid build parameters")
    ErrTaskNotFound      = errors.New("task not found")
    ErrInvalidTaskStatus = errors.New("invalid task status")
)
```

## 注意事项

1. 任务提交
   - 确保提供必要的参数（仓库名称、URL、分支等）
   - 使用 Option 模式设置可选参数
   - 任务ID会自动生成

2. 任务状态
   - pending: 任务已创建，等待处理
   - running: 任务正在执行
   - completed: 任务已完成
   - failed: 任务执行失败

3. 错误处理
   - 所有错误都应该被适当处理和记录
   - 使用预定义的错误类型
   - 包含足够的上下文信息

4. 扩展性
   - 添加新的索引类型时，创建新的管理器
   - 实现必要的接口方法
   - 注册到工厂 
package summary

import (
	"context"
	"fmt"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/application/codebase/repo/index/manager"
	"code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
	taskRepoEntity "code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
	taskRepo "code.byted.org/ies/codin/application/codebase/repo/taskrepo/interface"
)

// SummaryIndexManager 摘要索引管理器
// 负责摘要索引的构建和更新
type SummaryIndexManager struct {
	*manager.BaseIndexManager
}

// NewSummaryIndexManager 创建摘要索引管理器
// @param repository 任务存储库
// @return *SummaryIndexManager 摘要索引管理器实例
func NewSummaryIndexManager(repository taskRepo.TaskRepository) *SummaryIndexManager {
	baseManager := manager.NewBaseIndexManager(
		taskRepoEntity.IndexTypeSummary,
		"SummaryIndexManager",
		repository,
	)

	return &SummaryIndexManager{
		BaseIndexManager: baseManager,
	}
}

// Name 返回索引管理器的名称标识
func (s *SummaryIndexManager) Name() string {
	return "SummaryIndexManager"
}

// Build 构建摘要索引
// @param ctx 上下文
// @param taskInfo 任务信息
// @return error 构建过程中的错误
func (s *SummaryIndexManager) Build(ctx context.Context, taskInfo *entity.TaskInfo) error {
	logs.CtxInfo(ctx, "[%s] Build summary index start", s.Name())

	logs.CtxInfo(ctx, "taskInfo: %+v", taskInfo)

	if err := s.ValidateTask(taskInfo); err != nil {
		return err
	}

	// 调用基础实现创建任务
	err := s.BaseIndexManager.Build(ctx, taskInfo)
	if err != nil {
		logs.CtxError(ctx, "[%s] Build summary index failed: %v", s.Name(), err)
		return fmt.Errorf("build summary index failed: %w", err)
	}

	logs.CtxInfo(ctx, "[%s] Build summary index task submitted successfully", s.Name())
	return nil
}

// Update 更新摘要索引
// @param ctx 上下文
// @param taskInfo 任务信息
// @return error 更新过程中的错误
func (s *SummaryIndexManager) Update(ctx context.Context, taskInfo *entity.TaskInfo) error {
	logs.CtxInfo(ctx, "[%s] Update summary index start", s.Name())

	if err := s.ValidateTask(taskInfo); err != nil {
		return err
	}

	// 调用基础实现创建任务
	err := s.BaseIndexManager.Update(ctx, taskInfo)
	if err != nil {
		logs.CtxError(ctx, "[%s] Update summary index failed: %v", s.Name(), err)
		return fmt.Errorf("update summary index failed: %w", err)
	}

	logs.CtxInfo(ctx, "[%s] Update summary index task submitted successfully", s.Name())
	return nil
}

// ValidateTask 验证摘要索引任务参数
// @param task 任务实体
// @return error 验证过程中的错误
func (s *SummaryIndexManager) ValidateTask(task *entity.TaskInfo) error {
	// 根据任务类型进行相应的验证
	switch task.TaskType {
	case entity.IndexTaskTypeBuild:
		return s.ValidateBuildTask(task)
	case entity.IndexTaskTypeUpdate:
		return s.ValidateUpdateTask(task)
	default:
		return taskRepoEntity.ErrInvalidBuildParams
	}
}

// ValidateTask 验证任务参数
// @param task 任务实体
// @return error 验证过程中的错误
func (s *SummaryIndexManager) ValidateBuildTask(task *entity.TaskInfo) error {
	if task.SummaryBuildInfo == nil {
		return taskRepoEntity.ErrInvalidBuildParams
	}

	return nil
}

func (s *SummaryIndexManager) ValidateUpdateTask(task *entity.TaskInfo) error {
	if task.SummaryUpdateInfo == nil {
		return taskRepoEntity.ErrInvalidBuildParams
	}

	return nil
}

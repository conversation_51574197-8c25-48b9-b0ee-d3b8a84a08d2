package test

import (
	"context"
	"fmt"
	"testing"

	summaryConsume "code.byted.org/ies/codin/application/codebase/logic/consume/summary"
	vectorConsume "code.byted.org/ies/codin/application/codebase/logic/consume/vector"
	"code.byted.org/ies/codin/application/codebase/repo/consume/factory"
	indexFactory "code.byted.org/ies/codin/application/codebase/repo/index/factory"
	"code.byted.org/ies/codin/application/codebase/repo/taskrepo/entity"
	"code.byted.org/ies/codin/application/codebase/repo/taskrepo/repository"
	service "code.byted.org/ies/codin/application/codebase/service"
	"code.byted.org/overpass/capcut_devops_codebase/kitex_gen/codebase"
)

// TestCoreIndexWorkflowSimple 简单的核心索引工作流测试
func TestCoreIndexWorkflowSimple(t *testing.T) {
	// 创建测试上下文
	ctx := context.Background()

	// 创建真实的依赖
	repository := repository.NewMemoryTaskRepository()
	indexFactory := indexFactory.NewIndexManagerFactory(repository)
	consumerFactory := factory.NewConsumerFactory()

	consumerManager := consumerFactory.CreateManager(repository)
	consumerManager.RegisterConsumer(summaryConsume.NewSummaryConsumer())
	consumerManager.RegisterConsumer(vectorConsume.NewVectorConsumer())

	// 创建服务实例
	service := &service.CodebaseServiceImpl{
		IndexFactory:    indexFactory,
		ConsumerManager: consumerManager,
	}

	// 测试用例1: 构建索引的核心流程
	t.Run("构建索引核心流程", func(t *testing.T) {
		// 构造构建索引请求
		request := &codebase.BuildIndexRequest{
			Uid:      "simple_test_user",
			RepoName: "simple-test-repo",
			RepoURL:  "https://code.byted.org/simple-test-repo",
			Branch:   "main",
			Language: "go",
		}

		// 执行构建索引
		response, err := service.BuildIndex(ctx, request)
		if err != nil {
			t.Fatalf("构建索引失败: %v", err)
		}
		if response == nil {
			t.Fatal("构建索引响应为空")
		}

		// 验证任务是否被正确创建
		tasks, err := repository.GetAllTasks(ctx)
		if err != nil {
			t.Fatalf("获取任务列表失败: %v", err)
		}
		if len(tasks) != 2 {
			t.Fatalf("期望2个任务，实际有%d个任务", len(tasks))
		}

		// 验证任务类型和状态
		vectorTaskFound := false
		summaryTaskFound := false

		for _, task := range tasks {
			if task.TaskType != entity.IndexTaskTypeBuild {
				t.Errorf("任务类型错误，期望 %s，实际 %s", entity.IndexTaskTypeBuild, task.TaskType)
			}
			if task.Status != entity.IndexTaskStatusPending {
				t.Errorf("任务状态错误，期望 %s，实际 %s", entity.IndexTaskStatusPending, task.Status)
			}

			if task.IndexType == entity.IndexTypeVector {
				vectorTaskFound = true
				if task.BuildParams.VectorBuildInfo == nil {
					t.Error("向量构建任务信息为空")
				} else {
					if task.BuildParams.VectorBuildInfo.Uid != request.Uid {
						t.Errorf("用户ID不匹配，期望 %s，实际 %s", request.Uid, task.BuildParams.VectorBuildInfo.Uid)
					}
					if task.BuildParams.VectorBuildInfo.RepoName != request.RepoName {
						t.Errorf("仓库名不匹配，期望 %s，实际 %s", request.RepoName, task.BuildParams.VectorBuildInfo.RepoName)
					}
				}
			}

			if task.IndexType == entity.IndexTypeSummary {
				summaryTaskFound = true
				if task.BuildParams.SummaryBuildInfo == nil {
					t.Error("摘要构建任务信息为空")
				} else {
					if task.BuildParams.SummaryBuildInfo.Uid != request.Uid {
						t.Errorf("用户ID不匹配，期望 %s，实际 %s", request.Uid, task.BuildParams.SummaryBuildInfo.Uid)
					}
					if task.BuildParams.SummaryBuildInfo.RepoName != request.RepoName {
						t.Errorf("仓库名不匹配，期望 %s，实际 %s", request.RepoName, task.BuildParams.SummaryBuildInfo.RepoName)
					}
				}
			}
		}

		if !vectorTaskFound {
			t.Error("向量构建任务应该被创建")
		}
		if !summaryTaskFound {
			t.Error("摘要构建任务应该被创建")
		}

		// 验证索引管理器是否正确获取
		vectorManager, err := indexFactory.GetManager(entity.IndexTypeVector)
		if err != nil {
			t.Fatalf("获取向量索引管理器失败: %v", err)
		}
		if vectorManager == nil {
			t.Fatal("向量索引管理器为空")
		}
		if vectorManager.Name() != "VectorIndexManager" {
			t.Errorf("向量索引管理器名称错误，期望 VectorIndexManager，实际 %s", vectorManager.Name())
		}

		summaryManager, err := indexFactory.GetManager(entity.IndexTypeSummary)
		if err != nil {
			t.Fatalf("获取摘要索引管理器失败: %v", err)
		}
		if summaryManager == nil {
			t.Fatal("摘要索引管理器为空")
		}
		if summaryManager.Name() != "SummaryIndexManager" {
			t.Errorf("摘要索引管理器名称错误，期望 SummaryIndexManager，实际 %s", summaryManager.Name())
		}

		fmt.Printf("✅ 构建索引核心流程测试通过\n")
	})

	// 测试用例2: 更新索引的核心流程
	t.Run("更新索引核心流程", func(t *testing.T) {
		// 构造更新索引请求
		request := &codebase.UploadMerkleTreeRequest{
			MerkleTreeDownloadKey:    "simple_test_merkle_tree_key",
			BaseUserKnowledgeId:      "simple_base_user_knowledge",
			Uid:                      "simple_test_user",
			RepoName:                 "simple-test-repo",
			Branch:                   "main",
			RepoPath:                 "/path/to/simple/repo",
			Did:                      "simple_device",
			ChunkFileDownloadKey:     "simple_chunk_file_key",
			RelationsFileDownloadKey: "simple_relations_file_key",
			DeleteFileIds:            []string{"simple_file1", "simple_file2"},
			ClientMerkleId:           "simple_client_merkle",
		}

		// 执行更新索引
		response, err := service.UploadMerkleTree(ctx, request)
		if err != nil {
			t.Fatalf("更新索引失败: %v", err)
		}
		if response == nil {
			t.Fatal("更新索引响应为空")
		}
		if response.Id == "" {
			t.Fatal("用户知识库ID为空")
		}

		// 验证任务是否被正确创建
		tasks, err := repository.GetAllTasks(ctx)
		if err != nil {
			t.Fatalf("获取任务列表失败: %v", err)
		}

		// 查找向量更新任务
		vectorUpdateTaskFound := false
		for _, task := range tasks {
			if task.TaskType == entity.IndexTaskTypeUpdate && task.IndexType == entity.IndexTypeVector {
				vectorUpdateTaskFound = true
				if task.Status != entity.IndexTaskStatusPending {
					t.Errorf("任务状态错误，期望 %s，实际 %s", entity.IndexTaskStatusPending, task.Status)
				}
				if task.BuildParams.VectorUpdateInfo == nil {
					t.Error("向量更新任务信息为空")
				} else {
					if task.BuildParams.VectorUpdateInfo.Uid != request.Uid {
						t.Errorf("用户ID不匹配，期望 %s，实际 %s", request.Uid, task.BuildParams.VectorUpdateInfo.Uid)
					}
					if task.BuildParams.VectorUpdateInfo.RepoName != request.RepoName {
						t.Errorf("仓库名不匹配，期望 %s，实际 %s", request.RepoName, task.BuildParams.VectorUpdateInfo.RepoName)
					}
					if task.BuildParams.VectorUpdateInfo.MerkleTreeDownloadKey != request.MerkleTreeDownloadKey {
						t.Errorf("Merkle树下载键不匹配，期望 %s，实际 %s", request.MerkleTreeDownloadKey, task.BuildParams.VectorUpdateInfo.MerkleTreeDownloadKey)
					}
				}
				break
			}
		}

		if !vectorUpdateTaskFound {
			t.Error("向量更新任务应该被创建")
		}

		fmt.Printf("✅ 更新索引核心流程测试通过\n")
	})

	// 测试用例3: 更新摘要的核心流程
	t.Run("更新摘要核心流程", func(t *testing.T) {
		// 构造更新摘要请求
		groupedRelatedFileInfoDownloadKey := "simple_grouped_related_file_info_key"
		request := &codebase.UpdateSummaryRequest{
			MerkleTreeDownloadKey:             "simple_test_merkle_tree_key",
			Uid:                               "simple_test_user",
			RepoName:                          "simple-test-repo",
			Branch:                            "main",
			RepoPath:                          "/path/to/simple/repo",
			Did:                               "simple_device",
			GroupedRelatedFileInfoDownloadKey: &groupedRelatedFileInfoDownloadKey,
			BaseUserKnowledgeId:               "simple_base_user_knowledge",
			ClientMerkleId:                    "simple_client_merkle",
		}

		// 执行更新摘要
		response, err := service.UpdateSummary(ctx, request)
		if err != nil {
			t.Fatalf("更新摘要失败: %v", err)
		}
		if response == nil {
			t.Fatal("更新摘要响应为空")
		}

		// 验证任务是否被正确创建
		tasks, err := repository.GetAllTasks(ctx)
		if err != nil {
			t.Fatalf("获取任务列表失败: %v", err)
		}

		// 查找摘要更新任务
		summaryUpdateTaskFound := false
		for _, task := range tasks {
			if task.TaskType == entity.IndexTaskTypeUpdate && task.IndexType == entity.IndexTypeSummary {
				summaryUpdateTaskFound = true
				if task.Status != entity.IndexTaskStatusPending {
					t.Errorf("任务状态错误，期望 %s，实际 %s", entity.IndexTaskStatusPending, task.Status)
				}
				if task.BuildParams.SummaryUpdateInfo == nil {
					t.Error("摘要更新任务信息为空")
				} else {
					if task.BuildParams.SummaryUpdateInfo.Uid != request.Uid {
						t.Errorf("用户ID不匹配，期望 %s，实际 %s", request.Uid, task.BuildParams.SummaryUpdateInfo.Uid)
					}
					if task.BuildParams.SummaryUpdateInfo.RepoName != request.RepoName {
						t.Errorf("仓库名不匹配，期望 %s，实际 %s", request.RepoName, task.BuildParams.SummaryUpdateInfo.RepoName)
					}
					if task.BuildParams.SummaryUpdateInfo.MerkleTreeDownloadKey != request.MerkleTreeDownloadKey {
						t.Errorf("Merkle树下载键不匹配，期望 %s，实际 %s", request.MerkleTreeDownloadKey, task.BuildParams.SummaryUpdateInfo.MerkleTreeDownloadKey)
					}
				}
				break
			}
		}

		if !summaryUpdateTaskFound {
			t.Error("摘要更新任务应该被创建")
		}

		fmt.Printf("✅ 更新摘要核心流程测试通过\n")
	})
}

// TestIndexFactoryTaskCreationSimple 专门测试 indexFactory 的任务创建功能
func TestIndexFactoryTaskCreationSimple(t *testing.T) {
	// 创建测试上下文
	ctx := context.Background()

	// 创建真实的依赖
	repository := repository.NewMemoryTaskRepository()
	indexFactory := indexFactory.NewIndexManagerFactory(repository)
	consumerFactory := factory.NewConsumerFactory()

	consumerManager := consumerFactory.CreateManager(repository)
	consumerManager.RegisterConsumer(summaryConsume.NewSummaryConsumer())
	consumerManager.RegisterConsumer(vectorConsume.NewVectorConsumer())

	// 测试用例1: 验证向量索引管理器
	t.Run("向量索引管理器任务创建", func(t *testing.T) {
		vectorManager, err := indexFactory.GetManager(entity.IndexTypeVector)
		if err != nil {
			t.Fatalf("获取向量索引管理器失败: %v", err)
		}
		if vectorManager == nil {
			t.Fatal("向量索引管理器为空")
		}
		if vectorManager.Name() != "VectorIndexManager" {
			t.Errorf("向量索引管理器名称错误，期望 VectorIndexManager，实际 %s", vectorManager.Name())
		}

		// 创建向量构建任务
		vectorBuildTask := &entity.TaskInfo{
			IndexType: entity.IndexTypeVector,
			TaskType:  entity.IndexTaskTypeBuild,
			VectorBuildInfo: &entity.VectorBuildIndexTaskInfo{
				Uid:      "test_user",
				RepoPath: "user",
				RepoName: "test-repo",
				RepoURL:  "https://code.byted.org/test-repo",
				Branch:   "main",
				Language: "go",
			},
		}

		// 执行构建
		err = vectorManager.Build(ctx, vectorBuildTask)
		if err != nil {
			t.Fatalf("向量构建失败: %v", err)
		}

		// 验证任务是否被创建
		tasks, err := repository.GetAllTasks(ctx)
		if err != nil {
			t.Fatalf("获取任务列表失败: %v", err)
		}
		if len(tasks) != 1 {
			t.Fatalf("期望1个任务，实际有%d个任务", len(tasks))
		}

		task := tasks[0]
		if task.IndexType != entity.IndexTypeVector {
			t.Errorf("索引类型错误，期望 %s，实际 %s", entity.IndexTypeVector, task.IndexType)
		}
		if task.TaskType != entity.IndexTaskTypeBuild {
			t.Errorf("任务类型错误，期望 %s，实际 %s", entity.IndexTaskTypeBuild, task.TaskType)
		}
		if task.Status != entity.IndexTaskStatusPending {
			t.Errorf("任务状态错误，期望 %s，实际 %s", entity.IndexTaskStatusPending, task.Status)
		}
		if task.BuildParams.VectorBuildInfo == nil {
			t.Error("向量构建任务信息为空")
		} else {
			if task.BuildParams.VectorBuildInfo.Uid != "test_user" {
				t.Errorf("用户ID不匹配，期望 test_user，实际 %s", task.BuildParams.VectorBuildInfo.Uid)
			}
			if task.BuildParams.VectorBuildInfo.RepoName != "test-repo" {
				t.Errorf("仓库名不匹配，期望 test-repo，实际 %s", task.BuildParams.VectorBuildInfo.RepoName)
			}
		}

		fmt.Printf("✅ 向量索引管理器任务创建测试通过\n")
	})

	// 测试用例2: 验证摘要索引管理器
	t.Run("摘要索引管理器任务创建", func(t *testing.T) {
		summaryManager, err := indexFactory.GetManager(entity.IndexTypeSummary)
		if err != nil {
			t.Fatalf("获取摘要索引管理器失败: %v", err)
		}
		if summaryManager == nil {
			t.Fatal("摘要索引管理器为空")
		}
		if summaryManager.Name() != "SummaryIndexManager" {
			t.Errorf("摘要索引管理器名称错误，期望 SummaryIndexManager，实际 %s", summaryManager.Name())
		}

		// 创建摘要构建任务
		summaryBuildTask := &entity.TaskInfo{
			IndexType: entity.IndexTypeSummary,
			TaskType:  entity.IndexTaskTypeBuild,
			SummaryBuildInfo: &entity.SummaryBuildIndexTaskInfo{
				Uid:      "test_user",
				RepoPath: "user",
				RepoName: "test-repo",
				RepoURL:  "https://code.byted.org/test-repo",
				Branch:   "main",
				Language: "go",
			},
		}

		// 执行构建
		err = summaryManager.Build(ctx, summaryBuildTask)
		if err != nil {
			t.Fatalf("摘要构建失败: %v", err)
		}

		// 验证任务是否被创建
		tasks, err := repository.GetAllTasks(ctx)
		if err != nil {
			t.Fatalf("获取任务列表失败: %v", err)
		}
		if len(tasks) != 2 {
			t.Fatalf("期望2个任务，实际有%d个任务", len(tasks))
		}

		// 查找摘要任务
		summaryTaskFound := false
		for _, task := range tasks {
			if task.IndexType == entity.IndexTypeSummary {
				summaryTaskFound = true
				if task.TaskType != entity.IndexTaskTypeBuild {
					t.Errorf("任务类型错误，期望 %s，实际 %s", entity.IndexTaskTypeBuild, task.TaskType)
				}
				if task.Status != entity.IndexTaskStatusPending {
					t.Errorf("任务状态错误，期望 %s，实际 %s", entity.IndexTaskStatusPending, task.Status)
				}
				if task.BuildParams.SummaryBuildInfo == nil {
					t.Error("摘要构建任务信息为空")
				} else {
					if task.BuildParams.SummaryBuildInfo.Uid != "test_user" {
						t.Errorf("用户ID不匹配，期望 test_user，实际 %s", task.BuildParams.SummaryBuildInfo.Uid)
					}
					if task.BuildParams.SummaryBuildInfo.RepoName != "test-repo" {
						t.Errorf("仓库名不匹配，期望 test-repo，实际 %s", task.BuildParams.SummaryBuildInfo.RepoName)
					}
				}
				break
			}
		}

		if !summaryTaskFound {
			t.Error("摘要构建任务应该被创建")
		}

		fmt.Printf("✅ 摘要索引管理器任务创建测试通过\n")
	})
}

// TestConsumerManagerTaskConsumptionSimple 专门测试 consumerManager 的任务消费功能
func TestConsumerManagerTaskConsumptionSimple(t *testing.T) {
	// 创建测试上下文
	ctx := context.Background()

	// 创建真实的依赖
	taskRepository := repository.NewMemoryTaskRepository()
	consumerFactory := factory.NewConsumerFactory()
	consumerManager := consumerFactory.CreateManager(taskRepository)

	// 测试用例1: 验证消费者管理器可以处理任务
	t.Run("消费者管理器任务处理", func(t *testing.T) {
		// 先创建一些测试任务
		vectorTask := &entity.IndexTask{
			TaskID:    "test_vector_task",
			TaskType:  entity.IndexTaskTypeBuild,
			IndexType: entity.IndexTypeVector,
			Status:    entity.IndexTaskStatusPending,
			BuildParams: entity.TaskInfo{
				IndexType: entity.IndexTypeVector,
				TaskType:  entity.IndexTaskTypeBuild,
				VectorBuildInfo: &entity.VectorBuildIndexTaskInfo{
					Uid:      "test_user",
					RepoName: "test-repo",
					Branch:   "main",
					Language: "go",
				},
			},
		}

		summaryTask := &entity.IndexTask{
			TaskID:    "test_summary_task",
			TaskType:  entity.IndexTaskTypeBuild,
			IndexType: entity.IndexTypeSummary,
			Status:    entity.IndexTaskStatusPending,
			BuildParams: entity.TaskInfo{
				IndexType: entity.IndexTypeSummary,
				TaskType:  entity.IndexTaskTypeBuild,
				SummaryBuildInfo: &entity.SummaryBuildIndexTaskInfo{
					Uid:      "test_user",
					RepoName: "test-repo",
					Branch:   "main",
					Language: "go",
				},
			},
		}

		// 添加任务到存储库
		err := taskRepository.SubmitTask(ctx, vectorTask)
		if err != nil {
			t.Fatalf("创建向量任务失败: %v", err)
		}
		err = taskRepository.SubmitTask(ctx, summaryTask)
		if err != nil {
			t.Fatalf("创建摘要任务失败: %v", err)
		}

		// 验证任务被创建
		tasks, err := taskRepository.GetAllTasks(ctx)
		if err != nil {
			t.Fatalf("获取任务列表失败: %v", err)
		}
		if len(tasks) != 2 {
			t.Fatalf("期望2个任务，实际有%d个任务", len(tasks))
		}

		// 执行消费（注意：这里只是验证消费过程不会出错，具体的消费逻辑取决于消费者实现）
		consumerManager.ConsumeTask(ctx)

		// 验证消费者管理器正常工作（不抛出异常）
		if consumerManager == nil {
			t.Fatal("消费者管理器为空")
		}

		fmt.Printf("✅ 消费者管理器任务处理测试通过\n")
	})
}

// TestConsumerManagerRealConsumption 测试消费者管理器的真实消费功能
func TestConsumerManagerRealConsumption(t *testing.T) {
	// 创建测试上下文
	ctx := context.Background()

	// 创建真实的依赖
	taskRepository := repository.NewMemoryTaskRepository()
	consumerFactory := factory.NewConsumerFactory()
	consumerManager := consumerFactory.CreateManager(taskRepository)

	// 注册真实的消费者
	consumerManager.RegisterConsumer(summaryConsume.NewSummaryConsumer())
	consumerManager.RegisterConsumer(vectorConsume.NewVectorConsumer())

	// 测试用例1: 测试向量消费者真实消费
	t.Run("向量消费者真实消费", func(t *testing.T) {
		// 创建向量构建任务
		vectorTask := &entity.IndexTask{
			TaskID:    "test_vector_build_task",
			TaskType:  entity.IndexTaskTypeBuild,
			IndexType: entity.IndexTypeVector,
			Status:    entity.IndexTaskStatusPending,
			BuildParams: entity.TaskInfo{
				IndexType: entity.IndexTypeVector,
				TaskType:  entity.IndexTaskTypeBuild,
				VectorBuildInfo: &entity.VectorBuildIndexTaskInfo{
					Uid:      "test_user",
					RepoName: "test-repo",
					RepoURL:  "https://code.byted.org/test-repo",
					Branch:   "main",
					Language: "go",
				},
			},
		}

		// 添加任务到存储库
		err := taskRepository.SubmitTask(ctx, vectorTask)
		if err != nil {
			t.Fatalf("创建向量任务失败: %v", err)
		}

		// 验证任务初始状态
		tasks, err := taskRepository.GetAllTasks(ctx)
		if err != nil {
			t.Fatalf("获取任务列表失败: %v", err)
		}
		if len(tasks) != 1 {
			t.Fatalf("期望1个任务，实际有%d个任务", len(tasks))
		}

		task := tasks[0]
		if task.Status != entity.IndexTaskStatusPending {
			t.Errorf("任务初始状态错误，期望 %s，实际 %s", entity.IndexTaskStatusPending, task.Status)
		}

		// 执行消费
		consumerManager.ConsumeTask(ctx)

		// 验证任务状态是否被更新（注意：由于消费者可能依赖外部服务，这里主要验证消费过程不报错）
		// 在实际环境中，任务状态应该从 Pending 变为 Running 或 Completed
		fmt.Printf("✅ 向量消费者真实消费测试通过\n")
	})

	// 测试用例2: 测试摘要消费者真实消费
	t.Run("摘要消费者真实消费", func(t *testing.T) {
		// 创建摘要构建任务
		summaryTask := &entity.IndexTask{
			TaskID:    "test_summary_build_task",
			TaskType:  entity.IndexTaskTypeBuild,
			IndexType: entity.IndexTypeSummary,
			Status:    entity.IndexTaskStatusPending,
			BuildParams: entity.TaskInfo{
				IndexType: entity.IndexTypeSummary,
				TaskType:  entity.IndexTaskTypeBuild,
				SummaryBuildInfo: &entity.SummaryBuildIndexTaskInfo{
					Uid:      "test_user",
					RepoName: "test-repo",
					RepoURL:  "https://code.byted.org/test-repo",
					Branch:   "main",
					Language: "go",
				},
			},
		}

		// 添加任务到存储库
		err := taskRepository.SubmitTask(ctx, summaryTask)
		if err != nil {
			t.Fatalf("创建摘要任务失败: %v", err)
		}

		// 验证任务初始状态
		tasks, err := taskRepository.GetAllTasks(ctx)
		if err != nil {
			t.Fatalf("获取任务列表失败: %v", err)
		}
		if len(tasks) != 2 {
			t.Fatalf("期望2个任务，实际有%d个任务", len(tasks))
		}

		// 查找摘要任务
		var summaryTaskFound *entity.IndexTask
		for _, task := range tasks {
			if task.TaskID == "test_summary_build_task" {
				summaryTaskFound = task
				break
			}
		}

		if summaryTaskFound == nil {
			t.Fatal("未找到摘要任务")
		}

		if summaryTaskFound.Status != entity.IndexTaskStatusPending {
			t.Errorf("任务初始状态错误，期望 %s，实际 %s", entity.IndexTaskStatusPending, summaryTaskFound.Status)
		}

		// 执行消费
		consumerManager.ConsumeTask(ctx)

		// 验证消费过程不报错
		fmt.Printf("✅ 摘要消费者真实消费测试通过\n")
	})

	// 测试用例3: 测试消费者注册和获取
	t.Run("消费者注册和获取", func(t *testing.T) {
		// 验证可以获取到注册的消费者
		vectorConsumer := vectorConsume.NewVectorConsumer()
		summaryConsumer := summaryConsume.NewSummaryConsumer()

		if vectorConsumer.GetType() != entity.IndexTypeVector {
			t.Errorf("向量消费者类型错误，期望 %s，实际 %s", entity.IndexTypeVector, vectorConsumer.GetType())
		}

		if summaryConsumer.GetType() != entity.IndexTypeSummary {
			t.Errorf("摘要消费者类型错误，期望 %s，实际 %s", entity.IndexTypeSummary, summaryConsumer.GetType())
		}

		// 验证消费者可以正确判断是否应该消费任务
		vectorTask := &entity.IndexTask{
			TaskType:  entity.IndexTaskTypeBuild,
			IndexType: entity.IndexTypeVector,
		}

		summaryTask := &entity.IndexTask{
			TaskType:  entity.IndexTaskTypeBuild,
			IndexType: entity.IndexTypeSummary,
		}

		if !vectorConsumer.ShouldConsume(vectorTask) {
			t.Error("向量消费者应该消费向量任务")
		}

		if !summaryConsumer.ShouldConsume(summaryTask) {
			t.Error("摘要消费者应该消费摘要任务")
		}

		if vectorConsumer.ShouldConsume(summaryTask) {
			t.Error("向量消费者不应该消费摘要任务")
		}

		if summaryConsumer.ShouldConsume(vectorTask) {
			t.Error("摘要消费者不应该消费向量任务")
		}

		fmt.Printf("✅ 消费者注册和获取测试通过\n")
	})

	// 测试用例4: 测试任务状态流转
	t.Run("任务状态流转", func(t *testing.T) {
		// 创建测试任务
		testTask := &entity.IndexTask{
			TaskID:    "test_status_flow_task",
			TaskType:  entity.IndexTaskTypeBuild,
			IndexType: entity.IndexTypeVector,
			Status:    entity.IndexTaskStatusPending,
			BuildParams: entity.TaskInfo{
				IndexType: entity.IndexTypeVector,
				TaskType:  entity.IndexTaskTypeBuild,
				VectorBuildInfo: &entity.VectorBuildIndexTaskInfo{
					Uid:      "test_user",
					RepoName: "test-repo",
					Branch:   "main",
					Language: "go",
				},
			},
		}

		// 添加任务
		err := taskRepository.SubmitTask(ctx, testTask)
		if err != nil {
			t.Fatalf("创建测试任务失败: %v", err)
		}

		// 验证初始状态
		tasks, err := taskRepository.GetAllTasks(ctx)
		if err != nil {
			t.Fatalf("获取任务列表失败: %v", err)
		}

		var foundTask *entity.IndexTask
		for _, task := range tasks {
			if task.TaskID == "test_status_flow_task" {
				foundTask = task
				break
			}
		}

		if foundTask == nil {
			t.Fatal("未找到测试任务")
		}

		if foundTask.Status != entity.IndexTaskStatusPending {
			t.Errorf("任务初始状态错误，期望 %s，实际 %s", entity.IndexTaskStatusPending, foundTask.Status)
		}

		// 执行消费
		consumerManager.ConsumeTask(ctx)

		// 验证消费过程不报错（在实际环境中，任务状态应该发生变化）
		fmt.Printf("✅ 任务状态流转测试通过\n")
	})
}

// TestVectorUpdateMerkleTreeConsume 专门测试 update vector merkle tree 的 consume 流程
func TestVectorUpdateMerkleTreeConsume(t *testing.T) {
	// 创建测试上下文
	ctx := context.Background()

	// 创建真实的依赖

	taskRepository := repository.NewMemoryTaskRepository()
	consumerFactory := factory.NewConsumerFactory()
	consumerManager := consumerFactory.CreateManager(taskRepository)

	// 注册向量消费者
	consumerManager.RegisterConsumer(vectorConsume.NewVectorConsumer())

	// 测试用例1: 测试向量更新任务的完整消费流程
	t.Run("向量更新任务完整消费流程", func(t *testing.T) {
		// 创建向量更新任务
		vectorUpdateTask := &entity.IndexTask{
			TaskID:    "test_vector_update_merkle_task",
			TaskType:  entity.IndexTaskTypeUpdate,
			IndexType: entity.IndexTypeVector,
			Status:    entity.IndexTaskStatusPending,
			BuildParams: entity.TaskInfo{
				IndexType: entity.IndexTypeVector,
				TaskType:  entity.IndexTaskTypeUpdate,
				VectorUpdateInfo: &entity.VectorUpdateIndexTaskInfo{
					MerkleTreeDownloadKey:    "test_merkle_tree_download_key",
					BaseUserKnowledgeId:      "test_base_user_knowledge_id",
					Uid:                      "test_user",
					RepoName:                 "test-repo",
					Branch:                   "main",
					RepoPath:                 "/path/to/test/repo",
					Did:                      "test_device",
					ChunkFileDownloadKey:     "test_chunk_file_download_key",
					RelationsFileDownloadKey: "test_relations_file_download_key",
					DeleteFileIds:            []string{"file1", "file2", "file3"},
					ClientMerkleId:           "test_client_merkle_id",
				},
			},
		}

		// 添加任务到存储库
		err := taskRepository.SubmitTask(ctx, vectorUpdateTask)
		if err != nil {
			t.Fatalf("创建向量更新任务失败: %v", err)
		}

		// 验证任务初始状态
		tasks, err := taskRepository.GetAllTasks(ctx)
		if err != nil {
			t.Fatalf("获取任务列表失败: %v", err)
		}
		if len(tasks) != 1 {
			t.Fatalf("期望1个任务，实际有%d个任务", len(tasks))
		}

		task := tasks[0]
		if task.Status != entity.IndexTaskStatusPending {
			t.Errorf("任务初始状态错误，期望 %s，实际 %s", entity.IndexTaskStatusPending, task.Status)
		}

		// 验证任务参数
		if task.BuildParams.VectorUpdateInfo == nil {
			t.Fatal("向量更新任务信息为空")
		}

		updateInfo := task.BuildParams.VectorUpdateInfo
		if updateInfo.Uid != "test_user" {
			t.Errorf("用户ID不匹配，期望 test_user，实际 %s", updateInfo.Uid)
		}
		if updateInfo.RepoName != "test-repo" {
			t.Errorf("仓库名不匹配，期望 test-repo，实际 %s", updateInfo.RepoName)
		}
		if updateInfo.BaseUserKnowledgeId != "test_base_user_knowledge_id" {
			t.Errorf("基础用户知识库ID不匹配，期望 test_base_user_knowledge_id，实际 %s", updateInfo.BaseUserKnowledgeId)
		}
		if updateInfo.ClientMerkleId != "test_client_merkle_id" {
			t.Errorf("客户端Merkle ID不匹配，期望 test_client_merkle_id，实际 %s", updateInfo.ClientMerkleId)
		}
		if len(updateInfo.DeleteFileIds) != 3 {
			t.Errorf("删除文件ID数量不匹配，期望 3，实际 %d", len(updateInfo.DeleteFileIds))
		}

		// 执行消费
		consumerManager.ConsumeTask(ctx)

		// 验证消费过程不报错（注意：由于依赖外部服务，这里主要验证消费流程不崩溃）
		fmt.Printf("✅ 向量更新任务完整消费流程测试通过\n")
	})

	// 测试用例2: 测试向量更新任务的参数验证
	t.Run("向量更新任务参数验证", func(t *testing.T) {
		// 测试空的基础用户知识库ID
		vectorUpdateTaskEmptyBase := &entity.IndexTask{
			TaskID:    "test_vector_update_empty_base_task",
			TaskType:  entity.IndexTaskTypeUpdate,
			IndexType: entity.IndexTypeVector,
			Status:    entity.IndexTaskStatusPending,
			BuildParams: entity.TaskInfo{
				IndexType: entity.IndexTypeVector,
				TaskType:  entity.IndexTaskTypeUpdate,
				VectorUpdateInfo: &entity.VectorUpdateIndexTaskInfo{
					MerkleTreeDownloadKey:    "test_merkle_tree_download_key",
					BaseUserKnowledgeId:      "", // 空的基础用户知识库ID
					Uid:                      "test_user",
					RepoName:                 "test-repo",
					Branch:                   "main",
					RepoPath:                 "/path/to/test/repo",
					Did:                      "test_device",
					ChunkFileDownloadKey:     "test_chunk_file_download_key",
					RelationsFileDownloadKey: "test_relations_file_download_key",
					DeleteFileIds:            []string{},
					ClientMerkleId:           "test_client_merkle_id",
				},
			},
		}

		// 添加任务到存储库
		err := taskRepository.SubmitTask(ctx, vectorUpdateTaskEmptyBase)
		if err != nil {
			t.Fatalf("创建向量更新任务失败: %v", err)
		}

		// 执行消费（应该会处理空的基础用户知识库ID的情况）
		consumerManager.ConsumeTask(ctx)

		// 验证消费过程不报错
		fmt.Printf("✅ 向量更新任务参数验证测试通过\n")
	})

	// 测试用例3: 测试向量更新任务的并发下载
	t.Run("向量更新任务并发下载", func(t *testing.T) {
		// 创建包含下载键的向量更新任务
		vectorUpdateTaskWithDownload := &entity.IndexTask{
			TaskID:    "test_vector_update_download_task",
			TaskType:  entity.IndexTaskTypeUpdate,
			IndexType: entity.IndexTypeVector,
			Status:    entity.IndexTaskStatusPending,
			BuildParams: entity.TaskInfo{
				IndexType: entity.IndexTypeVector,
				TaskType:  entity.IndexTaskTypeUpdate,
				VectorUpdateInfo: &entity.VectorUpdateIndexTaskInfo{
					MerkleTreeDownloadKey:    "test_merkle_tree_download_key",
					BaseUserKnowledgeId:      "test_base_user_knowledge_id",
					Uid:                      "test_user",
					RepoName:                 "test-repo",
					Branch:                   "main",
					RepoPath:                 "/path/to/test/repo",
					Did:                      "test_device",
					ChunkFileDownloadKey:     "test_chunk_file_download_key",     // 包含chunk文件下载键
					RelationsFileDownloadKey: "test_relations_file_download_key", // 包含关系文件下载键
					DeleteFileIds:            []string{"file1", "file2"},
					ClientMerkleId:           "test_client_merkle_id",
				},
			},
		}

		// 添加任务到存储库
		err := taskRepository.SubmitTask(ctx, vectorUpdateTaskWithDownload)
		if err != nil {
			t.Fatalf("创建向量更新任务失败: %v", err)
		}

		// 执行消费（应该会尝试并发下载文件）
		consumerManager.ConsumeTask(ctx)

		// 验证消费过程不报错
		fmt.Printf("✅ 向量更新任务并发下载测试通过\n")
	})

	// 测试用例4: 测试向量更新任务的语义搜索集成
	t.Run("向量更新任务语义搜索集成", func(t *testing.T) {
		// 创建完整的向量更新任务
		vectorUpdateTaskSemantic := &entity.IndexTask{
			TaskID:    "test_vector_update_semantic_task",
			TaskType:  entity.IndexTaskTypeUpdate,
			IndexType: entity.IndexTypeVector,
			Status:    entity.IndexTaskStatusPending,
			BuildParams: entity.TaskInfo{
				IndexType: entity.IndexTypeVector,
				TaskType:  entity.IndexTaskTypeUpdate,
				VectorUpdateInfo: &entity.VectorUpdateIndexTaskInfo{
					MerkleTreeDownloadKey:    "test_merkle_tree_download_key",
					BaseUserKnowledgeId:      "test_base_user_knowledge_id",
					Uid:                      "test_user",
					RepoName:                 "test-repo",
					Branch:                   "main",
					RepoPath:                 "/path/to/test/repo",
					Did:                      "test_device",
					ChunkFileDownloadKey:     "test_chunk_file_download_key",
					RelationsFileDownloadKey: "test_relations_file_download_key",
					DeleteFileIds:            []string{"file1", "file2", "file3", "file4"},
					ClientMerkleId:           "test_client_merkle_id",
				},
			},
		}

		// 添加任务到存储库
		err := taskRepository.SubmitTask(ctx, vectorUpdateTaskSemantic)
		if err != nil {
			t.Fatalf("创建向量更新任务失败: %v", err)
		}

		// 执行消费（应该会调用语义搜索管理器）
		consumerManager.ConsumeTask(ctx)

		// 验证消费过程不报错
		fmt.Printf("✅ 向量更新任务语义搜索集成测试通过\n")
	})

	// 测试用例5: 测试向量更新任务的状态流转
	t.Run("向量更新任务状态流转", func(t *testing.T) {
		// 创建向量更新任务
		vectorUpdateTaskStatus := &entity.IndexTask{
			TaskID:    "test_vector_update_status_task",
			TaskType:  entity.IndexTaskTypeUpdate,
			IndexType: entity.IndexTypeVector,
			Status:    entity.IndexTaskStatusPending,
			BuildParams: entity.TaskInfo{
				IndexType: entity.IndexTypeVector,
				TaskType:  entity.IndexTaskTypeUpdate,
				VectorUpdateInfo: &entity.VectorUpdateIndexTaskInfo{
					MerkleTreeDownloadKey:    "test_merkle_tree_download_key",
					BaseUserKnowledgeId:      "test_base_user_knowledge_id",
					Uid:                      "test_user",
					RepoName:                 "test-repo",
					Branch:                   "main",
					RepoPath:                 "/path/to/test/repo",
					Did:                      "test_device",
					ChunkFileDownloadKey:     "test_chunk_file_download_key",
					RelationsFileDownloadKey: "test_relations_file_download_key",
					DeleteFileIds:            []string{"file1"},
					ClientMerkleId:           "test_client_merkle_id",
				},
			},
		}

		// 添加任务到存储库
		err := taskRepository.SubmitTask(ctx, vectorUpdateTaskStatus)
		if err != nil {
			t.Fatalf("创建向量更新任务失败: %v", err)
		}

		// 验证初始状态
		tasks, err := taskRepository.GetAllTasks(ctx)
		if err != nil {
			t.Fatalf("获取任务列表失败: %v", err)
		}

		var foundTask *entity.IndexTask
		for _, task := range tasks {
			if task.TaskID == "test_vector_update_status_task" {
				foundTask = task
				break
			}
		}

		if foundTask == nil {
			t.Fatal("未找到测试任务")
		}

		if foundTask.Status != entity.IndexTaskStatusPending {
			t.Errorf("任务初始状态错误，期望 %s，实际 %s", entity.IndexTaskStatusPending, foundTask.Status)
		}

		// 执行消费
		consumerManager.ConsumeTask(ctx)

		// 验证消费过程不报错（在实际环境中，任务状态应该从 Pending 变为 Running 或 Completed）
		fmt.Printf("✅ 向量更新任务状态流转测试通过\n")
	})
}

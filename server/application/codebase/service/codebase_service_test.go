package service

import (
	"code.byted.org/gopkg/jsonx"
	summaryBuildRepo "code.byted.org/ies/codin/application/codebase/repo/summary_build_record"
	"code.byted.org/ies/codin/common/contexts"
	codeBaseManager "code.byted.org/ies/codin/common/semantic/codebase/manager"
	"code.byted.org/overpass/capcut_devops_codebase/kitex_gen/codebase"
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestCodebaseServiceImpl_GetSummaryUpdateFiles(t *testing.T) {
	ctx := context.Background()
	service := New(ctx)

	req := &codebase.GetSummaryUpdateFilesRequest{
		MerkleTreeDownloadKey: "10031303946593115898",
		Uid:                   "1c1e9d7c",
		Did:                   "ef7f6a67-de9e-578b-9394-40f112214b04",
		RepoPath:              "/User/qukecheng/code_dev_ut_dir",
		RepoName:              "ies/code-index",
		Branch:                "master",
	}
	resp, err := service.GetSummaryUpdateFiles(ctx, req)
	assert.Nil(t, err)

	summaryUserKnowledgeId := codeBaseManager.GetSummaryUserKnowledgeIdByCtx(contexts.WithRepoContext(ctx, &contexts.RepoContext{
		Uid:      req.Uid,
		Did:      req.Did,
		RepoPath: req.RepoPath,
		RepoName: req.RepoName,
		RepoURL:  "",
		Branch:   req.Branch,
		Language: "",
	}))

	t.Logf("resp: %v", jsonx.ToString(resp))
	assert.Equal(t, resp.ServerMerkleId, req.MerkleTreeDownloadKey)
	assert.Equal(t, resp.OriginUserKnowledgeId, summaryUserKnowledgeId)
	assert.Len(t, resp.GroupedRelatedPathInfo.LeafGroups, 0)
	assert.Len(t, resp.GroupedRelatedPathInfo.ModuleGroups, 0)
}

func TestCodebaseServiceImpl_QuerySummaryBuildRecord(t *testing.T) {
	ctx := context.Background()
	service := New(ctx)

	req := &codebase.QuerySummaryBuildRecordRequest{
		Uid:      "1c1e9d7c",
		Did:      "ef7f6a67-de9e-578b-9394-40f112214b04",
		RepoPath: "/User/qukecheng/code_dev_ut_dir",
		RepoName: "ies/code-index",
		Branch:   "master",
	}
	resp, err := service.QuerySummaryBuildRecord(ctx, req)
	assert.Nil(t, err)

	t.Logf("resp: %v", jsonx.ToString(resp))
	assert.Equal(t, resp.RootMerkleId, "10031303946593115898")
	assert.Equal(t, resp.BuildStatus, summaryBuildRepo.SummaryBuildStateSucceeded)
}

func TestCodebaseServiceImpl_GetSummaryUpdateFiles1(t *testing.T) {
	ctx := context.Background()
	service := New(ctx)

	resp, err := service.GetSummaryUpdateFiles(ctx, &codebase.GetSummaryUpdateFilesRequest{
		MerkleTreeDownloadKey: "10031303946593115898",
		Uid:                   "1c1e9d7c",
		Did:                   "ef7f6a67-de9e-578b-9394-40f112214b04",
		RepoName:              "ies/lv-lynx",
		Branch:                "cc/qkc",
		RepoPath:              "/Users/<USER>/code_dev/lv-lynx-1",
	})
	assert.Nil(t, err)
	t.Log(111, jsonx.ToString(resp))
}

package eval

import (
	"testing"
)

func TestParseUserKnowledgeId(t *testing.T) {
	tests := []struct {
		name             string
		userKnowledgeId  string
		expectedUID      string
		expectedPath     string
		expectedDID      string
		expectedRepoName string
		expectedBranch   string
		expectError      bool
	}{
		{
			name:             "数据库格式 - 多路径段",
			userKnowledgeId:  "613gb6bf//Users/<USER>/Code/iMovie/09fe9c08-b9e5-5d4d-9e61-b768c5e99c81/code.byted.org:faceu-ios/iMovie/tree/dreamina/develop",
			expectedUID:      "613gb6bf",
			expectedPath:     "/Users/<USER>/Code/iMovie",
			expectedDID:      "09fe9c08-b9e5-5d4d-9e61-b768c5e99c81",
			expectedRepoName: "faceu-ios/iMovie",
			expectedBranch:   "dreamina/develop",
			expectError:      false,
		},
		{
			name:             "数据库格式 - 单路径段",
			userKnowledgeId:  "613gb6bf/simple/path/09fe9c08-b9e5-5d4d-9e61-b768c5e99c81/code.byted.org:faceu-ios/iMovie/tree/dreamina/develop",
			expectedUID:      "613gb6bf",
			expectedPath:     "simple/path",
			expectedDID:      "09fe9c08-b9e5-5d4d-9e61-b768c5e99c81",
			expectedRepoName: "faceu-ios/iMovie",
			expectedBranch:   "dreamina/develop",
			expectError:      false,
		},
		{
			name:             "TOS格式 - 多路径段",
			userKnowledgeId:  "613gb6bf/Users/<USER>/Code/iMovie/09fe9c08-b9e5-5d4d-9e61-b768c5e99c81/code.byted.org/faceu-ios/iMovie/tree/dreamina/develop",
			expectedUID:      "613gb6bf",
			expectedPath:     "Users/bytedance/Code/iMovie",
			expectedDID:      "09fe9c08-b9e5-5d4d-9e61-b768c5e99c81",
			expectedRepoName: "faceu-ios/iMovie",
			expectedBranch:   "dreamina/develop",
			expectError:      false,
		},
		{
			name:             "TOS格式 - 单路径段",
			userKnowledgeId:  "613gb6bf/simple/path/09fe9c08-b9e5-5d4d-9e61-b768c5e99c81/code.byted.org/faceu-ios/iMovie/tree/dreamina/develop",
			expectedUID:      "613gb6bf",
			expectedPath:     "simple/path",
			expectedDID:      "09fe9c08-b9e5-5d4d-9e61-b768c5e99c81",
			expectedRepoName: "faceu-ios/iMovie",
			expectedBranch:   "dreamina/develop",
			expectError:      false,
		},
		{
			name:            "无效格式 - 缺少必要部分",
			userKnowledgeId: "invalid/format",
			expectError:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			uid, path, did, repoName, branch, err := ParseUserKnowledgeId(tt.userKnowledgeId)

			if tt.expectError {
				if err == nil {
					t.Errorf("ParseUserKnowledgeId() expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("ParseUserKnowledgeId() error = %v", err)
				return
			}

			if uid != tt.expectedUID {
				t.Errorf("ParseUserKnowledgeId() uid = %v, want %v", uid, tt.expectedUID)
			}
			if path != tt.expectedPath {
				t.Errorf("ParseUserKnowledgeId() path = %v, want %v", path, tt.expectedPath)
			}
			if did != tt.expectedDID {
				t.Errorf("ParseUserKnowledgeId() did = %v, want %v", did, tt.expectedDID)
			}
			if repoName != tt.expectedRepoName {
				t.Errorf("ParseUserKnowledgeId() repoName = %v, want %v", repoName, tt.expectedRepoName)
			}
			if branch != tt.expectedBranch {
				t.Errorf("ParseUserKnowledgeId() branch = %v, want %v", branch, tt.expectedBranch)
			}
		})
	}
}

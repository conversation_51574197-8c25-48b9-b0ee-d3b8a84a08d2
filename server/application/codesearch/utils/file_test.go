package utils

import (
	"testing"

	commonUtils "code.byted.org/ies/codin/common/utils"
)

func TestGetFilePath(t *testing.T) {
	tests := []struct {
		name     string
		filePath string
		rootPath string
		want     string
	}{
		{
			name:     "filePath 带前导斜杠，rootPath 为标准路径",
			filePath: "/apps/dreamina",
			rootPath: "apps/dreamina",
			want:     "apps/dreamina",
		},
		{
			name:     "filePath 与 rootPath 完全一致",
			filePath: "apps/dreamina",
			rootPath: "apps/dreamina",
			want:     "apps/dreamina",
		},
		{
			name:     "filePath 是 rootPath 的子路径",
			filePath: "apps/dreamina",
			rootPath: "apps",
			want:     "apps/dreamina",
		},
		{
			name:     "filePath 含有相对路径 ./ 前缀",
			filePath: "./apps/dreamina",
			rootPath: "apps/dreamina",
			want:     "apps/dreamina",
		},

		{
			name:     "处理 . 开头的情况",
			filePath: ".apps/dreamina",
			rootPath: "apps/dreamina",
			want:     "apps/dreamina",
		},
		{
			name:     "filePath 是 .. 应返回 clean 后结果",
			filePath: ".",
			rootPath: "apps/dreamina",
			want:     "apps/dreamina", // 或者你想返回更安全的路径时调整函数逻辑
		},
		{
			name:     "filePath 带多个 ./../ 混合路径",
			filePath: "./a/../b/./c",
			rootPath: "apps",
			want:     "apps/b/c",
		},
		{
			name:     "filePath 是空字符串",
			filePath: "",
			rootPath: "apps/dreamina",
			want:     "apps/dreamina",
		},
		{
			name:     "filePath 含有多余的斜杠",
			filePath: "apps//dreamina/",
			rootPath: "",
			want:     "apps/dreamina",
		},
		{
			name:     "filePath 和 rootPath 完全不相关",
			filePath: "otherservice/path",
			rootPath: "apps/dreamina",
			want:     "apps/dreamina/otherservice/path", // 或根据你的实现行为调整
		},
		{
			name:     "filePath 是 Windows 风格路径",
			filePath: `apps\dreamina`,
			rootPath: "apps",
			want:     "apps/dreamina", // 注意是否你的逻辑中自动转换了 `\` -> `/`
		},

		{
			name:     "在 root 下面",
			filePath: `apps/dreamina`,
			rootPath: "apps",
			want:     "apps/dreamina",
		},
		{
			name:     "filePath 是 ./",
			filePath: "./",
			rootPath: "apps/dreamina",
			want:     "apps/dreamina",
		},
		{
			name:     "filePath 是 .apps/dreamina",
			filePath: ".apps/dreamina",
			rootPath: "",
			want:     "apps/dreamina", // clean 后的结果
		},
	}

	for index, tt := range tests {
		index++

		t.Run(tt.name, func(t *testing.T) {
			got := commonUtils.GetFilePath(tt.filePath, tt.rootPath)
			if got != tt.want {
				t.Errorf("index = %v, GetFilePath() = %v, want %v", index, got, tt.want)
				return
			}
			t.Logf("index = %v, got success = %v", index, got)
		})
	}
}

package codesearch_query_record

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/common/contexts"
	"context"
	"database/sql/driver"
	"fmt"
	"github.com/bytedance/sonic"
	"github.com/samber/lo"
	"sync"

	. "code.byted.org/ies/codin/application/codesearch/common/mysql"
	"gorm.io/gorm"
)

var CodesearchQueryRecordRepository *codesearchQueryRecordRepository
var once sync.Once

func init() {
	once.Do(func() {
		CodesearchQueryRecordRepository = newCodesearchQueryRecordRepository(Get())
	})
}

type CodesearchQueryRecord struct {
	ID      int64        `gorm:"id"`
	Input   string       `gorm:"input"`
	Output  string       `gorm:"output"`
	Extra   StringAnyMap `gorm:"extra"`
	Channel string       `gorm:"channel"`
	LogID   string       `gorm:"log_id"`
	ErrInfo string       `gorm:"err_info"`
}

func (c *CodesearchQueryRecord) TableName() string {
	return "codesearch_query_record"
}

type StringAnyMap map[string]any

func (v StringAnyMap) Value() (driver.Value, error) { return sonic.Marshal(v) }
func (v *StringAnyMap) Scan(input any) error        { return sonic.Unmarshal(input.([]byte), v) }

type codesearchQueryRecordRepository struct {
	QueryEngine
}

func (c *codesearchQueryRecordRepository) prepare(query *ListQuery) *gorm.DB {
	return Where(c.Db().Model(&CodesearchQueryRecord{}), query)
}

func newCodesearchQueryRecordRepository(db *gorm.DB) *codesearchQueryRecordRepository {
	return &codesearchQueryRecordRepository{
		NewQueryEngine(db),
	}
}

func CreateQueryRecord(ctx context.Context, channel, input, output string, callErr error, extra map[string]any) error {
	createErr := CodesearchQueryRecordRepository.Create(ctx, &CodesearchQueryRecord{
		Input:   input,
		Output:  lo.If(callErr != nil, "").Else(output),
		Extra:   extra,
		Channel: channel,
		LogID:   contexts.GetLogID(ctx),
		ErrInfo: lo.If(callErr != nil, fmt.Sprintf("%v", callErr)).Else(""),
	})
	if createErr != nil {
		logs.CtxError(ctx, "CreateQueryRecord failed, err=%v, input=%v, output=%v", createErr, input, output)
		return createErr
	}
	return nil
}

func (c *codesearchQueryRecordRepository) Create(ctx context.Context, record *CodesearchQueryRecord) error {
	return c.Tx(ctx).Create(&record).Error
}

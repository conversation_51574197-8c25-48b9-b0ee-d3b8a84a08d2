package semantic

import (
	"code.byted.org/ies/codin/common/semantic/codebase/manager"
	"context"
	"errors"
	"fmt"
	"sync"

	"code.byted.org/flow/datamind-code-index/model"
	semanticCodebase "code.byted.org/ies/codin/common/semantic/codebase"
	code "code.byted.org/ies/codin/common/semantic/codebase/entity"

	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/logs/v2/log"
	commonUtils "code.byted.org/ies/codin/common/utils"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
)

var SemanticRepository *semanticRepository
var once sync.Once

func init() {
	once.Do(func() {
		SemanticRepository = newSemanticRepository()
	})
}

// SearchManager 搜索管理器
type semanticRepository struct {
	codeBase manager.Manager
}

func newSemanticRepository() *semanticRepository {
	return &semanticRepository{
		codeBase: semanticCodebase.SemanticManager,
	}
}

// SearchCode 搜索代码片段
func (m *semanticRepository) SearchCode(ctx context.Context, req *code.GetCodeRequest) ([]*code.CodeSnippet, error) {
	// 调用 codebase 模块的 GetCode 方法进行搜索
	logs.CtxInfo(ctx, "SearchCode, req: %v", req)
	results, err := m.codeBase.GetCode(ctx, req)
	// utils.WriteJsonToFile(results)
	if err != nil {
		log.V2.Error().With(ctx).Str("搜索代码失败").Error(err).Emit()
		return nil, errors.New("搜索代码失败")
	}
	fmt.Printf("results: %v %v\n", len(results.Chunks), len(results.Entities))
	// 转换为代码片段
	codeSnippets := commonUtils.ConvertToCodeSnippets(results)
	logs.CtxInfo(ctx, "SearchCode, codeSnippets: %d", len(codeSnippets))

	return codeSnippets, nil
}

func (m *semanticRepository) SearchCodeEntity(ctx context.Context, req *code.GetCodeRequest) ([]*code.CodeEntity, error) {
	results, err := m.codeBase.GetCode(ctx, req)
	if err != nil {
		log.V2.Error().With(ctx).Str("搜索代码实体失败").Error(err).Emit()
		return nil, errors.New("搜索代码实体失败")
	}

	// 收集所有需要查询的ChunkIDs
	allChunkIDs := make([]string, 0)
	chunkIDSet := make(map[string]bool) // 用于去重

	for _, entity := range results.Entities {
		if entity.RelevanceScore < 0.5 {
			continue
		}
		for _, chunkID := range entity.ChunkIDs {
			if !chunkIDSet[chunkID] {
				allChunkIDs = append(allChunkIDs, chunkID)
				chunkIDSet[chunkID] = true
			}
		}
	}

	// 通过SQL预取所有需要的chunks
	chunkMap, err := m.codeBase.GetChunksByIDs(ctx, allChunkIDs, req.UserKnowledgeId)
	if err != nil {
		log.V2.Error().With(ctx).Str("批量查询chunks失败").Error(err).Emit()
		return nil, errors.New("批量查询chunks失败")
	}

	// 优化：构筑一个 map 存储查询过的 filePath 结果，避免重复计算
	filePathCache := make(map[string]string)

	entities := make([]*code.CodeEntity, 0)
	for _, entity := range results.Entities {
		if entity.RelevanceScore < 0.5 {
			continue
		}

		// 查询所有 chunk,提取他们的 filePath 写入到 entity 中.
		filePaths := make([]string, 0)
		filePathSet := make(map[string]bool) // 用于去重

		// 遍历实体的所有chunkID
		for _, chunkID := range entity.ChunkIDs {
			// 先检查缓存
			if cachedPath, exists := filePathCache[chunkID]; exists {
				if !filePathSet[cachedPath] {
					filePaths = append(filePaths, cachedPath)
					filePathSet[cachedPath] = true
				}
				continue
			}

			// 从通过SQL查询的完整chunkMap中查找对应的chunk
			if chunk, ok := chunkMap[chunkID]; ok {
				// 使用normalizeChunkIDToPath提取文件路径
				filePath := commonUtils.NormalizeChunkIDToPath(chunk.ChunkID)
				// 缓存结果
				filePathCache[chunkID] = filePath
				// 去重添加到filePaths中
				if !filePathSet[filePath] {
					filePaths = append(filePaths, filePath)
					filePathSet[filePath] = true
				}
			}
		}

		codeEntity := &code.CodeEntity{
			EntityName: entity.EntityName,
			EntityDesc: entity.EntityDesc,
			ChunkIDs:   entity.ChunkIDs,
			FilePath:   filePaths,
		}
		entities = append(entities, codeEntity)
	}
	return entities, nil
}

func (m *semanticRepository) SearchCodeDependency(ctx context.Context, req *code.GetCodeDependencyRequest) ([]*code.CodeRelationSnippet, error) {
	result, err := m.codeBase.GetCodeDependency(ctx, req)
	if err != nil {
		log.V2.Error().With(ctx).Str("搜索代码依赖失败").Error(err).Emit()
		return nil, errors.New("搜索代码失败")
	}
	codeRelationSnippets := commonUtils.ConvertDependencyToCodeRelationSnippets(result)
	return codeRelationSnippets, nil
}

func (m *semanticRepository) SearchCodeReference(ctx context.Context, req *code.GetCodeDependencyRequest) ([]*code.CodeRelationSnippet, error) {
	result, err := m.codeBase.GetCodeReference(ctx, req)
	if err != nil {
		log.V2.Error().With(ctx).Str("搜索代码依赖失败").Error(err).Emit()
		return nil, errors.New("搜索代码失败")
	}
	codeRelationSnippets := commonUtils.ConvertDependencyToCodeRelationSnippets(result)
	return codeRelationSnippets, nil
}

func (m *semanticRepository) QueryBuildRecord(ctx context.Context, req *code.QueryBuildRecordRequest) (*model.BuildRecord, error) {
	return m.codeBase.QueryBuildRecord(ctx, req)
}

func (m *semanticRepository) FuzzyQueryBuildRecord(ctx context.Context, req *code.FuzzyQueryBuildRecordRequest) (*model.BuildRecord, error) {
	return m.codeBase.FuzzyQueryBuildRecord(ctx, req)
}

func (m *semanticRepository) QueryBuildRepo(ctx context.Context) (*codesearch.QueryBuildRepoResponse, error) {
	resp, err := m.codeBase.QueryBuildRepo(ctx, &code.QueryBuildRepoRequest{})
	if err != nil {
		logs.CtxError(ctx, "[QueryBuildRepo] QueryBuildRepo error: %v", err)
		return nil, err
	}

	items := make([]*codesearch.QueryBuildRepoItem, 0, len(resp.Items))
	for _, item := range resp.Items {
		items = append(items, &codesearch.QueryBuildRepoItem{
			RepoName: item.RepoName,
			CreateAt: item.CreateAt,
			ModifyAt: item.ModifyAt,
			Branch:   item.Branch,
			Language: item.Language,
			Uid:      item.Uid,
			Status:   item.Status,
		})
	}
	return &codesearch.QueryBuildRepoResponse{
		Items: items,
	}, nil
}

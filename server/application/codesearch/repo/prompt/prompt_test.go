package prompt

import (
	"code.byted.org/ies/codin/common/contexts"
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestGet(t *testing.T) {
	var (
		ctx                      = contexts.WithLogID(context.Background())
		systemPrompt, userPrompt string
		err                      error
	)

	systemPrompt, userPrompt, err = Get(ctx, "capcut.codesearch.complexity_analyze", map[string]any{
		"path":         "/",
		"repo_name":    "ies/codin",
		"path_content": "123\n321\n12345676",
	})
	assert.Nil(t, err)
	t.Logf("systemPrompt: %s", systemPrompt)
	t.Logf("userPrompt: %s", userPrompt)

	systemPrompt, _, err = Get(ctx, "capcut.devops.codesearch_fast_get_code", map[string]any{})
	assert.Nil(t, err)
	t.Logf("systemPrompt: %s", systemPrompt)

	systemPrompt, _, err = Get(ctx, "capcut.devops.codesearch_get_code", map[string]any{})
	assert.Nil(t, err)
	t.Logf("systemPrompt: %s", systemPrompt)
}

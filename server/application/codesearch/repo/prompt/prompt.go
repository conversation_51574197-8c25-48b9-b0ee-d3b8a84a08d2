package prompt

import (
	"code.byted.org/flowdevops/fornax_sdk"
	"code.byted.org/flowdevops/fornax_sdk/domain/chatmodel"
	"code.byted.org/flowdevops/fornax_sdk/domain/prompt"
	"code.byted.org/ies/codin/common/fornax"
	"context"
	"fmt"
)

var fornaxClient *fornax_sdk.Client

func init() {
	fornaxClient = fornax.GetFornaxClient()
}

func Get(ctx context.Context, ptKey string, variableMap map[string]any, options ...prompt.Option) (systemPrompt, userPrompt string, err error) {
	//会使用已发布版本进行执行，在调用接口前，请务必在 Fornax 平台完成发布操作
	ptResult, err := fornaxClient.GetPrompt(ctx, &prompt.GetPromptParam{Key: ptKey})
	if err != nil {
		return systemPrompt, userPrompt, fmt.Errorf("get prompt error: %v, key = %v", err, ptKey)
	}

	getPromptResult := ptResult.GetPrompt()
	if getPromptResult == nil {
		return systemPrompt, userPrompt, fmt.Errorf("prompt not found, key = %v", ptKey)
	}

	messageList, err := fornaxClient.FormatPrompt(ctx, getPromptResult, variableMap, options...)
	if err != nil {
		return systemPrompt, userPrompt, fmt.Errorf("format prompt error: %v, key = %v", err, ptKey)
	}

	if len(messageList) == 0 {
		return systemPrompt, userPrompt, fmt.Errorf("get prompt messageList is empty, key = %v", ptKey)
	}

	// 第 0 个元素是 systemPrompt，如果托管了 userPrompt，第 1 个元素是 userPrompt
	for _, message := range messageList {
		switch message.Role {
		case chatmodel.RoleTypeSystem:
			systemPrompt = message.Content
		case chatmodel.RoleTypeUser:
			userPrompt = message.Content
		}
	}
	return systemPrompt, userPrompt, nil
}

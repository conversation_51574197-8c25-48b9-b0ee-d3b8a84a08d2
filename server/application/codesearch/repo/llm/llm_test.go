package llm

import (
	"code.byted.org/gopkg/jsonx"
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

// 测试已知场景IndexScene
func TestGetChatModel_IndexScene(t *testing.T) {
	ctx := context.Background()

	// 保存原始环境变量以便测试后恢复
	originalArkKey := os.Getenv("ARK_KEY")
	originalIndexModel := os.Getenv("INDEX_SEED16_MODEL")
	originalEnv := os.Getenv("TCE_HOST_ENV")
	defer func() {
		_ = os.Setenv("ARK_KEY", originalArkKey)
		_ = os.Setenv("INDEX_SEED16_MODEL", originalIndexModel)
		_ = os.Setenv("TCE_HOST_ENV", originalEnv)
	}()

	// 测试本地环境分支
	t.Run("boe environment", func(t *testing.T) {
		// 确保不是PPE或Product环境
		_ = os.Setenv("TCE_HOST_ENV", "boe")
		// 设置本地环境所需的环境变量
		_ = os.Setenv("ARK_KEY", "test_local_key")
		_ = os.Setenv("INDEX_SEED16_MODEL", "seed16-local")

		model, err := GetChatModel(ctx, IndexScene)
		assert.NoError(t, err)
		assert.NotNil(t, model)
		t.Log("BOE MODEL =>", jsonx.ToString(model))
	})

	// 测试PPE环境分支
	t.Run("ppe environment", func(t *testing.T) {
		// 设置为PPE环境
		_ = os.Setenv("TCE_HOST_ENV", "ppe")

		model, err := GetChatModel(ctx, IndexScene)
		assert.NoError(t, err)
		assert.NotNil(t, model)
		t.Log("PPE MODEL =>", jsonx.ToString(model))
	})
}

// 测试未知场景
func TestGetChatModel_UnknownScene(t *testing.T) {
	ctx := context.Background()
	unknownScene := Scene("unknown_scene")

	model, err := GetChatModel(ctx, unknownScene)

	assert.Error(t, err)
	assert.Nil(t, model)
	assert.Contains(t, err.Error(), "unknown scene: unknown_scene")
}

// 测试本地环境下环境变量未设置的情况
func TestCreateSeed16ForIndex_LocalEnv_MissingVars(t *testing.T) {
	ctx := context.Background()

	// 保存原始环境变量以便恢复
	originalArkKey := os.Getenv("ARK_KEY")
	originalIndexModel := os.Getenv("INDEX_SEED16_MODEL")
	originalEnv := os.Getenv("TCE_HOST_ENV")
	defer func() {
		_ = os.Setenv("ARK_KEY", originalArkKey)
		_ = os.Setenv("INDEX_SEED16_MODEL", originalIndexModel)
		_ = os.Setenv("TCE_HOST_ENV", originalEnv)
	}()

	// 确保是本地环境
	_ = os.Setenv("TCE_HOST_ENV", "boe")
	// 清除必要的环境变量
	_ = os.Unsetenv("ARK_KEY")
	_ = os.Unsetenv("INDEX_SEED16_MODEL")

	model, err := CreateSeed16ForIndex(ctx)

	assert.Error(t, err)
	assert.Nil(t, model)
	assert.Equal(t, "ARK_KEY or INDEX_SEED16_MODEL is not set", err.Error())
}

// 测试本地环境下仅缺少ARK_KEY的情况
func TestCreateSeed16ForIndex_LocalEnv_MissingArkKey(t *testing.T) {
	ctx := context.Background()

	// 保存原始环境变量以便恢复
	originalArkKey := os.Getenv("ARK_KEY")
	originalEnv := os.Getenv("TCE_HOST_ENV")
	defer func() {
		_ = os.Setenv("ARK_KEY", originalArkKey)
		_ = os.Setenv("TCE_HOST_ENV", originalEnv)
	}()

	_ = os.Setenv("TCE_HOST_ENV", "boe")
	_ = os.Unsetenv("ARK_KEY")
	_ = os.Setenv("INDEX_SEED16_MODEL", "test_model")

	model, err := CreateSeed16ForIndex(ctx)

	assert.Error(t, err)
	assert.Nil(t, model)
	assert.Equal(t, "ARK_KEY or INDEX_SEED16_MODEL is not set", err.Error())
}

// 测试本地环境下仅缺少INDEX_SEED16_MODEL的情况
func TestCreateSeed16ForIndex_LocalEnv_MissingModel(t *testing.T) {
	ctx := context.Background()

	// 保存原始环境变量以便恢复
	originalIndexModel := os.Getenv("INDEX_SEED16_MODEL")
	originalEnv := os.Getenv("TCE_HOST_ENV")
	defer func() {
		_ = os.Setenv("INDEX_SEED16_MODEL", originalIndexModel)
		_ = os.Setenv("TCE_HOST_ENV", originalEnv)
	}()

	_ = os.Setenv("TCE_HOST_ENV", "boe")
	_ = os.Setenv("ARK_KEY", "test_key")
	_ = os.Unsetenv("INDEX_SEED16_MODEL")

	model, err := CreateSeed16ForIndex(ctx)
	assert.Error(t, err)
	assert.Nil(t, model)
	assert.Equal(t, "ARK_KEY or INDEX_SEED16_MODEL is not set", err.Error())
}

package service

import (
	"code.byted.org/gopkg/jsonx"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
	"context"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"testing"
)

func Test_codesearchServiceImpl_GetCode(t *testing.T) {
	ctx := context.Background()
	service := New(ctx)
	resp, err := service.GetCode(ctx, &codesearch.CodeSearchRequest{
		Uid:      "1c1e9d7c",
		Did:      "ef7f6a67-de9e-578b-9394-40f112214b04",
		RepoName: "ies/lv-lynx",
		Branch:   "test/666",
		RepoPath: "/Users/<USER>/code_dev/lv-lynx-1",
		PathList: []string{},
		Query:    "启动流程在哪里",
	})
	assert.Nil(t, err)
	t.Logf("resp => %v", jsonx.ToString(resp))

	t.Log(resp.Summary)
	assert.True(t, resp.Summary != "")
}

func Test_codesearchServiceImpl_GetCode1(t *testing.T) {
	ctx := context.Background()
	service := New(ctx)
	resp, err := service.GetCode(ctx, &codesearch.CodeSearchRequest{
		Uid:      "1c1e9d7c",
		Did:      "ef7f6a67-de9e-578b-9394-40f112214b04",
		RepoName: "faceu-ios/iMovie",
		Branch:   "dreamina/develop",
		RepoPath: "/Users",
		Query:    "生成流页面的代码在哪里",
		Mode:     lo.ToPtr("fast"),
	})
	assert.Nil(t, err)
	t.Logf("resp => %v", jsonx.ToString(resp))

	t.Log(resp.Summary)
	assert.True(t, resp.Summary != "")
}

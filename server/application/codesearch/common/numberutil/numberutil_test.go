package numberutil

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestParseInt(t *testing.T) {
	tests := []struct {
		name string
		val  any
		def  int
		want int
	}{
		// 正常情况测试
		{
			name: "int value",
			val:  123,
			def:  0,
			want: 123,
		},
		{
			name: "string number",
			val:  "456",
			def:  0,
			want: 456,
		},
		{
			name: "float value",
			val:  78.9,
			def:  0,
			want: 78,
		},
		{
			name: "bool true",
			val:  true,
			def:  0,
			want: 1,
		},
		{
			name: "bool false",
			val:  false,
			def:  0,
			want: 0,
		},

		// 默认值测试
		{
			name: "invalid value",
			val:  nil,
			def:  999,
			want: 999,
		},
		{
			name: "nil pointer",
			val:  (*int)(nil),
			def:  888,
			want: 888,
		},
		{
			name: "unconvertible string",
			val:  "not a number",
			def:  777,
			want: 0, // cast.ToInt对无法转换的字符串返回0
		},

		// 边界情况测试
		{
			name: "max int",
			val:  9223372036854775807,
			def:  0,
			want: 9223372036854775807,
		},
		{
			name: "min int",
			val:  -9223372036854775808,
			def:  0,
			want: -9223372036854775808,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ParseInt(tt.val, tt.def)
			assert.Equal(t, tt.want, got)
		})
	}
}

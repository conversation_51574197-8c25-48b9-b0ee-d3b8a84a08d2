package mysql

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewLimiter(t *testing.T) {
	assert.Nil(t, NewLimiter(nil, nil))

	limiter := NewLimiter(nil, 10)
	assert.NotNil(t, limiter)
	assert.Equal(t, 0, limiter.Offset)
	assert.Equal(t, 10, limiter.Limit)

	limiter = NewLimiter(2, nil)
	assert.NotNil(t, limiter)
	assert.Equal(t, 30, limiter.Offset)
	assert.Equal(t, 30, limiter.Limit)
}

func TestNewListQuery(t *testing.T) {
	query := NewListQuery().Set("Name", "alice")
	assert.NotNil(t, query)

	val, ok := query.Get("Name")
	assert.True(t, ok)
	assert.Equal(t, "alice", val)

	alice := "alice"
	query.Set("Name", alice)
	val, _ = query.Get("Name")
	assert.Equal(t, "alice", val)

	query.Like("Name")
	val, _ = query.Get("Name")
	assert.Equal(t, "%alice%", val)
}

package mysql

import (
	"code.byted.org/ies/codin/application/codesearch/common/numberutil"
	"context"
	"reflect"
	"strings"

	"github.com/fatih/structs"
	"gorm.io/gorm"
)

type QueryEngine struct {
	db *gorm.DB
}

func NewQueryEngine(db *gorm.DB) QueryEngine {
	return QueryEngine{
		db,
	}
}

func (r *QueryEngine) Db() *gorm.DB {
	return r.db
}

func (r *QueryEngine) Tx(ctx context.Context) *gorm.DB {
	return r.tx(ctx)
}

// tx 获取当前事务
func (r *QueryEngine) tx(ctx context.Context) *gorm.DB {
	tx, ok := ctx.Value("tx").(*gorm.DB)
	if !ok || tx == nil {
		tx = r.db
	}
	return tx.WithContext(ctx)
}

type Limiter struct {
	Limit  int
	Offset int
}

func NewLimiter(page any, pageSize any) *Limiter {
	if reflect.TypeOf(page) == nil && reflect.TypeOf(pageSize) == nil {
		return nil
	}

	Page, PageSize := numberutil.ParseInt(page, 1), numberutil.ParseInt(pageSize, 30)
	return &Limiter{PageSize, PageSize * (Page - 1)}
}

func (l *Limiter) Next() {
	l.Offset = l.Offset + l.Limit
}

// ListQuery 查询通用参数封装
type ListQuery struct {
	param      map[string]any
	limiter    *Limiter
	orderBy    string
	selectStrs []string
}

// NewListQuery 使用结构创建ListQuery
func NewListQuery(args ...any) *ListQuery {
	data := make(map[string]any)

	for _, arg := range args {
		structs.FillMap(arg, data)
	}

	return &ListQuery{
		param: data,
	}
}

// Set 设置查询参数
func (q *ListQuery) Set(key string, val any) *ListQuery {
	q.param[key] = val
	return q
}

// Get 获取查询参数
func (q *ListQuery) Get(key string) (any, bool) {
	val, ok := q.param[key]
	return val, ok
}

func (q *ListQuery) Like(key string) *ListQuery {
	val, exist := q.param[key]
	if exist && val != "" {
		q.param[key] = "%" + val.(string) + "%"
	}
	return q
}

func (q *ListQuery) Limiter(limiter *Limiter) *ListQuery {
	q.limiter = limiter
	return q
}

func (q *ListQuery) OrderBy(orderBy string) *ListQuery {
	q.orderBy = orderBy
	return q
}

func (q *ListQuery) Select(query string, args ...string) *ListQuery {
	q.selectStrs = append(q.selectStrs, query)
	q.selectStrs = append(q.selectStrs, args...)
	return q
}

// Test 定义检查函数
func Test(test func() bool, segment string) func(*string) {
	return func(sql *string) {
		if !test() {
			return
		}

		if strings.HasPrefix(segment, " ") {
			*sql = *sql + segment
		} else {
			*sql = *sql + " " + segment
		}
	}
}

func Where(tx *gorm.DB, query *ListQuery, tests ...func(*string)) *gorm.DB {
	where := ""
	for _, t := range tests {
		t(&where)
	}

	where = strings.TrimSpace(where)
	if strings.HasPrefix(where, "and ") {
		where = where[4:]
	}

	if where != "" {
		tx = tx.Where(where, query.param) // ignore_security_alert
	}

	if query.limiter != nil {
		if query.limiter.Limit > 0 {
			tx = tx.Limit(query.limiter.Limit)
		}
		if query.limiter.Offset > 0 {
			tx = tx.Offset(query.limiter.Offset)
		}
	}

	if query.orderBy != "" {
		tx = tx.Order(query.orderBy)
	}

	if len(query.selectStrs) > 0 {
		tx = tx.Select(strings.Join(query.selectStrs, ", "))
	}

	return tx
}

// Paginate 分页处理
// 1. 按主键分页
// - Select函数支持 id > @MinId 查询条件
// - ListQuery.OrderBy("id")
// - Paginate 处理函数 handleFunc中: query.Set("MinId", list[len(list)-1].Id)
//
// 2. 按Limit/Offset分页
// - 定义limiter, 传递给ListQuery.Limiter
// - Paginate 处理函数 handleFunc中: limiter.Next()
//
//	@param	query		基础查询条件,	可选pageSize
//	@param	queryFunc	单页查询
//	@param	handleFunc	单页处理函数,	更新查询条件(变更MinId、Offset等)
func Paginate[T any](
	ctx context.Context,
	query *ListQuery,
	queryFunc func(context.Context, *ListQuery) ([]T, error),
	handleFunc func([]T, *ListQuery) bool) error { //

	if query.limiter == nil {
		query.limiter = NewLimiter(1, 2000)
	}

	hasMore := true
	for hasMore {
		list, err := queryFunc(ctx, query)
		if err != nil {
			return err
		}

		// 有数据 -> 处理成功 -> 可能有剩余数据
		hasMore = len(list) > 0 && handleFunc(list, query) && len(list) == query.limiter.Limit
	}
	return nil
}

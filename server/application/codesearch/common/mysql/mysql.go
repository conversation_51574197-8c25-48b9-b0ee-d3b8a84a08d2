package mysql

import (
	"time"

	"code.byted.org/gopkg/env"
	"code.byted.org/gorm/bytedgorm"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type DbConfig struct {
	Name string
	BOE  *bytedgorm.DBConfig
	PROD *bytedgorm.DBConfig
}

func (d *DbConfig) Connect() *gorm.DB {
	conf := d.BOE
	if env.IsProduct() || env.IsPPE() {
		conf = d.PROD
	}

	db, err := gorm.Open(conf, bytedgorm.WithDefaults())
	if err != nil {
		panic(errors.Wrap(err, "init gorm db connection failed"))
	}
	return db
}

func NewDbConfig(psm string, dbName string, handlers ...func(*bytedgorm.DBConfig)) *bytedgorm.DBConfig {
	conf := bytedgorm.MySQL(psm, dbName).With(func(conf *bytedgorm.DBConfig) {
		conf.Timeout = 200 * time.Second
		conf.ReadTimeout = 200 * time.Second
		conf.WriteTimeout = 200 * time.Second
	})
	for _, handle := range handlers {
		handle(conf)
	}
	return conf
}

var (
	boe          = NewDbConfig("toutiao.mysql.capcut_devops_boe", "capcut_devops_boe")
	CapcutDevops = &DbConfig{
		Name: "CapcutDevops",
		BOE:  boe,
		PROD: NewDbConfig("toutiao.mysql.capcut_devops", "capcut_devops", func(conf *bytedgorm.DBConfig) {
			// 注意，统一使用_write节点，避免主从延迟，不要打开读写分离
			conf.ReadDBHostname = conf.DBHostname
		}),
	}

	capcutDevops = CapcutDevops.Connect()
)

func Get() *gorm.DB {
	return capcutDevops
}

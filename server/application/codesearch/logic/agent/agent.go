package agent

import (
	"context"

	"code.byted.org/flow/eino-byted-ext/callbacks/fornax"
	"code.byted.org/gopkg/jsonx"
	"code.byted.org/gopkg/logs"
	agentsdk "code.byted.org/ies/codin/common/agentsdk/core"
	"code.byted.org/ies/codin/common/agentsdk/tokencalculator"
	fornaxcommon "code.byted.org/ies/codin/common/fornax"
	"code.byted.org/ies/codinmodel/kitex_gen/conversation"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codesearch/entity"
	"code.byted.org/ies/codin/application/codesearch/repo/llm"

	// "code.byted.org/ies/codin/common/semantic/code/llm"
	"code.byted.org/ies/codin/common/utils"
	"github.com/cloudwego/eino/callbacks"
	"github.com/cloudwego/eino/schema"
)

type GenerateParams struct {
	Uid           string
	RawMessage    string
	SystemMessage string
	Scene         llm.Scene
	LogParams     *entity.LogParams
}

func Generate(ctx context.Context, params *GenerateParams) (outMessage *schema.Message, err error) {
	logParams := params.LogParams
	chatModel, err := llm.GetChatModel(ctx, params.Scene)
	if err != nil {
		log.V2.Error().With(ctx).Str("创建chat model失败").Error(err).Emit()
		return nil, err
	}

	// 上报 Fornax trace
	fornaxHandler := fornax.NewDefaultCallbackHandler(fornaxcommon.GetFornaxClient())
	newCtx := callbacks.InitCallbacks(ctx, nil, fornaxHandler)

	cid, err := llm.AgentSDK.CreateConversation(ctx, params.Uid, conversation.ConversationType_CodeSearch)
	if err != nil {
		log.V2.Error().With(ctx).Str("创建会话失败").Error(err).Emit()
		return nil, err
	}
	outMessage, err = llm.AgentSDK.Ask(newCtx,
		&agentsdk.ConversationOptions{
			ConversationId: cid,
			Uid:            params.Uid,
			RawMessage:     params.RawMessage,
		}, &agentsdk.ChatOptions{
			SystemPrompt: params.SystemMessage,
			ChatModel:    chatModel,
			TokenCalculator: tokencalculator.NewUserCalculator(ctx, &tokencalculator.UserConfig{
				Uid:    params.Uid,
				ConvId: cid,
			}),
		})
	if err != nil {
		logs.CtxError(ctx, "Generate error, err = %v", err)
		utils.WriteLogToDir(logParams.RequestKey, logParams.Dir, "Generate error: "+err.Error())
		return nil, err
	}

	utils.WriteLogToDir(logParams.RequestKey, logParams.Dir, "result: \n"+jsonx.ToString(outMessage))
	return outMessage, nil
}

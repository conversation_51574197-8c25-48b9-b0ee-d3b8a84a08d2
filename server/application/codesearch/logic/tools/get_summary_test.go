package tools

import (
	"code.byted.org/ies/codin/common/contexts"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestGetSummary(t *testing.T) {
	ctx := contexts.WithLogID(context.Background())
	ret, err := GetSummary(ctx, &codesearch.CodeSearchRequest{
		Uid:      "1c1e9d7c",
		Did:      "ef7f6a67-de9e-578b-9394-40f112214b04",
		RepoName: "ies/lv-lynx",
		Branch:   "master",
		RepoPath: "/Users/<USER>/code_dev/lv-lynx-1",
		PathList: []string{},
		Query:    "摘要一下这个仓库",
	})
	assert.Nil(t, err)
	t.Log(111, ret)
}

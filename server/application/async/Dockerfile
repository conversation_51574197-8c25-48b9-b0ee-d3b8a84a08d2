# 内场可用版本
# 基准镜像
FROM hub.byted.org/base/toutiao.nodejs.v22:latest

RUN echo "当前用户名: $(whoami)"

# FROM hub.byted.org/codercom/code-server:latest
# 安装 code-server，https://github.com/coder/code-server
RUN mkdir -p /tmp/code-server \
    && cd /tmp/code-server \
    && curl -fSL -o code-server.deb "https://tosv.byted.org/obj/faceuweb-test/codin/code-server_4.102.1_amd64.deb" \
    && dpkg -i code-server.deb \
    && rm -rf /tmp/code-server/*

COPY server/application/async/bin/code-server-config.yaml /root/.config/code-server/config.yaml
COPY server/application/async/bin/code-server-settings.json /root/.local/share/code-server/User/settings.json

# 安装插件
RUN curl -o /tmp/codin.vsix https://tosv.byted.org/obj/faceuweb-test/codin/codin-0.2.78.vsix
RUN code-server --install-extension /tmp/codin.vsix
RUN rm /tmp/codin.vsix
RUN code-server --install-extension ms-python.python \
    && code-server --install-extension dbaeumer.vscode-eslint \
    && code-server --install-extension esbenp.prettier-vscode \
    && code-server --install-extension ms-vscode.vscode-typescript-next \
    && code-server --install-extension dracula-theme.theme-dracula \
    && code-server --install-extension golang.go

# 安装依赖
RUN apt-get update \
    && apt-get install -y libgbm1 ffmpeg xvfb golang nodejs npm python3 python3-pip unzip \
    && apt-get install -y fonts-wqy-microhei fonts-noto-cjk fonts-arphic-uming \
    && apt-get install -f -y

RUN . ~/.bashrc

WORKDIR /app

# 脚本相关
COPY server/application/async/*.json /app/
COPY server/application/async/playwright.config.ts /app/
RUN mkdir -p /app/script
COPY server/application/async/script /app/script


#ARG faas_server_ver=********
#ENV faas_server_ver=$faas_server_ver
#
#RUN wget -O "faas.tar.gz" "https://luban-source.byted.org/repository/scm/capcut.codin.async_faas_${faas_server_ver}.tar.gz" && \
#    tar -zxvf faas.tar.gz

RUN npm config set registry http://bnpm.byted.org && npm i && npx playwright install
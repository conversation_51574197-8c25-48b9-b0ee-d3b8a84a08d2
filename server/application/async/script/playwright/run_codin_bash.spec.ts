import { test, Page, BrowserContext, chromium, Browser, Response } from '@playwright/test';
import fs from 'fs/promises';
import { parseCodeServerUrl, getFileContent, uploadLocalFile } from '../utils/file';
import { uploadFile, asyncTaskReport, asyncTaskList } from '../utils/request';
import { sleep } from '../utils/time';
const { execSync } = require('child_process');

const ASYNC_TASK_CID_FILE_PATH = '/tmp/async_task_cid.txt';
const ASYNC_TASK_DONE_FILE_PATH = '/tmp/async_task_done.txt';

const FAIL_MSG_CODE_HOST = '获取CodeServer访问地址失败';
const FAIL_MSG_CODE_NOT_BEGIN = '会话任务未开始';
const FAIL_MSG_GET_TASK_LIST = '获取任务信息失败';
const FAIL_MSG_PUSH_CODE = '代码PUSH失败';

const AsyncTaskStatusSuccess = 2;
const AsyncTaskStatusFail = 3;
const AsyncTaskStatusTimeout = 5;

const ExecCommandErr = 'ExecCommand err';

export function startPageTest({ coderLogPath = '/tmp/code-server.log' }: { coderLogPath?: string }) {
  test('run codin', async () => {
    // 任务UUID
    const taskID: string = process.env.TaskID ?? '';
    const localBranch: string = process.env.Branch ?? '';
    console.log(`taskID: ${taskID}; localBranch: ${localBranch}`);

    // 解析code-server host
    let codeHost: string = '';
    for (let i = 0; i < 5; i++) {
      codeHost = await parseCodeServerUrl(coderLogPath);
      console.log(`${i + 1} get code host: ${codeHost}`);
      if (codeHost.length > 0) {
        break;
      }
    }
    console.log(`codeHost: ${codeHost}`);
    if (codeHost === '') {
      await asyncTaskReport(taskID, '', AsyncTaskStatusFail, FAIL_MSG_CODE_HOST, []);
      return;
    }

    // 访问code-server host
    const browser = await chromium.launch();
    const context = await browser.newContext({});
    const page = await context.newPage();
    await page.goto(codeHost);
    await sleep(5000);
    console.log(`page url: ${page.url()}`);

    // 获取会话CID，最大等待5分钟
    let cid: string = '';
    for (let i = 0; i < 30; i++) {
      if (i % 10 === 0) {
        await screenshot(page);
      }
      cid = await getFileContent(ASYNC_TASK_CID_FILE_PATH);
      if (cid !== '') {
        console.log(`cid: ${cid}`);
        await asyncTaskReport(taskID, cid, 0, '', []);
        break;
      }
      await sleep(10 * 1000);
    }
    console.log(`结束获取会话CID, cid: ${cid}`);
    await screenshot(page);

    // 打印日志
    execCommand(`cat /tmp/async_task_log.txt`, 3);

    // CID取不到任务失败
    if (cid === '') {
      await asyncTaskReport(taskID, '', AsyncTaskStatusFail, FAIL_MSG_CODE_NOT_BEGIN, []);
      await browser.close();
      return;
    }

    // 等待会话结束，最大等待1小时
    let doneTimestamp: string = '';
    for (let i = 0; i < 360; i++) {
      if (i % 10 === 0) {
        await screenshot(page);
      }
      doneTimestamp = await getFileContent(ASYNC_TASK_DONE_FILE_PATH);
      if (doneTimestamp !== '') {
        console.log(`doneTimestamp: ${doneTimestamp}`);
        break;
      }
      await sleep(10 * 1000);
    }
    await screenshot(page);
    // 会话超时
    if (doneTimestamp === '') {
      await asyncTaskReport(taskID, '', AsyncTaskStatusTimeout, '', []);
      await browser.close();
      return;
    }

    // 各个仓库提交代码
    const taskData = await asyncTaskList(taskID);
    if (taskData === null) {
      await asyncTaskReport(taskID, '', AsyncTaskStatusFail, FAIL_MSG_GET_TASK_LIST, []);
      await browser.close();
      return;
    }
    const content = taskData['data'][0]['content'];
    const cloudIDESeq = content['cloud_ide_run_seq'];
    const caseNumber = content['case_info']['number'];
    const repoInfos = content['repo_infos'];
    const baseDir = '/home/<USER>';
    const aiBranch = `feat/cc-bench-cloud-${cloudIDESeq}/pr-${caseNumber}/interface`;

    let allPushSuccess: boolean = true;
    let pushFailRepo: string = '';
    for (let i = 0; i < repoInfos.length; i++) {
      const repo = repoInfos[i];

      // 确认仓库有无变更
      const gitStatus: string = execCommand(
        `
                cd ${baseDir}/${repo['repo']}
                git status --porcelain
                `,
        5,
      );
      console.log(`gitStatus: ${gitStatus}`);
      if (gitStatus.includes(ExecCommandErr)) {
        allPushSuccess = false;
        pushFailRepo = pushFailRepo + `${repo['repo']}; `;
        continue;
      }
      if (gitStatus.trim() === '') {
        continue;
      }

      // 上传patch.diff
      const patchDiffRet: string = execCommand(
        `
        cd ${baseDir}/${repo['repo']}
        git diff > patch.diff
        `,
        5,
      );
      console.log(`patchDiffRet: ${patchDiffRet}`);
      const patchDiffFilePath: string = `${baseDir}/${repo['repo']}/patch.diff`;
      const diffTosUrl = await uploadLocalFile(
        patchDiffFilePath,
        `codin/diff/${taskID}_${repo['repo']}_${Date.now()}_patch.diff`,
      );
      console.log(`diffTosUrl: ${diffTosUrl}`);
      if (diffTosUrl === '') {
        // 判断生成patch.diff失败
        allPushSuccess = false;
        pushFailRepo = pushFailRepo + `${repo['repo']}; `;
        continue;
      }
      repo['patch_diff_url'] = diffTosUrl;
      repoInfos[i] = repo;

      const commitRet: string = execCommand(
        `
                cd ${baseDir}/${repo['repo']}
                git add .
                git commit -m "cloud ide commit"
                `,
        1,
      );
      console.log(`commitRet: ${commitRet}`);
      if (commitRet.includes(ExecCommandErr) || !commitRet.includes('cloud ide commit')) {
        // 判断commit失败
        allPushSuccess = false;
        pushFailRepo = pushFailRepo + `${repo['repo']}; `;
        continue;
      }

      const pushRet: string = execCommand(
        `
                cd ${baseDir}/${repo['repo']}
                git push origin ${localBranch}:${aiBranch}
            `,
        5,
      );
      console.log(`pushRet: ${pushRet}`);
      if (pushRet.includes(ExecCommandErr)) {
        // 判断push失败
        allPushSuccess = false;
        pushFailRepo = pushFailRepo + `${repo['repo']}; `;
        continue;
      } else {
        repo['ai_branch'] = aiBranch;
        repoInfos[i] = repo;
      }
    }

    if (allPushSuccess === true) {
      await asyncTaskReport(taskID, '', AsyncTaskStatusSuccess, '', repoInfos);
    } else {
      await asyncTaskReport(taskID, '', AsyncTaskStatusFail, `${FAIL_MSG_PUSH_CODE}: ${pushFailRepo}`, []);
    }

    await browser.close();
    return;
  });

  async function screenshot(page: Page) {
    const screenshotPath: string = `screenshot/${Date.now()}.png`;
    await page.screenshot({ path: screenshotPath });
    const buffer = await fs.readFile(screenshotPath);
    console.log(buffer.length);
    const tosUrl: string = await uploadFile(buffer, `codin/temp/${screenshotPath}`);
    console.log(tosUrl);
  }

  function execCommand(command: string, retryNum: number) {
    if (retryNum <= 0) {
      retryNum = 1;
    }
    let execErr;
    for (let i = 0; i < retryNum; i++) {
      try {
        const result: string = execSync(command, { encoding: 'utf8' });
        console.log(`execCommand ${command}; result: ${result}`);
        return result;
      } catch (err) {
        execErr = err;
        console.log(`execCommand ${command}; err: ${err}`);
      }
    }
    return `${ExecCommandErr}: ${execErr}`;
  }
}

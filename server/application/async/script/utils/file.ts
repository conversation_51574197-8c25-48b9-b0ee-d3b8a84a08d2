import fs from 'fs/promises';
import { sleep } from './time';
import { uploadFile } from './request';

export async function parseCodeServerUrl(logFilePath: string): Promise<string> {
  // return "https://www.baidu.com";
  try {
    await fs.access(logFilePath);

    const logContent = await fs.readFile(logFilePath, 'utf8');
    console.log(`logFilePath: ${logFilePath}, content: ${logContent}`);

    const urlRegex = /HTTP server listening on (http:\/\/\d+\.\d+\.\d+\.\d+:\d+\/)/;
    const matchResult = logContent.match(urlRegex);

    if (matchResult && matchResult[1]) {
      return matchResult[1];
    } else {
      console.warn('未在日志中找到HTTP服务器监听地址');
      return '';
    }
  } catch (error) {
    console.error(`解析日志时发生错误：${error}`);
    return '';
  }
}

export async function getFileContent(filePath: string): Promise<string> {
  try {
    const content = await fs.readFile(filePath, 'utf8');
    console.log(`getFileContent ${filePath} content: ${content}`);
    return content;
  } catch (err) {
    console.error(`${filePath} read err: ${err}`);
    return '';
  }
}

export async function uploadLocalFile(localFilePath: string, tosFilePath: string) {
  try {
    const content = await fs.readFile(localFilePath, 'utf8');
    console.log(`getFileContent ${localFilePath} content: ${content}`);
  } catch (err) {
    console.error(`${localFilePath} read err: ${err}`);
    return '';
  }

  const buffer = await fs.readFile(localFilePath);
  console.log(buffer.length);
  for (let i = 0; i < 5; i++) {
    const tosUrl: string = await uploadFile(buffer, tosFilePath);
    console.log(tosUrl);
    if (tosUrl.length > 0) {
      return tosUrl;
    }
  }
  return '';
}

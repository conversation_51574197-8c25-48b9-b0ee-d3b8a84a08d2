import axios from 'axios';
import { sleep } from './time';

export async function uploadFile(buffer: Buffer, videoPath: string) {
  const TOS_DOMAIN = 'http://tosv.byted.org/obj/faceuweb-test';
  try {
    const response = await axios.post('https://faceu-api.bytedance.net/webtest/v1/upload/resource_v2', buffer, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'x-jwt-token': 'PytestJWT',
        'object-key': videoPath,
      },
    });
    console.log('response.status: ', response.status);
    console.log('response.data: ', JSON.stringify(response.data));
    if (response.status === 200 && response.data['ret'] === '0') {
      return `${TOS_DOMAIN}/${videoPath}`;
    }
  } catch (error) {
    console.error(error);
  }
  return '';
}

export async function asyncTaskReport(
  taskID: string,
  convID: string,
  taskStatus: number,
  reportMsg: string,
  repoInfos: any,
) {
  const body = {
    task_id: taskID,
    conv_id: convID,
    task_status: taskStatus,
    report_msg: reportMsg,
    repo_infos: repoInfos,
  };
  console.log(`asyncTaskReport body: ${JSON.stringify(body)}`);
  for (let i = 0; i < 5; i++) {
    try {
      const response = await axios.post('https://capcut-devops.byted.org/api/async/task/report', body, {
        headers: {
          'Content-Type': 'application/json',
          // 'x-tt-env': 'ppe_codin_async',
          // 'x-use-ppe': '1',
        },
      });
      console.log('response.status: ', response.status);
      console.log('response.data: ', JSON.stringify(response.data));
      if (response.status === 200 && response.data['code'] === 0) {
        break;
      }
    } catch (error) {
      console.error(error);
    }
    await sleep(3000);
  }
}

export async function asyncTaskList(taskID: string) {
  const body = {
    task_id: taskID,
  };
  for (let i = 0; i < 10; i++) {
    try {
      const response = await axios.post('https://capcut-devops.byted.org/api/async/task/list', body, {
        headers: {
          'Content-Type': 'application/json',
          // 'x-tt-env': 'ppe_codin_async',
          // 'x-use-ppe': '1',
        },
      });
      console.log('response.status: ', response.status);
      console.log('response.data: ', JSON.stringify(response.data));
      if (response.status === 200 && response.data['code'] === 0) {
        return response.data;
      }
    } catch (error) {
      console.error(error);
    }
    await sleep(3000);
  }

  return null;
}

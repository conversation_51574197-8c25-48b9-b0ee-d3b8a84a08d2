package generateuidetail

import (
	"context"
	"encoding/json"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/coding/logic"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type GenerateUIDetailParams struct {
	Url      string `json:"url"`
	Business string `json:"business"`
}

type GenerateUIDetailTool struct {
	app            *logic.App
	figmaToken     string
	conversationId string
}

func NewGenerateUIDetailTool(app *logic.App, figmaToken string, conversationId string) tool.InvokableTool {
	return &GenerateUIDetailTool{
		app:            app,
		figmaToken:     figmaToken,
		conversationId: conversationId,
	}
}

func (t *GenerateUIDetailTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "generate_ui_detail",
		Desc: `This tool allows you to retrieve details of corresponding design mockup nodes using their node keys.
		These details are essential for achieving pixel-perfect recreation of the design mockup.
		You MUST use this tool to retrieve detailed information for all node keys in the design mockup.`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"url": {
				Type:     "string",
				Desc:     "The url of the design/figma.",
				Required: true,
			},
			"business": {
				Type:     "string",
				Desc:     "The business of current project, support dreamina_web, other business pass empty", // 目前支持dreamina_web，其他业务传空即可
				Required: false,
			},
		}),
	}, nil
}

func (t *GenerateUIDetailTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON").Str(argumentsInJSON).Emit()
	// 解析参数
	p := &GenerateUIDetailParams{}
	err := json.Unmarshal([]byte(argumentsInJSON), p)
	if err != nil {
		return "Generate UI detail error: " + err.Error(), nil
	}

	detail, err := t.app.UIDesigner.GenerateUIDetail(ctx, p.Url, t.figmaToken, t.conversationId, p.Business)
	if err != nil {
		return "Get UI detail error: " + err.Error(), nil
	}

	return detail, nil
}

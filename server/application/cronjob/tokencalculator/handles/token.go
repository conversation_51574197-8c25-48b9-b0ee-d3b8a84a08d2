package handles

import (
	"context"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/cronjob/tokencalculator/db"
	cModel "code.byted.org/ies/codin/common/agentsdk/conversation/model"
	"code.byted.org/ies/codin/common/agentsdk/tokencalculator"
	"code.byted.org/overpass/capcut_devops_expense/kitex_gen/expense"
)

func TokenCalculator(ctx context.Context) ([]bool, error) {
	records, err := db.Client.GetTokenConsumption(ctx)
	if err != nil {
		log.V2.Error().Str("GetTokenConsumption fail").Error(err).KVs().Emit()
		return nil, err
	}

	results := []bool{}

	for _, record := range records {
		tc := tokencalculator.NewSystemCalculator(ctx, &tokencalculator.SystemConfig{
			Scene: expense.Scene_CodeIndex,
			Extra: map[string]string{
				"user_knowledge_id": record.UserKnowledgeId,
			},
		})
		userRemaining, totalRemaining := tc.Record(ctx, &cModel.TokenUsage{
			Model:            "ep-20250714151539-s4vvm",
			PromptTokens:     record.TotalInputTokenCnt,
			CompletionTokens: record.TotalOutputTokenCnt,
			TotalTokens:      record.TotalTokenCnt,
		})
		if userRemaining <= 0 || totalRemaining <= 0 {
			results = append(results, false)
		} else {
			results = append(results, true)
		}
	}
	return results, nil
}

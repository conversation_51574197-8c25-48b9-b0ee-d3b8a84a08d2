package db

import (
	"context"
	"time"

	"code.byted.org/flow/datamind-code-index/dao"
	"code.byted.org/flow/datamind-code-index/model"
	"code.byted.org/gopkg/logs/v2/log"
	"gorm.io/gorm"
)

type client struct {
	client *dao.DBService
	db     *gorm.DB
}

var Client *client

func init() {
	instance, err := New()
	if err != nil {
		log.V2.Error().Str("init index db fail").Error(err).KVs().Emit()
		return
	}
	Client = instance
}

func New() (*client, error) {
	c := &client{
		client: &dao.DBService{},
	}
	db, err := c.Init()
	if err != nil {
		return nil, err
	}
	c.db = db
	return c, nil
}

func (c *client) Init() (*gorm.DB, error) {
	db, err := c.client.InitDB(
		"capcut_devops_rw:Kx6ymK9AOBkpUyB0@tcp([2605:340:cd51:4301:aa48:e858:498e:6a32]:9030)/code_index_512_16")
	if err != nil {
		return nil, err
	}
	return db, nil
}

const (
	TokenConsumptionWindow = 60 * time.Minute
)

func (c *client) GetTokenConsumption(ctx context.Context) ([]model.BuildRecord, error) {
	var results []model.BuildRecord

	now := time.Now()

	// 计算前两个5分钟时间窗口
	startTime := now.Add(-2 * TokenConsumptionWindow).Truncate(TokenConsumptionWindow)
	endTime := startTime.Add(TokenConsumptionWindow)

	log.V2.Info().With(ctx).
		Str("find build_record by time range").
		KVs(
			"startTime", startTime,
			"endTime", endTime,
		).
		Emit()

	result := c.db.Table("code_index_512_16.build_record").
		Select(
			"id",
			"gmt_modified",
			"total_input_token_cnt",
			"total_output_token_cnt",
			"total_token_cnt",
			"user_knowledge_id",
		).
		Where("gmt_modified >= ? AND gmt_modified < ?", startTime, endTime).
		Order("gmt_modified DESC").
		Find(&results)
	if result.Error != nil {
		log.V2.Error().With(ctx).Str("find build_record fail").Error(result.Error).KVs().Emit()
		return nil, result.Error
	}
	log.V2.Info().With(ctx).
		Str("find build_record success").
		KVs(
			"count", len(results),
		).
		Emit()
	return results, nil
}

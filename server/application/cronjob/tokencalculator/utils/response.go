package utils

import (
	"context"
	"encoding/json"

	"code.byted.org/bytefaas/faas-go/events"
	"code.byted.org/gopkg/logs/v2"
)

type Body struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

func CreateResponse(ctx context.Context, statusCode int, code int, msg string, data interface{}) *events.EventResponse {
	body := &Body{
		Code: code,
		Msg:  msg,
		Data: data,
	}
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		logs.V2.Error().With(ctx).
			Str("Marshal fail").
			Obj(body).
			Error(err).
			Emit()
		return &events.EventResponse{
			StatusCode: statusCode,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Body: []byte("Marshal fail"),
		}
	}

	return &events.EventResponse{
		StatusCode: 200,
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Body: bodyBytes,
	}
}

func CreateFailResponse(ctx context.Context, statusCode int, err error) *events.EventResponse {
	return CreateResponse(ctx, statusCode, 0, err.Error(), nil)
}

func CreateErrorResponse(ctx context.Context, code int, err error) *events.EventResponse {
	return CreateResponse(ctx, 200, code, err.Error(), nil)
}

func CreateSuccessResponse(ctx context.Context, body interface{}) *events.EventResponse {
	return CreateResponse(ctx, 200, 0, "", body)
}

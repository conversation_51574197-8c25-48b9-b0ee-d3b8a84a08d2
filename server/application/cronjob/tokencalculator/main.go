package main

import (
	"context"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/cronjob/tokencalculator/handles"
)

func main() {
	ctx := context.Background()
	result, err := handles.TokenCalculator(ctx)
	if err != nil {
		log.V2.Error().Str("TokenCalculator fail").Error(err).KVs().Emit()
		return
	}
	log.V2.Info().Str("TokenCalculator success").Int(len(result)).Emit()
}

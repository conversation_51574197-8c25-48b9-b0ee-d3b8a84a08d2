package async_task

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/application/asset/dal/client_db"
	"code.byted.org/ies/codin/application/asset/logic"
	"code.byted.org/ies/codin/common/rds/devopsgorm"
	"code.byted.org/overpass/capcut_devops_asset/kitex_gen/asset"
	"context"
	jsoniter "github.com/json-iterator/go"
)

func AsyncTaskList(ctx context.Context, app *logic.App, req *asset.AsyncTaskListRequest) ([]*asset.AsyncTaskInfo, error) {
	whereClause := make(map[string]interface{})
	whereClause["task_id"] = req.TaskID
	asyncTaskInfos := make([]*asset.AsyncTaskInfo, 0)

	dbTasks, dbErr := client_db.ListAsyncTask(ctx, whereClause, devopsgorm.ReaderMode)
	if dbErr != nil {
		logs.CtxError(ctx, "[AsyncTaskList] ListAsyncTask db err: %v", dbErr)
		return asyncTaskInfos, dbErr
	}

	for _, dbTask := range dbTasks {
		task := &asset.AsyncTaskInfo{
			ID:         dbTask.ID,
			UID:        dbTask.UID,
			TaskID:     dbTask.TaskID,
			TaskStatus: int32(dbTask.TaskStatus),
			ConvID:     dbTask.ConvID,
			Content:    nil,
			CreateTime: dbTask.CreateTime.Format("2006-01-02 15:04:05"),
			UpdateTime: dbTask.UpdateTime.Format("2006-01-02 15:04:05"),
		}
		var content client_db.AsyncTaskContent
		if err := jsoniter.UnmarshalFromString(dbTask.Content, &content); err != nil {
			logs.CtxError(ctx, "[AsyncTaskList] fail to unmarshal[%s], err: %v", dbTask.Content, err)
			asyncTaskInfos = append(asyncTaskInfos, task)
			continue
		}
		task.Content = &asset.AsyncTaskContent{
			UserName:          content.UserName,
			UserEmail:         content.UserEmail,
			CloudIDERunUrl:    content.CloudIDE.PipelineRunUrl,
			CloudIDEReportMsg: content.CloudIDE.ReportMsg,
			CloudIDERunSeq:    content.CloudIDE.PipelineRunSeq,
			RepoInfos:         content.RepoInfos,
			CaseInfo:          content.CaseInfo,
		}
		asyncTaskInfos = append(asyncTaskInfos, task)
	}

	return asyncTaskInfos, nil
}

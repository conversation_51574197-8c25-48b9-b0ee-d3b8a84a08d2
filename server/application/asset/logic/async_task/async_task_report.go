package async_task

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/pkg/errors"
	"code.byted.org/ies/codin/application/asset/dal/client_db"
	"code.byted.org/ies/codin/application/asset/logic"
	"code.byted.org/ies/codin/common/rds/devopsgorm"
	"code.byted.org/ies/codin/common/utils"
	"code.byted.org/overpass/capcut_devops_asset/kitex_gen/asset"
	"context"
	jsoniter "github.com/json-iterator/go"
	"time"
)

func AsyncTaskReport(ctx context.Context, app *logic.App, req *asset.AsyncTaskReportRequest) error {
	whereClause := make(map[string]interface{})
	whereClause["task_id"] = req.TaskID

	taskInfos, err := client_db.ListAsyncTask(ctx, whereClause, devopsgorm.WriterMode)
	if err != nil || len(taskInfos) == 0 {
		logs.CtxError(ctx, "[AsyncTaskReport] ListAsyncTask err: %v, taskInfos: %v", err, taskInfos)
		return errors.Errorf("[AsyncTaskReport] ListAsyncTask err: %v, taskInfos: %v", err, taskInfos)
	}

	updateClause := make(map[string]interface{})
	if len(req.ConvID) > 0 {
		updateClause["conv_id"] = req.ConvID
	}
	if req.TaskStatus > 0 {
		updateClause["task_status"] = req.TaskStatus
	}
	if len(req.ReportMsg) > 0 || len(req.RepoInfos) > 0 || req.TaskStatus == client_db.AsyncTaskStatusSuccess {
		var content client_db.AsyncTaskContent
		if err = jsoniter.UnmarshalFromString(taskInfos[0].Content, &content); err == nil {
			if req.TaskStatus == client_db.AsyncTaskStatusSuccess {
				content.CloudIDE.ReportMsg = ""
			}
			if len(req.ReportMsg) > 0 {
				content.CloudIDE.ReportMsg = req.ReportMsg
			}
			if len(req.RepoInfos) > 0 {
				content.RepoInfos = req.RepoInfos
			}
			updateClause["content"] = utils.JsonToString(content)
		} else {
			logs.CtxError(ctx, "[AsyncTaskReport] fail to unmarshal contentStr[%s], err: %v", taskInfos[0].Content, err)
		}
	}
	updateClause["update_time"] = time.Now()

	dbErr := client_db.UpdateAsyncTask(ctx, whereClause, updateClause)
	if dbErr != nil {
		logs.CtxError(ctx, "[AsyncTaskReport] UpdateAsyncTask db err: %v", dbErr)
		return dbErr
	}

	return nil
}

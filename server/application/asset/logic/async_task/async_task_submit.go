package async_task

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/pkg/errors"
	"code.byted.org/ies/codin/application/asset/dal/client_db"
	"code.byted.org/ies/codin/application/asset/dal/client_http"
	"code.byted.org/ies/codin/application/asset/logic"
	"code.byted.org/ies/codin/common/login"
	"code.byted.org/ies/codin/common/tcc"
	"code.byted.org/ies/codin/common/utils"
	"code.byted.org/overpass/capcut_devops_asset/kitex_gen/asset"
	"context"
	"fmt"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
	"strings"
	"time"
)

func AsyncTaskSubmit(ctx context.Context, app *logic.App, req *asset.AsyncTaskSubmitRequest) (string, error) {
	taskID := strings.ToUpper(uuid.New().String())
	logs.CtxInfo(ctx, "[AsyncTaskSubmit] init taskID: %s", taskID)

	uid := login.GetUid(ctx)
	email := login.GetEmail(ctx)
	userName := strings.Replace(email, "@bytedance.com", "", -1)
	logs.CtxInfo(ctx, "[AsyncTaskSubmit] uid: %s; email: %s; userName: %s", uid, email, userName)

	command := packCommand(ctx, req, email, userName, taskID)
	runRsp, runErr := runPipelines(ctx, userName, command)
	if runErr != nil {
		logs.CtxError(ctx, "[AsyncTaskSubmit] runPipelines err: %v", runErr)
		return "", runErr
	}
	content := client_db.AsyncTaskContent{
		UserName:  userName,
		UserEmail: email,
		CloudIDE: struct {
			PipelineRunSeq int64  `json:"pipeline_run_seq"`
			PipelineRunUrl string `json:"pipeline_run_url"`
			ReportMsg      string `json:"report_msg"`
		}{
			PipelineRunSeq: runRsp.PipelineRun.RunSeq,
			PipelineRunUrl: runRsp.PipelineRun.PipelineRunUrl,
			ReportMsg:      "",
		},
		RepoInfos: req.RepoInfos,
		CaseInfo:  req.CaseInfo,
	}

	var copyReq asset.AsyncTaskSubmitRequest
	if err := jsoniter.UnmarshalFromString(utils.JsonToString(req), &copyReq); err == nil {
		copyReq.Base = nil
	}
	extra := client_db.AsyncTaskExtra{Req: utils.JsonToString(copyReq)}

	insertTask := &client_db.AsyncTask{
		UID:        uid,
		TaskID:     taskID,
		TaskStatus: client_db.AsyncTaskStatusRunning,
		ConvID:     "",
		Content:    utils.JsonToString(content),
		Extra:      utils.JsonToString(extra),
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}
	insertID, insertErr := client_db.CreateAsyncTask(ctx, insertTask)
	if insertErr != nil {
		logs.CtxInfo(ctx, "[AsyncTaskSubmit] CreateAsyncTask db err: %v", insertErr)
		return "", insertErr
	}
	logs.CtxInfo(ctx, "[AsyncTaskSubmit] insertID: %d", insertID)

	return taskID, nil
}

func runPipelines(ctx context.Context, userName, command string) (*client_http.BitsRunPipelineRsp, error) {
	tccConfig, tccErr := tcc.GetTccReader().GetAsyncTaskConfig(ctx)
	if tccErr != nil || tccConfig == nil {
		logs.CtxError(ctx, "[AsyncTaskSubmit] tcc GetAsyncTaskConfig err: %v, config: %v", tccErr, tccConfig)
		return nil, errors.Errorf("[AsyncTaskSubmit] tcc GetAsyncTaskConfig err: %v, config: %v", tccErr, tccConfig)
	}

	logs.CtxInfo(ctx, "tccConfig: %s", utils.JsonToString(tccConfig))
	sk := tccConfig.SK
	mirror := tccConfig.CloudIDE.Mirror
	pipelineID := tccConfig.CloudIDE.PipelineID

	body := map[string]interface{}{
		"custom_vars": []interface{}{
			map[string]interface{}{
				"name": "custom.mirror",
				"value": map[string]string{
					"text":    mirror,
					"rawJson": mirror,
				},
			},
			map[string]interface{}{
				"name": "custom.command",
				"value": map[string]string{
					"text":    command,
					"rawJson": command,
				},
			},
		},
	}

	return client_http.BitsRunPipeline(ctx, pipelineID, userName, sk, body)
}

func packCommand(ctx context.Context, req *asset.AsyncTaskSubmitRequest, email, userName, taskID string) string {
	var builder strings.Builder
	builder.WriteString("date\n")
	builder.WriteString("pwd\n")

	builder.WriteString(fmt.Sprintf("echo '::set-output name=task_id::%s'\n", taskID))
	builder.WriteString(fmt.Sprintf("git config --global user.name %s\n", userName))
	builder.WriteString(fmt.Sprintf("git config --global user.email %s\n\n", email))

	param := map[string]string{
		"jwt":         req.JWT,
		"figma_token": req.FigmaToken,
	}

	builder.WriteString(fmt.Sprintf("echo '%s' > /tmp/async_task_msg.txt\n", utils.JsonToString(param)))
	builder.WriteString("cat /tmp/async_task_msg.txt\n\n")

	builder.WriteString(fmt.Sprintf("echo '%s' > /tmp/async_task_prompt.txt\n", req.CaseInfo.Prompt))
	builder.WriteString("cat /tmp/async_task_prompt.txt\n\n")

	tempBranch := fmt.Sprintf("feat_cloud_temp_%d", time.Now().UnixMilli())

	for _, git := range req.RepoInfos {
		builder.WriteString("date\n")
		builder.WriteString(fmt.Sprintf("mkdir %s && cd %s\n", git.Repo, git.Repo))
		builder.WriteString("git init\n")
		builder.WriteString(fmt.Sprintf("git remote add origin %s\n", fmt.Sprintf("https://%s/%s/%s.git", git.Domain, git.Org, git.Repo)))
		codeVersion := git.OriginCommitID
		if len(codeVersion) == 0 {
			codeVersion = git.OriginBranch
		}
		builder.WriteString(fmt.Sprintf("git fetch origin %s --depth=1\n", codeVersion))
		builder.WriteString(fmt.Sprintf("git checkout %s\n", codeVersion))
		builder.WriteString(fmt.Sprintf("git branch\n"))
		builder.WriteString(fmt.Sprintf("git checkout -b %s\n", tempBranch))
		builder.WriteString(fmt.Sprintf("git branch\n"))
		builder.WriteString("cd ..\n")
		builder.WriteString("date\n\n")
	}

	builder.WriteString("code-server --config ~/.config/code-server/config.yaml /home/<USER>/tmp/code-server.log 2>&1 &\n")
	builder.WriteString("cat /tmp/code-server.log\n\n")

	builder.WriteString("cd /app/\n")
	builder.WriteString(fmt.Sprintf("TaskID=%s Branch=%s xvfb-run -a npx playwright test script/playwright/run_codin.spec.ts\n", taskID, tempBranch))

	command := builder.String()
	logs.CtxInfo(ctx, "[AsyncTaskSubmit] command: %s", command)
	return command
}

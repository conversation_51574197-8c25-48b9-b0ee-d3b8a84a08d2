package client_http

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/pkg/errors"
	"code.byted.org/ies/codin/common/contexts"
	"code.byted.org/ies/codin/common/utils"
	"context"
	"fmt"
	"net/http"
)

type BitsRunPipelineRsp struct {
	PipelineRun struct {
		PipelineID     int64  `json:"pipeline_id"`
		RunSeq         int64  `json:"run_seq"`
		PipelineRunUrl string `json:"pipeline_run_url"`
	} `json:"pipeline_run"`
}

func BitsRunPipeline(ctx context.Context, pipelineID, userName, sk string, body map[string]interface{}) (*BitsRunPipelineRsp, error) {
	jwt, jwtErr := RequestAuthJwt(ctx, sk)
	if jwtErr != nil {
		logs.CtxError(ctx, "[BitsRunPipeline] fail to get jwt, err: %v", jwtErr)
		return nil, jwtErr
	}

	header := map[string]string{
		"Content-Type": "application/json",
		"Accept":       "application/json",
		XTTLogID:       contexts.GetLogID(ctx),
		"x-jwt-token":  jwt,
		"username":     userName,
	}

	rsp := new(BitsRunPipelineRsp)
	resp, err := httpClient.R().
		SetHeaders(header).
		SetBody(&body).
		SetResult(rsp).
		Post(fmt.Sprintf("https://bits.bytedance.net/api/v1/pipelines_open/%s/run", pipelineID))

	logs.CtxInfo(ctx, "[BitsRunPipeline] reqStr: %s, rspStr: %s", utils.JsonToString(body), utils.JsonToString(rsp))

	if err != nil || resp.StatusCode() != http.StatusOK || rsp == nil || rsp.PipelineRun.RunSeq == 0 {
		logs.CtxError(ctx, "[BitsRunPipeline] err: %v, status code: %d, rsp: %v", err, resp.StatusCode(), utils.JsonToString(rsp))
		return nil, errors.Errorf("[BitsRunPipeline] err: %v, status code: %d, rsp: %v", err, resp.StatusCode(), utils.JsonToString(rsp))
	}

	return rsp, nil
}

package client_http

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/pkg/errors"
	"code.byted.org/ies/codin/common/contexts"
	"context"
	"fmt"
	"net/http"
)

func RequestAuthJwt(ctx context.Context, sk string) (string, error) {
	header := map[string]string{
		"Content-Type":  "application/json",
		"Accept":        "application/json",
		"Authorization": fmt.Sprintf("Bearer %s", sk),
		XTTLogID:        contexts.GetLogID(ctx),
	}

	resp, err := httpClient.R().
		SetHeaders(header).
		Get("https://cloud.bytedance.net/auth/api/v1/jwt")

	if err != nil || resp.StatusCode() != http.StatusOK {
		logs.CtxError(ctx, "[RequestAuthJwt] request err: %v, status code: %d", err, resp.StatusCode())
		return "", errors.Errorf("[RequestAuthJwt] request err: %v, status code: %d", err, resp.StatusCode())
	}

	jwt := resp.Header().Get("X-Jwt-Token")
	return jwt, err
}

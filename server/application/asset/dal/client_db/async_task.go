package client_db

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/ies/codin/common/rds/devopsgorm"
	"code.byted.org/ies/codin/common/utils"
	"code.byted.org/overpass/capcut_devops_asset/kitex_gen/asset"
	"context"
	jsoniter "github.com/json-iterator/go"
	"time"
)

type AsyncTask struct {
	ID         int64     `gorm:"column:id" json:"id"`                   // 执行计划ID
	UID        string    `gorm:"column:uid" json:"uid"`                 // 用户ID
	TaskID     string    `gorm:"column:task_id" json:"task_id"`         // 任务UUID
	TaskStatus int       `gorm:"column:task_status" json:"task_status"` // 任务状态
	ConvID     string    `gorm:"column:conv_id" json:"conv_id"`         // 会话ID
	Content    string    `gorm:"column:content" json:"content"`         // 内容JSON
	Extra      string    `gorm:"column:extra" json:"extra"`             // Extra
	CreateTime time.Time `gorm:"column:create_time" json:"create_time"` // 创建时间
	UpdateTime time.Time `gorm:"column:update_time" json:"update_time"` // 更新时间
}

type AsyncTaskContent struct {
	UserName  string `json:"user_name"`
	UserEmail string `json:"user_email"`
	CloudIDE  struct {
		PipelineRunSeq int64  `json:"pipeline_run_seq"`
		PipelineRunUrl string `json:"pipeline_run_url"`
		ReportMsg      string `json:"report_msg"`
	} `json:"cloud_ide"`
	RepoInfos []*asset.RepoInfo `json:"repo_infos"`
	CaseInfo  *asset.CaseInfo   `json:"case_info"`
}

type AsyncTaskExtra struct {
	Req string `json:"req"`
}

const (
	AsyncTaskStatusInit    = 0
	AsyncTaskStatusRunning = 1
	AsyncTaskStatusSuccess = 2
	AsyncTaskStatusFail    = 3
	AsyncTaskStatusCancel  = 4
	AsyncTaskStatusTimeout = 5 // 任务超时
)

func (m *AsyncTask) TableName() string {
	return "t_async_task"
}

func CreateAsyncTask(ctx context.Context, task *AsyncTask) (int64, error) {
	taskStr, _ := jsoniter.MarshalToString(task)
	logs.CtxInfo(ctx, "[CreateAsyncTask] task: %s", taskStr)
	conn, err := devopsgorm.GetDBConn(devopsgorm.WriterMode)
	if err != nil {
		logs.CtxError(ctx, "[CreateAsyncTask] get db conn err: %s", err.Error())
		return 0, err
	}

	if err = conn.Create(task).Error; err != nil {
		logs.CtxError(ctx, "[CreateAsyncTask] err: %v", err)
		return 0, err
	}

	return task.ID, nil
}

func UpdateAsyncTask(ctx context.Context, whereClause, updateClause map[string]interface{}) error {
	logs.CtxInfo(ctx, "[UpdateAsyncTask] whereClause: %s; updateClause: %s", utils.JsonToString(whereClause), utils.JsonToString(updateClause))
	conn, err := devopsgorm.GetDBConn(devopsgorm.WriterMode)
	if err != nil {
		logs.CtxError(ctx, "get db conn err: %s", err.Error())
		return err
	}

	return conn.Model(&AsyncTask{}).Where(whereClause).Updates(updateClause).Error
}

func ListAsyncTask(ctx context.Context, whereClause map[string]interface{}, mode string) ([]AsyncTask, error) {
	logs.CtxInfo(ctx, "[ListAsyncTask] whereClause: %s; mode: %s", utils.JsonToString(whereClause), mode)
	conn, err := devopsgorm.GetDBConn(mode)
	if err != nil {
		logs.CtxError(ctx, "[ListAsyncTask] get db conn err: %s", err.Error())
		return nil, err
	}

	var (
		asyncTaskList = make([]AsyncTask, 0)
	)

	err = conn.Model(&AsyncTask{}).
		Where(whereClause).
		Order("id desc").
		Find(&asyncTaskList).Error

	if err != nil {
		logs.CtxError(ctx, "[ListAsyncTask] fail to get asyncTaskList: %v", err)
		return nil, err
	}

	return asyncTaskList, nil
}

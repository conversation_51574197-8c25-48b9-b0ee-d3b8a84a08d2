package token

import (
	"context"
	"time"

	"code.byted.org/ies/codin/application/expense/logic"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/overpass/capcut_devops_expense/kitex_gen/expense"
)

func GetTokenUsageByTime(ctx context.Context, app *logic.App, startTime int64, endTime int64) ([]*expense.TokenUsage, error) {
	tokenUsages, err := app.TokenUsageRepo.GetTokenUsageByTime(ctx, time.UnixMilli(startTime), time.UnixMilli(endTime))
	if err != nil {
		return nil, rpcerr.Wrapf(err, expense.ErrCode_GetTokenUsageFailed, "get token usage by time failed")
	}

	ret := make([]*expense.TokenUsage, 0, len(tokenUsages))
	for _, tokenUsage := range tokenUsages {
		ret = append(ret, &expense.TokenUsage{
			ModelType:        tokenUsage.ModelType,
			PromptTokens:     tokenUsage.PromptTokens,
			CompletionTokens: tokenUsage.CompletionTokens,
			TotalTokens:      tokenUsage.TotalTokens,
			Scene:            expense.Scene(tokenUsage.Scene),
			Uid:              &tokenUsage.Uid,
			ConvId:           &tokenUsage.ConvId,
			Extra:            tokenUsage.Extra,
		})
	}
	return ret, nil
}

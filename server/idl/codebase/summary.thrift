namespace go codebase

struct DirPathGroup {
  1: required string GroupPath (go.tag = "json:\"group_path\""),  // 组路径（module或subModule路径）
  2: required list<string> SubDirPaths (go.tag = "json:\"sub_dir_paths\""), // 该组下的所有子文件路径
}

struct GroupedRelatedPathInfo {
  1: required list<DirPathGroup> ModuleGroups (go.tag = "json:\"module_groups\""), // 模块组列表
  2: required list<DirPathGroup> LeafGroups (go.tag = "json:\"leaf_groups\""), // 叶子节点组列表
}

struct FileInfo {
  1: required string FilePath (go.tag = "json:\"file_path\""),  // 文件路径
  2: required string FileContent (go.tag = "json:\"file_content\""), // 文件内容
}

struct FileContentGroup {
  1: required string GroupPath (go.tag = "json:\"group_path\""),  // 组路径（module或subModule路径）
  2: required list<FileInfo> SubFileInfos (go.tag = "json:\"sub_file_infos\""), // 该组下的所有子文件路径
}

struct GroupedRelatedFileInfo {
  1: required list<FileContentGroup> ModuleGroups (go.tag = "json:\"module_groups\""), // 模块组列表
  2: required list<FileContentGroup> LeafGroups (go.tag = "json:\"leaf_groups\""), // 叶子节点组列表
}

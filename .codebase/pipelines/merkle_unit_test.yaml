name: Merkle Unit Test
trigger: [change]
jobs:
  build:
    name: Merkle Unit Test
    image: hub.byted.org/capcut/ccweb.playwright:e1c60f11f6b24969bee92660830e8f57
    envs:
      NODE_OPTIONS: "--max-old-space-size=32768"
    steps:
      - name: Init
        commands:
          - cd web/packages/merkle
          - npm config set registry https://bnpm.byted.org
          - pnpm i
      - name: Unit Test
        commands:
          - cd web/packages/merkle
          - pnpm run test

import { describe, it, expect } from 'vitest';
import { isIgnored } from './is-ignored';
import ignore from 'ignore';

const patterns = ['node_modules', '.git', '/playwright/*', '!starling.config.js', 'starling'];

describe('ignore', () => {
  it('should ignore', async () => {
    const ig = ignore().add(patterns);
    expect(isIgnored(ig, 'path/to/node_modules/pkg/index.ts')).toBe(true);
    expect(isIgnored(ig, '.git/x/y/z')).toBe(true);
    expect(isIgnored(ig, '.git')).toBe(true);
    expect(isIgnored(ig, 'playwright/x/y/z')).toBe(true);
    expect(isIgnored(ig, 'starling/locale.json')).toBe(true);
  });

  it('should not ignore', async () => {
    const ig = ignore().add(patterns);
    expect(isIgnored(ig, 'src/index.ts')).toBe(false);
    expect(isIgnored(ig, '.gitignore')).toBe(false);
    expect(isIgnored(ig, 'playwright')).toBe(false);
    expect(isIgnored(ig, 'starling.config.js')).toBe(false);
  });
});

import { AppSidebar } from '@/common/components/app-sidebar';
import {
  IInstantiationService,
  InstantiationContext,
  InstantiationService,
  ServiceRegistry,
  useService,
} from '@byted-image/lv-bedrock/di';
import { JobScheduler } from '@byted-image/lv-bedrock/launch';
import { useEffect, useMemo, useState } from 'react';
import { MoneyStage } from './stage/money-stage';
import { IMoneyService } from './services/money/money-service.interface';
import { MoneyService } from './services/money/money-service';

enum PageLifecycle {
  Open = 'open',
  Ready = 'ready',
  Idle = 'idle',
}

const makePageJobScheduler = (instantiationService: IInstantiationService) => {
  const jobScheduler = instantiationService.createInstance(JobScheduler<PageLifecycle>, PageLifecycle.Open);
  return jobScheduler;
};

const drivePageLifecycle = async (
  jobScheduler: JobScheduler<PageLifecycle>,
  onLifecycleChange: (lifecycle: PageLifecycle) => void,
) => {
  const openShouldWait = jobScheduler.prepare(PageLifecycle.Open);

  if (openShouldWait) {
    await jobScheduler.wait(PageLifecycle.Open);
  } else {
    jobScheduler.advanceToPhase(PageLifecycle.Open);
  }
  onLifecycleChange(PageLifecycle.Open);

  const readyShouldWait = jobScheduler.prepare(PageLifecycle.Ready);

  if (readyShouldWait) {
    await jobScheduler.wait(PageLifecycle.Ready);
  } else {
    jobScheduler.advanceToPhase(PageLifecycle.Ready);
  }
  onLifecycleChange(PageLifecycle.Ready);

  const idleShouldWait = jobScheduler.prepare(PageLifecycle.Idle);

  if (idleShouldWait) {
    await jobScheduler.wait(PageLifecycle.Idle);
  } else {
    jobScheduler.advanceToPhase(PageLifecycle.Idle);
  }
  onLifecycleChange(PageLifecycle.Idle);
};

const useLaunch = () => {
  const pageContainerService = useService(IInstantiationService);
  const instantiationService = useMemo(() => {
    const serviceRegistry = new ServiceRegistry();
    // 注册需要的服务
    serviceRegistry.register(IMoneyService, MoneyService);

    return new InstantiationService(serviceRegistry.makeCollection(), pageContainerService as InstantiationService);
  }, [pageContainerService]);

  const jobScheduler = useMemo(() => {
    return makePageJobScheduler(instantiationService);
  }, [instantiationService]);

  return {
    instantiationService,
    jobScheduler,
  };
};

export const MoneyPage = () => {
  const { instantiationService, jobScheduler } = useLaunch();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    drivePageLifecycle(jobScheduler, (lifecycle) => {
      switch (lifecycle) {
        case PageLifecycle.Open: {
          setLoading(true);
          break;
        }

        case PageLifecycle.Ready:
        case PageLifecycle.Idle: {
          setLoading(false);
          break;
        }
      }
    });
  }, [jobScheduler]);

  if (loading) {
    return null;
  }

  return (
    <InstantiationContext instantiationService={instantiationService}>
      <div className="flex h-screen w-screen">
        <AppSidebar />
        <div className="flex-1">
          <MoneyStage />
        </div>
      </div>
    </InstantiationContext>
  );
};

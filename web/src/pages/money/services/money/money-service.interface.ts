import { createDecorator } from '@byted-image/lv-bedrock/di';
import { ILvErrorOr } from '@byted-image/lv-bedrock/error';
import { TokenUsage } from '@/bam/namespaces/expense';

export interface IMoneyService {
  /**
   * 获取指定时间范围内的金额使用情况
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  getTokenUsage(startTime: Date, endTime: Date): Promise<ILvErrorOr<TokenUsage[]>>;
}

export const IMoneyService = createDecorator<IMoneyService>('money-service');

import { makeErrorBy, makeOkWith } from '@byted-image/lv-bedrock/error';
import { IMoneyService } from './money-service.interface';
import { TokenUsage } from '@/bam/namespaces/expense';
import type { NetworkClientInstance } from '@byted-image/lv-bedrock/network';
import { INetworkClientFactoryService } from '@/common/services/network-client/network-client-factory.interface';
import { LRUCache } from '@byted-image/lv-bedrock/cache';
import { API_PREFIX } from '@/const';

export class MoneyService implements IMoneyService {
  public _serviceBrand: undefined;

  private readonly _networkClient: NetworkClientInstance;
  private readonly _cache: LRUCache<TokenUsage[]> = new LRUCache<TokenUsage[]>(20);

  constructor(
    @INetworkClientFactoryService
    private readonly _networkClientFactoryService: INetworkClientFactoryService,
  ) {
    this._networkClient = this._networkClientFactoryService.createNetworkClient();
  }

  public async getTokenUsage(startTime: Date, endTime: Date) {
    const params = new URLSearchParams({
      start_time: startTime.getTime().toString(),
      end_time: endTime.getTime().toString(),
    });
    const cacheKey = params.toString();
    const cachedResult = this._cache.get(cacheKey);
    if (cachedResult) {
      return makeOkWith(cachedResult);
    }

    const baseUrl = `${API_PREFIX}/expense/token-usage`;
    const url = `${baseUrl}?${params.toString()}`;

    try {
      const response = await this._networkClient.get(url);
      const result = (response.data.data.token_usages ?? []) as TokenUsage[];
      this._cache.put(params.toString(), result);
      return makeOkWith(result);
    } catch (error) {
      return makeErrorBy(-1, '获取token使用记录失败', error as Error);
    }
  }
}

import { useService } from '@byted-image/lv-bedrock/di';
import { useCallback, useState } from 'react';
import { IMoneyService } from '../services/money/money-service.interface';
import { TokenUsage } from '@/bam/namespaces/expense';
import { MoneySearch } from './components/money-search';

export const MoneyStage = () => {
  const moneyService = useService(IMoneyService);
  const [tokenUsages, setTokenUsages] = useState<TokenUsage[]>([]);
  const [loading, setLoading] = useState(false);
  const [startDate, setStartDate] = useState<Date>(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000));
  const [endDate, setEndDate] = useState<Date>(new Date());

  const handleSearch = useCallback(() => {
    setLoading(true);
    // 设置开始时间为当天的 00:00:00
    const start = new Date(startDate);
    start.setHours(0, 0, 0, 0);

    // 设置结束时间为当天的 23:59:59.999
    const end = new Date(endDate);
    end.setHours(23, 59, 59, 999);

    moneyService.getTokenUsage(start, end).then((result) => {
      if (result.ok) {
        setTokenUsages(result.value);
      }
      setLoading(false);
    });
  }, [startDate, endDate, moneyService]);

  const handleSetLastWeek = useCallback(() => {
    const end = new Date();
    const start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000);
    setStartDate(start);
    setEndDate(end);
  }, []);

  if (loading) {
    return <div className="flex items-center justify-center h-full">Loading...</div>;
  }

  return (
    <div className="flex flex-col h-full bg-muted/40">
      <MoneySearch
        startDate={startDate}
        endDate={endDate}
        onStartDateChange={setStartDate}
        onEndDateChange={setEndDate}
        onSearch={handleSearch}
        onSetLastWeek={handleSetLastWeek}
      />

      <div className="flex-1 min-h-0 p-6">
        {tokenUsages.length > 0 ? (
          <div className="h-full overflow-hidden flex flex-col">
            <div className="flex-1 min-h-0 overflow-auto">
              <table className="min-w-full bg-white border border-gray-200">
                <thead className="sticky top-0 bg-gray-50 shadow-sm">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Model Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Scene
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Prompt Tokens
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Completion Tokens
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total Tokens
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Project
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {tokenUsages.map((usage, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{usage.model_type}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{usage.scene}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{usage.prompt_tokens}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{usage.completion_tokens}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{usage.total_tokens}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {usage.extra?.project || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="text-center text-gray-500">请选择时间范围并点击查询按钮</div>
        )}
      </div>
    </div>
  );
};

import { Button } from '@/common/components/ui/button';
import { DatePicker } from '@/common/components/ui/date-picker';

interface MoneySearchProps {
  startDate: Date;
  endDate: Date;
  onStartDateChange: (date: Date) => void;
  onEndDateChange: (date: Date) => void;
  onSearch: () => void;
  onSetLastWeek: () => void;
}

export const MoneySearch = ({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  onSearch,
  onSetLastWeek,
}: MoneySearchProps) => {
  return (
    <div className="p-4 bg-background border-b">
      <div className="flex items-center space-x-6">
        <div className="flex items-center space-x-2 min-w-[240px]">
          <div className="flex-1 flex items-center hover:bg-muted/40 rounded-md p-2 cursor-pointer">
            <span className="text-muted-foreground text-sm whitespace-nowrap">开始日期：</span>
            <div className="flex-1">
              <DatePicker
                date={startDate}
                onSelect={(date) => {
                  if (date) {
                    onStartDateChange(date);
                  }
                }}
                placeholder="选择开始日期"
                className="w-full"
              />
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2 min-w-[240px]">
          <div className="flex-1 flex items-center hover:bg-muted/40 rounded-md p-2 cursor-pointer">
            <span className="text-muted-foreground text-sm whitespace-nowrap">结束日期：</span>
            <div className="flex-1">
              <DatePicker
                date={endDate}
                onSelect={(date) => {
                  if (date) {
                    onEndDateChange(date);
                  }
                }}
                placeholder="选择结束日期"
                className="w-full"
              />
            </div>
          </div>
        </div>

        <Button variant="secondary" size="sm" onClick={onSetLastWeek}>
          最近7天
        </Button>
        <Button
          variant="secondary"
          size="sm"
          onClick={() => {
            const from = new Date();
            from.setDate(from.getDate() - 1);
            from.setHours(0, 0, 0, 0);

            const to = new Date();
            to.setDate(to.getDate() - 1);
            to.setHours(23, 59, 59, 999);

            onStartDateChange(from);
            onEndDateChange(to);
          }}
        >
          昨天
        </Button>
        <Button
          variant="secondary"
          size="sm"
          onClick={() => {
            const from = new Date();
            from.setDate(from.getDate());
            from.setHours(0, 0, 0, 0);

            const to = new Date();
            to.setDate(to.getDate());
            to.setHours(23, 59, 59, 999);

            onStartDateChange(from);
            onEndDateChange(to);
          }}
        >
          今天
        </Button>
        <Button variant="default" size="sm" className="bg-blue-500 hover:bg-blue-600 text-white" onClick={onSearch}>
          查询
        </Button>
      </div>
    </div>
  );
};

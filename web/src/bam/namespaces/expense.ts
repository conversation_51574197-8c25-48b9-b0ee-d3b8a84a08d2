// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from "./base";

export type Int64 = string | number;

export enum Scene {
  Unknown = 0,
  /** 默认场景，codin内会话推理 */
  Default = 1,
  /** 生成代码索引 */
  CodeIndex = 2,
  /** 生成代码总结 */
  CodeSummary = 3,
  /** 生成xml */
  XmlGenerator = 4,
  /** 图片转文字 */
  ImgToNl = 5,
  /** 对代码总结进行压缩 */
  CompactCodeSummary = 6,
  /** review规划 */
  PlanningReview = 7,
}

export interface GetTokenUsageResponse {
  token_usages: Array<TokenUsage>;
  base_resp?: base.BaseResp;
}

export interface TokenUsage {
  model_type: string;
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
  scene: Scene;
  uid?: string;
  conv_id?: string;
  extra?: Record<string, string>;
}
/* eslint-enable */

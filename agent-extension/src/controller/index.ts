import { type IInstantiationService } from '@byted-image/lv-bedrock/di';
import { Disposable } from '@byted-image/lv-bedrock/dispose';

import { ICodingChatService } from '@/services/coding-chat/coding-chat-service.interface';
import { ID2cChatService } from '@/services/d2c-chat/chat-service.interface';
import { IPrdChatService, SendMessageParams } from '@/services/prd-chat/chat-service.interface';
import { IConversationHistoryService } from '@/services/conversation/history/conversation-history.interface';
import { IGitService } from '@/services/git/git-service.interface';
import type { DiffFileInfo } from '@/types/conversation';

import { Int64, type coding } from '@/bam';
import type { UserInput } from '@/bam/namespaces/userinput';
import { IAccountService } from '@/services/account/account-service.interface';
import type { ToolProgressEvent } from '@/services/conversation/base-conversation-context/messages-manager';
import { IDiffViewService } from '@/services/diff-view/diff-view-service.interface';
import { IMerkleService } from '@/services/merkle/merkle-service.interface';
import { INotificationService } from '@/services/notification/notification-service.interface';
import * as vscode from 'vscode';
import { IRpcService } from '@/services/rpc/rpc-service.interface';
import { IUnderstandingChatService } from '@/services/understanding-chat/understanding-service.interface';
import { ClientMessage, Role } from '@/utils/conversation/client-message/abstract-message';
import { IPrivateChatService } from '@/services/private/private-chat-service.interface';
import { commands, type Webview } from 'vscode';
import { IFileLoggerService } from '@/services/file-logger/file-logger-service.interface';
import { IRpcV2Service } from '@/services/rpc-v2/rpc-service.interface';
import { IStorageService } from '@/services/storage/storage-service.interface';
import { LAST_SELECTED_CONVERSATION_MODE_KEY } from '@/constants/storage';
import { SystemMessage, type ContentMessageOptions } from '@/utils/conversation/client-message/system-message';
import type { WEBVIEW_VIEW_TYPE } from '@/const';
import { registerRpcCommands } from '@/services/commands/register-rpc-commands';
import type { RpcV2Service } from '@/services/rpc-v2/rpc-service';

interface IMessageChangeInfo {
  messages: ClientMessage[];
  type: 'add' | 'update';
  cid: string;
  parentCid?: string;
  parentMessageVersion?: Int64;
}

export class Controller extends Disposable {
  private _instantiationService!: IInstantiationService;
  private _chatService!: IPrdChatService;
  private _codingChatService!: ICodingChatService;
  private _d2cChatService!: ID2cChatService;
  private _privateChatService!: IPrivateChatService;
  private _accountService!: IAccountService;
  private _diffViewService!: IDiffViewService;
  private _merkleService!: IMerkleService;
  private _notificationService!: INotificationService;
  private _rpcService!: IRpcService;
  private _understandingChatService!: IUnderstandingChatService;
  private _loggerService!: IFileLoggerService;
  private _storageService!: IStorageService;
  private _historyService!: IConversationHistoryService;
  private _rpcV2Service!: IRpcV2Service;

  /**
   * 使用共享的DI容器初始化Controller
   * 这是推荐的初始化方式，确保服务实例的一致性
   */
  public initDI(instantiationService: IInstantiationService) {
    if (this._instantiationService) {
      return;
    }

    this._instantiationService = instantiationService;
    this._initServices();
  }

  private _initServices() {
    this._chatService = this._instantiationService.invokeFunction((accessor) => accessor.get(IPrdChatService));
    this._codingChatService = this._instantiationService.invokeFunction((accessor) => accessor.get(ICodingChatService));
    this._d2cChatService = this._instantiationService.invokeFunction((accessor) => accessor.get(ID2cChatService));
    this._understandingChatService = this._instantiationService.invokeFunction((accessor) =>
      accessor.get(IUnderstandingChatService),
    );
    this._privateChatService = this._instantiationService.invokeFunction((accessor) =>
      accessor.get(IPrivateChatService),
    );
    this._accountService = this._instantiationService.invokeFunction((accessor) => accessor.get(IAccountService));
    this._diffViewService = this._instantiationService.invokeFunction((accessor) => accessor.get(IDiffViewService));
    this._merkleService = this._instantiationService.invokeFunction((accessor) => accessor.get(IMerkleService));
    this._notificationService = this._instantiationService.invokeFunction((accessor) =>
      accessor.get(INotificationService),
    );
    this._rpcService = this._instantiationService.invokeFunction((accessor) => accessor.get(IRpcService));
    this._loggerService = this._instantiationService.invokeFunction((accessor) => accessor.get(IFileLoggerService));
    this._storageService = this._instantiationService.invokeFunction((accessor) => accessor.get(IStorageService));
    this._historyService = this._instantiationService.invokeFunction((accessor) =>
      accessor.get(IConversationHistoryService),
    );
    this._rpcV2Service = this._instantiationService.invokeFunction((accessor) => accessor.get(IRpcV2Service));

    // 手动初始化 GitService
    this._instantiationService.invokeFunction((accessor) => {
      const gitService = accessor.get(IGitService);
      gitService.initialize();
    });

    // 注册所有 RPC 命令
    registerRpcCommands(this, this._instantiationService);
    (this._rpcV2Service as RpcV2Service).setController(this);
    this._rpcService.registerFallbackHandler(this._rpcV2Service as any);

    // 设置事件监听器
    this._register(
      this._chatService.onPresentAssistantMessage(async () => {
        await this._ensureRpcServiceReady();
        this._rpcService.notify('state', {
          page: 'techDocs',
          payload: JSON.stringify({
            messages: this._chatService.getMessages().map((message) => message.toJSON()),
          }),
        });
      }),
    );

    this._register(
      this._codingChatService.onPresentAssistantMessage(async () => {
        await this._ensureRpcServiceReady();
        this._rpcService.notify('state', {
          page: 'coding',
          payload: JSON.stringify({
            messages: this._codingChatService.getMessages().map((message) => message.toJSON()),
          }),
        });
      }),
    );

    this._register(
      this._accountService.onDidLogin(async () => {
        await this._ensureRpcServiceReady();
        this._rpcService.notify('updateUserInfo', this._accountService.getUserInfo() || null);
      }),
    );

    this._register(
      this._d2cChatService.onPresentAssistantMessage(async () => {
        await this._ensureRpcServiceReady();
        this._rpcService.notify('state', {
          page: 'd2c',
          payload: JSON.stringify({
            messages: this._d2cChatService.getMessages().map((message) => message.toJSON()),
          }),
        });
      }),
    );

    this._register(
      this._understandingChatService.onPresentAssistantMessage(async () => {
        await this._ensureRpcServiceReady();
        this._rpcService.notify('state', {
          page: 'understanding',
          payload: JSON.stringify({
            messages: this._understandingChatService.getMessages().map((message) => message.toJSON()),
          }),
        });
      }),
    );

    this._register(
      this._privateChatService.onPresentAssistantMessage(async () => {
        await this._ensureRpcServiceReady();
        this._rpcService.notify('state', {
          page: 'private',
          payload: JSON.stringify({
            messages: this._privateChatService.getMessages().map((message) => message.toJSON()),
          }),
        });
      }),
    );

    this._register(
      this._merkleService.onUpdateIndex(() => {
        this._notificationService.onUpdateIndexInfo.fire({
          buildRecords: this._merkleService.buildRecords,
          workspaceFolders: vscode.workspace.workspaceFolders || [],
        });
      }),
    );

    if (this._accountService.hasLogin) {
      this._ensureRpcServiceReady().then(() => {
        this._rpcService.notify('updateUserInfo', this._accountService.getUserInfo() || null);
      });
    }
    this._register(
      this._accountService.onDidLogin(async () => {
        await this._ensureRpcServiceReady();
        this._rpcService.notify('didLogin', true);
        this._rpcService.notify('updateUserInfo', this._accountService.getUserInfo() || null);
        commands.executeCommand('setContext', 'codin.login', true);
      }),
    );
    this._register(
      this._accountService.onDidLogout(async () => {
        await this._ensureRpcServiceReady();
        this._rpcService.notify('didLogout', true);
        commands.executeCommand('setContext', 'codin.login', false);
      }),
    );
    this._register(
      this._accountService.onDidLogout(async () => {
        await this._ensureRpcServiceReady();
        this._rpcService.notify('updateUserInfo', null);
      }),
    );

    this._register(
      this._diffViewService.onChangeIsEditing(async () => {
        await this._ensureRpcServiceReady();
        this._rpcService.notify('updateDiffViewIsEditing', this._diffViewService.isEditing);
      }),
    );

    this._register(
      this._codingChatService.onConversationMessageChange((messageChangeInfo: IMessageChangeInfo) => {
        this._rpcService.notify('diffInfo', {
          ...messageChangeInfo,
        });
      }),
    );

    this._register(
      this._chatService.onConversationMessageChange((messageChangeInfo: IMessageChangeInfo) => {
        this._rpcService.notify('diffInfo', {
          ...messageChangeInfo,
        });
      }),
    );

    this._register(
      this._d2cChatService.onConversationMessageChange((messageChangeInfo: IMessageChangeInfo) => {
        this._rpcService.notify('diffInfo', {
          ...messageChangeInfo,
        });
      }),
    );

    this._register(
      this._understandingChatService.onConversationMessageChange((messageChangeInfo: IMessageChangeInfo) => {
        this._rpcService.notify('diffInfo', {
          ...messageChangeInfo,
        });
      }),
    );

    // 监听工具进度事件并转发给webview
    this._register(
      this._codingChatService.onToolProgress((progressEvent: ToolProgressEvent) => {
        this._rpcService.notify('toolProgress', progressEvent);
      }),
    );
  }

  public resetConversationServices() {
    this._chatService.getMessages(); // 触发服务初始化
    this._codingChatService.getMessages(); // 触发服务初始化
    this._d2cChatService.getMessages(); // 触发服务初始化

    this._chatService.resetMessageReceiverAndSocket();
    this._codingChatService.resetMessageReceiverAndSocket();
    this._d2cChatService.resetMessageReceiverAndSocket();
  }

  /**
   * 获取当前会话服务的实时状态
   * @param conversationType 会话类型
   */
  public async getCurrentConversationState(conversationType?: string, conversationId?: string) {
    const service = this._instantiationService.invokeFunction((accessor) => {
      // 根据会话类型选择对应的服务
      switch (conversationType) {
        case 'techDocs':
          return accessor.get(IPrdChatService);
        case 'd2c':
          return accessor.get(ID2cChatService);
        default:
          return accessor.get(ICodingChatService);
      }
    });

    await service.switchCurrentConversation(conversationId);
    // 获取当前会话的消息和状态
    const messages = service.getMessages();
    const result = await service.getCurrentContextState();

    const currentConversationId = conversationId ?? service.currentCid;
    const currentConversationType = conversationType;

    // 从 globalState 获取会话的 diffInfo
    let diffInfo: DiffFileInfo[] = [];
    if (currentConversationId) {
      try {
        const [error, conversation] = (await this._historyService.getConversation(currentConversationId)).pair();
        if (!error && conversation?.diffInfo) {
          diffInfo = conversation.diffInfo;
        }
      } catch (err) {
        this._loggerService.warn(`[Controller] Failed to get conversation diffInfo: ${err}`);
        vscode.window.showWarningMessage(`[Controller] Failed to get conversation diffInfo: ${err}`);
      }
    }

    // 发送当前会话状态给webview
    this._rpcService.notify('currentConversationState', {
      conversationId: currentConversationId,
      conversationType: currentConversationType || conversationType,
      // biome-ignore lint/suspicious/noExplicitAny: TODO: chezongshao
      messages: messages.map((message: any) =>
        message.toJSON
          ? message.toJSON()
          : {
              role: message.role || (message.sender === 'User' ? 'user' : 'assistant'),
              content: message.content || message.text || '',
              text: message.content || message.text || '',
              sender: message.sender || (message.role === 'user' ? 'User' : 'Assistant'),
            },
      ),
      contextState: result,
      diffInfo, // 包含从 globalState 读取的 diffInfo
    });
    // 在会话初始化完毕后，启动 Git 状态监听
    if (currentConversationId) {
      this._diffViewService.watchRepoState(currentConversationId, this._historyService, this._rpcService);
    }
  }

  /**
   * 从会话的 diffInfo 中移除指定文件
   * @param filePath 要移除的文件路径
   * @param conversationId 会话ID，如果不指定则使用当前会话
   */
  public async removeFileFromDiffInfo(filePath: string, conversationId?: string): Promise<void> {
    try {
      // 获取当前会话ID
      let currentConversationId = conversationId;
      if (!currentConversationId) {
        // 如果没有指定会话ID，尝试从当前的coding chat服务获取
        currentConversationId = this._codingChatService.currentCid;
      }

      if (!currentConversationId) {
        console.warn('[Controller] No conversation ID available for removing file from diffInfo');
        return;
      }

      // 获取当前会话的 diffInfo
      const [error, conversation] = (await this._historyService.getConversation(currentConversationId)).pair();
      if (error || !conversation || !conversation.diffInfo) {
        console.warn('[Controller] Failed to get conversation or no diffInfo available:', currentConversationId);
        return;
      }

      // 过滤掉要移除的文件
      const updatedDiffInfo = conversation.diffInfo.filter((file) => file.fullPath !== filePath);

      // 更新会话历史
      await this._historyService.updateDiffInfo(currentConversationId, updatedDiffInfo);

      // 通知前端更新
      this._rpcService.notify('updateDiffInfo', updatedDiffInfo);

      this._loggerService.info(`[Controller] Removed file from diffInfo: ${filePath}`);
    } catch (error) {
      this._loggerService.error(`[Controller] Failed to remove file from diffInfo: ${error}`);
      vscode.window.showErrorMessage(`[Controller] Failed to remove file from diffInfo: ${error}`);
    }
  }

  /**
   * 撤销文件操作
   * @param filePath 文件路径
   * @param actions 操作记录
   * @param conversationId 会话ID，如果不指定则使用当前会话
   */
  public async undoFileOperation(filePath: string, actions: any[], conversationId?: string): Promise<void> {
    try {
      // 获取当前会话ID
      let currentConversationId = conversationId;
      if (!currentConversationId) {
        currentConversationId = this._codingChatService.currentCid;
      }

      if (!currentConversationId) {
        console.warn('[Controller] No conversation ID available for undoing file operation');
        return;
      }

      // 使用 diffViewService 执行撤销操作
      await this._diffViewService.undoFileOperation(filePath, actions);

      // 从会话中移除该文件的 diffInfo
      await this.removeFileFromDiffInfo(filePath, currentConversationId);

      this._loggerService.info(`[Controller] Undone file operation: ${filePath}`);
    } catch (error) {
      this._loggerService.error(`[Controller] Failed to undo file operation: ${error}`);
      vscode.window.showErrorMessage(`[Controller] Failed to undo file operation: ${error}`);
    }
  }

  /**
   * 清空当前会话的 diffInfo
   * @param conversationId 会话ID，如果不指定则使用当前会话
   */
  public async clearDiffInfo(conversationId?: string): Promise<void> {
    try {
      // 获取当前会话ID
      let currentConversationId = conversationId;
      if (!currentConversationId) {
        currentConversationId = this._codingChatService.currentCid;
      }

      if (!currentConversationId) {
        console.warn('[Controller] No conversation ID available for clearing diffInfo');
        return;
      }

      // 清空会话的 diffInfo
      await this._historyService.updateDiffInfo(currentConversationId, []);

      // 通知前端更新
      this._rpcService.notify('updateDiffInfo', []);

      this._loggerService.info(`[Controller] Cleared diffInfo for conversation: ${currentConversationId}`);
    } catch (error) {
      this._loggerService.error(`[Controller] Failed to clear diffInfo: ${error}`);
      vscode.window.showErrorMessage(`[Controller] Failed to clear diffInfo: ${error}`);
    }
  }

  /**
   * 设置 webview 实例，用于 RPC 通信
   */
  public setWebview({ webview, viewType }: { webview: Webview; viewType: WEBVIEW_VIEW_TYPE }) {
    this._rpcService.setWebview(webview, viewType);
  }

  private async _ensureRpcServiceReady() {
    await this._rpcService.initialize();
  }

  public sendTechDocsMessage(userInput: UserInput, params?: SendMessageParams) {
    this._chatService.sendMessage(
      {
        role: 0,
        userContent: userInput,
      },
      params,
    );
  }

  public sendCodingMessage(userInput: UserInput, cid?: string) {
    this._codingChatService.sendMessage({
      cid,
      role: 0,
      userContent: userInput,
    });
  }

  public appendMessage({ cid, params, role }: { cid: string; params: Partial<ContentMessageOptions>; role: Role }) {
    const conversationContext = this._codingChatService.getConversationContext(cid);
    params.version = [-1, -1];
    if (role === Role.System) {
      conversationContext?.messagesManager.appendMessages([new SystemMessage(params as ContentMessageOptions)]);
    }
  }

  public cancelConversation(cid: string) {
    return this._codingChatService.cancelConversation(cid);
  }

  public async cancelConversationWithMessage(parentCid: string, childCid?: string) {
    await this._codingChatService.cancelConversationWithMessage(parentCid, childCid);
  }

  public sendUnderstandingMessage(userInput: UserInput, cid?: string) {
    this._understandingChatService.sendMessage({
      cid,
      role: 0,
      userContent: userInput,
    });
  }

  public sendD2cMessage(userInput: UserInput) {
    this._d2cChatService.sendMessage({
      role: 0,
      userContent: userInput,
    });
  }

  public sendPrivateMessage(userInput: UserInput) {
    this._privateChatService.sendMessage({
      role: 0,
      userContent: userInput,
    });
  }

  public async setConversationMode(cid: string, mode: coding.AgentWorkMode) {
    await this._codingChatService.setConversationMode(cid, mode);
    this._loggerService.info(`[Controller] 设置会话模式成功: ${cid}, ${mode}`);
  }

  public async getConversationMode(cid: string): Promise<coding.AgentWorkMode> {
    const mode = await this._codingChatService.getConversationMode(cid);
    this._loggerService.info(`[Controller] 获取会话模式成功: ${cid}, ${mode}`);
    return mode;
  }

  public async setLastSelectedConversationMode(mode: coding.AgentWorkMode) {
    return await this._storageService.set(LAST_SELECTED_CONVERSATION_MODE_KEY, mode);
  }
}

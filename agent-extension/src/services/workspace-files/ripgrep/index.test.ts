import { describe, it, expect, vi, beforeEach } from 'vitest';
import { getBinPath } from './index';
import * as path from 'node:path';

vi.mock('@utils/fs', () => ({
  fileExistsAtPath: vi.fn(),
}));

import { fileExistsAtPath } from '@utils/fs';

const mockFileExistsAtPath = vi.mocked(fileExistsAtPath);

describe('getBinPath', () => {
  const testAppRoot = '/test/vscode/root';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should find ripgrep in @vscode/ripgrep/bin/', async () => {
    const expectedPath = path.join(
      testAppRoot,
      'node_modules/@vscode/ripgrep/bin/',
      process.platform.startsWith('win') ? 'rg.exe' : 'rg',
    );
    mockFileExistsAtPath.mockResolvedValueOnce(true).mockResolvedValue(false);
    const result = await getBinPath(testAppRoot);
    expect(result).toBe(expectedPath);
    expect(mockFileExistsAtPath).toHaveBeenCalledWith(expectedPath);
  });

  it('should find ripgrep in vscode-ripgrep/bin if @vscode/ripgrep not found', async () => {
    const expectedPath = path.join(
      testAppRoot,
      'node_modules/vscode-ripgrep/bin',
      process.platform.startsWith('win') ? 'rg.exe' : 'rg',
    );
    mockFileExistsAtPath.mockResolvedValueOnce(false).mockResolvedValueOnce(true).mockResolvedValue(false);
    const result = await getBinPath(testAppRoot);
    expect(result).toBe(expectedPath);
    expect(mockFileExistsAtPath).toHaveBeenCalledTimes(2);
  });

  it('should find ripgrep in asar.unpacked/vscode-ripgrep/bin/', async () => {
    const expectedPath = path.join(
      testAppRoot,
      'node_modules.asar.unpacked/vscode-ripgrep/bin/',
      process.platform.startsWith('win') ? 'rg.exe' : 'rg',
    );
    mockFileExistsAtPath
      .mockResolvedValueOnce(false)
      .mockResolvedValueOnce(false)
      .mockResolvedValueOnce(true)
      .mockResolvedValue(false);
    const result = await getBinPath(testAppRoot);
    expect(result).toBe(expectedPath);
    expect(mockFileExistsAtPath).toHaveBeenCalledTimes(3);
  });

  it('should find ripgrep in asar.unpacked/@vscode/ripgrep/bin/', async () => {
    const expectedPath = path.join(
      testAppRoot,
      'node_modules.asar.unpacked/@vscode/ripgrep/bin/',
      process.platform.startsWith('win') ? 'rg.exe' : 'rg',
    );
    mockFileExistsAtPath
      .mockResolvedValueOnce(false)
      .mockResolvedValueOnce(false)
      .mockResolvedValueOnce(false)
      .mockResolvedValueOnce(true);
    const result = await getBinPath(testAppRoot);
    expect(result).toBe(expectedPath);
    expect(mockFileExistsAtPath).toHaveBeenCalledTimes(4);
  });

  it('should return undefined if ripgrep is not found in any location', async () => {
    mockFileExistsAtPath.mockResolvedValue(false);
    const result = await getBinPath(testAppRoot);
    expect(result).toBeUndefined();
    expect(mockFileExistsAtPath).toHaveBeenCalledTimes(4);
  });
});

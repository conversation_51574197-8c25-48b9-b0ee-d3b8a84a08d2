import * as path from 'node:path';
import { fileExistsAtPath } from '@utils/fs';

/*
This file provides functionality to perform regex searches on files using ripgrep.
Inspired by: https://github.com/DiscreteTom/vscode-ripgrep-utils

Key components:
1. getBinPath: Locates the ripgrep binary within the VSCode installation.
2. execRipgrep: Executes the ripgrep command and returns the output.
3. regexSearchFiles: The main function that performs regex searches on files.
   - Parameters:
     * cwd: The current working directory (for relative path calculation)
     * directoryPath: The directory to search in
     * regex: The regular expression to search for (Rust regex syntax)
     * filePattern: Optional glob pattern to filter files (default: '*')
   - Returns: A formatted string containing search results with context

The search results include:
- Relative file paths
- 2 lines of context before and after each match
- Matches formatted with pipe characters for easy reading

Usage example:
const results = await regexSearchFiles('/path/to/cwd', '/path/to/search', 'TODO:', '*.ts');

rel/path/to/app.ts
│----
│function processData(data: any) {
│  // Some processing logic here
│  // TODO: Implement error handling
│  return processedData;
│}
│----

rel/path/to/helper.ts
│----
│  let result = 0;
│  for (let i = 0; i < input; i++) {
│    // TODO: Optimize this function for performance
│    result += Math.pow(i, 2);
│  }
│----
*/

const isWindows = /^win/.test(process.platform);
const binName = isWindows ? 'rg.exe' : 'rg';

export async function getBinPath(vscodeAppRoot: string): Promise<string | undefined> {
  const checkPath = async (pkgFolder: string) => {
    const fullPath = path.join(vscodeAppRoot, pkgFolder, binName);
    return (await fileExistsAtPath(fullPath)) ? fullPath : undefined;
  };

  return (
    (await checkPath('node_modules/@vscode/ripgrep/bin/')) ||
    (await checkPath('node_modules/vscode-ripgrep/bin')) ||
    (await checkPath('node_modules.asar.unpacked/vscode-ripgrep/bin/')) ||
    (await checkPath('node_modules.asar.unpacked/@vscode/ripgrep/bin/'))
  );
}

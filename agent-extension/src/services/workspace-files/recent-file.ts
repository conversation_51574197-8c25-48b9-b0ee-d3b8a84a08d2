import * as vscode from 'vscode';
import { IExtensionContextService } from '../commands/extension-context.interface';
import { FileNode } from './types';
import { Emitter } from '@byted-image/lv-bedrock/event';
import { getWorkspacePath } from '@/utils/path';

export class RecentFileManager {
  private readonly MAX_RECENT_FILES = 20;
  private readonly MIN_RECENT_FILES = 10;
  private readonly STORAGE_KEY = 'recentFiles19';
  private readonly TIME_STORAGE_KEY = 'recentFilesAccessTime';
  private readonly WORKSPACE_PATH_KEY = 'recentFilesWorkspacePath';
  private recentFiles: FileNode[] = [];
  private fileAccessTime: Map<string, number> = new Map();
  private _onRecentVisitChange = new Emitter<[FileNode[]]>();
  private gitignorePatterns: string[] = [];

  constructor(@IExtensionContextService private readonly _extensionContextService: IExtensionContextService) {}

  public init(gitignorePatterns: string[]) {
    this.gitignorePatterns = gitignorePatterns;
    this.loadFromStorage();
    this.initializeEventListeners();
  }

  private async loadFromStorage() {
    const currentWorkspacePath = getWorkspacePath();
    const lastWorkspacePath = this._extensionContextService.context.globalState.get<string>(
      this.WORKSPACE_PATH_KEY,
      '',
    );
    if (lastWorkspacePath && lastWorkspacePath !== currentWorkspacePath) {
      // workspace发生变化，清空缓存
      await this.clearRecentFiles();
    }
    // 更新workspace路径
    await this._extensionContextService.context.globalState.update(this.WORKSPACE_PATH_KEY, currentWorkspacePath);
    // Load recent files
    const storedFiles = this._extensionContextService.context.globalState.get<FileNode[]>(this.STORAGE_KEY, []);
    this.recentFiles = storedFiles;
    // Load access times
    const storedTimes = this._extensionContextService.context.globalState.get<Record<string, number>>(
      this.TIME_STORAGE_KEY,
      {},
    );
    this.fileAccessTime = new Map(Object.entries(storedTimes));

    // 检查文件是否真实存在，不存在则移除
    const fs = vscode.workspace.fs;
    const validFiles: FileNode[] = [];
    for (const file of this.recentFiles) {
      try {
        await fs.stat(vscode.Uri.file(file.path));
        validFiles.push(file);
      } catch (e) {
        // 文件不存在，移除其访问时间
        this.fileAccessTime.delete(file.path);
      }
    }
    if (validFiles.length !== this.recentFiles.length) {
      this.recentFiles = validFiles;
      await this.saveToStorage();
    }

    // 如果有效文件数量少于最小数量，从工作区添加文件
    if (validFiles.length < this.MIN_RECENT_FILES) {
      await this.fillRecentFilesFromWorkspace();
    }
  }

  private async fillRecentFilesFromWorkspace() {
    const workspacePath = getWorkspacePath();
    if (!workspacePath) {
      return;
    }

    try {
      const files = await vscode.workspace.findFiles(
        '**/*.{go,java,ts,kotlin}',
        this.gitignorePatterns.length > 0 ? `{${this.gitignorePatterns.join(',')},**/.*}` : '**/node_modules/**,**/.*',
        this.MIN_RECENT_FILES,
      );
      for (const file of files) {
        if (this.recentFiles.length >= this.MIN_RECENT_FILES) {
          break;
        }

        const stat = await vscode.workspace.fs.stat(file);
        const path = file.fsPath;
        const name = path.split('/').pop() || '';
        const relativePath = vscode.workspace.asRelativePath(file);

        // 检查文件是否已经在最近文件列表中
        if (!this.recentFiles.some((f) => f.path === path)) {
          const fileNode: FileNode = {
            name,
            path,
            relativePath,
            type: stat.type === vscode.FileType.Directory ? 'directory' : 'file',
          };

          await this.addRecentFile(fileNode);
          this.fileAccessTime.set(path, Date.now());
        }
      }
      await this.saveToStorage();
    } catch (error) {
      console.error('Error filling recent files from workspace:', error);
    }
  }

  private async saveToStorage() {
    // Save recent files
    await this._extensionContextService.context.globalState.update(this.STORAGE_KEY, this.recentFiles);

    // Save access times
    const timesObject = Object.fromEntries(this.fileAccessTime);
    await this._extensionContextService.context.globalState.update(this.TIME_STORAGE_KEY, timesObject);
  }

  private initializeEventListeners() {
    // Listen for file/folder access events
    vscode.workspace.onDidOpenTextDocument((document) => {
      this.handleFileAccess(document.uri);
    });
  }

  private async handleFileAccess(uri: vscode.Uri) {
    try {
      await vscode.workspace.fs.stat(uri);
    } catch (error) {
      console.warn(`File does not exist: ${uri.fsPath}`);
      return;
    }

    const now = Date.now();
    const path = uri.fsPath;
    this.fileAccessTime.set(path, now);

    const stat = await vscode.workspace.fs.stat(uri);
    const name = path.split('/').pop() || '';
    const relativePath = vscode.workspace.asRelativePath(uri);

    const fileNode: FileNode = {
      name,
      path,
      relativePath,
      type: stat.type === vscode.FileType.Directory ? 'directory' : 'file',
    };

    await this.addRecentFile(fileNode);

    // Add parent directory as a recent item
    const parentDirPath = path.substring(0, path.lastIndexOf('/'));
    if (parentDirPath) {
      const parentDirUri = vscode.Uri.file(parentDirPath);
      const parentDirStat = await vscode.workspace.fs.stat(parentDirUri);
      const parentDirName = parentDirPath.split('/').pop() || '';
      const parentDirRelativePath = vscode.workspace.asRelativePath(parentDirUri);

      const parentDirNode: FileNode = {
        name: parentDirName,
        path: parentDirPath,
        relativePath: parentDirRelativePath,
        type: parentDirStat.type === vscode.FileType.Directory ? 'directory' : 'file',
      };

      await this.addRecentFile(parentDirNode);
    }

    this._onRecentVisitChange.fire([...this.recentFiles]);
  }

  private async addRecentFile(fileNode: FileNode) {
    // Remove the file if it already exists to update its position
    const index = this.recentFiles.findIndex((file) => file.path === fileNode.path);
    if (index !== -1) {
      this.recentFiles.splice(index, 1);
    }

    // Add the file to the beginning of the array
    this.recentFiles.unshift(fileNode);

    // If we exceed the maximum number of files, remove the oldest one
    if (this.recentFiles.length > this.MAX_RECENT_FILES) {
      const oldestFile = this.recentFiles.pop();
      if (oldestFile) {
        this.fileAccessTime.delete(oldestFile.path);
      }
    }
    // Save to storage after each update
    await this.saveToStorage();
  }

  public getRecentFiles(): FileNode[] {
    return [...this.recentFiles];
  }

  public async clearRecentFiles() {
    this.recentFiles = [];
    this.fileAccessTime.clear();
    await this.saveToStorage();
  }

  public getFileAccessTime(file: string): number | undefined {
    return this.fileAccessTime.get(file);
  }

  get onRecentVisitChange() {
    return this._onRecentVisitChange.event;
  }
}

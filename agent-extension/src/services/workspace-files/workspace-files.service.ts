import * as vscode from 'vscode';
import type { FileNode } from './types';
import type { IWorkspaceFilesService } from './workspace-files.interface';
import { RecentFileManager } from './recent-file';
import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { FileType, Uri, workspace } from 'vscode';
import path from 'node:path';
import micromatch from 'micromatch';

export class WorkspaceFilesService implements IWorkspaceFilesService {
  _serviceBrand: undefined;
  private _recentFilesManager: RecentFileManager;
  private gitignorePatterns: string[] = [];
  private workspaceRoot = '';
  private searchCancelTokenSource: vscode.CancellationTokenSource | null = null;

  constructor(@IInstantiationService private readonly _instantiationService: IInstantiationService) {
    this._recentFilesManager = this._instantiationService.createInstance(RecentFileManager);
  }

  public init() {
    // this._fileTreeManager.init();

    const workspaceFolders = workspace.workspaceFolders;

    if (workspaceFolders) {
      // 设置workspace根目录
      this.workspaceRoot = workspaceFolders[0].uri.fsPath;
      // 为每个工作区加载.gitignore
      Promise.all(workspaceFolders.map((folder) => this.loadGitignore(folder.uri))).then(() => {
        this._recentFilesManager.init(this.gitignorePatterns);
      });
    }
  }

  private toCaseInsensitiveGlob(inputStr: string) {
    // 用于存放最终结果的数组
    const resultParts = [];

    // 遍历输入字符串中的每一个字符
    for (const char of inputStr) {
      // 使用正则表达式检查当前字符是否是英文字母
      if (/^[a-zA-Z]$/.test(char)) {
        // 如果是字母，则创建 [小写大写] 格式的字符串
        const part = `[${char.toLowerCase()}${char.toUpperCase()}]`;
        resultParts.push(part);
      } else {
        // 如果不是字母（如数字、连字符等），则保持原样
        resultParts.push(char);
      }
    }

    // 将数组中的所有部分连接成一个最终的字符串并返回
    return resultParts.join('');
  }

  private async findDirectories(
    keyword: string,
    workspaceFolders: readonly vscode.WorkspaceFolder[],
    ignorePatterns: string,
  ): Promise<FileNode[]> {
    const lowerCaseKeyword = keyword.toLowerCase().slice(0, -1); // 去掉最后的斜杠

    const allMatches: FileNode[] = [];
    // --- 内部递归函数 ---
    // 这个函数会遍历所有子目录
    const findInDirectory = async (directoryUri: vscode.Uri) => {
      const entries = await vscode.workspace.fs.readDirectory(directoryUri);

      for (const [name, type] of entries) {
        const currentUri = vscode.Uri.joinPath(directoryUri, name);
        const fullPath = currentUri.fsPath;

        // 忽略 . 开头的文件夹 以及 gitignored
        if ((type !== FileType.Directory && name.startsWith('.')) || micromatch.isMatch(name, ignorePatterns)) {
          continue;
        }

        // 如果是目录，检查名称是否匹配
        if (name.toLowerCase().includes(lowerCaseKeyword)) {
          allMatches.push({
            name: this.getRelativePath(fullPath),
            type: 'directory',
            path: fullPath,
            relativePath: path.relative(workspaceFolders[0].uri.fsPath, fullPath),
          });
          await findInDirectory(currentUri);
        }
      }
    };
    // --- 内部函数结束 ---

    await Promise.all(workspaceFolders.map((folder) => findInDirectory(folder.uri)));

    return allMatches;
  }

  private async findFiles(
    keyword: string,
    workspaceFolders: readonly vscode.WorkspaceFolder[],
    ignorePatterns: string,
    cancelToken: vscode.CancellationToken,
  ): Promise<FileNode[]> {
    const workspaceRoot = workspaceFolders[0].uri;
    // Use a glob pattern to find files and directories
    // This is a simplified approach. For a more robust solution, we might need to walk the directory tree.
    const lowerCaseKeyword = keyword.toLowerCase();
    const glob = `**/*${this.toCaseInsensitiveGlob(keyword)}*`;
    // Find all files and directories, then filter
    const allUris = await vscode.workspace.findFiles(glob, ignorePatterns, 50, cancelToken);
    const matchedNodes: FileNode[] = [];

    for (const uri of allUris) {
      const name = this.getRelativePath(uri.fsPath);
      if (name.toLowerCase().includes(lowerCaseKeyword)) {
        matchedNodes.push({
          name: uri.path.split('/').pop() || '',
          path: uri.fsPath,
          relativePath: path.relative(workspaceRoot.fsPath, uri.fsPath),
          type: 'file',
        });
      }
    }

    return matchedNodes;
  }

  // 目前没有启动流程，只能做异步处理
  public async searchFileOrDir(keyword: string): Promise<FileNode[]> {
    if (!keyword) {
      return [];
    }

    const workspaceFolders = vscode.workspace.workspaceFolders;
    const ignorePatterns =
      this.gitignorePatterns.length > 0 ? `{${this.gitignorePatterns.join(',')},**/.*}` : '**/node_modules/**,**/.*';
    if (!workspaceFolders) {
      console.log('No workspace folder open.');
      return [];
    }
    // Always create a new token source for each search
    // Cancel and dispose of the previous one if it exists
    if (this.searchCancelTokenSource) {
      this.searchCancelTokenSource.cancel();
      this.searchCancelTokenSource.dispose();
    }
    this.searchCancelTokenSource = new vscode.CancellationTokenSource();

    try {
      if (keyword.endsWith('/')) {
        return await this.findDirectories(keyword, workspaceFolders, ignorePatterns);
      }

      const fileNodes = await this.findFiles(
        keyword,
        workspaceFolders,
        ignorePatterns,
        this.searchCancelTokenSource.token,
      );
      return fileNodes;
    } finally {
      if (this.searchCancelTokenSource) {
        // Ensure the token source is disposed after the search completes or is cancelled
        this.searchCancelTokenSource.dispose();
        this.searchCancelTokenSource = null;
      }
    }
  }

  public get recentVisit() {
    return this._recentFilesManager.getRecentFiles();
  }

  public get onRecentVisitChange() {
    return this._recentFilesManager.onRecentVisitChange;
  }

  public async readFileContent(path: string): Promise<string | null> {
    try {
      const uri = Uri.file(path);
      const content = await workspace.fs.readFile(uri);
      return content.toString();
    } catch (error) {
      console.error('readFileContent error', error);
      return null;
    }
  }

  public async getFolderListContent(path: string): Promise<string | null> {
    try {
      const uri = Uri.file(path);
      const entries = await workspace.fs.readDirectory(uri);
      const result = await Promise.all(
        entries.map(async ([name, type]) => {
          const entryUri = Uri.joinPath(uri, name);
          const relativePath = this.getRelativePath(entryUri.fsPath);

          // Filter out ignored files based on gitignore rules
          if (
            this.gitignorePatterns.length > 0 &&
            this.gitignorePatterns.some((pattern) => relativePath.includes(pattern))
          ) {
            return null;
          }

          let extraInfo = '';
          if (type === FileType.File) {
            try {
              const stat = await workspace.fs.stat(entryUri);
              const fileContent = await workspace.fs.readFile(entryUri);
              const lineCount = fileContent.toString().split('\n').length;
              extraInfo = `${(stat.size / 1024).toFixed(1)}KB, ${lineCount} lines`;
            } catch (error) {
              console.error('Error retrieving file line count', error);
            }
          }
          const typeStr = type === FileType.Directory ? '[dir]' : '[file]';

          if (type === FileType.Directory) {
            return `${typeStr} ${name}/ (? items)`;
          }
          return `${typeStr} ${name} (${extraInfo})`;
        }),
      );
      return result.filter((item) => item !== null).join('\n');
    } catch (error) {
      console.error('getFolderListContent error', error);
      return null;
    }
  }

  /**
   * 读取并解析.gitignore文件
   */
  private async loadGitignore(workspaceUri: Uri): Promise<void> {
    try {
      const gitignoreUri = Uri.joinPath(workspaceUri, '.gitignore');
      const content = await workspace.fs.readFile(gitignoreUri);
      const patterns = content
        .toString()
        .split('\n')
        .map((line) => line.trim())
        .filter((line) => line && !line.startsWith('#'));
      this.gitignorePatterns = patterns;
    } catch (error) {
      console.log('No .gitignore file found or error reading it');
      this.gitignorePatterns = [];
    }
  }

  private getRelativePath(absolutePath: string): string {
    return path.relative(this.workspaceRoot, absolutePath);
  }
}

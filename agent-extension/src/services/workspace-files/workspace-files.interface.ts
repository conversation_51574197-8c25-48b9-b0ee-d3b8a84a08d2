import { createDecorator } from '@byted-image/lv-bedrock/di';
import { type Event } from '@byted-image/lv-bedrock/event';
import type { FileNode } from './types';

export interface IWorkspaceFilesService {
  _serviceBrand: undefined;

  /**
   * 模糊匹配搜索内容
   */
  searchFileOrDir: (keyword: string) => Promise<FileNode[]>;

  /**
   * 最近访问的节点
   */
  recentVisit: FileNode[];

  /**
   * 最近访问的节点变化事件
   */
  onRecentVisitChange: Event<[FileNode[]]>;

  /**
   * 读取文件内容
   */
  readFileContent: (path: string) => Promise<string | null>;

  /**
   * 获取目录ls结果
   */
  getFolderListContent: (path: string) => Promise<string | null>;
}

export const IWorkspaceFilesService = createDecorator<IWorkspaceFilesService>('workspace-files');

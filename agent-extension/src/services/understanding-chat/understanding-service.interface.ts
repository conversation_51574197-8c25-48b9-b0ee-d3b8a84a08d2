import { ModelType } from '@/bam/namespaces/base';
import type { ClientMessage } from '@/utils/conversation/client-message/abstract-message';
import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { ILvErrorOr } from '@byted-image/lv-bedrock/error';
import { Event } from '@byted-image/lv-bedrock/event';
import { AskMessage, type IConversationContextState, type IMessageChangeInfo } from '../base-chat/types';
import type { Int64 } from '@/bam';
import type { UnderstandingConversationContext } from '../conversation/understanding-conversation-context/understanding-conversation';

export const DefaultModelType = ModelType.Seed1_6;

interface FileOptions {
  key: string;
  name: string;
  bucket: string;
  url: string;
}

export interface SendMessageParams {
  model?: ModelType;
  files?: FileOptions[];
  isSubAgent?: boolean;
}

export interface IUnderstandingChatService {
  _serviceBrand: undefined;
  currentCid: string;

  // 发送消息
  sendMessage(message: AskMessage, params?: SendMessageParams): void;

  getMessages(): ClientMessage[];

  resetMessageReceiverAndSocket(): void;

  onPresentAssistantMessage: Event<[]>;

  onSendMessageError: Event<ILvErrorOr<void>[]>;

  getCurrentContextState(): Promise<IConversationContextState>;

  createConversation(options: {
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): Promise<ILvErrorOr<string>>;

  getConversationContext(cid: string): UnderstandingConversationContext | null;

  switchCurrentConversation(cid?: string): Promise<ILvErrorOr<string>>;

  onConversationMessageChange: Event<[IMessageChangeInfo]>;
}

export const IUnderstandingChatService = createDecorator<IUnderstandingChatService>('understanding-chat-service');

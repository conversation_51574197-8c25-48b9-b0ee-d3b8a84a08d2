import { describe, it, expect, vi, beforeEach, type Mock } from 'vitest';
import { FWS } from '@byted/frontier-web-sdk';
import { SocketService } from './socket-service';
import { SocketClientImpl } from './socket-client';
import type { IAccountService } from '../account/account-service.interface';
import type { INetworkClientFactoryService } from '../network-client-factory/network-client-factory-service.interface';
import type { ConnectParams } from './socket-service.interface';

// Mock dependencies
vi.mock('@byted/frontier-web-sdk');
vi.mock('./socket-client');

describe('SocketService', () => {
  let socketService: SocketService;
  let mockAccountService: IAccountService;
  let mockNetworkClientFactoryService: INetworkClientFactoryService;

  beforeEach(() => {
    vi.clearAllMocks();

    mockAccountService = {
      initialize: vi.fn(),
      getLocalLoginState: vi.fn(),
      hasLogin: true,
      getUserInfo: vi.fn(),
      getJwt: vi.fn(),
      _serviceBrand: undefined,
      onDidLogin: vi.fn(),
      onDidLogout: vi.fn(),
      login: vi.fn(),
      logout: vi.fn(),
    };

    mockNetworkClientFactoryService = {
      build: vi.fn(),
      setPpeEnv: vi.fn(),
      getSocketEnv: vi.fn().mockReturnValue('production'),
      _serviceBrand: undefined,
    };

    socketService = new SocketService(mockAccountService, mockNetworkClientFactoryService);
  });

  it('should create a socket client with JWT token if available', () => {
    const jwtToken = 'test-jwt-token';
    (mockAccountService.getJwt as Mock).mockReturnValue(jwtToken);

    const params: ConnectParams = {
      conversationId: 'test-convo-id',
      serviceId: 12345,
    };

    const socketClient = socketService.connect(params);

    expect(mockAccountService.getJwt).toHaveBeenCalled();
    expect(FWS).toHaveBeenCalledWith(
      expect.objectContaining({
        customParams: { 'x-jwt-token': jwtToken },
        deviceID: params.conversationId,
        service: params.serviceId,
        env: 'production',
      }),
    );
    expect(SocketClientImpl).toHaveBeenCalledWith(expect.any(FWS));
    expect(socketClient).toBeInstanceOf(SocketClientImpl);
  });

  it('should create a socket client without JWT token if not available', () => {
    (mockAccountService.getJwt as Mock).mockReturnValue(null);

    const params: ConnectParams = {
      conversationId: 'test-convo-id',
      serviceId: 12345,
    };

    socketService.connect(params);

    expect(FWS).toHaveBeenCalledWith(
      expect.objectContaining({
        customParams: {},
      }),
    );
  });

  it('should use the environment from params if provided', () => {
    const params: ConnectParams = {
      conversationId: 'test-convo-id',
      serviceId: 12345,
      env: { xUseEnv: 'ppe', xTTEnv: 'staging' },
    };

    socketService.connect(params);

    expect(mockNetworkClientFactoryService.getSocketEnv).not.toHaveBeenCalled();
    expect(FWS).toHaveBeenCalledWith(
      expect.objectContaining({
        env: {
          xUseEnv: 'ppe',
          xTTEnv: 'staging',
        },
      }),
    );
  });

  it('should use the environment from network factory if not provided in params', () => {
    (mockNetworkClientFactoryService.getSocketEnv as Mock).mockReturnValue('development');

    const params: ConnectParams = {
      conversationId: 'test-convo-id',
      serviceId: 12345,
    };

    socketService.connect(params);

    expect(mockNetworkClientFactoryService.getSocketEnv).toHaveBeenCalled();
    expect(FWS).toHaveBeenCalledWith(
      expect.objectContaining({
        env: 'development',
      }),
    );
  });
});

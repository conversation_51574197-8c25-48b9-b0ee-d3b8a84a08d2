import { FWS, type Options } from '@byted/frontier-web-sdk';
import md5 from 'js-md5';
import { WebSocket } from 'ws';

import { IAccountService } from '../account/account-service.interface';
import { INetworkClientFactoryService } from '../network-client-factory/network-client-factory-service.interface';
import { appID, appKey, fpID, salt } from './constants';
import { SocketClientImpl } from './socket-client';
import { SocketPool } from './socket-pool';
import type { ConnectParams, ISocketService, SocketClient } from './socket-service.interface';

function getAccessKey(deviceID: string): string {
  return md5.md5(`${fpID + appKey + deviceID + salt}`).toLowerCase();
}

function getPoolKey(params: ConnectParams): string {
  // env 可能为对象或字符串，统一序列化，未定义时不拼到 key 里
  const envStr =
    params.env === undefined ? '' : typeof params.env === 'string' ? params.env : JSON.stringify(params.env);
  return `${params.serviceId}::${params.conversationId}::${envStr}`;
}

function parseKeyToParams(key: string): ConnectParams {
  // key格式: serviceId::conversationId::envStr
  const [serviceIdStr, conversationId, ...envArr] = key.split('::');
  let env: any = undefined;
  const envStr = envArr.join('::');
  if (envStr) {
    try {
      env = JSON.parse(envStr);
    } catch {
      env = envStr;
    }
  }
  return { serviceId: Number(serviceIdStr), conversationId, env };
}

export class SocketService implements ISocketService {
  public _serviceBrand: undefined;

  private _clientPool = new SocketPool<SocketClient>(
    (key) => this._connect(parseKeyToParams(key)),
    (client) => client.dispose(),
  );

  constructor(
    @IAccountService private readonly _accountService: IAccountService,
    @INetworkClientFactoryService private readonly _networkClientFactoryService: INetworkClientFactoryService,
  ) {}

  public connect(params: ConnectParams) {
    return this._clientPool.getOrCreate(getPoolKey(params));
  }

  private _connect(params: ConnectParams) {
    const customParams: Record<string, string> = {};
    const jwtToken = this._accountService.getJwt();

    if (jwtToken) {
      customParams['x-jwt-token'] = jwtToken;
    }

    // 将UUID转换为字符串ID
    const deviceID = params.conversationId;
    const options: Options = {
      url: 'ws://frontier-sinfv2.zijieapi.com',
      ws: WebSocket,
      fpID: fpID,
      aID: appID,
      accessKey: getAccessKey(deviceID),
      deviceID: deviceID,
      service: params.serviceId,
      env: params.env !== undefined ? params.env : this._networkClientFactoryService.getSocketEnv(),
      enableTransformTextPayload: true,
      customParams,
      timeoutInterval: 5000,
      disableAutoReconnect: false,
      listenNetworkChanged: true,
      initReconnectInterval: 5000,
      maxReconnectInterval: 5000,
      maxRetries: Number.MAX_SAFE_INTEGER,
    };
    const fws = new FWS(options);

    return new SocketClientImpl(fws);
  }

  public release(socketClient: SocketClient) {
    this._clientPool.release(socketClient);
  }
}

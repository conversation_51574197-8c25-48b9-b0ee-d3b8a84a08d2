import { Disposable } from '@byted-image/lv-bedrock/dispose';
import { Emitter, type Event } from '@byted-image/lv-bedrock/event';
import type { CustomCloseEvent, CustomErrorEvent, FWS, FrontierMessageEvent } from '@byted/frontier-web-sdk';
import type { ErrorEvent, MessageEvent, SocketClient } from './socket-service.interface';

export class SocketClientImpl extends Disposable implements SocketClient {
  private readonly _onMessage = new Emitter<[MessageEvent]>();
  private readonly _onError = new Emitter<[ErrorEvent]>();
  private readonly _onOpen = new Emitter<[]>();
  private readonly _onClose = new Emitter<[]>();

  public readonly onMessage: Event<[MessageEvent]>;
  public readonly onError: Event<[ErrorEvent]>;
  public readonly onOpen: Event<[]>;
  public readonly onClose: Event<[]>;

  constructor(
    private readonly _fws: FWS,
    // TODO: chezongshao 现在自动关闭frontier会影响会话还原的体验，先不自动关闭
    private readonly _autoClose = false,
  ) {
    super();

    this.onMessage = this._onMessage.event;
    this.onError = this._onError.event;
    this.onOpen = this._onOpen.event;
    this.onClose = this._onClose.event;

    this._fws.addEventListener('message', this._handleMessage);
    this._fws.addEventListener('error', this._handleError);
    this._fws.addEventListener('open', this._handleOpen);
    this._fws.addEventListener('close', this._handleClose);
  }

  send(message: string, options?: { method?: number; service?: number }) {
    console.log('[SocketClientImpl] send', message, options);
    this._fws.send(message, options);
  }

  dispose(): void {
    this._fws.removeEventListener('message', this._handleMessage);
    this._fws.removeEventListener('error', this._handleError);
    this._fws.removeEventListener('open', this._handleOpen);
    this._fws.removeEventListener('close', this._handleClose);
    this._safeCloseSocket();

    super.dispose();
  }

  private _handleMessage = (msg: FrontierMessageEvent) => {
    // console.log('[SocketClientImpl] handleMessage', msg);
    this._onMessage.fire({
      method: msg.message.method,
      value: msg.message.textPayload ?? '',
    });
  };

  private _handleError = (msg: CustomErrorEvent) => {
    console.error('[SocketClientImpl] handleError', msg);
    this._onError.fire({
      code: msg.code,
      message: msg.message,
      error: msg.error,
    });
  };

  private _handleOpen = () => {
    console.log('[SocketClientImpl] handleOpen');
    this._onOpen.fire();
  };

  private _handleClose = (msg: CustomCloseEvent) => {
    console.log('[SocketClientImpl] handleClose', msg);
    this._onClose.fire();
  };

  private _safeCloseSocket() {
    if (!this._autoClose) {
      return;
    }
    if (this._fws.readyState !== WebSocket.CONNECTING && this._fws.readyState !== WebSocket.OPEN) {
      return;
    }
    try {
      this._fws.close();
    } catch (error) {
      // ignore
    }
  }
}

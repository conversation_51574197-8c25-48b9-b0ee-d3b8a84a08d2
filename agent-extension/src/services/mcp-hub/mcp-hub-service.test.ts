import * as fs from 'node:fs/promises';
import { EventEmitter } from 'node:events';
// import { Client } from '@modelcontextprotocol/sdk/client/index.js';
// import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { McpHubService, McpServerConfig } from './mcp-hub-service';
import type { IRpcService } from '../rpc/rpc-service.interface';
import chokidar from 'chokidar';
import * as vscode from 'vscode';
import { createDirectoriesForFile, fileExistsAtPath } from '@/utils/fs';
import { DataType } from '@/bam/namespaces/agentclient';
import * as pathUtils from '@/utils/path';

// --- Mocks ---
vi.mock('node:fs/promises');
vi.mock('@/utils/fs');

const mockStdErr = new EventEmitter();
const mockTransportInstance = {
  start: vi.fn().mockResolvedValue(undefined),
  close: vi.fn().mockResolvedValue(undefined),
  send: vi.fn().mockResolvedValue(undefined),
  stderr: mockStdErr,
  onerror: vi.fn(),
  onclose: vi.fn(),
};
vi.mock('@modelcontextprotocol/sdk/client/stdio.js', () => ({
  StdioClientTransport: vi.fn(() => mockTransportInstance),
}));
vi.mock('@modelcontextprotocol/sdk/client/sse.js', () => ({
  SSEClientTransport: vi.fn(() => mockTransportInstance),
}));
vi.mock('@modelcontextprotocol/sdk/client/streamableHttp.js');

const mockClientInstance = {
  connect: vi.fn().mockResolvedValue(undefined),
  close: vi.fn().mockResolvedValue(undefined),
  request: vi.fn(),
};
vi.mock('@modelcontextprotocol/sdk/client/index.js', () => ({
  Client: vi.fn(() => mockClientInstance),
}));

const mockWatcherInstance = {
  on: vi.fn((event, callback) => {
    if (event === 'change') {
      // Store the callback to trigger it later
      mockWatcherInstance.triggerChange = callback;
    }
  }),
  close: vi.fn(),
  triggerChange: () => {}, // Placeholder for the trigger function
};
// Correctly mock chokidar to handle the default import
vi.mock('chokidar', () => ({
  default: {
    watch: vi.fn(() => mockWatcherInstance),
  },
}));

describe('McpHubService', () => {
  let mcpHubService: McpHubService;
  let mockRpcService: IRpcService;

  const mockClientVersion = '1.0.0';
  const mockSettingsFilePath = '/fake/path/.codin/settings/codin_mcp_settings.json';
  const mockMcpServersDirectory = '/fake/path/.codin/mcp';

  const runPromises = () => new Promise(setImmediate);

  // Helper function to create mock connection
  const createMockConnection = (name: string, client: any = {}, serverOverrides: any = {}) => ({
    server: { name, config: '{}', status: 'connected' as const, disabled: false, ...serverOverrides },
    client: { close: vi.fn(), ...client } as any,
    transport: { close: vi.fn() } as any,
  });

  beforeEach(async () => {
    vi.clearAllMocks();
    vi.resetAllMocks();

    mockRpcService = {
      _serviceBrand: undefined,
      initialize: vi.fn().mockResolvedValue(undefined),
      registerFallbackHandler: vi.fn(),
      notify: vi.fn(),
      register: vi.fn(),
      setWebview: vi.fn(),
      dispose: vi.fn(),
      focusWebview: vi.fn(),
    };

    // Default valid mock for fs.readFile - ensure it always returns valid JSON
    const defaultMockContent = `{
  "mcpServers": {}
}
`;
    vi.mocked(fs.readFile).mockResolvedValue(defaultMockContent);
    vi.mocked(fs.writeFile).mockResolvedValue(undefined);
    vi.mocked(createDirectoriesForFile).mockResolvedValue([]);
    vi.mocked(fileExistsAtPath).mockResolvedValue(true);
    vi.mocked(mockClientInstance.request).mockResolvedValue({ tools: [], resources: [], resourceTemplates: [] });
    vi.mocked(mockClientInstance.connect).mockResolvedValue(undefined);

    mcpHubService = new McpHubService(mockClientVersion, mockRpcService);

    Object.defineProperty(mcpHubService, 'settingsFilePath', { get: () => mockSettingsFilePath });
    Object.defineProperty(mcpHubService, '_mcpServersDirectory', { get: () => mockMcpServersDirectory });

    // We manually call initialization in tests to have more control
    vi.spyOn(mcpHubService as any, 'initializeMcpServers').mockResolvedValue(Promise.resolve());
    vi.spyOn(mcpHubService as any, 'watchMcpSettingsFile').mockResolvedValue(Promise.resolve());
    // Mock notifyWebviewOfServerChanges to prevent unhandled promise rejections
    vi.spyOn(mcpHubService as any, 'notifyWebviewOfServerChanges').mockResolvedValue(undefined);
  });

  afterEach(() => {
    // 确保清理所有 spies，防止测试间的相互干扰
    vi.restoreAllMocks();

    // 重新设置关键的 mock 以防止 unhandled promise rejection
    const defaultMockContent = `{
  "mcpServers": {}
}
`;
    vi.mocked(fs.readFile).mockResolvedValue(defaultMockContent);
    vi.spyOn(mcpHubService as any, 'notifyWebviewOfServerChanges').mockResolvedValue(undefined);
  });

  describe('Initialization', () => {
    it('should not create settings file if it already exists', async () => {
      vi.mocked(fileExistsAtPath).mockResolvedValue(true);
      expect(fs.writeFile).not.toHaveBeenCalled();
    });

    it('should create settings file if it does not exist', async () => {
      vi.mocked(fileExistsAtPath).mockResolvedValue(false);
      await mcpHubService.createMcpSettingsFileIfNeeded();
      expect(fs.writeFile).toHaveBeenCalledWith(mockSettingsFilePath, expect.stringContaining('"mcpServers"'));
    });

    it('should initialize and connect to servers from settings on startup', async () => {
      const settings: Record<string, McpServerConfig> = {
        'test-server': { transportType: 'stdio', command: 'test', timeout: 1000 },
      };
      vi.mocked(fs.readFile).mockResolvedValue(JSON.stringify({ mcpServers: settings }));
      // Manually call the real implementation
      vi.spyOn(mcpHubService as any, 'initializeMcpServers').mockRestore();
      await (mcpHubService as any).initializeMcpServers();
      expect(mcpHubService.connections).toHaveLength(1);
      expect(mcpHubService.connections[0].server.name).toBe('test-server');
    });
  });

  describe('Connection Management', () => {
    const serverConfig: Record<string, McpServerConfig> = {
      'my-server': { transportType: 'stdio', command: 'echo', timeout: 1000 },
    };

    it('should add a new connection', async () => {
      // 恢复 notifyWebviewOfServerChanges 方法以确保 RPC 通知正常工作
      vi.spyOn(mcpHubService as any, 'notifyWebviewOfServerChanges').mockRestore();
      vi.spyOn(mcpHubService as any, 'notifyWebviewOfServerChanges').mockImplementation(async () => {
        await mockRpcService.initialize();
        mockRpcService.notify('mcpServers', {});
      });

      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      await mcpHubService.updateServerConnections(serverConfig);
      expect(mcpHubService.connections).toHaveLength(1);
      expect(mcpHubService.connections[0].server.name).toBe('my-server');
      expect(mockRpcService.notify).toHaveBeenCalledWith('mcpServers', expect.any(Object));

      // 重新设置 mock 以防止其他测试受到影响
      vi.spyOn(mcpHubService as any, 'notifyWebviewOfServerChanges').mockResolvedValue(undefined);
    });

    it('should delete a connection', async () => {
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      await mcpHubService.updateServerConnections(serverConfig);
      expect(mcpHubService.connections).toHaveLength(1);
      await mcpHubService.updateServerConnections({});
      expect(mcpHubService.connections).toHaveLength(0);
      expect(mockTransportInstance.close).toHaveBeenCalled();
    });

    it('should handle connection failure', async () => {
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      vi.mocked(mockClientInstance.connect).mockRejectedValue(new Error('Connection failed'));
      await mcpHubService.updateServerConnections(serverConfig);
      expect(mcpHubService.connections).toHaveLength(1);
      expect(mcpHubService.connections[0].server.status).toBe('disconnected');
      expect(mcpHubService.connections[0].server.error).toContain('Connection failed');
    });

    it('should handle stderr from stdio transport', async () => {
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      await mcpHubService.updateServerConnections(serverConfig);
      const errorMessage = 'ERROR: Something went wrong';
      mockStdErr.emit('data', Buffer.from(errorMessage));
      await runPromises();
      expect(mcpHubService.connections[0].server.error).toContain(errorMessage);
    });
  });

  describe('API Methods', () => {
    const serverName = 'api-server';
    const serverConfig: Record<string, McpServerConfig> = {
      [serverName]: { transportType: 'stdio', command: 'test', timeout: 1000 },
    };

    it('should call a tool and return result', async () => {
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      await mcpHubService.updateServerConnections(serverConfig);
      const toolResponse = { content: [{ type: 'text', text: 'Success' }] };
      vi.mocked(mockClientInstance.request).mockResolvedValue(toolResponse);
      const result = await mcpHubService.callTool(serverName, 'test-tool');
      expect(result).toEqual(toolResponse);
    });

    it('should throw if calling tool on disabled server', async () => {
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      await mcpHubService.updateServerConnections(serverConfig);
      mcpHubService.connections[0].server.disabled = true;
      await expect(mcpHubService.callTool(serverName, 'test-tool')).rejects.toThrow('is disabled');
    });

    it('should read a resource', async () => {
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      await mcpHubService.updateServerConnections(serverConfig);
      const resourceResponse = { contents: [] };
      vi.mocked(mockClientInstance.request).mockResolvedValue(resourceResponse);
      const result = await mcpHubService.readResource(serverName, 'file:///test.txt');
      expect(result).toEqual(resourceResponse);
    });
  });

  describe('Server State Management', () => {
    it('should restart a connection', async () => {
      const config: Record<string, McpServerConfig> = {
        'restart-server': { transportType: 'stdio', command: 'test', timeout: 1000 },
      };
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      await mcpHubService.updateServerConnections(config);
      await mcpHubService.restartConnection('restart-server');
      expect(mockTransportInstance.close).toHaveBeenCalledTimes(1);
      expect(mockClientInstance.connect).toHaveBeenCalledTimes(2);
      expect(vscode.window.showInformationMessage).toHaveBeenCalledWith('Restarting restart-server MCP server...');
    });

    it('should toggle server disabled state and write to settings', async () => {
      const serverName = 'toggle-server';
      const config: Record<string, McpServerConfig> = {
        [serverName]: { transportType: 'stdio', command: 'test', timeout: 1000 },
      };
      vi.mocked(fs.readFile).mockResolvedValue(JSON.stringify({ mcpServers: config }));
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      await mcpHubService.updateServerConnections(config);
      await mcpHubService.toggleServerDisabledRPC(serverName, true);
      const writtenConfig = JSON.parse(vi.mocked(fs.writeFile).mock.calls[0][1] as string);
      expect(writtenConfig.mcpServers[serverName].disabled).toBe(true);
      expect(mcpHubService.connections[0].server.disabled).toBe(true);
    });
  });

  describe('File Watcher', () => {
    it('should setup and trigger a file watcher to restart connection', async () => {
      const serverName = 'watcher-server';
      const config: Record<string, McpServerConfig> = {
        [serverName]: { transportType: 'stdio', command: 'node', args: ['/path/to/build/index.js'], timeout: 1000 },
      };
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      await mcpHubService.updateServerConnections(config);
      expect(chokidar.watch).toHaveBeenCalledWith('/path/to/build/index.js', expect.any(Object));

      mockWatcherInstance.triggerChange();
      await runPromises();

      expect(vscode.window.showInformationMessage).toHaveBeenCalledWith('Restarting watcher-server MCP server...');
    });
  });

  describe('McpHubService extra functions', () => {
    it('toggleToolAutoApproveRPC: 新增和移除autoApprove', async () => {
      const serverName = 's1';
      const mcpServers: Record<string, McpServerConfig> = {
        [serverName]: { transportType: 'stdio', command: 'c', timeout: 1, autoApprove: [] },
      };
      const config = { mcpServers };
      vi.mocked(fs.readFile).mockResolvedValue(JSON.stringify(config));
      vi.mocked(fs.writeFile).mockResolvedValue(undefined);
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      await mcpHubService.updateServerConnections(mcpServers);

      const connection = mcpHubService.connections.find((conn) => conn.server.name === serverName);
      expect(connection).toBeDefined();
      connection!.server.tools = [{ name: 't1' }];
      // 新增
      const servers = await mcpHubService.toggleToolAutoApproveRPC(serverName, ['t1'], true);
      expect(servers[0].name).toBe(serverName);
      // 移除
      await mcpHubService.toggleToolAutoApproveRPC(serverName, ['t1'], false);
      // 不存在server
      await expect(mcpHubService.toggleToolAutoApproveRPC('notfound', ['t1'], true)).rejects.toThrow();
    });

    it('addRemoteServer: 新增/已存在/非法url/读取失败', async () => {
      // 新增
      const serverName = 'remote-server';
      vi.mocked(fs.readFile).mockResolvedValue(JSON.stringify({ mcpServers: {} }));
      vi.mocked(fs.writeFile).mockResolvedValue(undefined);
      const servers = await mcpHubService.addRemoteServer(serverName, 'http://localhost:1234');
      expect(servers[0].name).toBe(serverName);
      // 已存在
      vi.mocked(fs.readFile).mockResolvedValue(
        JSON.stringify({ mcpServers: { [serverName]: { url: 'http://localhost:1234' } } }),
      );
      await expect(mcpHubService.addRemoteServer(serverName, 'http://localhost:1234')).rejects.toThrow();
      // 非法url
      vi.mocked(fs.readFile).mockResolvedValue(JSON.stringify({ mcpServers: {} }));
      await expect(mcpHubService.addRemoteServer(serverName, 'not-a-url')).rejects.toThrow();
      // 读取失败
      vi.spyOn(mcpHubService as any, 'readAndValidateMcpSettingsFile').mockResolvedValue(undefined);
      await expect(mcpHubService.addRemoteServer(serverName, 'http://localhost:1234')).rejects.toThrow();
    });

    it('deleteServerRPC: 正常/不存在/字段异常', async () => {
      // 正常
      const config = { mcpServers: { s1: { transportType: 'stdio', command: 'c', timeout: 1 } } };
      vi.mocked(fs.readFile).mockResolvedValue(JSON.stringify(config));
      vi.mocked(fs.writeFile).mockResolvedValue(undefined);
      vi.spyOn(mcpHubService, 'updateServerConnectionsRPC').mockResolvedValue(undefined as any);
      const servers = await mcpHubService.deleteServerRPC('s1');
      expect(Array.isArray(servers)).toBe(true);
      // 不存在
      vi.mocked(fs.readFile).mockResolvedValue(JSON.stringify({ mcpServers: {} }));
      await expect(mcpHubService.deleteServerRPC('notfound')).rejects.toThrow();
      // mcpServers异常
      vi.mocked(fs.readFile).mockResolvedValue(JSON.stringify({ mcpServers: null }));
      await expect(mcpHubService.deleteServerRPC('notfound')).rejects.toThrow();
    });

    it('updateServerTimeoutRPC: 正常/非法timeout/不存在/读取失败', async () => {
      // 正常
      const config = { mcpServers: { s1: { transportType: 'stdio', command: 'c', timeout: 1 } } };
      vi.mocked(fs.readFile).mockResolvedValue(JSON.stringify(config));
      vi.mocked(fs.writeFile).mockResolvedValue(undefined);
      vi.spyOn(mcpHubService, 'updateServerConnectionsRPC').mockResolvedValue(undefined as any);
      const servers = await mcpHubService.updateServerTimeoutRPC('s1', 10);
      expect(Array.isArray(servers)).toBe(true);
      // 非法timeout
      await expect(mcpHubService.updateServerTimeoutRPC('s1', 0)).rejects.toThrow();
      // 不存在
      vi.mocked(fs.readFile).mockResolvedValue(JSON.stringify({ mcpServers: {} }));
      await expect(mcpHubService.updateServerTimeoutRPC('notfound', 10)).rejects.toThrow();
    });

    it('formatToToolInfos: tool类型/参数/内容', async () => {
      mcpHubService.connections = [
        {
          server: {
            name: 's1',
            config: '{}',
            status: 'connected',
            tools: [
              {
                name: 't1',
                description: 'desc',
                inputSchema: {
                  properties: { a: { type: 'string', description: 'desc' } },
                  required: ['a'],
                },
              },
            ],
            disabled: false,
          },
          client: {} as any,
          transport: {} as any,
        },
      ];
      const tools = mcpHubService.formatToToolInfos();
      expect(tools.length).toBe(1);
      const info = tools[0].getInfo();
      expect(info.name).toContain('mcp_s1_t1');
      expect(info.params?.a?.type).toBeDefined();
      // run方法
      vi.spyOn(mcpHubService, 'callTool').mockResolvedValue({ content: [{ type: 'text', text: 'ok' }] } as any);
      // @ts-expect-error 这个tool不需要其他参数
      const result = await tools[0].run(JSON.stringify({ arguments: JSON.stringify({ a: '1' }) }));
      expect(result.output).toContain('ok');
    });

    it('dispose: connections/disposables/settingsWatcher', async () => {
      const disposeFn = vi.fn();
      mcpHubService.connections = [
        {
          server: { name: 's1', config: '{}', status: 'connected', disabled: false },
          client: { close: vi.fn() } as any,
          transport: { close: vi.fn() } as any,
        },
      ];
      (mcpHubService as any).disposables = [{ dispose: disposeFn }];
      (mcpHubService as any).settingsWatcher = { dispose: disposeFn };
      await mcpHubService.dispose();
      expect(disposeFn).toHaveBeenCalled();
      expect(mcpHubService.connections.length).toBe(0);
    });
  });

  describe('异常与边界分支补测', () => {
    it('fetchToolsList: 连接不存在时返回空数组', async () => {
      const res = await (mcpHubService as any).fetchToolsList('not-exist');
      expect(res).toEqual([]);
    });

    it('fetchToolsList: client.request 抛异常时返回空数组', async () => {
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      mcpHubService.connections.push({
        server: { name: 's1', config: '{}', status: 'connected', disabled: false },
        client: { request: vi.fn().mockRejectedValue(new Error('fail')), close: vi.fn() } as any,
        transport: { close: vi.fn() } as any,
      });
      const res = await (mcpHubService as any).fetchToolsList('s1');
      expect(res).toEqual([]);
    });

    it('restartConnectionRPC: readAndValidateMcpSettingsFile 返回 undefined', async () => {
      vi.spyOn(mcpHubService as any, 'readAndValidateMcpSettingsFile').mockResolvedValue(undefined);
      await expect(mcpHubService.restartConnectionRPC('s1')).rejects.toThrow('Failed to read or validate MCP settings');
    });

    it('callTool: config 解析失败 fallback', async () => {
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      mcpHubService.connections.push({
        server: { name: 's1', config: '{bad}', status: 'connected', disabled: false },
        client: { request: vi.fn().mockResolvedValue({ content: [] }), close: vi.fn() } as any,
        transport: { close: vi.fn() } as any,
      });
      await mcpHubService.callTool('s1', 't1');
      // 只要不抛异常即可
    });

    it('updateServerTimeoutRPC: 校验失败', async () => {
      vi.mocked(fs.readFile).mockResolvedValue(JSON.stringify({ mcpServers: { s1: { timeout: 1 } } }));
      await expect(mcpHubService.updateServerTimeoutRPC('s1', 0)).rejects.toThrow('Invalid timeout value');
    });

    it('toggleServerDisabledRPC: fs.writeFile 抛异常', async () => {
      vi.mocked(fs.readFile).mockResolvedValue(JSON.stringify({ mcpServers: { s1: { disabled: false } } }));
      vi.mocked(fs.writeFile).mockRejectedValue(new Error('fail'));
      await expect(mcpHubService.toggleServerDisabledRPC('s1', true)).rejects.toThrow(
        'Failed to read or validate MCP settings',
      );
    });

    // it('dispose: dispose 抛异常', async () => {
    //   const disposeFn = vi.fn().mockImplementation(() => {
    //     throw new Error('fail');
    //   });
    //   mcpHubService.connections = [
    //     {
    //       server: { name: 's1', config: '{}', status: 'connected', disabled: false },
    //       client: { close: vi.fn() } as any,
    //       transport: { close: vi.fn() } as any,
    //     },
    //   ];
    //   (mcpHubService as any).disposables = [{ dispose: disposeFn }];
    //   (mcpHubService as any).settingsWatcher = { dispose: disposeFn };
    //   await expect(mcpHubService.dispose()).resolves.not.toThrow();
    // });
  });

  describe('不同传输类型的连接测试', () => {
    it('should connect to SSE server', async () => {
      const config: Record<string, any> = {
        'sse-server': { url: 'http://localhost:8080/sse', timeout: 1000 },
      };
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      await mcpHubService.updateServerConnections(config);
      expect(mcpHubService.connections).toHaveLength(1);
      expect(mcpHubService.connections[0].server.name).toBe('sse-server');
    });

    it('should connect to HTTP server', async () => {
      const config: Record<string, McpServerConfig> = {
        'http-server': { transportType: 'http', url: 'http://localhost:8080/api', timeout: 1000 },
      };
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      await mcpHubService.updateServerConnections(config);
      expect(mcpHubService.connections).toHaveLength(1);
      expect(mcpHubService.connections[0].server.name).toBe('http-server');
    });
  });

  describe('文件监听器相关测试', () => {
    it('should handle file creation event', async () => {
      const mockFile = vscode.Uri.file(mockSettingsFilePath);
      vi.spyOn(mcpHubService as any, 'updateMCPServer').mockResolvedValue(undefined);
      const onDidCreateCallback = vi.mocked(vscode.workspace.onDidCreateFiles).mock.calls[0][0];
      await onDidCreateCallback({ files: [mockFile] });
      expect(mcpHubService as any).toHaveProperty('updateMCPServer');
    });

    it('should handle file save event', async () => {
      const mockDocument = { uri: vscode.Uri.file(mockSettingsFilePath) } as vscode.TextDocument;
      vi.spyOn(mcpHubService as any, 'updateMCPServer').mockResolvedValue(undefined);
      const onDidSaveCallback = vi.mocked(vscode.workspace.onDidSaveTextDocument).mock.calls[0][0];
      await onDidSaveCallback(mockDocument);
      expect(mcpHubService as any).toHaveProperty('updateMCPServer');
    });

    it('should handle file deletion event', async () => {
      // 简化测试：测试文件删除事件的处理逻辑
      const mockDeleteAllConnections = vi.spyOn(mcpHubService, 'deleteAllConnections').mockResolvedValue(undefined);

      // 直接测试当设置文件路径匹配时是否调用 deleteAllConnections
      const testFile = { fsPath: mockSettingsFilePath };

      // 手动构造文件删除事件对象
      const mockEvent = { files: [testFile] };

      // 使用原始的 arePathsEqual 函数来匹配路径
      // 由于 arePathsEqual 比较的是两个路径是否相等，我们确保传入相同的路径
      vi.spyOn(pathUtils, 'arePathsEqual').mockReturnValue(true);

      // 模拟文件删除事件处理逻辑
      for (const file of mockEvent.files) {
        if (pathUtils.arePathsEqual(file.fsPath, mockSettingsFilePath)) {
          await mcpHubService.deleteAllConnections();
        }
      }

      expect(mockDeleteAllConnections).toHaveBeenCalled();
    });
  });

  describe('sendLatestMcpServers 方法测试', () => {
    it('should send latest MCP servers when file exists', async () => {
      vi.mocked(fileExistsAtPath).mockResolvedValue(true);
      vi.spyOn(mcpHubService as any, 'notifyWebviewOfServerChanges').mockResolvedValue(undefined);
      await mcpHubService.sendLatestMcpServers();
      expect(mcpHubService as any).toHaveProperty('notifyWebviewOfServerChanges');
    });

    it('should not send anything when file does not exist', async () => {
      vi.mocked(fileExistsAtPath).mockResolvedValue(false);
      vi.spyOn(mcpHubService as any, 'notifyWebviewOfServerChanges').mockResolvedValue(undefined);
      await mcpHubService.sendLatestMcpServers();
      expect(mcpHubService as any).toHaveProperty('notifyWebviewOfServerChanges');
    });
  });

  describe('资源获取相关测试', () => {
    it('fetchResourcesList: 正常获取资源列表', async () => {
      const mockResources = [{ uri: 'file:///test.txt', name: 'test.txt' }];
      mcpHubService.connections = [
        {
          server: { name: 's1', config: '{}', status: 'connected', disabled: false },
          client: { request: vi.fn().mockResolvedValue({ resources: mockResources }), close: vi.fn() } as any,
          transport: { close: vi.fn() } as any,
        },
      ];
      const res = await (mcpHubService as any).fetchResourcesList('s1');
      expect(res).toEqual(mockResources);
    });

    it('fetchResourcesList: 请求失败时返回空数组', async () => {
      mcpHubService.connections = [
        createMockConnection('s1', { request: vi.fn().mockRejectedValue(new Error('fail')) }),
      ];
      const res = await (mcpHubService as any).fetchResourcesList('s1');
      expect(res).toEqual([]);
    });

    it('fetchResourceTemplatesList: 正常获取资源模板列表', async () => {
      const mockTemplates = [{ uriTemplate: 'file:///{name}.txt', name: 'File template' }];
      mcpHubService.connections = [
        createMockConnection('s1', { request: vi.fn().mockResolvedValue({ resourceTemplates: mockTemplates }) }),
      ];
      const res = await (mcpHubService as any).fetchResourceTemplatesList('s1');
      expect(res).toEqual(mockTemplates);
    });

    it('fetchResourceTemplatesList: 请求失败时返回空数组', async () => {
      mcpHubService.connections = [
        createMockConnection('s1', { request: vi.fn().mockRejectedValue(new Error('fail')) }),
      ];
      const res = await (mcpHubService as any).fetchResourceTemplatesList('s1');
      expect(res).toEqual([]);
    });
  });

  describe('错误处理与边界情况补测', () => {
    it('readAndValidateMcpSettingsFile: JSON 解析失败', async () => {
      vi.mocked(fs.readFile).mockResolvedValue('invalid json');
      const result = await (mcpHubService as any).readAndValidateMcpSettingsFile();
      expect(result).toBeUndefined();
      expect(vscode.window.showErrorMessage).toHaveBeenCalledWith(
        'Invalid MCP settings format. Please ensure your settings follow the correct JSON format.',
      );
    });

    it('readAndValidateMcpSettingsFile: Schema 验证失败', async () => {
      vi.mocked(fs.readFile).mockResolvedValue(JSON.stringify({ invalidField: 'test' }));
      const result = await (mcpHubService as any).readAndValidateMcpSettingsFile();
      expect(result).toBeUndefined();
      expect(vscode.window.showErrorMessage).toHaveBeenCalledWith('Invalid MCP settings schema.');
    });

    it('readAndValidateMcpSettingsFile: 文件读取失败', async () => {
      vi.mocked(fs.readFile).mockRejectedValue(new Error('File not found'));
      const result = await (mcpHubService as any).readAndValidateMcpSettingsFile();
      expect(result).toBeUndefined();
    });

    it('updateMCPServer: 设置文件读取失败时不处理', async () => {
      // 创建一个干净的测试环境，避免beforeEach中的mocks影响
      const freshMcpHubService = new McpHubService(mockClientVersion, mockRpcService);
      vi.spyOn(freshMcpHubService as any, 'readAndValidateMcpSettingsFile').mockResolvedValue(undefined);
      vi.spyOn(freshMcpHubService, 'updateServerConnections').mockResolvedValue(undefined);
      await (freshMcpHubService as any).updateMCPServer();
      expect(freshMcpHubService.updateServerConnections).not.toHaveBeenCalled();
    });

    it('connectToServer: transport.onerror 处理', async () => {
      const config: Record<string, McpServerConfig> = {
        'error-server': { transportType: 'stdio', command: 'test', timeout: 1000 },
      };
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      await mcpHubService.updateServerConnections(config);

      // 触发 transport.onerror
      const connection = mcpHubService.connections.find((c) => c.server.name === 'error-server');
      if (connection) {
        await mockTransportInstance.onerror(new Error('Transport error'));
        expect(connection.server.status).toBe('disconnected');
        expect(connection.server.error).toContain('Transport error');
      }
    });

    it('connectToServer: transport.onclose 处理', async () => {
      const config: Record<string, McpServerConfig> = {
        'close-server': { transportType: 'stdio', command: 'test', timeout: 1000 },
      };
      await mcpHubService.createMcpSettingsFileIfNeeded();
      // @ts-expect-error 测试用
      await mcpHubService.initializeMcpServers();
      await mcpHubService.updateServerConnections(config);

      // 触发 transport.onclose
      const connection = mcpHubService.connections.find((c) => c.server.name === 'close-server');
      if (connection) {
        await mockTransportInstance.onclose();
        expect(connection.server.status).toBe('disconnected');
      }
    });

    it('deleteAllConnections: 关闭连接时发生异常', async () => {
      const mockConnection = {
        server: { name: 's1', config: '{}', status: 'connected' as const, disabled: false },
        client: { close: vi.fn().mockRejectedValue(new Error('Close failed')) } as any,
        transport: { close: vi.fn().mockRejectedValue(new Error('Close failed')) } as any,
      };
      mcpHubService.connections = [mockConnection];

      await mcpHubService.deleteAllConnections();
      expect(mcpHubService.connections).toEqual([]);
    });

    it('getServers: 只返回未禁用的服务器', async () => {
      mcpHubService.connections = [
        createMockConnection('s1'),
        { ...createMockConnection('s2'), server: { ...createMockConnection('s2').server, disabled: true } },
      ];

      const servers = mcpHubService.getServers();
      expect(servers).toHaveLength(1);
      expect(servers[0].name).toBe('s1');
    });

    it('_formatSchemaTypeToDataType: 处理各种类型', async () => {
      const service = mcpHubService as any;
      expect(service._formatSchemaTypeToDataType('string')).toBe(DataType.String);
      expect(service._formatSchemaTypeToDataType('number')).toBe(DataType.Number);
      expect(service._formatSchemaTypeToDataType('integer')).toBe(DataType.Integer);
      expect(service._formatSchemaTypeToDataType('boolean')).toBe(DataType.Boolean);
      expect(service._formatSchemaTypeToDataType('array')).toBe(DataType.Array);
      expect(service._formatSchemaTypeToDataType('object')).toBe(DataType.Object);
      expect(service._formatSchemaTypeToDataType('null')).toBe(DataType.Null);
      expect(service._formatSchemaTypeToDataType('unknown')).toBe('unknown');
    });
  });

  describe('工具处理相关测试', () => {
    it('formatToToolInfos: 处理没有输入模式的工具', async () => {
      mcpHubService.connections = [
        createMockConnection(
          's1',
          {},
          {
            tools: [
              {
                name: 't1',
                description: 'desc',
                // 没有 inputSchema
              },
            ],
          },
        ),
      ];
      const tools = mcpHubService.formatToToolInfos();
      expect(tools.length).toBe(1);
      const info = tools[0].getInfo();
      expect(info.params).toEqual({});
    });

    it('formatToToolInfos: 工具运行时参数解析失败', async () => {
      mcpHubService.connections = [
        createMockConnection(
          's1',
          {},
          {
            tools: [
              {
                name: 't1',
                description: 'desc',
                inputSchema: {
                  properties: { a: { type: 'string', description: 'desc' } },
                  required: ['a'],
                },
              },
            ],
          },
        ),
      ];

      vi.spyOn(mcpHubService, 'callTool').mockResolvedValue({ content: [{ type: 'text', text: 'ok' }] } as any);

      const tools = mcpHubService.formatToToolInfos();
      // @ts-expect-error 测试用
      const result = await tools[0].run(JSON.stringify({ arguments: 'invalid json' }));
      expect(result.output).toContain('ok');
    });

    it('formatToToolInfos: 处理资源类型的工具响应', async () => {
      mcpHubService.connections = [
        createMockConnection(
          's1',
          {},
          {
            tools: [
              {
                name: 't1',
                description: 'desc',
                inputSchema: {
                  properties: { a: { type: 'string', description: 'desc' } },
                  required: ['a'],
                },
              },
            ],
          },
        ),
      ];

      vi.spyOn(mcpHubService, 'callTool').mockResolvedValue({
        content: [
          {
            type: 'resource',
            resource: {
              uri: 'file:///test.txt',
              name: 'test.txt',
              blob: new Uint8Array([1, 2, 3]),
            },
          },
        ],
      } as any);

      const tools = mcpHubService.formatToToolInfos();
      // @ts-expect-error 测试用
      const result = await tools[0].run(JSON.stringify({ arguments: JSON.stringify({ a: '1' }) }));
      expect(result.output).toContain('uri');
      expect(result.output).toContain('name');
    });
  });
});

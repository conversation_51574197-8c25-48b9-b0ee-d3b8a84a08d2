import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { MerkleHolderSyncBuildIndexEvent } from '../merkle/merkle-holder';
import * as vscode from 'vscode';
import type { FileChangeInfo } from '../git/git-service.interface';

export interface PureEmitter<T = void> {
  fire: (arg: T) => void;
}

/**
 * 事件名必须以 on 开头
 */
export interface INotificationService {
  _serviceBrand: undefined;

  onUpdateIndexInfo: PureEmitter<{
    buildRecords: MerkleHolderSyncBuildIndexEvent[];
    workspaceFolders: readonly vscode.WorkspaceFolder[];
  }>;

  onUpdateChangedFiles: PureEmitter<
    | {
        files: FileChangeInfo[];
      }
    | undefined
  >;
}

export const INotificationService = createDecorator<INotificationService>('notification-service');

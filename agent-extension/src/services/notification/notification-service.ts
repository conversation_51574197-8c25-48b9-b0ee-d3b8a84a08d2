import { IRpcService } from '../rpc/rpc-service.interface';
import type { INotificationService } from './notification-service.interface';

// @ts-expect-error 无需实现具体的方法，只需要转发到 RPC 服务
export class NotificationServiceImplementation implements INotificationService {
  public readonly _serviceBrand: undefined;

  constructor(@IRpcService private readonly _rpcService: IRpcService) {
    // biome-ignore lint/correctness/noConstructorReturn: <explanation>
    return new Proxy(this, {
      get: (_, prop) => {
        return {
          fire: (ev: any) => {
            this._rpcService.notify(prop as string, ev);
          },
        };
      },
    });
  }
}

export const NotificationService = NotificationServiceImplementation as unknown as {
  new (...args: any[]): INotificationService;
};

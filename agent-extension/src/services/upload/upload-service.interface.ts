import { createDecorator } from '@byted-image/lv-bedrock/di';
import { ILvErrorOr } from '@byted-image/lv-bedrock/error';

export interface UploadResult {
  key: string;
  bucket: string;
  url?: string;
}

export interface IUploadService {
  _serviceBrand: undefined;

  uploadFile: (file: File) => Promise<ILvErrorOr<UploadResult>>;
}

export const IUploadService = createDecorator<IUploadService>('upload-service');

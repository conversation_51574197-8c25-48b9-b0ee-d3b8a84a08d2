import { createDecorator } from '@byted-image/lv-bedrock/di';

export interface IAsyncTaskService {
  readonly _serviceBrand: undefined;

  /** 读取异步任务信息 */
  getAsyncTaskMsg: () => string;

  /** 读取异步任务JWT */
  getMsgJwt: () => string;

  /** 读取异步任务Prompt */
  getMsgPrompt: () => string;

  /** 读取异步任务FigmaToken */
  getMsgFigmaToken: () => string;

  /** 读取异步任务Cid */
  getMsgCid: () => string;

  /** 写入异步任务cid */
  setAsyncTaskCid: (cid: string) => void;

  /** 写入异步任务结束 */
  setAsyncTaskDone: () => void;

  /** 写入异步任务的日志 */
  addAsyncTaskLog: (content: string) => void;
}

export const IAsyncTaskService = createDecorator<IAsyncTaskService>('async-task-service');

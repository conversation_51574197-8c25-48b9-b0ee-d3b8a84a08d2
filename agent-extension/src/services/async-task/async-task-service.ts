import { Disposable } from '@byted-image/lv-bedrock/dispose';

import type { IAsyncTaskService } from './async-task-service.interface';
import fs from 'fs';

// 异步任务信息文件
const ASYNC_TASK_MSG_FILE_PATH = '/tmp/async_task_msg.txt';
const ASYNC_TASK_MSG_PROMPT_PATH = '/tmp/async_task_prompt.txt';
const ASYNC_TASK_CID_FILE_PATH = '/tmp/async_task_cid.txt';
const ASYNC_TASK_DONE_FILE_PATH = '/tmp/async_task_done.txt';
const ASYNC_TASK_LOG_FILE_PATH = '/tmp/async_task_log.txt';

// 同步调用
export class AsyncTaskService extends Disposable implements IAsyncTaskService {
  public readonly _serviceBrand: undefined;

  public getAsyncTaskMsg(): string {
    try {
      const content = fs.readFileSync(ASYNC_TASK_MSG_FILE_PATH, 'utf8');
      console.log(`getAsyncTaskMsg ${ASYNC_TASK_MSG_FILE_PATH} content: ${content}`);
      return content;
    } catch (err) {
      console.error(`${ASYNC_TASK_MSG_FILE_PATH} read err: ${err}`);
      return '';
    }
  }

  public getMsgJwt(): string {
    try {
      const content = fs.readFileSync(ASYNC_TASK_MSG_FILE_PATH, 'utf8');
      console.log(`getMsgJwt ${ASYNC_TASK_MSG_FILE_PATH} content: ${content}`);
      const obj: any = JSON.parse(content);
      return obj.jwt;
    } catch (err) {
      console.error(`${ASYNC_TASK_MSG_FILE_PATH} read err: ${err}`);
      return '';
    }
  }

  public getMsgPrompt(): string {
    try {
      const content = fs.readFileSync(ASYNC_TASK_MSG_PROMPT_PATH, 'utf8');
      console.log(`getMsgPrompt ${ASYNC_TASK_MSG_PROMPT_PATH} content: ${content}`);
      return content;
    } catch (err) {
      console.error(`${ASYNC_TASK_MSG_FILE_PATH} read err: ${err}`);
      return '';
    }
  }

  public getMsgCid(): string {
    try {
      const content = fs.readFileSync(ASYNC_TASK_CID_FILE_PATH, 'utf8');
      console.log(`getMsgCid ${ASYNC_TASK_CID_FILE_PATH} content: ${content}`);
      return content;
    } catch (err) {
      console.error(`${ASYNC_TASK_CID_FILE_PATH} read err: ${err}`);
      return '';
    }
  }

  public getMsgFigmaToken(): string {
    try {
      const content = fs.readFileSync(ASYNC_TASK_MSG_FILE_PATH, 'utf8');
      console.log(`getMsgFigmaToken ${ASYNC_TASK_MSG_FILE_PATH} content: ${content}`);
      const obj: any = JSON.parse(content);
      return obj.figma_token;
    } catch (err) {
      console.error(`${ASYNC_TASK_MSG_FILE_PATH} read err: ${err}`);
      return '';
    }
  }

  public setAsyncTaskCid(cid: string): void {
    this._writeFile(ASYNC_TASK_CID_FILE_PATH, cid);
  }

  public setAsyncTaskDone(): void {
    this._writeFile(ASYNC_TASK_DONE_FILE_PATH, `${Date.now()}`);
  }

  public _writeFile(path: string, msg: string): void {
    for (let i = 0; i < 5; i++) {
      try {
        fs.writeFileSync(path, msg, 'utf8');
        console.log(`_writeFile write path[${path}] msg[${msg}] success`);
        break;
      } catch (err) {
        console.error(`setAsyncTaskCid write path[${path}] msg[${msg}] err: ${err}`);
      }
    }
  }

  public addAsyncTaskLog(content: string): void {
    fs.appendFile(ASYNC_TASK_LOG_FILE_PATH, `${content}\n`, (err) => {
      if (err) {
        console.error('addAsyncTaskLog 追加失败:', err);
        return;
      }
    });
  }
}

import { createDecorator } from '@byted-image/lv-bedrock/di';

export interface IGuideService {
  readonly _serviceBrand: undefined;

  /**
   * 显示首次使用指引
   * @param forceShow 强制显示指引，即使已经显示过（用于测试）
   */
  showFirstTimeGuide(forceShow?: boolean): Promise<void>;

  /**
   * 检查是否已经显示过指引
   */
  hasShownGuide(): Promise<boolean>;

  /**
   * 标记指引已显示
   */
  markGuideAsShown(): Promise<void>;
}

export const IGuideService = createDecorator<IGuideService>('guide-service');

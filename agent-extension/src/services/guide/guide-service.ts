import { commands, window } from 'vscode';
import { IStorageService } from '../storage/storage-service.interface';
import { IGuideService } from './guide-service.interface';

const GUIDE_STORAGE_KEY = 'codin.guide.firstTimeShown';

export class GuideService implements IGuideService {
  public readonly _serviceBrand: undefined;

  constructor(@IStorageService private readonly _storageService: IStorageService) {}

  public async showFirstTimeGuide(forceShow = false): Promise<void> {
    const hasShown = await this.hasShownGuide();
    if (hasShown && !forceShow) {
      return;
    }

    // 显示气泡指引
    const result = await window.showInformationMessage(
      '💡 您可以通过"在编辑器中打开"功能在IDE右侧使用 Codin 插件，获得更好的体验！',
      {
        modal: false,
      },
      '立即尝试',
      '稍后提醒',
      '不再提示',
    );

    switch (result) {
      case '立即尝试':
        // 先关闭左侧侧边栏，提供更清晰的界面
        await commands.executeCommand('workbench.action.closeSidebar');
        // 稍微延迟一下，确保侧边栏关闭动画完成
        await new Promise((resolve) => setTimeout(resolve, 200));
        // 执行 openInEditor 命令
        await commands.executeCommand('codin-agent.openInEditor');
        await this.markGuideAsShown();
        break;
      case '不再提示':
        await this.markGuideAsShown();
        break;
      case '稍后提醒':
        // 不标记为已显示，下次还会提示
        break;
    }
  }

  public async hasShownGuide(): Promise<boolean> {
    const result = await this._storageService.get(GUIDE_STORAGE_KEY);
    return result === 'true';
  }

  public async markGuideAsShown(): Promise<void> {
    await this._storageService.set(GUIDE_STORAGE_KEY, 'true');
  }
}

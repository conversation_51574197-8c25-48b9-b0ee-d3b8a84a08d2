import { readFile } from 'node:fs/promises';
import path from 'node:path';
import { cwd } from '@/utils/env/system';
import type { IRuleAdapter } from '../rule-adapter.interface';
import type { Rule } from '../rules-service.interface';
import { ensureRuleFile } from '../utils/ensure-rule-file';

export class RelativePathRuleAdapter implements IRuleAdapter {
  public async getRuleContent(rule: Rule): Promise<string> {
    const filePath = this._getRuleAbsoluteFilePath(rule);
    await ensureRuleFile(filePath);
    console.log('rulesConfig filePath', filePath);
    const content = await readFile(filePath, 'utf-8');
    console.log('rulesConfig content', content);
    return content;
  }
  public beforeAddRule(_rule: Rule): Promise<void> {
    return Promise.resolve();
  }
  public async afterAddRule(rule: Rule): Promise<void> {
    const filePath = this._getRuleAbsoluteFilePath(rule);
    await ensureRuleFile(filePath);
  }
  public beforeEditRule(_oldRule: Rule, _newRule: Rule): Promise<void> {
    return Promise.resolve();
  }
  public async afterEditRule(rule: Rule): Promise<void> {
    const filePath = this._getRuleAbsoluteFilePath(rule);
    await ensureRuleFile(filePath);
  }
  public beforeDeleteRule(_rule: Rule): Promise<void> {
    return Promise.resolve();
  }
  public afterDeleteRule(_rule: Rule): Promise<void> {
    return Promise.resolve();
  }

  private _getRuleAbsoluteFilePath(rule: Rule): string {
    return path.resolve(cwd, rule.relative_path!);
  }
}

import { execSync } from 'child_process';

/**
 * 获取当前分支是从哪个分支 checkout 出来的
 * @returns 返回来源分支名称，如果无法推断则返回 null
 */
export function getSourceBranch(gitPath: string, currentBranch: string): string | undefined {
  try {
    // 1. 获取 git reflog 记录
    const reflog = execSync('git reflog', { encoding: 'utf-8', cwd: gitPath });

    // 2. 解析 checkout 记录（惰性匹配，找到目标后立即返回）
    const reflogLines = reflog.split('\n');
    for (const line of reflogLines) {
      if (!line.includes('checkout: moving from')) {
        continue;
      }

      // 惰性解析：只有当前行可能匹配时，才执行正则
      const match = line.match(/checkout: moving from (.+?) to (.+?)$/);
      if (!match) {
        continue;
      }

      const from = match[1];
      const to = match[2];
      if (to === currentBranch) {
        return from; // 找到目标分支的创建记录，立即返回来源分支
      }
    }
    return;
  } catch (error) {
    console.error('Error:', error);
    return;
  }
}

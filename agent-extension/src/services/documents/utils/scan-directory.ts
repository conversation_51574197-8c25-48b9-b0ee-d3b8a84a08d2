import fs from 'node:fs';
import path from 'node:path';
import { DocumentItem } from '../documents-service.interface';

export function scanDirectory(dirPath: string): DocumentItem[] {
  const items: DocumentItem[] = [];

  if (!fs.existsSync(dirPath)) {
    return items;
  }

  const entries = fs.readdirSync(dirPath, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry.name);

    const item: DocumentItem = {
      id: fullPath,
      name: entry.name,
      path: fullPath,
      type: entry.isDirectory() ? 'folder' : 'file',
    };

    if (entry.isDirectory()) {
      const children = scanDirectory(fullPath);
      if (children.length > 0) {
        item.children = children;
      }
    }

    items.push(item);
  }

  // Sort folders first, then files, both alphabetically
  return items.sort((a, b) => {
    if (a.type !== b.type) {
      return a.type === 'folder' ? -1 : 1;
    }
    return a.name.localeCompare(b.name);
  });
}

import { createDecorator } from '@byted-image/lv-bedrock/di';
import { TreeDataProvider } from 'vscode';

export interface DocumentItem {
  id: string;
  name: string;
  path: string;
  type: 'file' | 'folder';
  children?: DocumentItem[];
  content?: string;
}

export interface IDocumentsService {
  _serviceBrand: undefined;

  specsDirectory: string;

  getDocumentsTreeDataProvider(): TreeDataProvider<DocumentItem>;

  getDocumentContent(path: string): Promise<string>;

  refreshDocumentsTree(): void;

  openDocument(path: string): Promise<void>;

  getDocumentItems(): Promise<DocumentItem[]>;

  getDocumentChildren(folderPath: string): Promise<DocumentItem[]>;
}

export const IDocumentsService = createDecorator<IDocumentsService>('documents-service');

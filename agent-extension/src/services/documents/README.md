# DocumentsService

The DocumentsService provides functionality to explore and manage documents in the `.codin/specs/` directory. It implements a tree-based file explorer that allows users to browse, open, and view document content.

## Features

- **File Tree Exploration**: Browse all files and folders in the `.codin/specs/` directory
- **Lazy Loading**: Children are loaded on demand for better performance
- **Document Opening**: Open documents directly in VSCode editor
- **Content Reading**: Read document content programmatically
- **Tree View Integration**: Integrates with VSCode's TreeDataProvider for native tree view experience
- **Webview Integration**: Provides document browsing in the webview interface

## Architecture

### Core Components

1. **DocumentsService** (`documents-service.ts`)
   - Main service implementation
   - Manages document operations
   - Provides TreeDataProvider instance
   - Implements lazy loading for better performance

2. **DocumentsTreeProvider** (`documents-tree-provider.ts`)
   - Implements VSCode's TreeDataProvider interface
   - Handles tree view rendering and interactions
   - Manages tree item expansion/collapse
   - Loads children on demand when folders are expanded

3. **DocumentItem Interface** (`documents-service.interface.ts`)
   - Defines the structure for document items
   - Supports both files and folders
   - Children are loaded lazily for better performance

### Utility Functions

- **scanDirectory** (`utils/scan-directory.ts`): Recursively scans directory structure
- **scanRootDirectory** (`utils/scan-root-directory.ts`): Scans only root level items
- **ensureSpecsDirectory** (`utils/ensure-specs-directory.ts`): Creates specs directory if missing

## Usage

### In VSCode Extension

```typescript
// Get documents service from DI container
const documentsService = accessor.get(IDocumentsService);

// Get root level document items (folders and files)
const items = await documentsService.getDocumentItems();

// Get children of a specific folder
const children = await documentsService.getDocumentChildren('/path/to/folder');

// Open a document
await documentsService.openDocument('/path/to/document.md');

// Read document content
const content = await documentsService.getDocumentContent('/path/to/document.md');
```

### In Webview

```typescript
// Load root level documents
const result = await rpcClient?.call('getDocumentItems', {});
const documents = result?.data;

// Load folder children (for expanding folders)
const childrenResult = await rpcClient?.call('getDocumentChildren', { folderPath: '/path/to/folder' });
const children = childrenResult?.data;

// Open document
await rpcClient?.call('openDocument', { filePath: '/path/to/document.md' });

// Get document content
const result = await rpcClient?.call('getDocumentContent', { filePath: '/path/to/document.md' });
const content = result?.content;
```

## RPC Methods

The service exposes the following RPC methods for webview communication:

- `getDocumentItems()`: Returns root level document items (folders and files)
- `getDocumentChildren(folderPath)`: Returns children of a specific folder
- `openDocument(filePath)`: Opens a document in VSCode editor
- `getDocumentContent(filePath)`: Returns the content of a document

## VSCode Integration

### Tree View

The service registers a tree view with ID `codinDocuments` that appears in the Codin activity bar. Users can:

- Expand/collapse folders
- Click on files to open them
- See file and folder icons
- Navigate the document hierarchy

### Commands

- `codin.openDocument`: Opens a document file in VSCode editor
- `codin.webview.documents.open`: Opens the documents webview
- `codin.webview.documents.close`: Closes the documents webview

## Directory Structure

The service operates on the `.codin/specs/` directory in the workspace root:

```
.codin/
└── specs/
    ├── folder1/
    │   ├── document1.md
    │   └── document2.txt
    ├── folder2/
    │   └── subfolder/
    │       └── document3.md
    └── root-document.md
```

## Error Handling

The service includes comprehensive error handling:

- File not found errors when attempting to read non-existent files
- Directory creation errors when specs directory cannot be created
- File system permission errors
- Invalid file path errors

## Testing

The service includes unit tests in `documents-service.test.ts` that cover:

- Directory creation
- File scanning
- Content reading
- Error handling
- Tree structure generation 
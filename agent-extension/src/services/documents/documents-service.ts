import fs from 'node:fs';
import { Uri, commands } from 'vscode';

import { specsDirectory } from './constants';
import { DocumentsTreeProvider } from './documents-tree-provider';
import { IDocumentsService, type DocumentItem } from './documents-service.interface';
import { scanDirectory } from './utils/scan-directory';
import { scanRootDirectory } from './utils/scan-root-directory';

export class DocumentsService implements IDocumentsService {
  public _serviceBrand: undefined;

  public readonly specsDirectory = specsDirectory;

  private readonly _treeProvider: DocumentsTreeProvider;

  constructor() {
    this._treeProvider = new DocumentsTreeProvider();
  }

  public getDocumentsTreeDataProvider(): DocumentsTreeProvider {
    return this._treeProvider;
  }

  public async getDocumentContent(filePath: string): Promise<string> {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      const stats = fs.statSync(filePath);
      if (!stats.isFile()) {
        throw new Error(`Path is not a file: ${filePath}`);
      }

      const content = fs.readFileSync(filePath, 'utf-8');
      return content;
    } catch (error) {
      console.error('Error reading document content:', error);
      throw error;
    }
  }

  public refreshDocumentsTree(): void {
    this._treeProvider.refresh();
  }

  public async openDocument(filePath: string): Promise<void> {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      const stats = fs.statSync(filePath);
      if (!stats.isFile()) {
        throw new Error(`Path is not a file: ${filePath}`);
      }

      const uri = Uri.file(filePath);
      await commands.executeCommand('vscode.open', uri);
    } catch (error) {
      console.error('Error opening document:', error);
      throw error;
    }
  }

  public async getDocumentItems(): Promise<DocumentItem[]> {
    try {
      const items = scanRootDirectory(this.specsDirectory);
      await this._treeProvider.updateDocuments(items);
      return items;
    } catch (error) {
      console.error('Error scanning documents directory:', error);
      return [];
    }
  }

  public async getDocumentChildren(folderPath: string): Promise<DocumentItem[]> {
    try {
      const children = scanDirectory(folderPath);
      return children;
    } catch (error) {
      console.error('Error scanning folder children:', error);
      return [];
    }
  }

  public async refreshAndGetDocuments(): Promise<DocumentItem[]> {
    return this.getDocumentItems();
  }
}

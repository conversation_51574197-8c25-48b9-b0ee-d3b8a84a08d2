import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import fs from 'node:fs';
import path from 'node:path';
import { DocumentsService } from './documents-service';

// Import after mocking
import { specsDirectory } from './constants';

describe('DocumentsService', () => {
  let documentsService: DocumentsService;
  let testDir: string;

  beforeEach(() => {
    documentsService = new DocumentsService();
    testDir = path.join(specsDirectory, 'test-docs');

    // Create test directory
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
  });

  afterEach(() => {
    // Clean up test directory
    if (fs.existsSync(testDir)) {
      fs.rmSync(testDir, { recursive: true, force: true });
    }
  });

  it('should create specs directory if it does not exist', () => {
    const tempDir = path.join(specsDirectory, 'temp-test');
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
    expect(fs.existsSync(specsDirectory)).toBe(true);
  });

  it('should scan root directory and return document items', async () => {
    // Create test files
    const testFile1 = path.join(testDir, 'test1.md');
    const testFile2 = path.join(testDir, 'test2.txt');
    const subDir = path.join(testDir, 'subdir');
    const testFile3 = path.join(subDir, 'test3.md');

    fs.writeFileSync(testFile1, '# Test 1');
    fs.writeFileSync(testFile2, 'Test 2 content');
    fs.mkdirSync(subDir, { recursive: true });
    fs.writeFileSync(testFile3, '# Test 3');

    const items = await documentsService.getDocumentItems();

    // Find our test directory in the results
    const testDirItem = items.find((item) => item.name === 'test-docs');
    expect(testDirItem).toBeDefined();
    expect(testDirItem!.type).toBe('folder');
    // Root level items should not have children pre-loaded
    expect(testDirItem!.children).toBeUndefined();
  });

  it('should get document children for a folder', async () => {
    // Create test files
    const testFile1 = path.join(testDir, 'test1.md');
    const testFile2 = path.join(testDir, 'test2.txt');
    const subDir = path.join(testDir, 'subdir');
    const testFile3 = path.join(subDir, 'test3.md');

    fs.writeFileSync(testFile1, '# Test 1');
    fs.writeFileSync(testFile2, 'Test 2 content');
    fs.mkdirSync(subDir, { recursive: true });
    fs.writeFileSync(testFile3, '# Test 3');

    const children = await documentsService.getDocumentChildren(testDir);
    expect(children.length).toBeGreaterThan(0);

    // Should have both files and subdirectory
    const files = children.filter((item) => item.type === 'file');
    const folders = children.filter((item) => item.type === 'folder');
    expect(files.length).toBeGreaterThan(0);
    expect(folders.length).toBeGreaterThan(0);
  });

  it('should read document content', async () => {
    const testContent = '# Test Document\n\nThis is test content.';
    const testFile = path.join(testDir, 'test.md');
    fs.writeFileSync(testFile, testContent);

    const content = await documentsService.getDocumentContent(testFile);
    expect(content).toBe(testContent);
  });

  it('should throw error when reading non-existent file', async () => {
    const nonExistentFile = path.join(testDir, 'non-existent.md');

    await expect(documentsService.getDocumentContent(nonExistentFile)).rejects.toThrow('File not found');
  });
});

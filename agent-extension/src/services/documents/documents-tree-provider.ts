import * as vscode from 'vscode';
import { DocumentItem } from './documents-service.interface';
import { scanDirectory } from './utils/scan-directory';

export class DocumentsTreeProvider implements vscode.TreeDataProvider<DocumentItem> {
  private _onDidChangeTreeData: vscode.EventEmitter<DocumentItem | undefined | null> = new vscode.EventEmitter<
    DocumentItem | undefined | null
  >();
  readonly onDidChangeTreeData: vscode.Event<DocumentItem | undefined | null> = this._onDidChangeTreeData.event;

  private _rootDocuments: DocumentItem[] = [];

  refresh(): void {
    this._onDidChangeTreeData.fire(null);
  }

  getTreeItem(element: DocumentItem): vscode.TreeItem {
    const treeItem = new vscode.TreeItem(
      element.name,
      element.type === 'folder' ? vscode.TreeItemCollapsibleState.Collapsed : vscode.TreeItemCollapsibleState.None,
    );

    if (element.type === 'file') {
      treeItem.command = {
        command: 'codin.openDocument',
        title: 'Open Document',
        arguments: [element.path],
      };
      treeItem.iconPath = new vscode.ThemeIcon('file');
    } else {
      treeItem.iconPath = new vscode.ThemeIcon('folder');
    }

    treeItem.tooltip = element.path;
    treeItem.contextValue = element.type;

    return treeItem;
  }

  async getChildren(element?: DocumentItem): Promise<DocumentItem[]> {
    if (!element) {
      // Root level - return top-level folders and files
      return this._rootDocuments;
    }

    if (element.type === 'folder') {
      // Load children on demand for better performance
      try {
        const children = scanDirectory(element.path);
        return children;
      } catch (error) {
        console.error(`Error loading children for ${element.path}:`, error);
        return [];
      }
    }

    return [];
  }

  async updateDocuments(documents: DocumentItem[]): Promise<void> {
    this._rootDocuments = documents;
    this.refresh();
  }
}

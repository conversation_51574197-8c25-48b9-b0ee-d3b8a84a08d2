import { IStorageService } from '../storage/storage-service.interface';
import { IInputHistoryService } from './input-history-service.interface';

export class InputHistoryService implements IInputHistoryService {
  readonly _serviceBrand: undefined;

  private readonly HISTORY_KEY = 'input_history_global'; // 全局历史记录键
  private readonly MAX_HISTORY_SIZE = 30; // 最大历史记录数量

  // 全局索引，所有会话共享
  private _globalHistoryIndex = -1;

  constructor(@IStorageService protected readonly _storageService: IStorageService) {}

  /**
   * 格式化时间戳为 ": {秒级时间戳}:0;" 格式
   */
  private formatTimestampedInput(input: string): string {
    const timestamp = Math.floor(Date.now() / 1000);
    return `: ${timestamp}:0;${input}`;
  }

  /**
   * 从带时间戳的输入中提取原始内容
   */
  private extractOriginalInput(timestampedInput: string): string {
    // 匹配 ": {timestamp}:0;{content}" 格式
    const match = timestampedInput.match(/^: (\d+):0;(.+)$/);
    if (match) {
      return match[2];
    }
    // 如果不是时间戳格式，直接返回原内容（向后兼容）
    return timestampedInput;
  }

  /**
   * 检查两个输入是否相同（基于原始内容）
   */
  private isSameInput(input1: string, input2: string): boolean {
    const original1 = this.extractOriginalInput(input1);
    const original2 = this.extractOriginalInput(input2);
    return original1 === original2;
  }

  async addHistory(input: string): Promise<void> {
    if (!input || input.trim() === '') {
      return; // 空内容不保存
    }

    try {
      const existingHistory = await this.getHistory();

      // 移除所有相同的输入项（基于原始内容去重）
      const filteredHistory = existingHistory.filter((item) => !this.isSameInput(item, input));

      // 格式化输入并添加到开头
      const timestampedInput = this.formatTimestampedInput(input);
      const newHistory = [timestampedInput, ...filteredHistory];

      // 限制历史记录大小
      if (newHistory.length > this.MAX_HISTORY_SIZE) {
        newHistory.splice(this.MAX_HISTORY_SIZE);
      }

      // 保存历史记录 - 确保存储为 JSON 字符串
      await this._storageService.set(this.HISTORY_KEY, JSON.stringify(newHistory));

      // 重置全局索引
      this._globalHistoryIndex = -1;
    } catch (error) {
      console.error('Failed to add input history:', error);
    }
  }

  /**
   * 保存用户当前输入的内容到历史记录的第一位
   * 如果历史记录中已经存在相同内容，则不重复添加
   */
  async saveCurrentInput(currentInput: string): Promise<void> {
    if (!currentInput || currentInput.trim() === '') {
      return; // 空内容不保存
    }

    try {
      const existingHistory = await this.getHistory();

      // 如果历史记录为空，直接添加
      if (existingHistory.length === 0) {
        await this.addHistory(currentInput);
        return;
      }

      // 检查是否已经存在相同的内容（基于原始内容）
      if (existingHistory.length > 0 && this.isSameInput(existingHistory[0], currentInput)) {
        return;
      }

      // 如果存在相同内容但不是第一位，先移除它
      const filteredHistory = existingHistory.filter((item) => !this.isSameInput(item, currentInput));

      // 格式化输入并添加到第一位
      const timestampedInput = this.formatTimestampedInput(currentInput);
      const newHistory = [timestampedInput, ...filteredHistory];

      // 限制历史记录大小
      if (newHistory.length > this.MAX_HISTORY_SIZE) {
        newHistory.splice(this.MAX_HISTORY_SIZE);
      }

      // 保存历史记录
      await this._storageService.set(this.HISTORY_KEY, JSON.stringify(newHistory));

      // 重置全局索引
      this._globalHistoryIndex = -1;
    } catch (error) {
      console.error('Failed to save current input:', error);
    }
  }

  async getHistory(): Promise<string[]> {
    try {
      const history = await this._storageService.get(this.HISTORY_KEY);

      if (!history) {
        return [];
      }

      // 尝试解析 JSON，如果失败则返回空数组
      try {
        const parsed = JSON.parse(history);
        const result = Array.isArray(parsed) ? parsed : [];
        return result;
      } catch (parseError) {
        console.error('Failed to parse input history JSON:', parseError, 'Raw data:', history);
        // 如果解析失败，清空损坏的数据
        await this._storageService.delete(this.HISTORY_KEY);
        return [];
      }
    } catch (error) {
      console.error('Failed to get input history:', error);
      return [];
    }
  }

  async getHistoryIndex(): Promise<number> {
    // 返回全局索引
    return this._globalHistoryIndex;
  }

  async setHistoryIndex(index: number): Promise<void> {
    // 设置全局索引
    this._globalHistoryIndex = index;
  }

  /**
   * 获取下一个历史记录（向下箭头）（获取更新一次的输入）
   */
  async getNextHistory(): Promise<string | null> {
    const history = await this.getHistory();

    const currentIndex = await this.getHistoryIndex();

    if (history.length === 0) {
      return null;
    }

    const prevIndex = currentIndex - 1;

    if (prevIndex < 0) {
      return null; // 已经到最新记录
    }

    await this.setHistoryIndex(prevIndex);
    const timestampedResult = prevIndex === -1 ? '' : history[prevIndex];
    const result = this.extractOriginalInput(timestampedResult);
    return result;
  }

  /**
   * 获取上一个历史记录（向上箭头）(获取更老的输入)
   */
  async getPreviousHistory(): Promise<string | null> {
    const history = await this.getHistory();

    const currentIndex = await this.getHistoryIndex();

    if (history.length === 0) {
      return null;
    }

    const nextIndex = currentIndex + 1;

    if (nextIndex >= history.length) {
      return null; // 已经到最新一条记录
    }

    await this.setHistoryIndex(nextIndex);
    const timestampedResult = history[nextIndex];
    const result = this.extractOriginalInput(timestampedResult);
    return result;
  }

  /**
   * 重置历史记录索引（当用户切换到历史记录时）
   */
  async resetHistoryIndex(): Promise<void> {
    await this.setHistoryIndex(-1);
  }
}

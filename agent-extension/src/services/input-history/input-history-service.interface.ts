import { createDecorator } from '@byted-image/lv-bedrock/di';

export interface IInputHistoryService {
  readonly _serviceBrand: undefined;
  /** 添加输入历史记录 */
  addHistory: (input: string) => Thenable<void>;
  /** 保存用户当前输入的内容到历史记录的第一位 */
  saveCurrentInput: (currentInput: string) => Thenable<void>;
  /** 获取历史记录列表 */
  getHistory: () => Thenable<string[]>;
  /** 获取历史记录索引 */
  getHistoryIndex: () => Thenable<number>;
  /** 设置历史记录索引 */
  setHistoryIndex: (index: number) => Thenable<void>;
  /** 获取下一个历史记录（向下箭头） */
  getNextHistory: () => Thenable<string | null>;
  /** 获取上一个历史记录（向上箭头） */
  getPreviousHistory: () => Thenable<string | null>;
  /** 重置历史记录索引（当用户开始输入时） */
  resetHistoryIndex: () => Thenable<void>;
}

export const IInputHistoryService = createDecorator<IInputHistoryService>('input-history-service');

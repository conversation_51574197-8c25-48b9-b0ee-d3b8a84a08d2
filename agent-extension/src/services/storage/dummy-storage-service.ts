import { lvAssertNotHere } from '@byted-image/lv-bedrock/assert';
import type { IStorageService } from './storage-service.interface';

export class DummyStorageService implements IStorageService {
  public readonly _serviceBrand: undefined;

  public get(_key: string) {
    lvAssertNotHere('DummyStorageService should not be used');
    return Promise.resolve(undefined);
  }
  public set(_key: string, _value: string) {
    lvAssertNotHere('DummyStorageService should not be used');
    return Promise.resolve();
  }

  public delete(_key: string) {
    lvAssertNotHere('DummyStorageService should not be used');
    return Promise.resolve();
  }

  public secretStore(_key: string, _value: string) {
    lvAssertNotHere('DummyStorageService should not be used');
    return Promise.resolve();
  }
  public secretGet(_key: string) {
    lvAssertNotHere('DummyStorageService should not be used');
    return Promise.resolve(undefined);
  }
  public secretDelete(_key: string) {
    lvAssertNotHere('DummyStorageService should not be used');
    return Promise.resolve();
  }

  public parseGet<T>(_key: string, _defaultVal?: T) {
    lvAssertNotHere('DummyStorageService should not be used');
    return undefined;
  }
}

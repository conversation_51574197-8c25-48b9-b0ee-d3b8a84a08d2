import * as fsp from 'node:fs/promises';
import * as path from 'node:path';
import { constants } from 'node:fs';
import * as vscode from 'vscode';
import { Disposable } from '@byted-image/lv-bedrock/dispose';
import ignore from 'ignore';
import {
  ICodinIgnoreManager,
  ICodinIgnoreManagerStatus,
  ErrorCode,
  CodinIgnoreManagerStatusCode,
} from './codin-ignore-manager.interface';
import { makeError, makeOkWith, type ILvErrorOr } from '@byted-image/lv-bedrock/error';
import { cwd } from '@/utils/env/system';

/**
 * CodinIgnore 服务实现
 * 处理 .codinignore 文件，提供文件忽略功能
 */
export class CodinIgnoreManager extends Disposable implements ICodinIgnoreManager {
  static readonly ignoreFileName = '.codinignore';

  // 为每个目录存储 ignore 实例和对应的模式
  private _ignoreInstances: Map<string, { ignore: ReturnType<typeof ignore>; patterns: string[] }> = new Map();
  private _status: ICodinIgnoreManagerStatus = {
    code: CodinIgnoreManagerStatusCode.UNINITIALIZED,
  };

  private _createWatcher: vscode.Disposable | undefined;
  private _deleteWatcher: vscode.Disposable | undefined;
  private _saveWatcher: vscode.Disposable | undefined;

  // 等待更新的队列
  private _waitingUpdateQueue: Array<{ path: string; action: 'create' | 'delete' | 'save' }> = [];
  private _updatingQueue: Set<string> = new Set();

  // 等待完成的 Promise 相关属性
  private _completionPromise: Promise<ILvErrorOr<boolean, unknown>> | null = null;
  private _completionResolve: ((result: ILvErrorOr<boolean, unknown>) => void) | null = null;

  public async init(): Promise<ILvErrorOr<boolean, unknown>> {
    this._setupFileWatcher();
    return this._initIgnoreFiles();
  }

  /**
   * 获取当前服务状态
   */
  public getStatus(): ICodinIgnoreManagerStatus {
    return this._status;
  }

  /**
   * 等待服务完成初始化或更新
   * @param timeout 超时时间（毫秒），默认 30 秒
   * @returns [成功状态, 错误信息]
   */
  public async waitForCompletion(timeout = 30000): Promise<ILvErrorOr<boolean, unknown>> {
    if (this._status.code === CodinIgnoreManagerStatusCode.COMPLETED) {
      return makeOkWith(true);
    }

    if (this._status.code === CodinIgnoreManagerStatusCode.ERROR) {
      return this._status.result ?? makeError(ErrorCode.UNKNOWN_ERROR, 'Unknown error');
    }

    if (this._completionPromise) {
      // 如果已经有等待中的 Promise，返回现有的
      return Promise.race([
        this._completionPromise,
        new Promise<ILvErrorOr<boolean, unknown>>((resolve) => {
          setTimeout(() => resolve(makeError(ErrorCode.TIMEOUT, 'timeout')), timeout);
        }),
      ]);
    }

    // 创建新的等待 Promise
    this._completionPromise = new Promise<ILvErrorOr<boolean, unknown>>((resolve) => {
      this._completionResolve = resolve;
    });

    return Promise.race([
      this._completionPromise,
      new Promise<ILvErrorOr<boolean, unknown>>((resolve) => {
        setTimeout(() => resolve(makeError(ErrorCode.TIMEOUT, 'timeout')), timeout);
      }),
    ]);
  }

  /**
   * 设置状态并通知等待的 Promise
   */
  private _setStatus(status: ICodinIgnoreManagerStatus): void {
    this._status = status;
    console.log(`CodinIgnoreService status changed to: ${status.code}`);

    // 如果状态变为已完成，通知等待的 Promise
    if (status.code === CodinIgnoreManagerStatusCode.COMPLETED && this._completionResolve) {
      this._completionResolve(status.result ?? makeOkWith(true));
      this._completionPromise = null;
      this._completionResolve = null;
    }
    // 如果状态变为错误，通知等待的 Promise
    else if (status.code === CodinIgnoreManagerStatusCode.ERROR && this._completionResolve) {
      this._completionResolve(status.result ?? makeError(ErrorCode.UNKNOWN_ERROR, 'Unknown error'));
      this._completionPromise = null;
      this._completionResolve = null;
    }
  }

  /**
   * 设置文件系统监听器
   */
  private _setupFileWatcher(): void {
    // 监听所有 .codinignore 文件的变化
    const fileWatcher = vscode.workspace.createFileSystemWatcher(`**/${CodinIgnoreManager.ignoreFileName}`);

    // 文件创建时重新加载单个文件
    this._createWatcher = fileWatcher.onDidCreate(async (uri) => {
      this._waitingUpdateQueue.push({ path: uri.fsPath, action: 'create' });
      await this._updateIgnoreFiles();
    });

    // 文件删除时移除该文件的规则
    this._deleteWatcher = fileWatcher.onDidDelete(async (uri) => {
      this._waitingUpdateQueue.push({ path: uri.fsPath, action: 'delete' });
      await this._updateIgnoreFiles();
    });

    // 监听文件保存事件
    this._saveWatcher = vscode.workspace.onDidSaveTextDocument(async (document) => {
      // 检查保存的文件是否是 .codinignore 文件
      if (document.fileName.endsWith(CodinIgnoreManager.ignoreFileName)) {
        this._waitingUpdateQueue.push({ path: document.fileName, action: 'save' });
        await this._updateIgnoreFiles();
      }
    });

    // 注册到 disposable
    this._register(this._createWatcher);
    this._register(this._deleteWatcher);
    this._register(this._saveWatcher);
  }

  /**
   * 处理文件变化
   */
  private async _updateIgnoreFiles(): Promise<void> {
    try {
      // 如果失败，在下次触发更新的时候，触发重新初始化
      if (this._status.code === CodinIgnoreManagerStatusCode.ERROR) {
        await this._initIgnoreFiles();
        return;
      }

      const nextFilePath = this._waitingUpdateQueue.at(0);

      if (!nextFilePath) return;
      // 当前 path 已经在更新中
      if (this._updatingQueue.has(nextFilePath.path)) return;

      this._updatingQueue.add(nextFilePath.path);
      this._waitingUpdateQueue.shift();
      this._setStatus({ code: CodinIgnoreManagerStatusCode.UPDATING });

      switch (nextFilePath.action) {
        case 'create':
        case 'save':
          await this._loadIgnorePatterns(path.dirname(nextFilePath.path));
          break;
        case 'delete':
          this._removeIgnoreFile(nextFilePath.path);
          break;
      }
      this._updatingQueue.delete(nextFilePath.path);

      // 如果更新队列和等待队列都为空，则设置为完成状态
      if (this._updatingQueue.size === 0 && this._waitingUpdateQueue.length === 0) {
        this._setStatus({ code: CodinIgnoreManagerStatusCode.COMPLETED });
        return;
      }

      await this._updateIgnoreFiles();
    } catch (error) {
      const errorObj = makeError(ErrorCode.LOAD_IGNORE_FILES_ERROR, (error as Error)?.message);
      this._setStatus({ code: CodinIgnoreManagerStatusCode.ERROR, result: errorObj });
    }
  }

  /**
   * 获取当前工作区根目录
   */
  private _getCurrentWorkspaceRoot(): string {
    return cwd;
  }

  public getIgnoreList(): string[] {
    // 合并所有文件的忽略规则
    const allPatterns: string[] = [];
    for (const { patterns } of this._ignoreInstances.values()) {
      allPatterns.push(...patterns);
    }
    return allPatterns;
  }

  /**
   * 解析单个 .codinignore 文件
   * @param ignoreFilePath .codinignore 文件的绝对路径
   */
  private async _parseCodinIgnoreFile(ignoreFilePath: string): Promise<string[]> {
    try {
      await fsp.access(ignoreFilePath, constants.F_OK);
      console.log('codinIgnoreService.startRead', performance.now());
      const content = await fsp.readFile(ignoreFilePath, 'utf-8');
      console.log('codinIgnoreService.endRead', performance.now());

      const patterns = content
        .split('\n')
        .map((line) => line.trim())
        .filter((line) => line && !line.startsWith('#'));

      console.log('codinIgnoreService.parsedPatterns', performance.now());
      return patterns;
    } catch (error) {
      // 文件不存在或无法读取，返回空数组
      return [];
    }
  }

  /**
   * 获取指定目录的 ignore 模式
   */
  private async _loadIgnorePatterns(dirPath: string): Promise<void> {
    const actualRootPath = this._getCurrentWorkspaceRoot();
    const fullDirPath = path.resolve(actualRootPath, dirPath);
    const codinIgnorePath = path.join(fullDirPath, CodinIgnoreManager.ignoreFileName);

    const patterns = await this._parseCodinIgnoreFile(codinIgnorePath);

    // 为该目录创建 ignore 实例
    if (patterns.length > 0) {
      const ig = ignore().add(patterns);
      this._ignoreInstances.set(codinIgnorePath, { ignore: ig, patterns });
    } else {
      this._ignoreInstances.delete(codinIgnorePath);
    }
  }

  /**
   * 移除指定 .codinignore 文件的规则
   */
  private _removeIgnoreFile(ignoreFilePath: string): void {
    this._ignoreInstances.delete(ignoreFilePath);
  }

  /**
   * 检查文件是否被忽略
   */
  async isIgnored(filePath: string): Promise<boolean> {
    // 将 filePath 统一转换为相对于 cwd 的路径
    const cwd = this._getCurrentWorkspaceRoot();
    let normalizedFilePath: string;

    if (path.isAbsolute(filePath)) {
      // 如果是绝对路径，转换为相对路径
      normalizedFilePath = path.relative(cwd, filePath);
    } else {
      // 如果已经是相对路径，直接使用
      normalizedFilePath = filePath;
    }

    // 统一使用 posix 路径分隔符
    normalizedFilePath = normalizedFilePath.replace(/\\/g, '/');

    // 检查所有的 ignore 实例
    for (const { ignore: ignoreInstance } of this._ignoreInstances.values()) {
      if (ignoreInstance.ignores(normalizedFilePath)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 加载所有 .codinignore 文件
   */
  private async _initIgnoreFiles(): Promise<ILvErrorOr<boolean, unknown>> {
    // 递归搜索所有 .codinignore 文件并预加载
    try {
      this._setStatus({ code: CodinIgnoreManagerStatusCode.INITIALIZING });

      // 清空等待队列和更新队列
      this._updatingQueue.clear();
      this._waitingUpdateQueue.length = 0;
      this._ignoreInstances.clear();

      console.log('codinIgnoreService.loadIgnoreFiles', performance.now());
      const actualRootPath = this._getCurrentWorkspaceRoot();

      const codinIgnoreFiles = await vscode.workspace.findFiles(
        `**/${CodinIgnoreManager.ignoreFileName}`,
        '**/node_modules/**',
      );

      const loadPromises: Promise<void>[] = [];
      // 预加载所有找到的 .codinignore 文件
      for (const file of codinIgnoreFiles) {
        const fileDir = path.dirname(path.relative(actualRootPath, file.fsPath));
        loadPromises.push(this._loadIgnorePatterns(fileDir));
      }
      await Promise.all(loadPromises);

      if (this._waitingUpdateQueue.length === 0 && this._updatingQueue.size === 0) {
        this._setStatus({ code: CodinIgnoreManagerStatusCode.COMPLETED });
        return makeOkWith(true);
      }

      this._updateIgnoreFiles();

      return makeOkWith(true);
    } catch (error) {
      this._setStatus({
        code: CodinIgnoreManagerStatusCode.ERROR,
        result: makeError(ErrorCode.LOAD_IGNORE_FILES_ERROR, (error as Error)?.message),
      });

      return makeError(ErrorCode.LOAD_IGNORE_FILES_ERROR, (error as Error)?.message);
    }
  }

  dispose(): void {
    // 清理等待的 Promise
    if (this._completionResolve) {
      this._completionResolve(makeError(ErrorCode.DISPOSE_ERROR, 'CodinIgnoreManager disposed'));
      this._completionPromise = null;
      this._completionResolve = null;
    }

    this._waitingUpdateQueue.length = 0;
    this._updatingQueue.clear();
    this._ignoreInstances.clear();

    super.dispose();
  }
}

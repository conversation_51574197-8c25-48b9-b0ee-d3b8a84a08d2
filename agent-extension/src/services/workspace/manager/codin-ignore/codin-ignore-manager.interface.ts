import type { ILvErrorOr } from '@byted-image/lv-bedrock/error';

export enum ErrorCode {
  TIMEOUT = 10001,
  LOAD_IGNORE_FILES_ERROR = 10002,
  DISPOSE_ERROR = 10003,
  UNKNOWN_ERROR = 10004,
}

export interface ICodinIgnoreManagerStatus {
  code: CodinIgnoreManagerStatusCode;
  result?: ILvErrorOr<boolean, unknown>;
}

/**
 * CodinIgnore 服务状态枚举
 */
export enum CodinIgnoreManagerStatusCode {
  /** 未初始化 */
  UNINITIALIZED = 'uninitialized',
  /** 初始化中 */
  INITIALIZING = 'initializing',
  /** 更新中 */
  UPDATING = 'updating',
  /** 已完成 */
  COMPLETED = 'completed',
  /** 错误 */
  ERROR = 'error',
}

/**
 * CodinIgnore 服务接口
 * 类似于 cursorignore，用于处理 .codinignore 文件
 */
export interface ICodinIgnoreManager {
  /**
   * 获取所有 ignore 模式
   */
  getIgnoreList(): string[];

  /**
   * 检查文件是否被忽略
   * @param filePath 文件路径
   */
  isIgnored(filePath: string): Promise<boolean>;

  /**
   * 获取当前服务状态
   */
  getStatus(): ICodinIgnoreManagerStatus;

  /**
   * 等待服务完成初始化或更新
   * @param timeout 超时时间（毫秒），默认 30 秒
   * @returns [成功状态, 错误信息]
   */
  waitForCompletion(timeout?: number): Promise<ILvErrorOr<boolean, unknown>>;

  /**
   * 初始化
   * @returns [成功状态, 错误信息]
   */
  init(): Promise<ILvErrorOr<boolean, unknown>>;

  dispose(): void;
}

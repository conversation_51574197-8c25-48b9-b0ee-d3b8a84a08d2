import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { IRepoInfo } from './workspace.service';
import type { ICodinIgnoreManager } from './manager/codin-ignore';

export interface IWorkspaceService {
  _serviceBrand: undefined;

  codinIgnoreManager: ICodinIgnoreManager;

  initRepository(): Promise<void>;

  dispose(): void;

  getRepoInfo(repoName: string): IRepoInfo | undefined;

  getAllRepoInfo(): Map<string, IRepoInfo>;

  getBusinessRepoInfo(repoName: string): IRepoInfo[];

  getBusinessList(): string[];

  getBusinessPathList(repoName: string, business: string): string[] | undefined;

  getDid(): string;
}

export const IWorkspaceService = createDecorator<IWorkspaceService>('workspace');

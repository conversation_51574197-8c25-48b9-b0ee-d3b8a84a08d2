// 工具进度详细信息
export interface ToolProgress {
  toolId: string;
  toolName: string;
  progress: number;
  message?: string;
  status: 'running' | 'completed' | 'failed';
  startTime: number;
  lastUpdate: number;
}

// 工具进度管理器
export class ToolProgressManager {
  private progressMap = new Map<string, ToolProgress>();
  private listeners: ((progresses: Map<string, ToolProgress>) => void)[] = [];

  updateProgress(toolId: string, toolName: string, progress: number, message?: string) {
    const existing = this.progressMap.get(toolId);
    const now = Date.now();

    const toolProgress: ToolProgress = {
      toolId,
      toolName,
      progress,
      message,
      status: progress >= 100 ? 'completed' : 'running',
      startTime: existing?.startTime || now,
      lastUpdate: now,
    };

    this.progressMap.set(toolId, toolProgress);
    this.notifyListeners();

    // 自动清理已完成的进度信息（延迟3秒）
    if (progress >= 100) {
      setTimeout(() => {
        this.removeProgress(toolId);
      }, 3000);
    }
  }

  removeProgress(toolId: string) {
    if (this.progressMap.delete(toolId)) {
      this.notifyListeners();
    }
  }

  getProgress(toolId: string): ToolProgress | undefined {
    return this.progressMap.get(toolId);
  }

  getAllProgresses(): Map<string, ToolProgress> {
    return new Map(this.progressMap);
  }

  onProgressChange(callback: (progresses: Map<string, ToolProgress>) => void): () => void {
    this.listeners.push(callback);
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener(this.getAllProgresses()));
  }

  dispose() {
    this.progressMap.clear();
    this.listeners = [];
  }
}

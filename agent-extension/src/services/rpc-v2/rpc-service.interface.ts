import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { FileNode } from '../workspace-files/types';

/**
 * 1. 成员函数规范：只支持一个参数，不支持多个参数，返回值必须为 Promise 类型
 * 2. 禁止使用 onXxx 格式的方法名称，否则会被当成是事件处理，永远不会被触发
 * 3. 最多只能使用一层命名空间，不要使用多层命名空间
 */
export interface IRpcV2Service {
  _serviceBrand: undefined;

  inputHistory: {
    addHistory: (params: { input: string }) => Promise<void>;
    saveCurrentInput: (params: { currentInput: string }) => Promise<void>;
    getNextHistory: () => Promise<{ history: string }>;
    getPreviousHistory: () => Promise<{ history: string }>;
    resetHistoryIndex: () => Promise<void>;
  };

  workspace: {
    readFileContent: (params: { path: string }) => Promise<{ content: string | null }>;
    readFolderListContent: (params: { path: string }) => Promise<{ content: string | null }>;
  };

  conversation: {
    cancel: (params: { parentCid: string; childCid?: string }) => Promise<void>;
  };

  getRecentFiles: () => Promise<{ list: FileNode[] }>;
  showInformationMessage: (params: { content: string; detail?: string; modal?: boolean }) => Promise<void>;
  showErrorMessage: (params: { content: string; detail?: string; modal?: boolean }) => Promise<void>;

  requestIndexInfo(): Promise<void>;
  requestChangedFiles(): Promise<void>;
}

export const IRpcV2Service = createDecorator<IRpcV2Service>('rpc-v2-service');

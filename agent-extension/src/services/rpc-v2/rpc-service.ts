/**
 * @file RpcService.ts
 * @description RPC 的服务端实现，旨在运行于 VSCode Extension host。
 * 监听来自 Webview 的请求，将其分发给已注册的处理器，并返回响应。
 */

import { Controller } from '@/controller';
import { IGitService } from '@/services/git/git-service.interface';
import { IInputHistoryService } from '@/services/input-history/input-history-service.interface';
import * as vscode from 'vscode';
import { IMerkleService } from '../merkle/merkle-service.interface';
import { IRpcV2Service } from './rpc-service.interface';
import { INotificationService } from '../notification/notification-service.interface';
import { IWorkspaceFilesService } from '../workspace-files/workspace-files.interface';

/**
 * 所有成员函数的返回值都必须是 Promise 类型，否则 webview 那边提示会出错
 */
export class RpcV2Service implements IRpcV2Service {
  public _serviceBrand: undefined;

  private _controller!: Controller;

  constructor(
    @IMerkleService private readonly _merkleService: IMerkleService,
    @IGitService private readonly _gitService: IGitService,
    @IInputHistoryService private readonly _inputHistoryService: IInputHistoryService,
    @INotificationService private readonly _notificationService: INotificationService,
    @IWorkspaceFilesService private readonly _workspaceFilesService: IWorkspaceFilesService,
  ) {}

  setController(controller: Controller) {
    this._controller = controller;
  }

  inputHistory = {
    addHistory: async (params: { input: string }) => {
      await this._inputHistoryService.addHistory(params.input);
    },
    saveCurrentInput: async (params: { currentInput: string }) => {
      await this._inputHistoryService.saveCurrentInput(params.currentInput);
    },
    getNextHistory: async () => {
      const history = await this._inputHistoryService.getNextHistory();
      return history ? { history } : { history: '' };
    },
    getPreviousHistory: async () => {
      const history = await this._inputHistoryService.getPreviousHistory();
      return history ? { history } : { history: '' };
    },
    resetHistoryIndex: async () => {
      await this._inputHistoryService.resetHistoryIndex();
    },
  };

  workspace = {
    readFileContent: async (params: { path: string }) => {
      const content = await this._workspaceFilesService.readFileContent(params.path);
      return { content };
    },
    readFolderListContent: async (params: { path: string }) => {
      const content = await this._workspaceFilesService.getFolderListContent(params.path);
      return { content };
    },
  };

  conversation = {
    cancel: async (params: { parentCid: string; childCid?: string }) => {
      await this._controller.cancelConversationWithMessage(params.parentCid, params.childCid);
    },
  };

  getRecentFiles = async () => {
    return { list: this._workspaceFilesService.recentVisit };
  };

  showInformationMessage = async (params: { content: string; detail?: string; modal?: boolean }) => {
    vscode.window.showInformationMessage(params.content, {
      detail: params.detail,
      modal: params.modal ?? false,
    });
  };

  showErrorMessage = async (params: { content: string; detail?: string; modal?: boolean }) => {
    vscode.window.showErrorMessage(params.content, {
      detail: params.detail,
      modal: params.modal ?? true,
    });
  };

  async requestIndexInfo() {
    this._notificationService.onUpdateIndexInfo.fire({
      buildRecords: this._merkleService.buildRecords,
      workspaceFolders: vscode.workspace.workspaceFolders || [],
    });
  }

  async requestChangedFiles() {
    // First send basic file info quickly
    const basicFiles = this._gitService.getChangedFiles();
    this._notificationService.onUpdateChangedFiles.fire(basicFiles);

    // Then enhance with diff stats asynchronously
    try {
      const filesWithStats = await this._gitService.getChangedFilesWithStats();
      if (filesWithStats) {
        this._notificationService.onUpdateChangedFiles.fire(filesWithStats);
      }
    } catch (error) {
      console.warn('Failed to get diff stats:', error);
    }
  }
}

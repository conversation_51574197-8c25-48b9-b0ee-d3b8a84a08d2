import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { makeError, makeError<PERSON>y, makeOk, type ILvErrorOr } from '@byted-image/lv-bedrock/error';
import { Emitter } from '@byted-image/lv-bedrock/event';
import * as vscode from 'vscode';

import { ISocketService } from '@/services/socket/socket-service.interface';
import { ClientMessageType, type ClientMessage } from '@/utils/conversation/client-message/abstract-message';

import type { AgentInferenceParams } from '@/bam/namespaces/agentserver';
import { getSystemEnv } from '@/utils/env/system';
import { ClientToolMessage } from '@/utils/conversation/client-message/tool-message';
import { API_PREFIX } from '@/const';
// Tools
import type { Tool } from '@/tools/base';
import { ListDirectoryTool } from '@/tools/list-directory';
import { ReadFileTool } from '@/tools/read-file/read-file';
import { AskMessage, AskMessageRole } from '../../base-chat/types';
import { IMcpHubService } from '../../mcp-hub/mcp-hub-service.interface';
import { INetworkClientFactoryService } from '../../network-client-factory/network-client-factory-service.interface';
import { IRulesService, RuleType } from '../../rules/rules-service.interface';
import { IWorkspaceService } from '../../workspace/workspace.interface';
import { DefaultModelType } from '../../codesearch-chat/codesearch-chat.interface';
import { ChatErrorCode } from '../../base-chat/chat-error';
import { ILoggerFactoryService, type ILoggerService } from '../../logger/logger.interface';
import { BaseConversationContext } from '../base-conversation-context';
import type { Int64 } from '@/bam';
import { ConversationTypeEnum } from '@/constants/conversation-types';
import { AgentId } from '../const';
import type { RetryCodeAgentRequest } from '@/bam/namespaces/agw';
import { ModelType } from '@/bam/namespaces/base';
import { IFileLoggerService } from '../../file-logger/file-logger-service.interface';
import { IStorageService } from '../../storage/storage-service.interface';
import { GrepSearchTool } from '@/tools/grep-search';

export class CodeSearchConversationContext extends BaseConversationContext {
  public _serviceBrand: undefined;
  protected readonly _conversationType = ConversationTypeEnum.Coding;
  protected readonly _agentType = AgentId.Codesearch;

  get onSendMessageError() {
    return this._onSendMessageError.event;
  }

  get messages() {
    return this._messagesManager.getMessages();
  }

  private readonly _onSendMessageError = new Emitter<ILvErrorOr<void>[]>();

  private _getTools(): Tool[] {
    return [
      this._instantiationService.createInstance(ListDirectoryTool),
      this._instantiationService.createInstance(ReadFileTool),
      this._instantiationService.createInstance(GrepSearchTool),
      ...this._mcpHubService.formatToToolInfos(),
    ];
  }

  private _logger: ILoggerService;

  constructor(
    options: { cid: string; parentCid?: string; parentMessageVersion?: Int64 },
    initMessages: ClientMessage[],
    @ISocketService _socketService: ISocketService,
    @IInstantiationService _instantiationService: IInstantiationService,
    @IRulesService private readonly _rulesService: IRulesService,
    @IMcpHubService private readonly _mcpHubService: IMcpHubService,
    @INetworkClientFactoryService
    _networkClientFactoryService: INetworkClientFactoryService,
    @IWorkspaceService private readonly _workspaceService: IWorkspaceService,
    @ILoggerFactoryService private readonly _loggerFactoryService: ILoggerFactoryService,
    @IFileLoggerService _fileLoggerService: IFileLoggerService,
    @IStorageService _storageService: IStorageService,
  ) {
    super(
      options,
      initMessages,
      _instantiationService,
      _socketService,
      _networkClientFactoryService,
      _storageService,
      _fileLoggerService,
    );
    this._logger = this._loggerFactoryService.create('CodeSearchConversationContext');
    this._logger.info('create instance');
  }

  public async bootstrap(): Promise<void> {
    await super.bootstrap();
    this._messageReceiver.onAddMessages((messages) => {
      const toolMessages = messages.filter((message) => message.type === ClientMessageType.Tool);
      this._handleToolMessages(toolMessages as ClientToolMessage[]);
    });
  }

  /**
   * 发送消息
   */
  public async sendMessage(message: AskMessage) {
    this._logger.info('sendMessage', message, this.id);

    const alwaysRulesContent = await this._rulesService.getRulesContentByType(RuleType.Always);

    const repoInfos = Array.from(this._workspaceService.getAllRepoInfo().values());

    this._logger.info('current repo info list', repoInfos);
    const modelType = await this._getCurrentModel();
    const askParams = {
      params: {
        id: this._options.cid,
        client_env: {
          system_env: getSystemEnv(repoInfos),
          tools: this._getTools()
            .filter((tool) => tool.enable())
            .map((tool) => tool.getInfo()),
          project_rules: alwaysRulesContent,
          did: this._workspaceService.getDid(),
          repo_infos: repoInfos.map((info) => ({
            branch: info.branch,
            path: info.path,
            repo_name: info.repoName,
          })),
        },
        model_type: modelType,
      } as AgentInferenceParams,
    };
    if (message.role === AskMessageRole.User) {
      askParams.params.user_message = {
        content: message.userContent,
      };
    } else {
      askParams.params.tool_messages = message.toolsData.map((data) => ({
        content: data.content,
        request_id: data.requestId,
      }));
    }
    this._logger.info('askParams', askParams);

    try {
      const resp = await this._networkClient(`${API_PREFIX}/conversation/codesearch`, {
        method: 'POST',
        data: JSON.stringify(askParams),
      });
      this._logger.info('askParamsResponse', resp.headers, resp.data);
      if (resp.data.code !== 0) {
        vscode.window.showErrorMessage(
          `[CodeSearchConversationContext]send message error: [${resp.data.code}]-${resp.data.message}`,
        );
        const sendMessageError = makeError(
          ChatErrorCode.SendMessageFailed,
          `[CodeSearchConversationContext]send message failed: ${resp.data.message}`,
        );
        this._onSendMessageError.fire(sendMessageError);
        return sendMessageError;
      }
      this._updateMessageFromLocal(message, resp.headers['x-tt-logid']);
    } catch (e) {
      this._logger.error('send message error', e);
      const sendMessageError = makeErrorBy(ChatErrorCode.SendMessageFailed, 'send message failed', e as Error);
      this._onSendMessageError.fire(sendMessageError);
      return sendMessageError;
    }
    return makeOk();
  }

  protected async _getModelQueue(): Promise<ModelType[]> {
    const defaultModel = await this._getDefaultModelType();
    return [defaultModel, ModelType.Claude, ModelType.Kimi_K2, ModelType.DeepSeek];
  }

  private async _getDefaultModelType() {
    return DefaultModelType;
  }

  private async _handleToolMessages(messages: ClientToolMessage[]) {
    const clientToolRunners: Promise<{ content: string; requestId: string; name: string }>[] = [];

    for (const toolMessage of messages) {
      const tool = this._getTools().find((tool) => tool.getInfo().name === toolMessage.name);
      if (!tool) {
        continue;
      }

      const runner = async () => {
        const result = await tool.run(toolMessage.input ?? '', toolMessage.id, this.id, toolMessage.version[0]);
        return {
          content: result.value,
          requestId: toolMessage.id,
          name: tool.getInfo().name,
        };
      };

      clientToolRunners.push(runner());
    }

    if (clientToolRunners.length === 0) {
      return;
    }

    const toolsData = await Promise.all(clientToolRunners);
    for (const tool of toolsData) {
      this._logger.info(`handleToolMessage[${tool.name}]`, toolsData);
    }

    this.sendMessage({
      cid: this._options.cid,
      role: AskMessageRole.Tool,
      toolsData,
    });
  }

  protected async _retryWhenMessageError(): Promise<void> {
    try {
      const alwaysRulesContent = await this._rulesService.getRulesContentByType(RuleType.Always);

      const repoInfos = Array.from(this._workspaceService.getAllRepoInfo().values());
      const retryParams = {
        params: {
          id: this._options.cid,
          model_type: await this._getCurrentModel(),
          client_env: {
            system_env: getSystemEnv(repoInfos),
            tools: this._getTools()
              .filter((tool) => tool.enable())
              .map((tool) => tool.getInfo()),
            project_rules: alwaysRulesContent,
            did: this._workspaceService.getDid(),
            repo_infos: repoInfos.map((info) => ({
              branch: info.branch,
              path: info.path,
              repo_name: info.repoName,
            })),
          },
        },
      } as RetryCodeAgentRequest;

      const resp = await this._networkClient(`${API_PREFIX}/conversation/retry-codesearch`, {
        method: 'POST',
        data: JSON.stringify(retryParams),
      });

      if (resp.data.code !== 0) {
        vscode.window.showErrorMessage(`retry message error: [${resp.data.code}]-${resp.data.message}`);
        const sendMessageError = makeError(
          ChatErrorCode.SendMessageFailed,
          `retry message failed: ${resp.data.message}`,
        );
        this._onSendMessageError.fire(sendMessageError);
      }
    } catch (e) {
      this._logger.error('retry message error: ', e);
      const sendMessageError = makeErrorBy(ChatErrorCode.SendMessageFailed, 'retry message failed', e as Error);
      this._onSendMessageError.fire(sendMessageError);
    }
  }
}

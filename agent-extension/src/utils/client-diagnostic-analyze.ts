/**
 * DiagnosticAnalyzer - Manages diagnostic endpoint discovery and updates
 */
export class DiagnosticAnalyzer {
  private port: string | null = null;
  private endpoint: string | null = null;
  private readonly PORT_RANGE_START = 63342;
  private readonly PORT_RANGE_END = 63352;
  private isInitialized = false;

  /**
   * Gets the current diagnostic endpoint
   */
  getEndpoint(): string | null {
    return this.endpoint;
  }

  /**
   * Initializes the diagnostic endpoint
   */
  async initializeEndpoint(): Promise<void> {
    if (this.isInitialized) {
      return;
    }
    await this.updateEndpoint();
    this.isInitialized = true;
  }

  /**
   * Finds and returns a working diagnostic endpoint by:
   * 1. Checking process.env.DIAGNOSTIC_PORT, or
   * 2. Scanning ports 63342-63352
   *
   * Throws if none found.
   */
  private async findWorkingDiagnosticEndpoint(): Promise<{ endpoint: string; port: string }> {
    console.log('Attempting to find a working diagnostic endpoint...');

    // 2. Reuse existing endpoint if it's still working
    if (this.endpoint !== null && this.port !== null && (await this.testPort(this.port))) {
      console.log("Using cached endpoint, it's still working");
      return { endpoint: this.endpoint, port: this.port };
    }

    // 3. Otherwise, scan a range of ports
    for (let port = this.PORT_RANGE_START; port <= this.PORT_RANGE_END; port++) {
      const candidateEndpoint = `http://localhost:${port}/api/mcp/get_errors`;
      console.log(`Testing port ${port}...`);
      const isWorking = await this.testPort(port.toString());
      if (isWorking) {
        console.log(`Found working diagnostic endpoint at ${candidateEndpoint}`);
        return { endpoint: candidateEndpoint, port: port.toString() };
      }
      console.log(`Port ${port} is not responding correctly.`);
    }

    throw new Error('No working diagnostic endpoint found');
  }

  /**
   * Tests if a port is reachable by making a request to the MCP tools endpoint
   */
  private async testPort(port: string): Promise<boolean> {
    try {
      const url = `http://localhost:${port}/api/mcp/list_tools`;
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Add a timeout to avoid hanging
        signal: AbortSignal.timeout(5000),
      });
      return response.ok;
    } catch (error) {
      console.log(`Port ${port} test failed:`, error);
      return false;
    }
  }

  /**
   * Updates the cached endpoint by finding a working diagnostic endpoint.
   * This can be called periodically to ensure the endpoint is still working.
   */
  async updateEndpoint(): Promise<void> {
    const { endpoint, port } = await this.findWorkingDiagnosticEndpoint();
    this.endpoint = endpoint;
    this.port = port;
    console.log(`Updated diagnostic endpoint to: ${endpoint}`);
  }

  /**
   * get diagnostic data from the current endpoint
   */
  async getDiagnostics(paths: string[]): Promise<
    Array<{
      filePath: string;
      severity: string;
      description: string;
      lineContent: string;
      lineNumber: string;
      lineStartOffset: string;
      lineEndOffset: string;
    }>
  > {
    await this.initializeEndpoint();
    const endpoint = this.endpoint;
    if (!endpoint) {
      throw new Error('No diagnostic endpoint found');
    }
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ paths }),
    });
    console.log('endpoint', endpoint);
    console.log('body', JSON.stringify({ paths }));
    if (response.ok) {
      const data = (await response.json()) as any;
      console.log('raw data', data);
      if (data.status) {
        return JSON.parse(data.status);
      }
      throw new Error(data.error || 'Failed to get diagnostics');
    }
    throw new Error('Failed to get diagnostics');
  }

  /**
   * Checks if the current endpoint is still working
   */
  async isEndpointWorking(): Promise<boolean> {
    if (!this.endpoint) {
      return false;
    }
    return await this.testPort(this.endpoint);
  }
}

export const diagnosticAnalyzer = new DiagnosticAnalyzer();

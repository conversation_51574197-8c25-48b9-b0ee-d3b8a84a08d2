import type { BaseClient, UserConfig } from '@slardar/base';
import { getErrorInfo } from '../error';

export class SlardarLogger {
  private _client: BaseClient;
  constructor(client: BaseClient) {
    this._client = client;
  }

  config(config: Partial<UserConfig>) {
    this._client.config(config);
  }

  private _sendLog(level: string, message: string, extra: Record<string, string> = {}) {
    this._client.sendLog?.({
      content: message,
      extra,
      level,
    });
  }

  event(eventName: string, extra: Record<string, string | number> = {}) {
    const metrics: Record<string, number> = {};
    const categories: Record<string, string> = {};
    for (const key in extra) {
      const value = extra[key];
      if (typeof value === 'number') {
        metrics[key] = value;
      } else {
        categories[key] = value;
      }
    }

    this._client.sendEvent?.({
      name: eventName,
      metrics,
      categories,
    });
  }

  log(message: string, extra: Record<string, string> = {}) {
    this._sendLog('info', message, extra);
  }

  warn(message: string, extra: Record<string, string> = {}) {
    this._sendLog('warn', message, extra);
  }

  error(maybeError: any, extra: Record<string, string> = {}) {
    console.error('[slardar]', maybeError, extra);
    const errorInfo = getErrorInfo(maybeError);
    this._client.report({
      ev_type: 'js_error',
      payload: {
        error: {
          message: errorInfo.message,
          stack: errorInfo.stack,
        },
        extra,
        breadcrumbs: [],
      },
    });
  }
}

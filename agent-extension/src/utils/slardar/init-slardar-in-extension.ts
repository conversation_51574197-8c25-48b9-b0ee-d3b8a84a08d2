import Slardar from '@slardar/base';
import axios from 'axios';

const slardarBid = 'codin_ide';
const PREFIX = '[codin_ide]';

export let slardarInstance: typeof Slardar | undefined;

export function initSlardarInExtension() {
  Slardar.init({
    bid: slardarBid,
    release: PACKAGE_VERSION,
    transport: {
      get: ({ success, fail, ...otherOptions }) => {
        axios({
          method: 'get',
          ...otherOptions,
        })
          .then((res) => {
            success?.(res);
          })
          .catch(fail);
      },
      post: (options) => {
        console.log('Sending slardar request with post:', options.data);
        axios({
          method: 'post',
          ...options,
        })
          .then((res) => {
            console.log('Received post response:', res.data);
          })
          .catch((err) => {
            console.error('Request post failed:', err);
          });
      },
    },
  });

  Slardar.start();
  slardarInstance = Slardar;
  process.on('uncaughtException', (error: Error) => {
    console.error('uncaughtException:', error);
    slardarInstance?.report({
      ev_type: 'js_error',
      payload: {
        error: {
          name: `${PREFIX} ${error.name}`,
          message: error.message,
          stack: error.stack,
        },
        breadcrumbs: [
          {
            type: 'dom',
            timestamp: Date.now(),
            category: 'uncaughtException',
            message: `${PREFIX} uncaughtException`,
          },
        ],
      },
    });
  });

  process.on('unhandledRejection', (reason: unknown) => {
    console.error('unhandledRejection:', reason);
    const error = reason instanceof Error ? reason : new Error(String(reason));
    slardarInstance?.report({
      ev_type: 'js_error',
      payload: {
        error: {
          name: `${PREFIX} ${error.name}`,
          message: error.message,
          stack: error.stack,
        },
        breadcrumbs: [
          {
            type: 'dom',
            timestamp: Date.now(),
            category: 'unhandledRejection',
            message: `${PREFIX} unhandledRejection`,
          },
        ],
      },
    });
  });
}

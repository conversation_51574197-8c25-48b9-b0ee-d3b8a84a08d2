import { slardarLogger } from './slardar';

export class BaseEvent<EventParams extends Record<string, string | number>> {
  protected _eventName: string;
  protected _eventParams: Partial<EventParams>;

  constructor(eventName: string, eventParams: Partial<EventParams> = {}) {
    this._eventName = eventName;
    this._eventParams = eventParams;
  }

  update(eventParams: Partial<EventParams> | null) {
    this._eventParams = {
      ...this._eventParams,
      ...eventParams,
    };
  }

  emit(eventParams: Partial<EventParams>) {
    slardarLogger.event(this._eventName, {
      ...this._eventParams,
      ...eventParams,
    } as EventParams);
  }
}

import { ContentType, type ContentPart } from '@/bam/namespaces/message';
import { lvAssertNever } from '@byted-image/lv-bedrock/assert';

/**
 * 获取模型回复的内容
 * @param part
 * @returns
 */
export function getContent(part: ContentPart) {
  // 兼容历史脏数据
  if (!part) {
    return {
      type: ContentType.Content,
      content: '',
    };
  }
  let content = '';
  switch (part.type) {
    case ContentType.Content:
      content = part.content!;
      break;
    case ContentType.Reasoning:
      content = part.reasoning_content!;
      break;
    case ContentType.Image:
      content = part.image!.url;
      break;
    case ContentType.File:
      content = part.file!.url;
      break;
    default:
      lvAssertNever(part.type, 'getContent error');
  }
  return {
    type: part.type,
    content,
  };
}

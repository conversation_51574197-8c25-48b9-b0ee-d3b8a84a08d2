import { BaseJsonMessage, ClientMessage, ClientMessageType } from './client-message/abstract-message';
import { ClientContentMessage } from './client-message/content-message';
import { ClientStreamingMessage } from './client-message/streaming-message';
import { ClientToolMessage } from './client-message/tool-message';
import { ClientMultiPartMessage } from './client-message/multi-part-message';
import { ClientErrorMessage } from './client-message/error-message';
import { SubAgentMessage } from './client-message/sub-agent-message';

const messageTypeMap = {
  [ClientMessageType.Content]: ClientContentMessage,
  [ClientMessageType.Tool]: ClientToolMessage,
  [ClientMessageType.Streaming]: ClientStreamingMessage,
  [ClientMessageType.MultiPart]: ClientMultiPartMessage,
  [ClientMessageType.Error]: ClientErrorMessage,
  [ClientMessageType.SubAgent]: SubAgentMessage,
};

export class MessageSerializer {
  serialize(message: ClientMessage): BaseJsonMessage {
    return message.toJSON();
  }

  deserialize(data: BaseJsonMessage): ClientMessage | null {
    try {
      const MessageClass = messageTypeMap[data.type];
      if (!MessageClass) {
        console.warn('Unknown message type:', data.type);
        return null;
      }
      return MessageClass.fromJSON(data as any); // 使用静态方法反序列化
    } catch (error) {
      console.error('Failed to deserialize message:', error);
      return null;
    }
  }
}

import { ClientMessage, ClientMessageType, Role, type BaseJsonMessage, type MessageOptions } from './abstract-message';
import { isArray } from 'lodash';

export interface ToolMessageOptions extends MessageOptions {
  name: string;
  input?: string;
  output?: string;
}

export interface ToolMessageJson extends BaseJsonMessage, Omit<ToolMessageOptions, keyof MessageOptions> {
  name: string;
  input?: string;
  output?: string;
  role: Role;
}

export class ClientToolMessage extends ClientMessage {
  private _name: string;
  private _input?: string;
  private _output?: string;

  static fromJSON(data: ToolMessageJson): ClientToolMessage {
    // 兼容旧版本
    if (!isArray(data.version)) {
      data.version = [data.version, data.version];
    }

    return new ClientToolMessage(data);
  }

  constructor(options: ToolMessageOptions) {
    super(options);

    this._name = options.name;
    this._input = options.input;
    this._output = options.output;
  }

  public toJSON(): ToolMessageJson {
    return {
      ...super.toJSON(),
      name: this._name,
      input: this._input,
      output: this._output,
      role: this.role,
    };
  }

  public copy(): ClientToolMessage {
    const message = new ClientToolMessage({
      ...this.toJSON(),
    });
    message._id = this._id;
    message._createdAt = this._createdAt;
    message._version = [...this._version];
    message.finishReason = this.finishReason;
    return message;
  }

  public get type(): ClientMessageType {
    return ClientMessageType.Tool;
  }

  public get role(): Role {
    return Role.Assistant;
  }

  public get name(): string {
    return this._name;
  }

  public get input(): string | undefined {
    return this._input;
  }

  public set input(input: string) {
    this._input = input;
  }

  public get output(): string | undefined {
    return this._output;
  }

  public set output(output: string) {
    this._output = output;
  }
}

import { uuid } from '@byted-image/lv-bedrock/uuid';
import {
  ClientMessage,
  ClientMessageType,
  type BaseJsonMessage,
  type MessageOptions,
  type Role,
} from './abstract-message';
import type { UserInput } from '@/bam/namespaces/userinput';
import { isArray } from 'lodash';

export interface Attachment {
  key: string;
  name: string;
  bucket: string;
  url: string;
}

export interface ContentMessageOptions extends MessageOptions {
  role: Role;
  content: UserInput;
  attachments?: Attachment[];
  logID?: string;
}

export interface MultiPartMessageJson extends BaseJsonMessage, Omit<ContentMessageOptions, keyof MessageOptions> {
  content: UserInput;
  role: Role;
}

export class ClientMultiPartMessage extends ClientMessage {
  protected _id: string = uuid();
  protected _content: UserInput;
  protected _role: Role;
  protected _logID?: string;

  static fromJSON(data: MultiPartMessageJson): ClientMultiPartMessage {
    // 兼容旧版本
    if (!isArray(data.version)) {
      data.version = [data.version, data.version];
    }

    return new ClientMultiPartMessage(data);
  }

  constructor(options: ContentMessageOptions) {
    super(options);

    this._role = options.role;
    this._content = options.content;
    this._logID = options.logID;
  }

  public copy(): ClientMultiPartMessage {
    const messageJson = this.toJSON();
    const message = new ClientMultiPartMessage({
      ...messageJson,
    });
    message._id = this._id;
    message._createdAt = this._createdAt;
    message._version = [...this._version];
    message.finishReason = this.finishReason;
    return message;
  }

  public toJSON(): MultiPartMessageJson {
    return {
      ...super.toJSON(),
      content: this._content,
      role: this._role,
      logID: this._logID,
    };
  }

  get type(): ClientMessageType {
    return ClientMessageType.MultiPart;
  }

  get role(): Role {
    return this._role;
  }

  get content(): UserInput {
    return this._content;
  }

  set content(content: UserInput) {
    this._content = content;
  }

  get logID(): string | undefined {
    return this._logID;
  }
  set logID(id) {
    this._logID = id;
  }

  get textContent(): string {
    // 根据 prompt.items 结构，拼接所有 type 为 0 的 text.text 字段
    if (this._content && typeof this._content === 'object' && Array.isArray(this._content.prompt?.items)) {
      // 兼容 items 可能为 undefined
      return this._content.prompt.items
        .filter((item: any) => item?.[0]?.type === 0 && item?.[0]?.text?.text)
        .map((item: any) => item[0].text.text)
        .join('\n');
    }
    // 兜底：如果 content 是字符串，直接返回
    if (typeof this._content === 'string') {
      return this._content;
    }
    // 其他情况返回空字符串
    return '';
  }
}

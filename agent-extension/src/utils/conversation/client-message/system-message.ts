import { uuid } from '@byted-image/lv-bedrock/uuid';
import { BaseJsonMessage, ClientMessage, ClientMessageType, Role, type MessageOptions } from './abstract-message';
import { isArray } from 'lodash';

export interface ContentMessageOptions extends MessageOptions {
  content: string;
}

export interface ContentMessageJson extends BaseJsonMessage, Omit<ContentMessageOptions, keyof MessageOptions> {
  content: string;
  role: Role;
}

export class SystemMessage extends ClientMessage {
  protected _id: string = uuid();
  protected _content: string;
  protected _role: Role = Role.System;

  static fromJSON(data: ContentMessageJson): SystemMessage {
    // 兼容旧版本
    if (!isArray(data.version)) {
      data.version = [data.version, data.version];
    }

    return new SystemMessage(data);
  }

  constructor(options: ContentMessageOptions) {
    super(options);
    this._content = options.content;
  }

  public toJSON(): ContentMessageJson {
    return {
      ...super.toJSON(),
      content: this._content,
      role: this._role,
    };
  }

  public copy(): SystemMessage {
    const message = new SystemMessage({
      ...this.toJSON(),
    });
    message._id = this._id;
    message._createdAt = this._createdAt;
    message._version = [...this._version];
    message.finishReason = this.finishReason;
    return message;
  }

  get type(): ClientMessageType {
    return ClientMessageType.Content;
  }

  get role(): Role {
    return this._role;
  }

  get content(): string {
    return this._content;
  }

  set content(content: string) {
    this._content = content;
  }
}

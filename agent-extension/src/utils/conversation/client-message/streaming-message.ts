import { ClientMessageType, Role } from './abstract-message';
import { ClientContentMessage, type ContentMessageJson, type ContentMessageOptions } from './content-message';
import { ContentType } from '@/bam/namespaces/message';
import { isArray } from 'lodash';

export interface StreamingMessageOptions extends Omit<ContentMessageOptions, 'role'> {
  role: Role;
}

export interface StreamingMessageJson
  extends ContentMessageJson,
    Omit<StreamingMessageOptions, keyof ContentMessageJson> {
  role: Role;
  renderContent: string;
}

interface Chunk {
  content: string;
  type: ContentType;
}

export class ClientStreamingMessage extends ClientContentMessage {
  private _chunkMap = new Map<string | number, Chunk[]>();
  protected _renderContent = '';

  static fromJSON(data: StreamingMessageJson): ClientStreamingMessage {
    // 兼容旧版本
    if (!isArray(data.version)) {
      data.version = [data.version, data.version];
    }

    return new ClientStreamingMessage(data);
  }

  public copy(): ClientStreamingMessage {
    const messageJson = this.toJSON();
    const message = new ClientStreamingMessage({
      ...messageJson,
    });

    message._chunkMap = new Map(this._chunkMap);
    message._attachments = [...this._attachments];
    message._id = this._id;
    message._createdAt = this._createdAt;
    message._version = [...this._version];
    message.finishReason = this.finishReason;

    return message;
  }

  constructor(options: Omit<ContentMessageOptions, 'role'>) {
    super({
      ...options,
      role: Role.Assistant,
    });
  }

  public toJSON(): StreamingMessageJson {
    return {
      ...super.toJSON(),
      role: this.role,
      renderContent: this.renderContent,
    };
  }

  public get role() {
    return Role.Assistant;
  }

  public get type(): ClientMessageType {
    return ClientMessageType.Streaming;
  }

  public get content(): string {
    if (this._content === '') {
      return '::: waiting';
    }
    return this._content;
  }

  public set content(content: string) {
    this._content = content;
  }

  public get renderContent(): string {
    return this._renderContent || this._content;
  }

  public appendChunk(chunk: Chunk, version: string | number) {
    // 处理每个 content，按 version 去重
    // if (!this._chunkMap.has(version)) {
    //   this._chunkMap.set(version, '');
    // }
    const prevChunks = this._chunkMap.get(version) ?? [];
    this._chunkMap.set(version, [...prevChunks, chunk]); // 同一个version按照调用顺序追加

    // 按 version 排序拼接内容
    const orderedVersions = Array.from(this._chunkMap.keys()).sort((a, b) => {
      // version 可能为 string 或 number，统一转 number 排序
      return Number(a) - Number(b);
    });
    let fullContent = '';
    let renderContent = '';
    for (const [idx, v] of orderedVersions.entries()) {
      const chunks = this._chunkMap.get(v) ?? [];
      fullContent += chunks.map((chunk) => chunk.content).join('');
      renderContent += chunks
        .map((chunk) =>
          chunk.type === ContentType.Reasoning
            ? `${idx === 0 ? '> ' : ''}${chunk.content.replace(/\n/g, '\n> ')}`
            : chunk.content,
        )
        .join('');
    }

    // 更新 content
    this._content = fullContent;
    this._renderContent = renderContent;
  }
}

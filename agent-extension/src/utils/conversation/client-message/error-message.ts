import { uuid } from '@byted-image/lv-bedrock/uuid';
import { ClientMessage, ClientMessageType, Role, type BaseJsonMessage, type MessageOptions } from './abstract-message';
import { isArray } from 'lodash';

export interface ErrorMessageOptions extends MessageOptions {
  code: number;
  message: string;
}

export interface ErrorMessageJson extends BaseJsonMessage, Omit<ErrorMessageOptions, keyof MessageOptions> {
  code: number;
  message: string;
}

export class ClientErrorMessage extends ClientMessage {
  protected _id: string = uuid();
  protected _errCode: number;
  protected _errMsg: string;

  static fromJSON(data: ErrorMessageJson): ClientErrorMessage {
    // 兼容旧版本
    if (!isArray(data.version)) {
      data.version = [-1, -1];
    }

    return new ClientErrorMessage(data);
  }

  constructor(options: ErrorMessageOptions) {
    super({
      ...options,
      version: [-1, -1],
    });

    this._errCode = options.code;
    this._errMsg = options.message;
  }

  public copy(): ClientErrorMessage {
    const messageJson = this.toJSON();
    const message = new ClientErrorMessage({
      ...messageJson,
    });

    message._id = this._id;
    message._createdAt = this._createdAt;
    message._version = [...this._version];
    message.finishReason = this.finishReason;

    return message;
  }

  get role() {
    return Role.Assistant;
  }

  get code() {
    return this._errCode;
  }

  get message() {
    return this._errMsg;
  }

  public toJSON(): ErrorMessageJson {
    return {
      ...super.toJSON(),
      code: this._errCode,
      message: this._errMsg,
    };
  }

  get type(): ClientMessageType {
    return ClientMessageType.Error;
  }
}

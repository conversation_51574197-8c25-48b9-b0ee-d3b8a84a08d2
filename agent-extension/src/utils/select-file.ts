import { window, workspace } from 'vscode';
import path from 'path';

/**
 * 上传文件方法
 * @param filters 文件过滤器数组，例如 ['png', 'jpg', 'jpeg']
 * @returns 文件内容的Promise
 */
async function uploadFiles(
  filters: Record<string, string[]>,
): Promise<{ name: string; path: string; content: Uint8Array }[]> {
  const options = {
    canSelectMany: false,
    filters: filters,
  };

  const result = await window.showOpenDialog(options);
  const resultList: { path: string; content: Uint8Array; name: string }[] = [];

  if (result) {
    for (const file of result) {
      const filePath = file.fsPath;
      console.log('upload filePath', filePath);
      try {
        const content = await workspace.fs.readFile(file);
        const fileName = filePath.split('/').pop() || filePath.split('\\').pop();
        resultList.push({
          name: fileName ?? '',
          path: filePath,
          content,
        });
      } catch (error) {
        // @ts-ignore
        window.showErrorMessage(`Failed to read file: ${error.message}`);
      }
    }
  }
  return resultList;
}

export const selectLocalImage = async () => {
  const resultList = await uploadFiles({
    Images: ['png', 'jpg', 'jpeg', 'gif', 'webp'],
  });

  const imageList: { path: string; name: string; src: string }[] = [];
  for (const result of resultList) {
    const { path: filePath, name } = result;
    // 获取文件扩展名
    const ext = path.extname(filePath).toLowerCase().substring(1);

    // 转换为base64
    const base64 = Buffer.from(result.content).toString('base64');

    // 构建完整的base64字符串（包含MIME类型）
    const mimeType = `image/${ext === 'jpg' ? 'jpeg' : ext}`;
    const base64String = `data:${mimeType};base64,${base64}`;
    imageList.push({
      path: filePath,
      name: name,
      src: base64String,
    });
  }

  return imageList;
};

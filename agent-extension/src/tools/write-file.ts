import * as path from 'node:path';
import { setTimeout as setTimeoutPromise } from 'node:timers/promises';

import { IDiffViewService } from '../services/diff-view/diff-view-service.interface';
import { IConversationHistoryService } from '../services/conversation/history/conversation-history.interface';
import { IRpcService } from '../services/rpc/rpc-service.interface';
import { fileExistsAtPath, formatRelativePath } from '@utils/fs';
import { cwd } from '../utils/env/system';
import { ToolResult, ToolResultStatus, Tool } from './base';
import { DataType, type ToolInfo } from '@/bam/namespaces/agentclient';
import { fileEditWithoutUserChanges } from '@/utils/tools';

import type { Int64 } from '@/bam';

export class WriteFileTool extends Tool {
  constructor(
    @IDiffViewService private readonly _diffViewService: IDiffViewService,
    @IConversationHistoryService private readonly _historyService: IConversationHistoryService,
    @IRpcService private readonly _rpcService: IRpcService,
  ) {
    super();
  }

  getInfo(): ToolInfo {
    return {
      name: 'write_file',
      desc: "Request to write content to a file at the specified path. If the file exists, it will be overwritten with the provided content. If the file doesn't exist, it will be created. This tool will automatically create any directories needed to write the file.",
      params: {
        explanation: {
          type: DataType.String,
          desc: 'One sentence explanation as to why this tool is being used, and how it contributes to the goal.',
          required: false,
        },
        path: {
          type: DataType.String,
          desc: `The path of the file to modify (relative to the current working directory ${cwd.toPosix()})`,
          required: true,
        },
        content: {
          type: DataType.String,
          desc: "The content to write to the file. ALWAYS provide the COMPLETE intended content of the file, without any truncation or omissions. You MUST include ALL parts of the file, even if they haven't been modified.",
          required: true,
        },
      },
    };
  }

  async run(inputStr: string, _toolId: string, _conversationId: string, _version: Int64): Promise<ToolResult> {
    try {
      const input = JSON.parse(inputStr);
      const relPath = formatRelativePath(input.path as string, cwd); // 相对路径
      const absolutePath = path.resolve(cwd, relPath);
      const content = input.content as string;
      if (!content || !relPath) {
        return new ToolResult(ToolResultStatus.AutoRunError, 'Invalid parameters: content or path is empty.');
      }
      let newContent = content;

      // fix content
      // pre-processing newContent for cases where weaker models might add artifacts like markdown codeblock markers (deepseek/llama) or extra escape characters (gemini)
      if (newContent.startsWith('```')) {
        // this handles cases where it includes language specifiers like ```python ```js
        newContent = newContent.split('\n').slice(1).join('\n').trim();
      }
      if (newContent.endsWith('```')) {
        newContent = newContent.split('\n').slice(0, -1).join('\n').trim();
      }

      // TODO: handle claude issue
      // if (!this.api.getModel().id.includes("claude")) {
      //   // it seems not just llama models are doing this, but also gemini and potentially others
      //   newContent = fixModelHtmlEscaping(newContent)
      //   newContent = removeInvalidChars(newContent)
      // }

      newContent = newContent.trimEnd(); // remove any trailing newlines, since it's automatically inserted by the editor

      const fileExists = await fileExistsAtPath(absolutePath);
      this._diffViewService.editType = fileExists ? 'modify' : 'create';
      await this._diffViewService.open(relPath);
      await this._diffViewService.update(newContent, true);
      await setTimeoutPromise(300); // wait for diff view to update
      this._diffViewService.scrollToFirstDiff();

      // auto save file
      const { newProblemsMessage } = await this._diffViewService.saveChanges(true);

      // 更新会话信息，记录文件操作
      await this._diffViewService.updateConversationDiffInfo({
        conversationId: _conversationId,
        fileName: path.basename(relPath),
        fullPath: path.resolve(cwd, relPath),
        operation: this._diffViewService.editType,
        actions: [],
        addedLines: 0,
        deletedLines: 0,
        historyService: this._historyService,
        rpcService: this._rpcService,
        toolName: 'WriteFileTool',
      });

      return new ToolResult(ToolResultStatus.AutoRunSuccess, fileEditWithoutUserChanges(relPath, newProblemsMessage));
    } catch (error) {
      console.error(`${Date.now()} Write file error:`, error);
      // 返回错误信息字符串
      if (error instanceof Error) {
        return new ToolResult(ToolResultStatus.AutoRunError, `${error.message}`);
      }
      return new ToolResult(ToolResultStatus.AutoRunError, `${String(error)}`);
    }
  }
}

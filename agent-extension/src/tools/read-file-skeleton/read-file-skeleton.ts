import fs from 'node:fs/promises';
import path from 'node:path';
import type { Int64 } from '@/bam';
import { DataType, type ToolInfo } from '@/bam/namespaces/agentclient';
import { formatRelativePath } from '@/utils/fs';
import { cwd } from '../../utils/env/system';
import { Tool, ToolResult, ToolResultStatus } from '../base';
import { IWorkspaceService } from '@/services/workspace/workspace.interface';
import { loadRequiredLanguageParsers } from './utils/language-parser';
import { toolDesc, toolName } from './tool-info';

interface ReadFileSkeletonInput {
  path: string;
  explanation?: string;
}

interface ParsedInput {
  path: string;
}

export class ReadFileSkeletonTool extends Tool {
  constructor(@IWorkspaceService private readonly _workspaceService: IWorkspaceService) {
    super();
  }

  getInfo(): ToolInfo {
    return {
      name: toolName,
      desc: toolDesc,
      params: {
        explanation: {
          type: DataType.String,
          desc: 'One sentence explanation as to why this tool is being used, and how it contributes to the goal.',
          required: false,
        },
        path: {
          type: DataType.String,
          desc: `The path to the file to read (relative to the current working directory ${cwd.toPosix()}.`,
          required: true,
        },
      },
    };
  }

  private validateInput(input: ReadFileSkeletonInput): ParsedInput {
    // 验证path参数
    if (!input.path || typeof input.path !== 'string') {
      throw new Error('Path parameter is required and must be a string');
    }

    const filePath = input.path.trim();
    if (filePath.length === 0) {
      throw new Error('Path cannot be empty');
    }

    return { path: filePath };
  }

  private async checkFileAccess(filePath: string): Promise<void> {
    try {
      const stats = await fs.stat(filePath);

      if (!stats.isFile()) {
        throw new Error('Path does not point to a file');
      }
    } catch (error) {
      if (error instanceof Error && (error as NodeJS.ErrnoException).code === 'ENOENT') {
        throw new Error('File does not exist');
      }
      throw error;
    }
  }

  async run(inputStr: string, _toolId: string, _conversationId: string, _version: Int64): Promise<ToolResult> {
    try {
      // 解析输入
      let input: ReadFileSkeletonInput;
      try {
        input = JSON.parse(inputStr);
      } catch (parseError) {
        return new ToolResult(
          ToolResultStatus.AutoRunError,
          `Invalid JSON input: ${parseError instanceof Error ? parseError.message : String(parseError)}`,
        );
      }

      // 验证输入参数
      const { path: filePath } = this.validateInput(input);

      // 检查文件是否被忽略
      const isIgnored = await this._workspaceService.codinIgnoreManager.isIgnored(filePath);
      if (isIgnored) {
        return new ToolResult(ToolResultStatus.AutoRunError, 'Access denied: File is ignored by user.');
      }

      // 处理路径（包含安全检查）
      let relPath: string;
      try {
        relPath = formatRelativePath(filePath, cwd);
      } catch (error) {
        if (error instanceof Error && error.message.includes('Access denied')) {
          return new ToolResult(ToolResultStatus.AutoRunError, error.message);
        }
        throw error;
      }
      const absolutePath = path.resolve(cwd, relPath);

      // 确保路径在允许的范围内
      if (!absolutePath.startsWith(cwd)) {
        return new ToolResult(
          ToolResultStatus.AutoRunError,
          `Access denied: Path is outside the allowed directory (allowed directory: ${cwd})`,
        );
      }

      // 检查文件访问权限
      await this.checkFileAccess(absolutePath);

      // 读取文件内容
      const content = await loadRequiredLanguageParsers(absolutePath);

      console.info('ReadFileTool: read file content', absolutePath, content);

      return new ToolResult(ToolResultStatus.AutoRunSuccess, String(content));
    } catch (error) {
      // 返回详细的错误信息
      if (error instanceof Error) {
        return new ToolResult(ToolResultStatus.AutoRunError, `${error.message}`);
      }
      return new ToolResult(ToolResultStatus.AutoRunError, `${String(error)}`);
    }
  }
}

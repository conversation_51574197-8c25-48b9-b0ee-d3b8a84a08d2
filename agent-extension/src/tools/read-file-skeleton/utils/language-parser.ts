import * as path from 'node:path';
import * as fs from 'node:fs/promises';
import Parser from 'web-tree-sitter';
import {
  javascriptQuery,
  typescriptQ<PERSON>y,
  pythonQuery,
  rustQ<PERSON>y,
  goQuery,
  cppQuery,
  cQuery,
  csharpQuery,
  rubyQuery,
  javaQuery,
  phpQ<PERSON>y,
  swiftQ<PERSON>y,
  kotlinQuery,
} from './queries';

export interface LanguageParser {
  [key: string]: {
    parser: Parser;
    query: Parser.Query;
  };
}

async function loadLanguage(langName: string) {
  // WASM files are in the out directory
  return await Parser.Language.load(path.join(__dirname, `tree-sitter-${langName}.wasm`));
}

let isParserInitialized = false;

async function initializeParser() {
  if (!isParserInitialized) {
    await Parser.init();
    isParserInitialized = true;
  }
}

/*
Using node bindings for tree-sitter is problematic in vscode extensions 
because of incompatibility with electron. Going the .wasm route has the 
advantage of not having to build for multiple architectures.

We use web-tree-sitter and tree-sitter-wasms which provides auto-updating prebuilt WASM binaries for tree-sitter's language parsers.

This function loads WASM modules for relevant language parsers based on input files:
1. Extracts unique file extensions
2. Maps extensions to language names
3. Loads corresponding WASM files (containing grammar rules)
4. Uses WASM modules to initialize tree-sitter parsers

This approach optimizes performance by loading only necessary parsers once for all relevant files.

Sources:
- https://github.com/tree-sitter/node-tree-sitter/issues/169
- https://github.com/tree-sitter/node-tree-sitter/issues/168
- https://github.com/Gregoor/tree-sitter-wasms/blob/main/README.md
- https://github.com/tree-sitter/tree-sitter/blob/master/lib/binding_web/README.md
- https://github.com/tree-sitter/tree-sitter/blob/master/lib/binding_web/test/query-test.js
*/
export async function loadRequiredLanguageParsers(fileToParse: string): Promise<string | null> {
  await initializeParser();
  const ext = path.extname(fileToParse).toLowerCase().slice(1);
  const parsers: LanguageParser = {};
  let language: Parser.Language;
  let query: Parser.Query;
  switch (ext) {
    case 'js':
    case 'jsx':
      language = await loadLanguage('javascript');
      query = language.query(javascriptQuery);
      break;
    case 'ts':
      language = await loadLanguage('typescript');
      query = language.query(typescriptQuery);
      break;
    case 'tsx':
      language = await loadLanguage('tsx');
      query = language.query(typescriptQuery);
      break;
    case 'py':
      language = await loadLanguage('python');
      query = language.query(pythonQuery);
      break;
    case 'rs':
      language = await loadLanguage('rust');
      query = language.query(rustQuery);
      break;
    case 'go':
      language = await loadLanguage('go');
      query = language.query(goQuery);
      break;
    case 'cpp':
    case 'hpp':
      language = await loadLanguage('cpp');
      query = language.query(cppQuery);
      break;
    case 'c':
    case 'h':
      language = await loadLanguage('c');
      query = language.query(cQuery);
      break;
    case 'cs':
      language = await loadLanguage('c_sharp');
      query = language.query(csharpQuery);
      break;
    case 'rb':
      language = await loadLanguage('ruby');
      query = language.query(rubyQuery);
      break;
    case 'java':
      language = await loadLanguage('java');
      query = language.query(javaQuery);
      break;
    case 'php':
      language = await loadLanguage('php');
      query = language.query(phpQuery);
      break;
    case 'swift':
      language = await loadLanguage('swift');
      query = language.query(swiftQuery);
      break;
    case 'kt':
      language = await loadLanguage('kotlin');
      query = language.query(kotlinQuery);
      break;
    default:
      throw new Error(`Unsupported language: ${ext}`);
  }
  const parser = new Parser();
  parser.setLanguage(language);
  parsers[ext] = { parser, query };

  const definitions = await parseFile(fileToParse, parsers);

  console.info('Parsing complete', fileToParse, parsers);

  return definitions;
}

async function parseFile(filePath: string, languageParsers: LanguageParser): Promise<string | null> {
  const fileContent = await fs.readFile(filePath, 'utf8');
  const ext = path.extname(filePath).toLowerCase().slice(1);

  const { parser, query } = languageParsers[ext] || {};
  if (!parser || !query) {
    return `Unsupported file type: ${filePath}`;
  }

  let formattedOutput = '';

  try {
    // Parse the file content into an Abstract Syntax Tree (AST), a tree-like representation of the code
    const tree = parser.parse(fileContent);

    // Apply the query to the AST and get the captures
    // Captures are specific parts of the AST that match our query patterns, each capture represents a node in the AST that we're interested in.
    const captures = query.captures(tree.rootNode);

    // Sort captures by their start position
    captures.sort((a, b) => a.node.startPosition.row - b.node.startPosition.row);

    // Split the file content into individual lines
    const lines = fileContent.split('\n');

    captures.forEach((capture) => {
      const { node, name } = capture;
      // Get the start and end lines of the current AST node
      const startLine = node.startPosition.row;
      // Once we've retrieved the nodes we care about through the language query, we filter for lines with definition names only.
      // name.startsWith("name.reference.") > refs can be used for ranking purposes, but we don't need them for the output
      // previously we did `name.startsWith("name.definition.")` but this was too strict and excluded some relevant definitions

      // Only add the first line of the definition
      // query captures includes the definition name and the definition implementation, but we only want the name (I found discrepancies in the naming structure for various languages, i.e. javascript names would be 'name' and typescript names would be 'name.definition)
      // For import/export statements, we want the whole statement even if there's no name capture
      if ((name.includes('name.definition') || name.includes('name')) && lines[startLine]) {
        formattedOutput += `${startLine + 1}→${lines[startLine]}\n`;
      }
    });
  } catch (error) {
    console.log(`Error parsing file: ${error}\n`);
  }

  if (formattedOutput.length > 0) {
    return formattedOutput;
  }
  return null;
}

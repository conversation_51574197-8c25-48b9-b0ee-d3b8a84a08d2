/*
- function signatures and declarations
- method signatures and definitions
- abstract method signatures
- class declarations (including abstract classes)
- module declarations
- variable declarations (including arrow functions)
- interface declarations
- type alias declarations
- enum declarations
*/
export default `
(function_signature
  name: (identifier) @name.definition.function) @definition.function

(method_signature
  name: (property_identifier) @name.definition.method) @definition.method

(abstract_method_signature
  name: (property_identifier) @name.definition.method) @definition.method

(abstract_class_declaration
  name: (type_identifier) @name.definition.class) @definition.class

(module
  name: (identifier) @name.definition.module) @definition.module

(function_declaration
  name: (identifier) @name.definition.function) @definition.function

(method_definition
  name: (property_identifier) @name.definition.method) @definition.method

(class_declaration
  name: (type_identifier) @name.definition.class) @definition.class

(lexical_declaration
  (variable_declarator
    name: (identifier) @name.definition.variable
    value: (arrow_function))) @definition.variable

(lexical_declaration
  (variable_declarator
    name: (identifier) @name.definition.variable
    value: (function_expression))) @definition.variable

(variable_declaration
  (variable_declarator
    name: (identifier) @name.definition.variable
    value: (arrow_function))) @definition.variable

(variable_declaration
  (variable_declarator
    name: (identifier) @name.definition.variable
    value: (function_expression))) @definition.variable

(interface_declaration
  name: (type_identifier) @name.definition.interface) @definition.interface

(type_alias_declaration
  name: (type_identifier) @name.definition.type) @definition.type

(enum_declaration
  name: (identifier) @name.definition.enum) @definition.enum
`;

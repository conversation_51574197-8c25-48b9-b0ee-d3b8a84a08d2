import type { Int64 } from '@/bam';
import { DataType, type ToolInfo } from '@/bam/namespaces/agentclient';
import { Tool, ToolResult, ToolResultStatus } from '../base';
import { toolName, toolDesc } from './tool-info';
import { INetworkClientFactoryService } from '@/services/network-client-factory/network-client-factory-service.interface';
import { API_PREFIX } from '@/const';
import { IFileLoggerService } from '@/services/file-logger/file-logger-service.interface';
import type { NetworkClientInstance } from '@byted-image/lv-bedrock/network';

interface GetUsageRequest {
  library?: string;
  version?: string;
  query?: string;
}

interface GetUsageResponse {
  usage?: string;
}

export class GetUsageTool extends Tool {
  private readonly _networkClient: NetworkClientInstance;

  constructor(
    @INetworkClientFactoryService networkClientFactoryService: INetworkClientFactoryService,
    @IFileLoggerService private readonly _fileLoggerService: IFileLoggerService,
  ) {
    super();
    this._networkClient = networkClientFactoryService.build({
      validateStatus: () => true,
    });
  }

  getInfo(): ToolInfo {
    return {
      name: toolName,
      desc: toolDesc,
      params: {
        library: {
          type: DataType.String,
          desc: 'The library name',
          required: true,
        },
        version: {
          type: DataType.String,
          desc: 'The library version',
          required: false,
        },
        query: {
          type: DataType.String,
          desc: 'The query to search for, can be a natural language query or a specific function name',
          required: true,
        },
      },
    };
  }

  async run(inputStr: string, _toolId: string, _conversationId: string, _version: Int64): Promise<ToolResult> {
    try {
      const input = JSON.parse(inputStr);
      const request: GetUsageRequest = {
        library: input.library,
        version: input.version,
        query: input.query,
      };

      this._fileLoggerService.info(`[GetUsageTool] request: ${JSON.stringify(request)}`);

      const resp = await this._networkClient.post(`${API_PREFIX}/codesearch/get_usage`, request);

      this._fileLoggerService.info(`[GetUsageTool] response code=${resp.data.code} logid=${resp.data.log_id}`);

      if (resp.data.code !== 0) {
        return new ToolResult(ToolResultStatus.AutoRunError, `[${resp.data.code}] ${resp.data.message}`);
      }

      const usageResponse: GetUsageResponse = resp.data.data;
      return new ToolResult(ToolResultStatus.AutoRunSuccess, usageResponse.usage ?? '(No result)');
    } catch (error) {
      this._fileLoggerService.error(`[GetUsageTool] error: ${error}`);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return new ToolResult(ToolResultStatus.AutoRunError, `Failed to get usage: ${errorMessage}`);
    }
  }
}

import fs from 'node:fs/promises';
import path from 'node:path';
import type { Int64 } from '@/bam';
import { DataType, type ToolInfo } from '@/bam/namespaces/agentclient';
import { formatRelativePath } from '@/utils/fs';
import { cwd } from '../../utils/env/system';
import { Tool, ToolResult, ToolResultStatus } from '../base';
import { IWorkspaceService } from '@/services/workspace/workspace.interface';
import { MAX_LINE_LENGTH, MAX_LINES, toolDesc, toolName } from './tool-info';

export interface ReadFileInput {
  path: string;
  start_line: number;
  end_line: number;
  should_read_entire_file: boolean;
  explanation?: string;
}

interface ParsedInput {
  path: string;
  startLine: number;
  endLine: number;
}

export class ReadFileTool extends Tool {
  constructor(@IWorkspaceService private readonly _workspaceService: IWorkspaceService) {
    super();
  }

  getInfo(): ToolInfo {
    return {
      name: toolName,
      desc: toolDesc,
      params: {
        explanation: {
          type: DataType.String,
          desc: 'One sentence explanation as to why this tool is being used, and how it contributes to the goal.',
          required: false,
        },
        path: {
          type: DataType.String,
          desc: `The path to the file to read (relative to the current working directory ${cwd.toPosix()}.`,
          required: true,
        },
        start_line: {
          type: DataType.Integer,
          desc: 'The line index to start reading from (inclusive, 1-based indexing).',
          required: true,
        },
        end_line: {
          type: DataType.Integer,
          desc: 'The line index to end reading at (inclusive, 1-based indexing).',
          required: true,
        },
        should_read_entire_file: {
          desc: 'Whether to read the entire file. Defaults to false.',
          type: DataType.Boolean,
          required: true,
        },
      },
    };
  }

  private validateInput(input: ReadFileInput): ParsedInput {
    // 验证path参数
    if (!input.path || typeof input.path !== 'string') {
      throw new Error('Path parameter is required and must be a string');
    }

    const filePath = input.path.trim();
    if (filePath.length === 0) {
      throw new Error('Path cannot be empty');
    }

    // 如果 should_read_entire_file 为 true，则忽略行号参数
    if (input.should_read_entire_file) {
      return { path: filePath, startLine: 1, endLine: Number.MAX_SAFE_INTEGER };
    }

    // 如果不读取整个文件，则验证行号参数
    if (input.start_line === undefined || input.end_line === undefined) {
      throw new Error(
        'Parameter start_line and parameter end_line must be provided together when should_read_entire_file is false.',
      );
    }

    // 验证行号参数
    const startLine = Number(input.start_line);
    const endLine = Number(input.end_line);

    if (!Number.isInteger(startLine) || startLine < 1) {
      throw new Error('start_line must be a positive integer');
    }

    if (!Number.isInteger(endLine) || endLine < 1) {
      throw new Error('end_line must be a positive integer');
    }

    if (endLine - startLine > MAX_LINES - 1) {
      throw new Error(`The number of lines to read cannot be greater than ${MAX_LINES}`);
    }

    if (startLine > endLine) {
      throw new Error('start_line cannot be greater than end_line');
    }

    return { path: filePath, startLine, endLine };
  }

  private async checkFileAccess(filePath: string): Promise<void> {
    try {
      const stats = await fs.stat(filePath);

      if (!stats.isFile()) {
        throw new Error('Path does not point to a file');
      }
    } catch (error) {
      if (error instanceof Error && (error as NodeJS.ErrnoException).code === 'ENOENT') {
        throw new Error('File does not exist');
      }
      throw error;
    }
  }

  async run(inputStr: string, _toolId: string, _conversationId: string, _version: Int64): Promise<ToolResult> {
    try {
      // 解析输入
      let input: ReadFileInput;
      try {
        input = JSON.parse(inputStr);
      } catch (parseError) {
        return new ToolResult(
          ToolResultStatus.AutoRunError,
          `Invalid JSON input: ${parseError instanceof Error ? parseError.message : String(parseError)}`,
        );
      }

      // 验证输入参数
      const { path: filePath, startLine, endLine } = this.validateInput(input);

      // 检查文件是否被忽略
      const isIgnored = await this._workspaceService.codinIgnoreManager.isIgnored(filePath);
      if (isIgnored) {
        return new ToolResult(ToolResultStatus.AutoRunError, 'Access denied: File is ignored by user.');
      }

      // 处理路径（包含安全检查）
      let relPath: string;
      try {
        relPath = formatRelativePath(filePath, cwd);
      } catch (error) {
        if (error instanceof Error && error.message.includes('Access denied')) {
          return new ToolResult(ToolResultStatus.AutoRunError, error.message);
        }
        throw error;
      }
      const absolutePath = path.resolve(cwd, relPath);

      // 确保路径在允许的范围内
      if (!absolutePath.startsWith(cwd)) {
        return new ToolResult(
          ToolResultStatus.AutoRunError,
          `Access denied: Path is outside the allowed directory (allowed directory: ${cwd})`,
        );
      }

      // 检查文件访问权限
      await this.checkFileAccess(absolutePath);

      // 读取文件内容
      const content = await fs.readFile(absolutePath, 'utf8');

      // 处理行号范围
      const lines = content.split('\n');

      // 处理行长度限制
      for (let i = 0; i < lines.length; i++) {
        const lineLength = lines[i].length;
        if (lineLength > MAX_LINE_LENGTH) {
          lines[i] =
            `${lines[i].substring(0, MAX_LINE_LENGTH + 1)} <${lineLength - MAX_LINE_LENGTH} more characters...>`;
        }
      }

      // 如果 should_read_entire_file 为 true，返回整个文件内容
      if (input.should_read_entire_file) {
        return new ToolResult(ToolResultStatus.AutoRunSuccess, lines.join('\n'), 100_000);
      }

      // 否则按照指定的行号范围读取
      const start = Math.max(0, startLine - 1);
      const end = Math.min(lines.length, endLine);

      if (start >= lines.length) {
        return new ToolResult(
          ToolResultStatus.AutoRunError,
          `Start line ${startLine} is beyond the end of the file (${lines.length} lines)`,
        );
      }

      const selectedLines = lines.slice(start, end);
      const reminder = `The total number of lines in the file is ${lines.length}.`;
      return new ToolResult(ToolResultStatus.AutoRunSuccess, selectedLines.join('\n'), 30_000, reminder);
    } catch (error) {
      // 返回详细的错误信息
      if (error instanceof Error) {
        return new ToolResult(ToolResultStatus.AutoRunError, `${error.message}`);
      }
      return new ToolResult(ToolResultStatus.AutoRunError, `${String(error)}`);
    }
  }
}

export const toolName = 'read_file';

// 最大文件大小限制 (10MB)
export const MAX_FILE_SIZE = 10 * 1024 * 1024;

// 最大行数限制
export const MAX_LINES = 500;

// 最大行长度限制
export const MAX_LINE_LENGTH = 1500;

export const toolDesc = `Read the contents of a file. the output of this tool call will be the 1-indexed file contents from start_line to end_line, together with a summary of the lines outside start_line and end_line.
Note:
1) If should_read_entire_file is false, this call can view at most ${MAX_LINES} lines at a time.
2) Can view at most ${MAX_LINE_LENGTH} characters per line.

When using this tool to gather information, it's your responsibility to ensure you have the COMPLETE context. Specifically, each time you call this command you should:
1) Assess if the contents you viewed are sufficient to proceed with your task.
2) Take note of where there are lines not shown.
3) If the file contents you have viewed are insufficient, and you suspect they may be in lines not shown, proactively call the tool again to view those lines.
4) When in doubt, call this tool again to gather more information. Remember that partial file views may miss critical dependencies, imports, or functionality.

In some cases, if reading a range of lines is not enough, you may choose to read the entire file.
If should_read_entire_file is true, the tool will ignore start_line and end_line, and read the entire file instead.
Reading entire files is often wasteful and slow, especially for large files (i.e. more than a few hundred lines). So you should use this option sparingly.
Reading the entire file is not allowed in most cases. You are only allowed to read the entire file if it has been edited or manually attached to the conversation by the user.`;

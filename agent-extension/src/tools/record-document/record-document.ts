import fs from 'node:fs/promises';
import path from 'node:path';
import { cwd } from '../../utils/env/system';
import { ToolResult, ToolResultStatus, Tool } from '../base';
import { DataType, type ToolInfo } from '@/bam/namespaces/agentclient';
import * as vscode from 'vscode';
import documentInteraction from './document-interaction';
import { toolDesc, toolName } from './tool-info';
import { diffLines, type Change } from 'diff';
import type { IDisposable } from '@byted-image/lv-bedrock/dispose';
import type { Int64 } from '@/bam';
import { IStorageService } from '@/services/storage/storage-service.interface';
import { isTrue } from '@/utils';
import { CHAT_SETTINGS } from '@/constants/storage';
import type { ChatSettings } from '@/types/settings';

// 通用的记录文档工具，用于记录文档内容，并等待用户确认
export class RecordDocumentTool extends Tool {
  constructor(@IStorageService private readonly _storageService: IStorageService) {
    super();
  }

  getInfo(): ToolInfo {
    return {
      name: toolName,
      desc: toolDesc,
      params: {
        explanation: {
          type: DataType.String,
          desc: 'One sentence explanation as to why this tool is being used, and how it contributes to the goal.',
          required: false,
        },
        path: {
          type: DataType.String,
          desc: `The relative path of the file to record the document, like ".codin/specs/{FEATURE_NAME}/{DOCUMENT_NAME}.md" (relative to the current working directory ${cwd.toPosix()})`,
          required: true,
        },
        document: {
          type: DataType.String,
          desc: 'Document content.',
          required: true,
        },
        documentOverview: {
          type: DataType.String,
          desc: 'Short summary of the content of the document. Be concise and meaningful, for example, it should describe the approaches a plan will use to implement the feature. You should avoid using markdown headings unless it is necessary.',
          required: true,
        },
      },
    };
  }

  async run(inputStr: string, toolId: string, _conversationId: string, _version: Int64): Promise<ToolResult> {
    try {
      const input = JSON.parse(inputStr);

      const inputPath = input.path as string;

      const document = input.document as string;

      // 使用 cwd 来处理相对路径
      const resolvedPath = path.isAbsolute(inputPath) ? inputPath : path.join(cwd, inputPath);

      // 确保目录存在，如果不存在则创建
      const dir = path.dirname(resolvedPath);
      await fs.mkdir(dir, { recursive: true });

      await fs.writeFile(resolvedPath, document, 'utf-8');
      const originDocument = await fs.readFile(resolvedPath, 'utf-8');

      // 打开文件
      const uri = vscode.Uri.file(resolvedPath);
      await vscode.window.showTextDocument(uri);

      // 等待用户确认
      const [action, reason] = await this._waitUserConfirm(toolId);
      console.log('record-document.run', action, reason);

      // 文档被用户拒绝
      if (action === 'reject') {
        // 异步删除
        fs.unlink(resolvedPath);

        const rejectMessage = reason
          ? `Your document has been rejected by the user, and here is the reason: ${reason}. The document file has been completely deleted; please consider how to revise it according to the reason as the suggestion.`
          : 'Your document has been rejected by the user, and the document file has been completely deleted; please consider how to revise it.';

        return new ToolResult(ToolResultStatus.UserRejected, rejectMessage);
      }

      // 文档被用户接受，需要做差异对比
      const currentDocument = await fs.readFile(resolvedPath, 'utf-8');
      const diff = await this._getContentDiff({
        origin: originDocument,
        current: currentDocument,
      });

      if (diff.length === 0) {
        return new ToolResult(ToolResultStatus.UserAccepted, 'Your document has been accepted by the user.');
      }

      return new ToolResult(
        ToolResultStatus.UserModifiedInput,
        `The user has made some changes to your document. The changes are as follows: ${JSON.stringify(diff)}`,
      );
    } catch (error) {
      // 返回错误信息字符串
      if (error instanceof Error) {
        return new ToolResult(ToolResultStatus.AutoRunError, error.message);
      }
      return new ToolResult(ToolResultStatus.AutoRunError, String(error));
    }
  }

  private async _waitUserConfirm(toolId: string): Promise<['accept', string] | ['reject', string]> {
    const chatSettings = await this._storageService.parseGet<ChatSettings>(CHAT_SETTINGS);
    if (isTrue(chatSettings?.tool?.autoRun!)) {
      return ['accept', ''];
    }

    return new Promise<['accept', string] | ['reject', string]>((resolve) => {
      let acceptDisposable: IDisposable | null = documentInteraction.onAccept((id: string) => {
        console.log('record-document.run', id);
        if (id === toolId) {
          acceptDisposable?.dispose();
          rejectDisposable?.dispose();
          acceptDisposable = null;
          rejectDisposable = null;
          resolve(['accept', '']);
        }
      });

      let rejectDisposable: IDisposable | null = documentInteraction.onReject((id: string, reason: string) => {
        if (id === toolId) {
          acceptDisposable?.dispose();
          rejectDisposable?.dispose();
          acceptDisposable = null;
          rejectDisposable = null;
          resolve(['reject', reason]);
        }
      });
    });
  }

  private _getContentDiff(params: { origin: string; current: string }) {
    console.log('record-document.run params', params);
    const { origin, current } = params;
    const diff = diffLines(origin, current);
    console.log('record-document.run diff', diff);
    const parsedDiff = this._parseDiffToReplaceFormat(diff);
    return parsedDiff;
  }

  /**
   * 将 diffLines 结果解析为可用于字符串替换的格式
   */
  private _parseDiffToReplaceFormat(diffs: Change[]): Array<{ YourOriginalContent: string; UserModifiedTo: string }> {
    const result: Array<{ YourOriginalContent: string; UserModifiedTo: string }> = [];
    let i = 0;
    let lastUnchangedContent = '';

    while (i < diffs.length) {
      const current = diffs[i];

      // 处理未变更的内容
      if (!current.added && !current.removed) {
        lastUnchangedContent = current.value;
        i++;
        continue;
      }

      // 收集连续的变更块
      const removedParts = [];
      const addedParts = [];

      // 收集所有连续的 removed 部分
      while (i < diffs.length && diffs[i].removed) {
        removedParts.push(diffs[i]);
        i++;
      }

      // 收集所有连续的 added 部分
      while (i < diffs.length && diffs[i].added) {
        addedParts.push(diffs[i]);
        i++;
      }

      // 处理不同的情况
      if (removedParts.length > 0 && addedParts.length > 0) {
        // 修改情况：尝试逐行分析
        const oldContent = removedParts.map((p) => p.value).join('');
        const newContent = addedParts.map((p) => p.value).join('');

        // 尝试逐行匹配
        const oldLines = oldContent.split('\n');
        const newLines = newContent.split('\n');

        // 如果最后一行是空字符串，说明原本有换行符结尾，需要保留
        if (oldLines[oldLines.length - 1] === '') oldLines.pop();
        if (newLines[newLines.length - 1] === '') newLines.pop();

        if (oldLines.length === newLines.length) {
          // 逐行一对一匹配
          for (let j = 0; j < oldLines.length; j++) {
            const oldLine = oldLines[j] + (j < oldLines.length - 1 || oldContent.endsWith('\n') ? '\n' : '');
            const newLine = newLines[j] + (j < newLines.length - 1 || newContent.endsWith('\n') ? '\n' : '');

            if (oldLine !== newLine) {
              result.push({
                YourOriginalContent: oldLine,
                UserModifiedTo: newLine,
              });
            }
          }
        } else {
          // 行数不匹配，整体替换
          result.push({
            YourOriginalContent: oldContent,
            UserModifiedTo: newContent,
          });
        }
      } else if (removedParts.length > 0) {
        // 单纯删除
        const oldContent = removedParts.map((p) => p.value).join('');
        result.push({
          YourOriginalContent: oldContent,
          UserModifiedTo: '',
        });
      } else if (addedParts.length > 0) {
        // 单纯新增：old 为最近的没有变更的行，new 为 old + 新增内容
        const newContent = addedParts.map((p) => p.value).join('');
        result.push({
          YourOriginalContent: lastUnchangedContent,
          UserModifiedTo: lastUnchangedContent + newContent,
        });
        // 更新 lastUnchangedContent 为包含新增内容的版本
        lastUnchangedContent = lastUnchangedContent + newContent;
      }
    }

    return result;
  }
}

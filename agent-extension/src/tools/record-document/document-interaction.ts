import { Emitter, Event } from '@byted-image/lv-bedrock/event';

/**
 * 文档工具交互层
 */
class DocumentInteraction {
  public onAccept: Event<[string]>;
  public onReject: Event<[string, string]>;

  private readonly _onAccept = new Emitter<[string]>();
  private readonly _onReject = new Emitter<[string, string]>();

  constructor() {
    this.onAccept = this._onAccept.event;
    this.onReject = this._onReject.event;
  }

  accept(toolId: string) {
    this._onAccept.fire(toolId);
  }

  reject(toolId: string, reason?: string) {
    this._onReject.fire(toolId, reason ?? '');
  }
}

export default new DocumentInteraction();

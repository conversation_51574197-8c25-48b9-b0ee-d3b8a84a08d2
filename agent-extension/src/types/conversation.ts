import type { IConversationHistoryService } from '@/services/conversation/history/conversation-history.interface';
import type { IRpcService } from '@/services/rpc/rpc-service.interface';

/**
 * 文件操作类型
 */
export type FileOperation = 'create' | 'modify' | 'delete';

/**
 * 行操作类型
 */
export type LineOperation = 'add' | 'delete' | 'modify';

/**
 * 行范围操作信息
 */
export interface LineAction {
  /** 操作类型 */
  type: LineOperation;
  /** 起始行号（从1开始） */
  startLine: number;
  /** 结束行号（从1开始，包含） */
  endLine: number;
}

/**
 * 文件操作动作
 */
export interface FileAction {
  /** 操作类型 */
  type: FileOperation;
  /** 涉及的行操作列表 */
  lineActions: LineAction[];
}

/**
 * 文件操作信息数据结构
 */
export interface DiffFileInfo {
  /** 操作动作列表 */
  actions: FileAction[];
  /** 文件名 */
  fileName: string;
  operation: FileOperation;
  /** 文件完整绝对路径 */
  fullPath: string;
  addedLines: number;
  deletedLines: number;
}

/**
 * 更新会话 diffInfo 的参数对象
 */
export interface UpdateConversationDiffInfoParams extends DiffFileInfo {
  conversationId: string;
  historyService: IConversationHistoryService;
  rpcService: IRpcService;
  toolName?: string;
}

import { useState, useContext } from 'react';
import { Flex, Text } from '@radix-ui/themes';
import { X, File, Check, CheckCheck } from 'lucide-react';
import { useConversationHistory } from '@/webview/hooks/use-conversation-history';
import { RpcContext } from '@/webview/contexts/rpc-context';
import type { DiffFileInfo, FileAction } from '@/types/conversation';
import AdditionCollapible from '../additional-collapsible';

const getFileStatusText = (actions: FileAction[]) => {
  // 获取第一个操作的类型，如果没有操作则默认为 modify
  const operation = actions.length > 0 ? actions[0].type : 'modify';
  const statusMap = {
    create: 'C',
    modify: 'M',
    delete: 'D',
  };
  return statusMap[operation] || 'M';
};

const formatDiffStats = (addedLines?: number, deletedLines?: number) => {
  const hasAdded = typeof addedLines === 'number' && addedLines > 0;
  const hasDeleted = typeof deletedLines === 'number' && deletedLines > 0;

  // 如果没有任何变更，不显示
  if (!hasAdded && !hasDeleted) {
    return null;
  }

  return (
    <span className="ml-1 text-sm">
      {hasAdded && <span className="text-green-600 dark:text-green-400">+{addedLines}</span>}
      {hasDeleted && <span className="text-red-600 dark:text-red-400">-{deletedLines}</span>}
    </span>
  );
};

const DiffViewDrawer = (props: { className?: string }) => {
  const conversationState = useConversationHistory();
  const [isCollapsibleOpen, setIsCollapsibleOpen] = useState(false);
  const rpcClient = useContext(RpcContext);

  // 处理文件路径，转换为更好的显示格式
  const processConversationDiffFiles = (): (DiffFileInfo & { displayPath: string })[] => {
    const diffInfo = conversationState.diffInfo || [];
    return diffInfo.map((fileInfo) => {
      // 从完整路径提取相对路径用于显示
      const pathParts = fileInfo.fullPath.split(/[\\/]/);
      const displayPath = pathParts.slice(-2).join('/'); // 显示最后两级目录

      const cleanedFileInfo = {
        ...fileInfo,
        displayPath,
        addedLines: typeof fileInfo.addedLines === 'number' ? fileInfo.addedLines : 0,
        deletedLines: typeof fileInfo.deletedLines === 'number' ? fileInfo.deletedLines : 0,
      };

      return cleanedFileInfo;
    });
  };

  const processedFiles = processConversationDiffFiles();

  const handleFileClick = (file: DiffFileInfo & { displayPath: string }) => {
    // 通过RPC调用扩展命令来显示文件的diff view
    rpcClient?.call('codin.webview.codediff.showDiffView', { filePath: file.fullPath });
  };

  const handleRemoveDiffInfo = (filePath: string) => {
    // 通过RPC调用移除单个文件的diffInfo
    rpcClient?.call('codin.webview.codediff.removeFile', { filePath });
  };

  const handleUndoOperation = (file: DiffFileInfo & { displayPath: string }) => {
    // 通过RPC调用撤销文件操作
    rpcClient?.call('codin.webview.codediff.undoFileOperation', {
      filePath: file.fullPath,
      actions: file.actions,
    });
  };

  // 如果没有文件修改，不展示任何内容
  if (processedFiles.length === 0) {
    return null;
  }

  return (
    <AdditionCollapible
      open={isCollapsibleOpen}
      onOpenChange={setIsCollapsibleOpen}
      rootClassName="top-2"
      titleClassName={props.className}
      title={
        <div className="w-full flex items-center justify-between">
          <Text size="1">
            {processedFiles.length} File{processedFiles.length !== 1 ? 's' : ''}
          </Text>

          <div className="flex items-center gap-2">
            <span title="撤销所有文件操作">
              <X
                size={12}
                className="opacity-80 hover:opacity-100"
                onClick={async (e) => {
                  e.stopPropagation();
                  await Promise.all([
                    ...processedFiles.map((file) => handleUndoOperation(file)),
                    rpcClient?.call('codin.webview.codediff.clearDiffInfo'),
                  ]);
                }}
              />
            </span>
            <span title="接收所有">
              <CheckCheck
                className="opacity-80 hover:opacity-100"
                size={12}
                onClick={(e) => {
                  e.stopPropagation();
                  rpcClient?.call('codin.webview.codediff.clearDiffInfo');
                }}
              />
            </span>
          </div>
        </div>
      }
    >
      {processedFiles.map((file) => (
        <Flex
          key={file.fullPath}
          title={file.fullPath}
          align="center"
          justify="between"
          className={'p-2 cursor-pointer transition-colors duration-150 hover:bg-vscode-editor-background'}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            handleFileClick(file);
          }}
        >
          <div className="flex items-center">
            <Text
              size="1"
              weight="medium"
              className={`
                ${file.actions[0]?.type === 'modify' ? 'text-orange-600 dark:text-orange-400' : ''}
                ${file.actions[0]?.type === 'create' ? 'text-green-600 dark:text-green-400' : ''}
                ${file.actions[0]?.type === 'delete' ? 'text-red-600 dark:text-red-400' : ''}
                mr-2
              `}
            >
              {getFileStatusText(file.actions)}
            </Text>

            <File size={14} className="text-gray-600 dark:text-gray-400 flex-shrink-0" />
            <Text size="1" className="font-normal truncate">
              {file.fileName}
              {formatDiffStats(file.addedLines, file.deletedLines)}
            </Text>
          </div>

          <div className="flex items-center gap-1">
            <X
              size={12}
              className="opacity-80 hover:opacity-100"
              onClick={(e) => {
                e.stopPropagation();
                handleUndoOperation(file);
              }}
            />
            <Check
              size={12}
              className="opacity-80 hover:opacity-100"
              onClick={(e) => {
                e.stopPropagation();
                handleRemoveDiffInfo(file.fullPath);
              }}
            />
          </div>
        </Flex>
      ))}
    </AdditionCollapible>
  );
};

export default DiffViewDrawer;

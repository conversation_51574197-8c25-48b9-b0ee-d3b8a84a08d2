import { Barrier } from '@byted-image/lv-bedrock/async';
import Document from '@tiptap/extension-document';
import History from '@tiptap/extension-history';
import Mention from '@tiptap/extension-mention';
import Placeholder from '@tiptap/extension-placeholder';
import Text from '@tiptap/extension-text';
import { EditorContent, Mark, useEditor, type Editor } from '@tiptap/react';
import React, { useImperativeHandle, useRef, useContext, useEffect } from 'react';
import CustomBlock from './extensions/custom-block/extension';
import suggestion from './mention/mention-suggestion';
import CharacterCount from '@tiptap/extension-character-count';
import HardBreak from '@tiptap/extension-hard-break';
import FileNode, { type FileNodeConfig } from './extensions/file-node/extension';
import Image from './extensions/image/extension';
import { DocNodeItem, type FileNodeItem, type InlineNodeItem } from './types';
import { RpcContext } from '@/webview/contexts/rpc-context';
import { RpcContextV2 } from '@/webview/contexts/rpc-context-v2';

interface TiptapEditorProps {
  className?: string;
  content?: string;
  onChange?: (content: string) => void;
  readOnly?: boolean;
  placeholder?: string;
  onKeyDown?: (event: React.KeyboardEvent<HTMLDivElement>) => void;
  onAttachFiles?: (files: FileNodeItem[]) => void;
}

export interface TipTapEditorRef {
  getValue: () => Promise<DocNodeItem>;
  clearValue: () => void;
  getCharCount: () => number;
  setStrValue: (content: string | DocNodeItem) => Promise<void>;
  setValue: (val: any) => void;
  addFilesToChat: (files: FileNodeConfig[]) => void;
  editor: Editor | null;
}

export const TiptapEditor = React.forwardRef<TipTapEditorRef, TiptapEditorProps>(
  ({ content = '', onChange, readOnly = false, placeholder = '', onKeyDown, className, onAttachFiles }, ref) => {
    const isMentionOpenRef = useRef(false);
    const rpcClient = useContext(RpcContext);
    const rpcClientV2 = useContext(RpcContextV2);
    const lastNavigatedContentRef = useRef<string>(''); // 记录最后一次通过历史导航写入的内容
    const placeholderRef = useRef<string>(placeholder);

    // 定义Hidden扩展
    const Hidden = Mark.create({
      name: 'hidden',
      renderHTML({ HTMLAttributes }) {
        return ['span', { ...HTMLAttributes, class: 'tippy-char-hidden' }, 0];
      },
    });

    const editor = useEditor({
      extensions: [
        Mention.configure({
          HTMLAttributes: {
            class: 'mention',
          },
          suggestion: suggestion((isOpen: boolean) => {
            isMentionOpenRef.current = isOpen;
          }),
        }),
        CustomBlock,
        Document,
        History,
        Placeholder.configure({ placeholder: () => placeholderRef.current }),
        Text,
        Image,
        FileNode,
        HardBreak,
        CharacterCount.configure(),
        Hidden,
      ],
      editorProps: {
        attributes: {
          class: 'tiptap-editor',
        },
        handleKeyDown(_view, event) {
          // 当 mention 列表打开时，阻止 tiptap 按键切换
          if (isMentionOpenRef.current) {
            return false;
          }

          // 拦截其他可能冲突的快捷键
          // https://bytedance.larkoffice.com/docx/NmotdykJ8oNzvGxVCjTcWND8ncg
          if ((event.metaKey || event.ctrlKey) && ['z', 'Z', 'y', 'Y', 'a', 'A'].includes(event.key)) {
            event.preventDefault();
            event.stopPropagation();
            // 不手动调用 undo/redo/selectAll，让 TipTap 的 History 扩展 & TipTap的core做自然处理
            return false; // 让 TipTap 继续处理，但阻止默认行为
          }

          if (event.key === 'Enter') {
            if (event.shiftKey) {
              editor?.commands.setHardBreak();
              return true;
            }

            event.preventDefault();
            return true; // 阻止 tiptap 处理 Enter
          }

          // 切换历史记录的快捷键，按下箭头键时，会在对话框输入历史对话
          const toggleInputHistoryKeys = ['ArrowDown', 'ArrowUp'];
          if (toggleInputHistoryKeys.includes(event.key) && !event.shiftKey && !event.ctrlKey && !event.metaKey) {
            const content = JSON.stringify(editor?.$doc.content.toJSON());
            if (rpcClient) {
              event.preventDefault();

              // 检查当前内容是否与最后一次导航的内容不同
              const shouldSaveCurrentInput = content !== lastNavigatedContentRef.current;

              const rpcCall =
                event.key === 'ArrowDown'
                  ? rpcClientV2.inputHistory.getNextHistory
                  : rpcClientV2.inputHistory.getPreviousHistory;

              if (shouldSaveCurrentInput) {
                // 用户手动修改过内容，需要保存
                rpcClientV2.inputHistory
                  .saveCurrentInput({ currentInput: content ?? '' })
                  .then(() => {
                    // 保存完成后再获取下一个历史记录
                    return rpcCall();
                  })
                  .then((result) => {
                    if (result?.history) {
                      const content = JSON.parse(result.history);
                      const files = content?.[0].content?.filter((item: InlineNodeItem) => item.type === 'file');
                      onAttachFiles?.(files as FileNodeItem[]);
                      editor?.commands.setContent(content);
                      lastNavigatedContentRef.current = result.history; // 更新导航内容记录
                    }
                  })
                  .catch((error) => {
                    console.error('Failed to get history:', rpcCall, error);
                  });
              } else {
                // 内容来自历史导航，直接获取下一个历史记录
                rpcCall()
                  .then((result) => {
                    if (result?.history) {
                      const content = JSON.parse(result.history);
                      const files = content?.[0].content?.filter((item: InlineNodeItem) => item.type === 'file');
                      onAttachFiles?.(files as FileNodeItem[]);
                      editor?.commands.setContent(content);
                      lastNavigatedContentRef.current = result.history; // 更新导航内容记录
                    }
                  })
                  .catch((error) => {
                    console.error('Failed to get history:', rpcCall, error);
                  });
              }
              return true;
            }
          }

          return false;
        },
      },
      content: content || '',
      editable: !readOnly,
      onUpdate: ({ editor }) => {
        onChange?.(editor.$doc.content.toJSON());
      },
      onCreate: () => {
        readyBarrierRef.current.open();
      },
    });
    const readyBarrierRef = useRef(new Barrier());

    useImperativeHandle(ref, () => ({
      getValue: async (): Promise<DocNodeItem> => {
        if (!readyBarrierRef.current.isOpen()) {
          await readyBarrierRef.current.wait();
        }
        return editor!.view.state.toJSON().doc;
      },
      clearValue: async (): Promise<void> => {
        if (!readyBarrierRef.current.isOpen()) {
          await readyBarrierRef.current.wait();
        }
        editor!.commands.setContent('');
        lastNavigatedContentRef.current = ''; // 重置导航内容记录
      },
      getCharCount: (): number => {
        return editor?.storage.characterCount?.characters() || 0;
      },
      setStrValue: async (content: string | DocNodeItem): Promise<void> => {
        if (!readyBarrierRef.current.isOpen()) {
          await readyBarrierRef.current.wait();
        }
        await editor!.commands.setContent(content);
      },
      setValue(value: unknown) {
        return editor?.commands.setContent(value as any);
      },
      addFilesToChat(files: FileNodeConfig[]) {
        files.forEach((file) => {
          editor?.commands.setFileNode({
            type: file.type,
            path: file.path,
            name: file.name,
          });
        });
      },
      editor,
    }));

    useEffect(() => {
      placeholderRef.current = placeholder;

      if (editor) {
        const content = editor.getText();
        editor.view.dom.style.maxHeight = '240px';
        // 如果内容为空，则强制刷新
        if (content.trim() === '') {
          editor.commands.clearContent();
          editor.commands.insertContent(content);
        }
      }
    }, [placeholder, editor]);

    useEffect(() => {
      const container = editor?.view.dom;
      if (container) {
        // 将容器的 scrollTop 设置为它的最大可滚动高度
        container.scrollTop = container.scrollHeight;
      }
    }, [
      // 依赖项：监听编辑器内容的大小
      editor?.state.doc.content.size,
    ]);

    return (
      <EditorContent
        editor={editor}
        className={`tiptap-wrapper ${className}`}
        onKeyDown={(event) => {
          if (isMentionOpenRef.current && event.key === 'Enter') {
            return;
          }
          if (event.nativeEvent.isComposing) {
            return; // IME 下不触发 onKeyDown
          }
          onKeyDown?.(event);
        }}
      />
    );
  },
);

import { forwardRef, useEffect, useImperativeHandle, useState, useRef } from 'react';
import { MentionListProps, MentionItem } from './types';
import MentionGroup from './mention-group';
import { useMentionItems } from './use-mention-items';

const MentionList = forwardRef((props: MentionListProps, ref) => {
  const { query, editor, range } = props;
  const isDark = true;
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const activeItemRef = useRef<HTMLDivElement>(null);
  const {
    items: mentionItems,
    enterCategory,
    exitCategory,
    selectLeafItem,
  } = useMentionItems({
    searchValue: query,
    editor,
    range,
  });
  const [currentItems, setCurrentItems] = useState<MentionItem[]>(mentionItems);
  const [selectedItem, setSelectedItem] = useState<MentionItem | null>(null);

  useEffect(() => {
    // 当query为空且有选中分类时，退出分类
    if (!query && selectedItem) {
      setCurrentItems(mentionItems);
      setSelectedItem(null);
      exitCategory();
    }
  }, [query]);

  useEffect(() => {
    setCurrentItems(mentionItems);
  }, [mentionItems]);

  useEffect(() => {
    // 当activeIndex改变时，滚动到当前选中项
    if (activeItemRef.current) {
      activeItemRef.current.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth',
      });
    }
  }, [activeIndex]);

  const selectItem = (item: MentionItem) => {
    if (item.isLeaf) {
      selectLeafItem(item);
    } else {
      setCurrentItems(item.children);
      setSelectedItem(item);
      setActiveIndex(0);
      enterCategory(item.value);
    }
  };

  const upHandler = () => {
    const newIndex = (activeIndex + currentItems.length - 1) % currentItems.length;
    setActiveIndex(newIndex);
  };

  const downHandler = () => {
    const newIndex = (activeIndex + 1) % currentItems.length;
    setActiveIndex(newIndex);
  };

  const enterHandler = () => {
    const item = currentItems[activeIndex];
    selectItem(item);
  };

  const backHandler = () => {
    if (selectedItem) {
      if (!query) {
        // 如果搜索关键词为空，回退到上一级分类
        setCurrentItems(mentionItems);
        setSelectedItem(null);
        exitCategory();
      }
      // 如果有搜索关键词，让编辑器处理删除操作
      return false;
    }
    // 如果没有选中分类，让编辑器处理删除操作
    return false;
  };

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }: { event: KeyboardEvent }) => {
      if (event.key === 'ArrowUp') {
        upHandler();
        return true;
      }

      if (event.key === 'ArrowDown') {
        downHandler();
        return true;
      }

      if (event.key === 'Enter') {
        enterHandler();
        return true;
      }

      if (event.key === 'Backspace') {
        return backHandler();
      }

      return false;
    },
  }));

  const headerStyle = {
    padding: '4px 8px',
    fontSize: '12px',
    color: isDark ? '#bfc4cc' : '#666',
    borderBottom: `1px solid ${isDark ? '#363b42' : '#f0f0f0'}`,
    marginBottom: '4px',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  };

  return (
    <>
      {selectedItem && (
        <div style={headerStyle} tabIndex={-1}>
          <span onClick={backHandler} style={{ cursor: 'pointer' }}>
            ←
          </span>
          <span>{selectedItem.label}</span>
        </div>
      )}
      {currentItems.length ? (
        <MentionGroup
          items={currentItems}
          onSelect={selectItem}
          activeIndex={activeIndex}
          onHover={setActiveIndex}
          isDark={isDark}
          activeItemRef={activeItemRef}
        />
      ) : (
        <div className="p-2 bg-vscode-input-background text-vscode-editor-foreground">没有找到匹配项</div>
      )}
    </>
  );
});

MentionList.displayName = 'MentionList';

export default MentionList;

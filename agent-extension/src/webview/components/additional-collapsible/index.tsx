import { type ReactNode } from 'react';
import { Collapsible } from 'radix-ui';
import { ChevronRight } from 'lucide-react';

interface AdditionCollapibleProps {
  open?: boolean;
  title: ReactNode;
  children: ReactNode;
  onOpenChange?: (open: boolean) => void;
  rootClassName?: string;
  titleClassName?: string;
}

const AdditionCollapible = (props: AdditionCollapibleProps) => {
  const { open, title, children, onOpenChange, rootClassName, titleClassName } = props;
  return (
    <Collapsible.Root open={open} onOpenChange={onOpenChange} className={`relative w-full ${rootClassName}`}>
      <Collapsible.Trigger className="w-full">
        <div
          className={`flex items-center bg-vscode-input-background px-3 py-2 rounded-tl-lg rounded-tr-lg border border-vscode-input-border cursor-pointer ${titleClassName}`}
        >
          <ChevronRight size={14} className={`transition-transform duration-200  ${open ? 'rotate-90' : ''}`} />
          {title}
        </div>

        {/* Collapsible 内容 */}
        <Collapsible.Content className="bg-vscode-input-background">{children}</Collapsible.Content>
      </Collapsible.Trigger>
    </Collapsible.Root>
  );
};

export default AdditionCollapible;

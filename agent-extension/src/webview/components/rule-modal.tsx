import React from 'react';
import Modal from 'react-modal';
import { Rule, useRuleSettings } from '../hooks/use-rule-settings';
import { RuleForm } from '../pages/settings/components/rule-tab/rule-form';

export enum RuleModalMode {
  ADD = 'add',
  EDIT = 'edit',
}

interface RuleModalProps {
  isOpen: boolean;
  onClose: () => void;
  rule?: Rule;
  mode?: RuleModalMode;
}

export const RuleModal: React.FC<RuleModalProps> = ({ isOpen, onClose, rule, mode = RuleModalMode.ADD }) => {
  const { addRule, editRule } = useRuleSettings();

  const handleSubmitRuleForm = (newRule: Rule) => {
    if (mode === RuleModalMode.EDIT && rule) {
      editRule(newRule);
    } else {
      addRule(newRule);
    }
    onClose();
  };

  const handleCancelRuleForm = () => {
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      style={{
        overlay: {
          background: 'rgba(0, 0, 0, 0.75)',
        },
        content: {
          height: 'fit-content',
          inset: 16,
          padding: 0,
          borderRadius: 8,
          border: 'none',
          background: 'var(--vscode-editor-background, --vscode-sideBar-background, rgb(30 30 30))',
          boxShadow: 'var(--vscode-icube-box-shadow3)',
          margin: 'auto',
        },
      }}
    >
      <RuleForm rule={rule} onSubmit={handleSubmitRuleForm} onCancel={handleCancelRuleForm} />
    </Modal>
  );
};

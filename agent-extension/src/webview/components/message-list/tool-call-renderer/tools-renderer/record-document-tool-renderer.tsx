import React, { useCallback, useContext, useMemo, useState } from 'react';
import type { ToolCallRendererProps } from '../../../../managers/conversation-manager/types';
import { RpcContext } from '@/webview/contexts/rpc-context';
import { ToolResult, ToolResultStatus } from '../../../../../tools/base';
import { Code } from '@radix-ui/themes';

const defaultMessageContent =
  '文档已经生成成功🎉 如果您对文档满意，请直接确认。如需微调，可修改文档后确认。若有不满意，请在输入框输入修改意见重新生成。';

const MessageContent: React.FC<{ documentPath: string; overview: string }> = ({ documentPath, overview }) => {
  return (
    <div>
      {defaultMessageContent}
      <br />
      <em>文档地址：</em>
      <br />
      <Code>{documentPath}</Code>
      <br />
      <em>文档概览：</em>
      <br />
      {overview}
    </div>
  );
};

export const RecordDocumentToolRenderer: React.FC<ToolCallRendererProps> = ({ message }) => {
  const rpcClient = useContext(RpcContext);

  // 检查是否有output，如果有则禁用按钮
  const isDisabled = useMemo(() => Boolean(message.output), [message.output]);
  const [hasError, setHasError] = useState(false);

  const content = useMemo(() => {
    try {
      let messageContent: any = defaultMessageContent;

      // 如果消息有input，则显示input的内容
      if (message.input) {
        const input = JSON.parse(message.input);
        messageContent = <MessageContent documentPath={input.path} overview={input.documentOverview} />;
      }

      // 如果消息还没有output，不用处理后续的流程
      if (!message.output) {
        return messageContent;
      }

      const output = ToolResult.deserialize(message.output);
      const isAccepted = output.status === ToolResultStatus.UserAccepted;
      const isRejected = output.status === ToolResultStatus.UserRejected;
      const isModified = output.status === ToolResultStatus.UserModifiedInput;
      const isError = output.status === ToolResultStatus.AutoRunError;

      if (isAccepted) {
        return '文档已确认。';
      }

      if (isRejected) {
        return '文档已拒绝。';
      }

      if (isModified) {
        return '文档已修改并确认。';
      }

      if (isError) {
        setHasError(true);
        return '文档记录失败';
      }

      return messageContent;
    } catch (error) {
      setHasError(true);
      return '文档记录失败';
    }
  }, [message.output]);

  const handleAccept = useCallback(() => {
    if (isDisabled) return;

    rpcClient!.call<{ toolId: string }, void>('documentToolAccept', {
      toolId: message.id,
    });
  }, [message.id, isDisabled, rpcClient]);

  // const handleReject = useCallback(() => {
  //   if (isDisabled) return;
  //   rpcClient!.call<{ toolId: string }, void>('documentToolReject', {
  //     toolId: message.id,
  //   });
  // }, [message.id, isDisabled, rpcClient]);

  return (
    <div className="text-vscode-description border rounded-lg p-4 mb-4">
      <div className="mb-4">
        <p>{content}</p>
      </div>

      {/* 参考 Devin，在卡片中确认，在对话框提出修改意见 */}
      <div className="flex justify-end gap-2">
        <button
          type="button"
          className={`px-2 py-1   font-medium border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
            isDisabled || hasError
              ? 'text-gray-400 bg-gray-300 cursor-not-allowed'
              : 'text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
          }`}
          onClick={handleAccept}
          disabled={isDisabled || hasError}
        >
          确认
        </button>
      </div>
    </div>
  );
};

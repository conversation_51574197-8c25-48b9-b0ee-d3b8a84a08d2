import type { ToolCallRendererProps } from '../../../../managers/conversation-manager/types';
import { DefaultToolRenderer } from './default-tool-renderer';
import { RecordDocumentToolRenderer } from './record-document-tool-renderer';
import { toolName as recordDocumentToolName } from '../../../../../tools/record-document/tool-info';
import { AskUserToolRenderer } from './ask-user-tool-renderer';
import { toolName as askUserToolName } from '../../../../../tools/ask-user/tool-info';
import { toolName as technicalPlanningToolName } from '../../../../../tools/technical-planning/tool-info';
import { toolName as UnderstandRequirementToolName } from '../../../../../tools/understand-requirement';
import { TechnicalPlanningToolRenderer } from './technical-planning-tool-renderer';
import { UnderstandRequirementToolRenderer } from './understand-requirement-tool-renderer';

const supportedToolsRenderer: Record<string, React.FC<ToolCallRendererProps> | undefined> = {
  [recordDocumentToolName]: RecordDocumentToolRenderer,
  [askUserToolName]: AskUserToolRenderer,
  [technicalPlanningToolName]: TechnicalPlanningToolRenderer,
  [UnderstandRequirementToolName]: UnderstandRequirementToolRenderer,
};

export const getToolRenderer = (toolName: string) => {
  return supportedToolsRenderer[toolName] ?? DefaultToolRenderer;
};

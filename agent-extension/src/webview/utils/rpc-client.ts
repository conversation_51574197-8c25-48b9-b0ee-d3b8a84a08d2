/**
 * @file rpc-client.ts
 * @description RPC 的客户端实现，运行于 Webview。
 * 负责发送请求、管理回调以及监听通知。
 */

import { generateUuid } from '@byted-image/lv-bedrock/uuid';
import { RpcRequest, RpcResponse, RpcNotification, RpcMessagePacket, RpcCallOptions } from '../../services/rpc/types';
import { vscode } from './acquire';

type PendingRequest = {
  resolve: (result: any) => void;
  reject: (error: any) => void;
  timeoutTimer: ReturnType<typeof setTimeout>;
};

const DEFAULT_TIMEOUT = 30000; // 30 seconds

/**
 * 用于向服务器（如 VSCode 扩展）发起 RPC 调用的客户端。
 */
export class RpcClient {
  private _pending = new Map<string, PendingRequest>();
  private _eventListeners = new Map<string, Set<(params: any) => void>>();

  constructor() {
    window.addEventListener('message', this._handleMessage.bind(this));
  }

  /**
   * 生成一个用于新请求的唯一 ID。
   * @returns 唯一的消息 ID 字符串。
   */
  private _getNextId(): string {
    return generateUuid();
  }

  /**
   * @deprecated 不要直接使用这个方法，使用 RpcClientV2 上面的新方法代替
   * 参考文档 https://bytedance.larkoffice.com/docx/BssEdUpXxosnCsx6hH1cLmC1nJb#share-DlU7dWlosouapDxQGs0ccQ0mnAg
   * 向服务器发起 RPC 调用。
   * @param cmd - 要执行的命令。
   * @param params - 命令的参数。
   * @param options - 调用的可选设置，如超时时间。
   * @returns 一个 Promise，成功时返回结果，失败时抛出错误。
   */
  public call<P = any, R = any>(cmd: string, params?: P, options?: RpcCallOptions): Promise<R> {
    return new Promise((resolve, reject) => {
      const id = this._getNextId();
      const timeout = options?.timeout ?? DEFAULT_TIMEOUT;

      const timeoutTimer = setTimeout(() => {
        this._pending.delete(id);
        reject(new Error(`RPC call "${String(cmd)}" timed out after ${timeout}ms`));
      }, timeout);

      this._pending.set(id, { resolve, reject, timeoutTimer });

      const request: RpcRequest = {
        type: 'request',
        id,
        cmd: String(cmd),
        params,
      };

      vscode.postMessage(request);
    });
  }

  /**
   * @deprecated 不要直接使用这个方法，使用 RpcClientV2 上面的新事件代替
   * 参考文档 https://bytedance.larkoffice.com/docx/BssEdUpXxosnCsx6hH1cLmC1nJb#share-DlU7dWlosouapDxQGs0ccQ0mnAg
   * 注册服务器通知事件的监听器。
   * @param event - 要监听的事件名。
   * @param listener - 事件收到时执行的回调函数。
   */
  public on<E extends string>(event: E, listener: (params: any) => void): void {
    if (!this._eventListeners.has(event)) {
      this._eventListeners.set(event, new Set());
    }
    this._eventListeners.get(event)!.add(listener);
  }

  /**
   * @deprecated 不要直接使用这个方法，使用 RpcClientV2 上面的新事件代替
   * 参考文档 https://bytedance.larkoffice.com/docx/BssEdUpXxosnCsx6hH1cLmC1nJb#share-DlU7dWlosouapDxQGs0ccQ0mnAg
   * 注销通知事件的监听器。
   * @param event - 事件名。
   * @param listener - 要移除的回调函数。
   */
  public off<E extends string>(event: E, listener: (params: any) => void): void {
    const listeners = this._eventListeners.get(event);
    if (listeners) {
      listeners.delete(listener);
      if (listeners.size === 0) {
        this._eventListeners.delete(event);
      }
    }
  }

  /**
   * 处理来自服务器的消息。
   * @param event - window 的消息事件。
   */
  private _handleMessage(event: MessageEvent<RpcMessagePacket>): void {
    const message = event.data;
    if (!message) return;

    if (message.type === 'response' && 'id' in message) {
      this._handleResponse(message as RpcResponse);
    } else if (message.type === 'notification' && 'event' in message) {
      this._handleNotification(message as RpcNotification);
    }
  }

  /**
   * 处理来自服务器的响应消息。
   * @param response - RPC 响应消息。
   */
  private _handleResponse(response: RpcResponse): void {
    const { id, data, error } = response;
    const pendingRequest = this._pending.get(id);

    if (pendingRequest) {
      clearTimeout(pendingRequest.timeoutTimer);
      if (error) {
        pendingRequest.reject(error);
      } else {
        pendingRequest.resolve(data);
      }
      this._pending.delete(id);
    }
  }

  /**
   * 处理来自服务器的通知消息。
   * @param notification - RPC 通知消息。
   */
  private _handleNotification(notification: RpcNotification): void {
    const listeners = this._eventListeners.get(notification.event);
    if (listeners) {
      listeners.forEach((listener) => {
        try {
          listener(notification.params);
        } catch (e) {
          console.error(`Error in RPC notification listener for "${notification.event}":`, e);
        }
      });
    }
  }
}

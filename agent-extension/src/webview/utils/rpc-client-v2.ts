import type { IRpcV2Service } from '@/services/rpc-v2/rpc-service.interface';
import { RpcClient } from './rpc-client';
import type { INotificationService, PureEmitter } from '@/services/notification/notification-service.interface';
import { Event, Emitter } from '@byted-image/lv-bedrock/event';
import _ from 'lodash';

type BackgroundEvents = {
  [K in keyof INotificationService]: INotificationService[K] extends PureEmitter<infer T> ? Event<[T]> : never;
};

export type RpcClientV2 = {
  // 必须直接使用 K in keyof IRpcV2Service，然后无法跳转到原始定义
  [K in keyof IRpcV2Service]: IRpcV2Service[K] extends (...args: any[]) => any
    ? IRpcV2Service[K]
    : IRpcV2Service[K] extends object
      ? {
          [P in keyof IRpcV2Service[K]]: IRpcV2Service[K][P] extends (...args: any[]) => any
            ? IRpcV2Service[K][P]
            : never;
        }
      : never;
} & BackgroundEvents;

// 除了构造函数外，不要声明任何成员变量，有需要直接使用闭包即可
class RpcClientV2Implementation {
  constructor() {
    const rpcClient = new RpcClient();
    const emitterMap = new Map<string, Emitter<any>>();
    const namespaceProxyMap = new Map<string, any>();

    // biome-ignore lint/correctness/noConstructorReturn: <explanation>
    return new Proxy<RpcClientV2>({} as RpcClientV2, {
      get: (_target, prop) => {
        // 处理 Symbol 类型的属性
        if (typeof prop === 'symbol') {
          return undefined;
        }

        // 如果 prop 是 onXxx 格式，则返回 emitterProxy
        if (/^on[A-Z]/.test(prop)) {
          const emitter = emitterMap.get(prop);
          if (emitter) {
            return emitter.event;
          }
          const newEmitter = new Emitter<any>();
          rpcClient.on(prop, (...args) => {
            newEmitter.fire(...args);
          });
          emitterMap.set(prop, newEmitter);
          return newEmitter.event;
        }

        // 检查是否已经有缓存的命名空间代理
        const namespaceOrProp = prop;
        if (namespaceProxyMap.has(namespaceOrProp)) {
          return namespaceProxyMap.get(namespaceOrProp);
        }

        // 创建新的命名空间代理并缓存
        const methodMap = new Map<string, (...args: any[]) => any>();
        const namespaceProxy = new Proxy(_.noop, {
          get: (_target, prop) => {
            // 处理 Symbol 类型的属性
            if (typeof prop === 'symbol') {
              return undefined;
            }

            // 检查是否已经有缓存的方法
            if (methodMap.has(prop)) {
              return methodMap.get(prop);
            }

            // 创建新的方法并缓存
            const method = (...args: any[]) => {
              return rpcClient.call(`${namespaceOrProp}.${prop}`, ...args);
            };
            methodMap.set(prop, method);
            return method;
          },
          apply: (_target, _thisArg, args) => {
            return rpcClient.call(namespaceOrProp, ...args);
          },
        });

        namespaceProxyMap.set(namespaceOrProp, namespaceProxy);
        return namespaceProxy;
      },
    });
  }
}

export const RpcClientV2 = RpcClientV2Implementation as { new (): RpcClientV2 };

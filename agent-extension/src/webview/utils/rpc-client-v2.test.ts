// Mock RpcClient - must be before any imports
vi.mock('./rpc-client', () => {
  return {
    RpcClient: vi.fn().mockImplementation(() => ({
      call: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
    })),
  };
});

// Mock vscode - must be before any imports
vi.mock('./acquire', () => ({
  vscode: {
    postMessage: vi.fn(),
  },
}));

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { RpcClientV2 } from './rpc-client-v2';
import { RpcClient } from './rpc-client';

describe('RpcClientV2', () => {
  let rpcClientV2: InstanceType<typeof RpcClientV2>;
  let mockRpcClient: any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockRpcClient = {
      call: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
    };
    (RpcClient as any).mockImplementation(() => mockRpcClient);
    rpcClientV2 = new RpcClientV2();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('RPC method calls', () => {
    it('should call flat methods correctly', async () => {
      const mockResult = { success: true };
      mockRpcClient.call.mockResolvedValue(mockResult);

      const result = await rpcClientV2.showInformationMessage({
        content: 'test message',
        detail: 'test detail',
        modal: true,
      });

      expect(mockRpcClient.call).toHaveBeenCalledWith('showInformationMessage', {
        content: 'test message',
        detail: 'test detail',
        modal: true,
      });
      expect(result).toBe(mockResult);
    });

    it('should call namespaced methods correctly', async () => {
      const mockResult = { content: 'file content' };
      mockRpcClient.call.mockResolvedValue(mockResult);

      const result = await rpcClientV2.workspace.readFileContent({
        path: '/test/path',
      });

      expect(mockRpcClient.call).toHaveBeenCalledWith('workspace.readFileContent', { path: '/test/path' });
      expect(result).toBe(mockResult);
    });

    it('should handle methods without parameters', async () => {
      const mockResult = { list: [] };
      mockRpcClient.call.mockResolvedValue(mockResult);

      const result = await rpcClientV2.getRecentFiles();

      expect(mockRpcClient.call).toHaveBeenCalledWith('getRecentFiles');
      expect(result).toBe(mockResult);
    });

    it('should handle methods with empty parameters', async () => {
      const mockResult = { history: 'previous' };
      mockRpcClient.call.mockResolvedValue(mockResult);

      const result = await rpcClientV2.inputHistory.getPreviousHistory();

      expect(mockRpcClient.call).toHaveBeenCalledWith('inputHistory.getPreviousHistory');
      expect(result).toBe(mockResult);
    });

    it('should handle RPC call errors', async () => {
      const mockError = new Error('RPC call failed');
      mockRpcClient.call.mockRejectedValue(mockError);

      await expect(rpcClientV2.showErrorMessage({ content: 'error' })).rejects.toThrow('RPC call failed');

      expect(mockRpcClient.call).toHaveBeenCalledWith('showErrorMessage', { content: 'error' });
    });
  });

  describe('Event handling', () => {
    it('should create event listeners for onXxx methods', () => {
      const mockListener = vi.fn();
      const event = rpcClientV2.onUpdateIndexInfo;

      expect(event).toBeDefined();
      expect(typeof event).toBe('function');

      // Subscribe to the event
      const disposable = event(mockListener);
      expect(disposable).toBeDefined();

      // Verify that the underlying RpcClient.on was called
      expect(mockRpcClient.on).toHaveBeenCalledWith('onUpdateIndexInfo', expect.any(Function));
    });

    it('should handle multiple event listeners for the same event', () => {
      const mockListener1 = vi.fn();
      const mockListener2 = vi.fn();
      const event = rpcClientV2.onUpdateChangedFiles;

      const disposable1 = event(mockListener1);
      const disposable2 = event(mockListener2);

      expect(disposable1).toBeDefined();
      expect(disposable2).toBeDefined();

      // Should call RpcClient.on only once per event
      expect(mockRpcClient.on).toHaveBeenCalledTimes(1);
      expect(mockRpcClient.on).toHaveBeenCalledWith('onUpdateChangedFiles', expect.any(Function));
    });

    it('should fire events when underlying RPC client fires them', () => {
      const mockListener = vi.fn();
      const event = rpcClientV2.onUpdateIndexInfo;
      event(mockListener);

      // Get the callback that was passed to RpcClient.on
      const rpcCallback = mockRpcClient.on.mock.calls[0][1];
      const testData = { buildRecords: [], workspaceFolders: [] };

      // Simulate the RPC client firing the event
      rpcCallback(testData);

      expect(mockListener).toHaveBeenCalledWith(testData);
    });

    it('should reuse event instances for the same event', () => {
      const event1 = rpcClientV2.onUpdateIndexInfo;
      const event2 = rpcClientV2.onUpdateIndexInfo;

      expect(event1).toBe(event2);
      expect(mockRpcClient.on).toHaveBeenCalledTimes(1);
    });
  });

  describe('Proxy behavior', () => {
    it('should create proxy instances for properties', () => {
      const proxy1 = rpcClientV2.workspace;
      const proxy2 = rpcClientV2.inputHistory;

      expect(proxy1).toBeDefined();
      expect(proxy2).toBeDefined();
      expect(typeof proxy1).toBe('function');
      expect(typeof proxy2).toBe('function');
    });

    it('should ensure property access is idempotent', () => {
      const proxy1 = rpcClientV2.workspace;
      const proxy2 = rpcClientV2.workspace;
      const proxy3 = rpcClientV2.inputHistory;
      const proxy4 = rpcClientV2.inputHistory;

      // Same property should return the same proxy instance
      expect(proxy1).toBe(proxy2);
      expect(proxy3).toBe(proxy4);

      // Different properties should return different proxy instances
      expect(proxy1).not.toBe(proxy3);
    });

    it('should ensure method access is idempotent', () => {
      const method1 = rpcClientV2.workspace.readFileContent;
      const method2 = rpcClientV2.workspace.readFileContent;
      const method3 = rpcClientV2.inputHistory.addHistory;
      const method4 = rpcClientV2.inputHistory.addHistory;

      // Same method should return the same function
      expect(method1).toBe(method2);
      expect(method3).toBe(method4);

      // Different methods should return different functions
      expect(method1).not.toBe(method3);
    });

    it('should handle non-existent properties gracefully', () => {
      const nonExistent = (rpcClientV2 as any).nonExistent;
      expect(nonExistent).toBeDefined();
      expect(typeof nonExistent).toBe('function');
    });

    it('should handle nested property access on non-existent properties', () => {
      const nested = (rpcClientV2 as any).nonExistent.nested;
      expect(nested).toBeDefined();
      expect(typeof nested).toBe('function');
    });

    it('should handle Symbol properties gracefully', () => {
      const symbolProp = Symbol('test');

      // Should not throw when accessing Symbol properties
      expect(() => {
        (rpcClientV2 as any)[symbolProp];
      }).not.toThrow();

      // Should return undefined for Symbol properties
      const result = (rpcClientV2 as any)[symbolProp];
      expect(result).toBeUndefined();
    });
  });

  describe('Constructor behavior', () => {
    it('should create a new instance each time', () => {
      const instance1 = new RpcClientV2();
      const instance2 = new RpcClientV2();

      expect(instance1).not.toBe(instance2);
    });

    it('should create a new RpcClient for each instance', () => {
      // Clear previous calls from beforeEach
      vi.clearAllMocks();

      new RpcClientV2();
      new RpcClientV2();

      expect(RpcClient).toHaveBeenCalledTimes(2);
    });
  });

  describe('Type safety', () => {
    it('should maintain type safety for method calls', () => {
      // This test ensures TypeScript compilation works correctly
      // The actual type checking happens at compile time
      expect(() => {
        // These should compile without errors
        rpcClientV2.showInformationMessage({ content: 'test' });
        rpcClientV2.workspace.readFileContent({ path: '/test' });
        rpcClientV2.inputHistory.addHistory({ input: 'test' });
      }).not.toThrow();
    });

    it('should maintain type safety for event listeners', () => {
      // This test ensures TypeScript compilation works correctly for events
      expect(() => {
        // These should compile without errors
        rpcClientV2.onUpdateIndexInfo(() => {});
        rpcClientV2.onUpdateChangedFiles(() => {});
      }).not.toThrow();
    });
  });

  describe('Error handling', () => {
    it('should handle undefined method calls gracefully', async () => {
      const undefinedMethod = (rpcClientV2 as any).undefinedMethod;

      // Should not throw when accessing undefined method
      expect(() => {
        undefinedMethod();
      }).not.toThrow();

      // Should call RPC client with undefined method name
      await undefinedMethod();
      expect(mockRpcClient.call).toHaveBeenCalledWith('undefinedMethod');
    });

    it('should handle null/undefined parameters', async () => {
      await rpcClientV2.showInformationMessage({ content: 'test' });

      expect(mockRpcClient.call).toHaveBeenCalledWith('showInformationMessage', { content: 'test' });
    });
  });

  describe('Performance considerations', () => {
    it('should reuse proxy instances for the same property', () => {
      const proxy1 = rpcClientV2.workspace;
      const proxy2 = rpcClientV2.workspace;
      const proxy3 = rpcClientV2.inputHistory;

      expect(proxy1).toBe(proxy2);
      expect(proxy1).not.toBe(proxy3);
      expect(typeof proxy1).toBe('function');
      expect(typeof proxy3).toBe('function');
    });

    it('should reuse method instances for the same method', () => {
      const method1 = rpcClientV2.workspace.readFileContent;
      const method2 = rpcClientV2.workspace.readFileContent;
      const method3 = rpcClientV2.inputHistory.addHistory;

      expect(method1).toBe(method2);
      expect(method1).not.toBe(method3);
      expect(typeof method1).toBe('function');
      expect(typeof method3).toBe('function');
    });

    it('should reuse event instances for the same event', () => {
      const event1 = rpcClientV2.onUpdateIndexInfo;
      const event2 = rpcClientV2.onUpdateIndexInfo;

      expect(event1).toBe(event2);
      expect(mockRpcClient.on).toHaveBeenCalledTimes(1);
    });
  });
});

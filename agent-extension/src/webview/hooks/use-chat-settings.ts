import { useCallback, useContext, useEffect, useState } from 'react';
import { RpcContext } from '@/webview/contexts/rpc-context';
import { DEFAULT_CHAT_SETTINGS } from '@/const';
import type { ChatSettings } from '@/types/settings';
import { deepMerge } from '@/utils';

export function useChatSettings() {
  const [chatSettings, setChatSettings] = useState<ChatSettings>(DEFAULT_CHAT_SETTINGS);
  const rpcClient = useContext(RpcContext);

  const requestChatSettings = useCallback(async () => {
    const res = await rpcClient?.call('getChatSettings');
    setChatSettings(res);
  }, [rpcClient]);

  useEffect(() => {
    const handleChatSettings = (params: ChatSettings) => {
      setChatSettings(params);
    };

    rpcClient?.on('updateChatSettings', handleChatSettings);
    requestChatSettings();

    return () => {
      rpcClient?.off('updateChatSettings', handleChatSettings);
    };
  }, [rpcClient, requestChatSettings]);

  const saveChatSettings = (params: Partial<ChatSettings>) => {
    rpcClient?.call('saveChatSettings', deepMerge(chatSettings, params));
  };

  return {
    chatSettings,
    saveChatSettings,
  };
}

import { useContext, useEffect, useState } from 'react';
import type { MerkleHolderSyncBuildIndexEvent } from '@/services/merkle/merkle-holder';
import type { WorkspaceFolder } from 'vscode';
import { uniqBy } from 'lodash';
import { RpcContextV2 } from '../contexts/rpc-context-v2';

export interface IndexInfo {
  buildRecords: MerkleHolderSyncBuildIndexEvent[];
  workspaceFolders: readonly WorkspaceFolder[];
}

const isIndexExist = (buildRecords: MerkleHolderSyncBuildIndexEvent[]) => {
  return buildRecords.at(-1)?.indexExist === true;
};

const isIndexLoading = (buildRecords: MerkleHolderSyncBuildIndexEvent[]) => {
  return buildRecords.at(-1)?.status === 'loading';
};

export function useIndexInfo() {
  const [indexInfo, setIndexInfo] = useState<IndexInfo | null>(null);
  const [indexState, setIndexState] = useState<'init' | 'loading' | 'success' | 'failed'>('loading');
  const [indexExist, setIndexExist] = useState<boolean | undefined>(undefined);
  const rpcClientV2 = useContext(RpcContextV2);

  useEffect(() => {
    const handler = (params: IndexInfo) => {
      setIndexInfo(params);

      for (const folder of params.workspaceFolders) {
        const buildRecord = uniqBy(
          params.buildRecords.filter((record) => record.repoInfo.path === folder.uri.path),
          (record) => record.label,
        ).filter((record) => record.label === 'vector');

        if (buildRecord.length === 0) {
          setIndexExist(undefined);
        }

        if (isIndexExist(buildRecord)) {
          setIndexExist(true);
        } else {
          setIndexExist(false);
        }
        // 目前每个仓库有 vector / summary 索引
        if (buildRecord.length === 0) {
          setIndexState('init');
          return;
        }

        if (isIndexLoading(buildRecord)) {
          setIndexState('loading');
          return;
        }

        const containsError = buildRecord.some((record) => record.status === 'failed');
        if (containsError) {
          setIndexState('failed');
          return;
        }
        const containsLoading = buildRecord.some((record) => record.status === 'loading');
        if (containsLoading) {
          setIndexState('loading');
          return;
        }
      }
      setIndexState('success');
    };
    const dispose = rpcClientV2.onUpdateIndexInfo(handler);
    requestIndexInfo();
    return () => dispose.dispose();
  }, []);

  const requestIndexInfo = () => {
    rpcClientV2.requestIndexInfo();
  };

  return { indexInfo, indexState, indexExist, requestIndexInfo };
}

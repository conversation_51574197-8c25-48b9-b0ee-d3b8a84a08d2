import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { ControlledTreeEnvironment, Tree, TreeItemIndex } from 'react-complex-tree';
import { RpcContext } from '@/webview/contexts/rpc-context';
import { DocumentItem } from '@/services/documents/documents-service.interface';
import { Button, Heading } from '@radix-ui/themes';

// 文案常量
const TEXT = {
  TITLE: ' Specs',
  REFRESH: '刷新',
  CLOSE: '关闭',
  LOADING: '正在加载文档...',
  NO_DOCUMENTS_TITLE: '当前还未创建 Specs 文档',
  NO_DOCUMENTS_DESCRIPTION: '使用 Codin 的规划执行能力会自动为需求生成对应的 Specs 文档',
  DOCUMENTS: '文档',
  TREE_LABEL: '文档',
} as const;

const defaultSelectedItems: TreeItemIndex[] = [];

export const Documents: React.FC = () => {
  const rpcClient = React.useContext(RpcContext);
  const [documents, setDocuments] = useState<DocumentItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedFolders, setExpandedFolders] = useState<string[]>([]);
  const [folderChildren, setFolderChildren] = useState<Map<string, DocumentItem[]>>(new Map());
  const [treeKey, setTreeKey] = useState(0); // 用于强制刷新树组件

  const handleClose = () => {
    rpcClient?.call('closeDocumentsWebview', {});
  };

  const loadDocuments = async () => {
    try {
      setLoading(true);
      const result = await rpcClient?.call('getDocumentItems', {});
      if (result?.data) {
        setDocuments(result.data);
      }
    } catch (error) {
      console.error('Failed to load documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadFolderChildren = async (folderId: string) => {
    console.log('loadFolderChildren called with folderId:', folderId);
    if (!folderChildren.has(folderId)) {
      try {
        console.log('Loading children for folder:', folderId);
        const result = await rpcClient?.call('getDocumentChildren', { folderPath: folderId });
        console.log('Result from getDocumentChildren:', result);
        if (result?.data) {
          const newFolderChildren = new Map(folderChildren);
          newFolderChildren.set(folderId, result.data);
          setFolderChildren(newFolderChildren);
          // 强制刷新树组件
          setTreeKey((prev) => prev + 1);
          console.log('Updated folder children for:', folderId, 'New children:', result.data);
        }
      } catch (error) {
        console.error('Failed to load folder children:', error);
      }
    } else {
      console.log('Folder children already loaded for:', folderId);
    }
  };

  const openDocument = async (filePath: string) => {
    try {
      await rpcClient?.call('openDocument', { filePath });
    } catch (error) {
      console.error('Failed to open document:', error);
    }
  };

  // Transform documents to React Complex Tree format
  const treeItems = useMemo(() => {
    console.log(
      'Regenerating treeItems with documents:',
      documents.length,
      'folderChildren:',
      folderChildren.size,
      'treeKey:',
      treeKey,
    );
    if (documents.length === 0) return {};

    const transformed: Record<string, any> = {};

    const processItem = (item: DocumentItem): string => {
      // Check if we have loaded children for this folder
      const loadedChildren = folderChildren.get(item.id);
      const children = loadedChildren || item.children || [];

      console.log('Processing item:', item.id, item.name, 'type:', item.type, 'children count:', children.length);

      const transformedItem = {
        index: item.id,
        data: item.name,
        isFolder: item.type === 'folder',
        children: children.map((child) => processItem(child)),
        canRename: false,
        canMove: false,
        canSelect: true,
        // Store original path for document opening
        path: item.path,
      };

      transformed[item.id] = transformedItem;
      return item.id;
    };

    // Create a root item that contains all top-level items
    const rootChildren = documents.map(processItem);
    transformed.root = {
      index: 'root',
      data: TEXT.DOCUMENTS,
      isFolder: true,
      children: rootChildren,
      canRename: false,
      canMove: false,
      canSelect: false,
    };

    console.log('Generated treeItems:', Object.keys(transformed));
    return transformed;
  }, [documents, folderChildren, treeKey]); // 添加 treeKey 作为依赖项

  // Handle item expansion
  const handleExpandItem = useCallback(
    async (item: any) => {
      console.log('handleExpandItem called with:', item);
      if (item?.isFolder) {
        const itemIndex = item.index;
        console.log('Expanding folder:', itemIndex);
        setExpandedFolders((prev) => [...prev, itemIndex]);
        await loadFolderChildren(itemIndex);
      }
    },
    [loadFolderChildren],
  );

  // Handle item collapse
  const handleCollapseItem = useCallback((item: any) => {
    console.log('handleCollapseItem called with:', item);
    if (item?.isFolder) {
      const itemIndex = item.index;
      console.log('Collapsing folder:', itemIndex);
      setExpandedFolders((prev) => prev.filter((id) => id !== itemIndex));
    }
  }, []);

  // Handle item selection to open documents
  const handleSelectItems = (items: TreeItemIndex[], _treeId: string) => {
    if (items.length > 0) {
      const selectedItem = treeItems[String(items[0])];
      if (selectedItem && !selectedItem.isFolder && selectedItem.path) {
        openDocument(selectedItem.path);
      }
    }
  };

  useEffect(() => {
    loadDocuments();
  }, []);

  return (
    <div style={{ fontFamily: 'sans-serif', padding: '20px', height: '100%' }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid var(--vscode-panel-border)',
        }}
      >
        <Heading mb="0" size="4">
          {TEXT.TITLE}
        </Heading>
        <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
          <Button size="1" onClick={loadDocuments}>
            {TEXT.REFRESH}
          </Button>
          <button
            type="button"
            onClick={handleClose}
            className="settings-close-button"
            title={TEXT.CLOSE}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '18px',
              cursor: 'pointer',
              color: 'var(--vscode-foreground)',
            }}
          >
            &#x2715;
          </button>
        </div>
      </div>

      <div style={{ marginTop: '20px', height: '100%' }}>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <span>{TEXT.LOADING}</span>
          </div>
        ) : documents.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '20px', color: 'var(--vscode-descriptionForeground)' }}>
            <p>{TEXT.NO_DOCUMENTS_TITLE}</p>
            <p style={{ fontSize: '12px' }}>{TEXT.NO_DOCUMENTS_DESCRIPTION}</p>
          </div>
        ) : (
          <div
            style={{
              fontSize: '14px',
              color: 'var(--vscode-foreground)',
              backgroundColor: 'var(--vscode-sideBar-background)',
              border: '1px solid var(--vscode-panel-border)',
              borderRadius: '4px',
              padding: '8px',
              height: 'calc(100% - 60px)',
            }}
            className="rct-dark"
          >
            <ControlledTreeEnvironment
              key={treeKey} // 使用 key 强制重新渲染
              items={treeItems}
              getItemTitle={(item) => item.data}
              viewState={{
                'documents-tree': {
                  expandedItems: expandedFolders,
                  selectedItems: defaultSelectedItems,
                },
              }}
              onExpandItem={handleExpandItem}
              onCollapseItem={handleCollapseItem}
              onSelectItems={handleSelectItems}
              canDragAndDrop={false}
              canDropOnFolder={false}
              canReorderItems={false}
            >
              <Tree treeId="documents-tree" rootItem="root" treeLabel={TEXT.TREE_LABEL} />
            </ControlledTreeEnvironment>
          </div>
        )}
      </div>
    </div>
  );
};

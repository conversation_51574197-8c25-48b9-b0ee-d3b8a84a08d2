import React, { use<PERSON>ontex<PERSON>, useEffect, useState } from 'react';
import { ModelType } from '@/bam/namespaces/base';
import { useModelSettings } from '../../../../hooks/use-model-settings';
import { DefaultModelType as DefaultCodingModelType } from '../../../../../services/coding-chat/coding-chat-service.interface';
import { DefaultModelType as DefaultUnderstandingModelType } from '../../../../../services/understanding-chat/understanding-service.interface';
import { DefaultModelType as DefaultPlanningModelType } from '../../../../../services/prd-chat/chat-service.interface';
import { RpcContextV2 } from '@/webview/contexts/rpc-context-v2';

// 模型类型选项
const MODEL_OPTIONS = [
  { value: ModelType.DeepSeek, label: 'DeepSeek' },
  { value: ModelType.Claude, label: '<PERSON>' },
  { value: ModelType.DoubaoPro, label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { value: ModelType.DoubaoThinkingPro, label: 'DoubaoThinkingPro' },
  { value: ModelType.DeepSeekR1, label: 'DeepSeekR1' },
  { value: ModelType.GPT4_1, label: 'GPT4.1' },
  { value: ModelType.Gemini2_5_Pro, label: 'Gemini2.5 Pro' },
  { value: ModelType.Claude4_Sonnet, label: 'Claude4 Sonnet' },
  { value: ModelType.Claude4_Opus, label: 'Claude4 Opus' },
  { value: ModelType.Seed1_6_Thinking, label: 'Seed1.6 Thinking' },
  { value: ModelType.GPT_O3, label: 'GPT-O3' },
  { value: ModelType.Seed1_6, label: 'Seed1.6' },
  { value: ModelType.Kimi_K2, label: 'Kimi K2' },
  { value: ModelType.GPT_5, label: 'GPT-5' },
];

export const DevModeTabContent = () => {
  const {
    planningModel,
    codingModel,
    understandingModel,
    networkEnv,
    savePlanningModel,
    saveCodingModel,
    saveUnderstandingModel,
    saveNetworkEnv,
    clearAllModelSettings,
  } = useModelSettings();
  const [localPlanningModel, setLocalPlanningModel] = useState<ModelType>(DefaultPlanningModelType);
  const [localCodingModel, setLocalCodingModel] = useState<ModelType>(DefaultCodingModelType);
  const [localUnderstandingModel, setLocalUnderstandingModel] = useState<ModelType>(DefaultUnderstandingModelType);
  const [planningModelLoading, setPlanningModelLoading] = useState(false);
  const [codingModelLoading, setCodingModelLoading] = useState(false);
  const [understandingModelLoading, setUnderstandingModelLoading] = useState(false);
  const [clearAllLoading, setClearAllLoading] = useState(false);
  const [localNetworkEnv, setLocalNetworkEnv] = useState<string>('');
  const [networkEnvLoading, setNetworkEnvLoading] = useState(false);
  const rpcClientV2 = useContext(RpcContextV2);

  useEffect(() => {
    if (planningModel !== null) {
      setLocalPlanningModel(planningModel);
    }
  }, [planningModel]);

  useEffect(() => {
    if (codingModel !== null) {
      setLocalCodingModel(codingModel);
    }
  }, [codingModel]);

  useEffect(() => {
    if (understandingModel !== null) {
      setLocalUnderstandingModel(understandingModel);
    }
  }, [understandingModel]);

  useEffect(() => {
    if (networkEnv !== null) {
      setLocalNetworkEnv(networkEnv);
    }
  }, [networkEnv]);

  const handlePlanningModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setLocalPlanningModel(Number(e.target.value) as ModelType);
  };

  const handleCodingModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setLocalCodingModel(Number(e.target.value) as ModelType);
  };

  const handleUnderstandingModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setLocalUnderstandingModel(Number(e.target.value) as ModelType);
  };

  const handleNetworkEnvChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalNetworkEnv(e.target.value);
  };

  const handleSavePlanningModel = async () => {
    setPlanningModelLoading(true);
    try {
      await savePlanningModel(localPlanningModel);
    } catch (e: any) {
      rpcClientV2.showErrorMessage({
        content: 'Planning模型保存失败',
        detail: e.message,
        modal: true,
      });
    } finally {
      setPlanningModelLoading(false);
    }
  };

  const handleSaveCodingModel = async () => {
    setCodingModelLoading(true);
    try {
      await saveCodingModel(localCodingModel);
    } catch (e: any) {
      rpcClientV2.showErrorMessage({
        content: 'Coding模型保存失败',
        detail: e.message,
        modal: true,
      });
    } finally {
      setCodingModelLoading(false);
    }
  };

  const handleSaveUnderstandingModel = async () => {
    setUnderstandingModelLoading(true);
    try {
      await saveUnderstandingModel(localUnderstandingModel);
    } catch (e: any) {
      rpcClientV2.showErrorMessage({
        content: 'Understanding模型保存失败',
        detail: e.message,
        modal: true,
      });
    } finally {
      setUnderstandingModelLoading(false);
    }
  };

  const handleSaveNetworkEnv = async () => {
    setNetworkEnvLoading(true);
    try {
      await saveNetworkEnv(localNetworkEnv);
    } catch (e: any) {
      rpcClientV2.showErrorMessage({
        content: '网络环境设置保存失败',
        detail: e.message,
        modal: true,
      });
    } finally {
      setNetworkEnvLoading(false);
    }
  };

  const handleClearAllModelSettings = async () => {
    setClearAllLoading(true);
    try {
      await clearAllModelSettings();
    } catch (e: any) {
      rpcClientV2.showErrorMessage({
        content: '清除模型设置失败',
        detail: e.message,
        modal: true,
      });
    } finally {
      setClearAllLoading(false);
    }
  };

  return (
    <div>
      <h2 className="sub-title">开发模式设置</h2>
      <p className="tips-text">此面板用于开发调试，修改Planning、Coding和Understanding的模型类型</p>
      {/* 网络环境开关 */}
      <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center', gap: 12 }}>
        <label htmlFor="network-env" style={{ fontWeight: 500, flexShrink: 0 }}>
          网络环境
        </label>
        <input
          id="network-env"
          type="text"
          value={localNetworkEnv}
          onChange={handleNetworkEnvChange}
          disabled={networkEnvLoading}
          style={{
            width: '100%',
            height: 28,
            borderRadius: '4px',
            padding: '0 4px',
            cursor: networkEnvLoading ? 'not-allowed' : 'pointer',
          }}
        />
        <button
          type="button"
          className="button-primary"
          onClick={handleSaveNetworkEnv}
          disabled={networkEnvLoading}
          style={{
            minWidth: '60px',
            opacity: networkEnvLoading ? 0.6 : 1,
            cursor: networkEnvLoading ? 'not-allowed' : 'pointer',
            flexShrink: 0,
          }}
        >
          {networkEnvLoading ? '保存中...' : '保存'}
        </button>
      </div>
      {/* 全局清除按钮 */}
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'flex-end' }}>
        <button
          type="button"
          className="button-primary"
          onClick={handleClearAllModelSettings}
          disabled={clearAllLoading || planningModelLoading || codingModelLoading || understandingModelLoading}
          style={{
            minWidth: '120px',
            opacity:
              clearAllLoading || planningModelLoading || codingModelLoading || understandingModelLoading ? 0.6 : 1,
            cursor:
              clearAllLoading || planningModelLoading || codingModelLoading || understandingModelLoading
                ? 'not-allowed'
                : 'pointer',
            backgroundColor: 'var(--vscode-button-secondaryBackground)',
            color: 'var(--vscode-button-secondaryForeground)',
          }}
        >
          {clearAllLoading ? '清除中...' : '清除所有设置'}
        </button>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
        {/* Planning Model Settings */}
        <div
          style={{
            padding: 16,
            border: '1px solid var(--vscode-editorWidget-border, #454545)',
            borderRadius: 4,
            background: 'var(--vscode-editorWidget-background, #252526)',
          }}
        >
          <h3 style={{ margin: '0 0 12px 0', fontSize: '14px' }}>Planning Model</h3>
          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <label htmlFor="planning-model">模型类型:</label>
            <select
              id="planning-model"
              value={localPlanningModel}
              onChange={handlePlanningModelChange}
              disabled={planningModelLoading}
              className="monaco-select-box"
              style={{
                flex: 1,
                opacity: planningModelLoading ? 0.6 : 1,
              }}
            >
              {MODEL_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <button
              type="button"
              className="button-primary"
              onClick={handleSavePlanningModel}
              disabled={planningModelLoading}
              style={{
                minWidth: '60px',
                opacity: planningModelLoading ? 0.6 : 1,
                cursor: planningModelLoading ? 'not-allowed' : 'pointer',
              }}
            >
              {planningModelLoading ? '保存中...' : '保存'}
            </button>
          </div>
          <p
            style={{
              margin: '8px 0 0 0',
              fontSize: '12px',
              color: 'var(--vscode-descriptionForeground)',
            }}
          >
            当前:{' '}
            {planningModel !== null ? MODEL_OPTIONS.find((opt) => opt.value === planningModel)?.label : 'Loading...'}
          </p>
        </div>

        {/* Coding Model Settings */}
        <div
          style={{
            padding: 16,
            border: '1px solid var(--vscode-editorWidget-border, #454545)',
            borderRadius: 4,
            background: 'var(--vscode-editorWidget-background, #252526)',
          }}
        >
          <h3 style={{ margin: '0 0 12px 0', fontSize: '14px' }}>Coding Model</h3>
          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <label htmlFor="coding-model">模型类型:</label>
            <select
              id="coding-model"
              value={localCodingModel}
              onChange={handleCodingModelChange}
              disabled={codingModelLoading}
              className="monaco-select-box"
              style={{
                flex: 1,
                opacity: codingModelLoading ? 0.6 : 1,
              }}
            >
              {MODEL_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <button
              type="button"
              className="button-primary"
              onClick={handleSaveCodingModel}
              disabled={codingModelLoading}
              style={{
                minWidth: '60px',
                opacity: codingModelLoading ? 0.6 : 1,
                cursor: codingModelLoading ? 'not-allowed' : 'pointer',
              }}
            >
              {codingModelLoading ? '保存中...' : '保存'}
            </button>
          </div>
          <p
            style={{
              margin: '8px 0 0 0',
              fontSize: '12px',
              color: 'var(--vscode-descriptionForeground)',
            }}
          >
            当前: {codingModel !== null ? MODEL_OPTIONS.find((opt) => opt.value === codingModel)?.label : 'Loading...'}
          </p>
        </div>

        {/* Understanding Model Settings */}
        <div
          style={{
            padding: 16,
            border: '1px solid var(--vscode-editorWidget-border, #454545)',
            borderRadius: 4,
            background: 'var(--vscode-editorWidget-background, #252526)',
          }}
        >
          <h3 style={{ margin: '0 0 12px 0', fontSize: '14px' }}>Understanding Model</h3>
          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <label htmlFor="understanding-model">模型类型:</label>
            <select
              id="understanding-model"
              value={localUnderstandingModel}
              onChange={handleUnderstandingModelChange}
              disabled={understandingModelLoading}
              className="monaco-select-box"
              style={{
                flex: 1,
                opacity: understandingModelLoading ? 0.6 : 1,
              }}
            >
              {MODEL_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <button
              type="button"
              className="button-primary"
              onClick={handleSaveUnderstandingModel}
              disabled={understandingModelLoading}
              style={{
                minWidth: '60px',
                opacity: understandingModelLoading ? 0.6 : 1,
                cursor: understandingModelLoading ? 'not-allowed' : 'pointer',
              }}
            >
              {understandingModelLoading ? '保存中...' : '保存'}
            </button>
          </div>
          <p
            style={{
              margin: '8px 0 0 0',
              fontSize: '12px',
              color: 'var(--vscode-descriptionForeground)',
            }}
          >
            当前:{' '}
            {understandingModel !== null
              ? MODEL_OPTIONS.find((opt) => opt.value === understandingModel)?.label
              : 'Loading...'}
          </p>
        </div>
      </div>
    </div>
  );
};

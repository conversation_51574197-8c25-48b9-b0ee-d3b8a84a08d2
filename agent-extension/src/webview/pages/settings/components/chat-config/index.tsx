import { RPC_COMMANDS } from '@/const';
import type { ChatSettings } from '@/types/settings';
import { RpcContext } from '@/webview/contexts/rpc-context';
import { RpcContextV2 } from '@/webview/contexts/rpc-context-v2';
import { Card, Flex, Select, Separator, Switch, Text } from '@radix-ui/themes';
import { debounce } from 'lodash';
import { useContext } from 'react';
import { useChatSettings } from '../../../../hooks/use-chat-settings';

export const ChatConfigTabContent = () => {
  const { chatSettings, saveChatSettings } = useChatSettings();
  const rpcClient = useContext(RpcContext);
  const rpcClientV2 = useContext(RpcContextV2);
  const handConfigChange = async (params: Partial<ChatSettings>) => {
    try {
      await saveChatSettings({ tool: params.tool, preference: params.preference });
    } catch (e: any) {
      rpcClientV2.showErrorMessage({
        content: '设置保存失败',
        detail: e.message,
        modal: true,
      });
    }
  };

  const settings: {
    title: string;
    items: {
      title: string;
      description: string;
      additionalDescription?: string;
      actionNode: React.ReactNode;
    }[];
  }[] = [
    {
      title: '偏好设置',
      items: [
        {
          title: '默认布局',
          description: '会话框在编辑器中的位置（需重启IDE）',
          additionalDescription: '改为右侧展示后，插件入口会移至右下方状态栏',
          actionNode: (
            <Select.Root
              value={chatSettings.preference?.placement ?? 'right'}
              onValueChange={async (value) => {
                Promise.all([
                  rpcClient!.call(RPC_COMMANDS.RELOAD_WEBVIEW),
                  handConfigChange({ preference: { placement: value as 'left' | 'right' } }),
                ]);
              }}
            >
              <Select.Trigger />
              <Select.Content>
                <Select.Item value="left">左</Select.Item>
                <Select.Item value="right">右</Select.Item>
              </Select.Content>
            </Select.Root>
          ),
        },
      ],
    },
    {
      title: 'Tool 设置',
      items: [
        {
          title: '自动运行',
          description: '启用后，工具将自动运行而无需用户确认',
          actionNode: (
            <Switch
              className="cursor-pointer"
              checked={chatSettings.tool?.autoRun}
              onCheckedChange={debounce((checked) => handConfigChange({ tool: { autoRun: checked } }))}
            />
          ),
        },
      ],
    },
  ];

  return (
    <div className="flex flex-col">
      {settings.map((item) => (
        <div key={item.title}>
          <h2 className="sub-title">{item.title}</h2>
          <Card>
            {item.items.map((item, index) => (
              <>
                {index > 0 && <Separator my="3" size="4" />}
                <Flex key={item.title} gap="3" align="center" justify="between">
                  <Flex direction="column">
                    <Text size={'2'}>{item.title}</Text>
                    <Text color="gray" size="1">
                      {item.description}
                    </Text>
                    {item.additionalDescription && (
                      <Text color="gray" size="1">
                        {item.additionalDescription}
                      </Text>
                    )}
                  </Flex>
                  {item.actionNode}
                </Flex>
              </>
            ))}
          </Card>
        </div>
      ))}
    </div>
  );
};

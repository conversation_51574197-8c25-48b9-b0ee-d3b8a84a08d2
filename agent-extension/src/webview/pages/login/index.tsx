import { useContext } from 'react';
import { RpcContext } from '@/webview/contexts/rpc-context';

export const Login = () => {
  const rpcClient = useContext(RpcContext);
  const login = () => {
    if (rpcClient) {
      rpcClient.call('loginAccount');
    }
  };

  return (
    <div className="login-container">
      {/* <img src="/codin-icon.svg" alt="Codin Logo" className="logo" /> */}
      <h1 className="title">Codin AI</h1>
      <p className="subtitle">使用 Codin AI 轻松生成或理解代码，让你的开发更快更智能！</p>
      <button type="button" className="login-button" onClick={login}>
        Log in
      </button>
    </div>
  );
};

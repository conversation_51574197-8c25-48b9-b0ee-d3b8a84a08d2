import { AgentWorkMode } from '@/bam/namespaces/coding';
import { DEFAULT_WORK_MODE } from '@/constants/storage';
import { RpcContext } from '@/webview/contexts/rpc-context';
import { Tooltip } from 'radix-ui';
import { Button, DropdownMenu } from '@radix-ui/themes';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
function transform(mode: AgentWorkMode): string {
  if (mode === AgentWorkMode.Act) {
    return '快速执行';
  }
  return '规划执行';
}

interface Props {
  messageCount: number;
  currentConversationId: string;
}

export const CodingMode = (props: Props) => {
  const { messageCount, currentConversationId } = props;
  const [selectedMode, setSelectedMode] = useState<AgentWorkMode>(DEFAULT_WORK_MODE);
  const [isChangingMode, setIsChangingMode] = useState(false);
  const rpcClient = useContext(RpcContext);

  const isNewConversation = useMemo(() => {
    return messageCount === 0;
  }, [messageCount]);

  const disabled = useMemo(() => {
    return !isNewConversation || isChangingMode;
  }, [isNewConversation, isChangingMode]);

  const setConversationMode = useCallback(
    async (mode: AgentWorkMode) => {
      try {
        setIsChangingMode(true);
        setSelectedMode(mode);
        if (!rpcClient) return;

        rpcClient.call('setLastSelectedConversationMode', { mode });

        if (!currentConversationId) return;
        await rpcClient.call('setConversationMode', { cid: currentConversationId, mode });
      } finally {
        setIsChangingMode(false);
      }
    },
    [rpcClient, currentConversationId],
  );

  // 获取当前模式
  useEffect(() => {
    console.info('getConversationMode', currentConversationId);
    if (!rpcClient) return;

    const fetchCurrentMode = async () => {
      try {
        if (!currentConversationId) {
          await setConversationMode(DEFAULT_WORK_MODE);
          return;
        }

        const mode: AgentWorkMode = await rpcClient.call('getConversationMode', { cid: currentConversationId });
        console.info('current mode', mode);
        await setConversationMode(mode || DEFAULT_WORK_MODE);
      } catch (error) {
        console.error('Failed to fetch conversation mode:', error);
        await setConversationMode(DEFAULT_WORK_MODE);
      }
    };

    fetchCurrentMode();
  }, [rpcClient, currentConversationId, setConversationMode]);

  const handleModeClick = (mode: AgentWorkMode) => {
    if (!disabled && mode !== selectedMode) {
      setConversationMode(mode);
    }
  };
  const renderButton = () =>
    disabled && !isNewConversation ? (
      <Tooltip.Provider>
        <Tooltip.Root>
          <Tooltip.Trigger asChild>
            <Button className="mode-button" disabled={disabled} radius="none" highContrast variant="soft">
              {transform(selectedMode)}
              <DropdownMenu.TriggerIcon fontSize={12} />
            </Button>
          </Tooltip.Trigger>
          <Tooltip.Portal>
            <Tooltip.Content side="top" sideOffset={6} className="tooltip-content">
              会话一旦开始，模式不再可变。如果需要切换模式，请新建会话。
            </Tooltip.Content>
          </Tooltip.Portal>
        </Tooltip.Root>
      </Tooltip.Provider>
    ) : (
      <Button className="mode-button" disabled={disabled} radius="none" highContrast variant="soft">
        {transform(selectedMode)}
        <DropdownMenu.TriggerIcon fontSize={12} />
      </Button>
    );

  return (
    <div className="coding-mode-toggle">
      <div>
        {disabled ? (
          renderButton()
        ) : (
          <DropdownMenu.Root>
            <DropdownMenu.Trigger>{renderButton()}</DropdownMenu.Trigger>
            <DropdownMenu.Content size={'1'}>
              <Tooltip.Provider>
                <Tooltip.Root>
                  <Tooltip.Trigger asChild>
                    <DropdownMenu.Item onSelect={() => handleModeClick(AgentWorkMode.PlanAct)}>
                      <p className="menu-item-text">规划执行</p>
                    </DropdownMenu.Item>
                  </Tooltip.Trigger>
                  <Tooltip.Portal>
                    <Tooltip.Content side="right" sideOffset={6} className="tooltip-content">
                      先规划，再执行。适合比较复杂、需要深度分析的任务（如需求文档生成代码）。
                    </Tooltip.Content>
                  </Tooltip.Portal>
                </Tooltip.Root>
              </Tooltip.Provider>
              <Tooltip.Provider>
                <Tooltip.Root>
                  <Tooltip.Trigger asChild>
                    <DropdownMenu.Item onSelect={() => handleModeClick(AgentWorkMode.Act)}>
                      <p className="menu-item-text">快速执行</p>
                    </DropdownMenu.Item>
                  </Tooltip.Trigger>
                  <Tooltip.Portal>
                    <Tooltip.Content side="right" sideOffset={6} className="tooltip-content">
                      直接执行编码任务。适合改动明确、简单重复的任务。
                    </Tooltip.Content>
                  </Tooltip.Portal>
                </Tooltip.Root>
              </Tooltip.Provider>
            </DropdownMenu.Content>
          </DropdownMenu.Root>
        )}
      </div>
    </div>
  );
};

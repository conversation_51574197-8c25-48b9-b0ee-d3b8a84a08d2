import type { FileNodeConfig } from '@/webview/components/tiptap-editor/extensions/file-node/extension';
import { CircleX } from 'lucide-react';

export const AttachedFile = (props: {
  file: FileNodeConfig;
  onDelete: () => void;
}) => {
  const { file, onDelete } = props;
  return (
    <div className="attached-file">
      {file.type === 'directory' ? (
        <svg
          width="12"
          height="12"
          viewBox="0 0 16 16"
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
          style={{ flexShrink: 0, opacity: 0.7 }}
        >
          <path d="M14.5 3H7.71l-.85-.85L6.51 2h-5l-.5.5v11l.5.5h13l.5-.5v-10L14.5 3zm-.51 8.49V13h-12V7h4.49l.35-.15.86-.86H14v1.5l-.01 4zm0-6.49h-6.5l-.35.15-.86.86H2v-3h4.29l.85.85.36.15H14l-.01.99z" />
        </svg>
      ) : (
        <svg
          width="12"
          height="12"
          viewBox="0 0 16 16"
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
          style={{ flexShrink: 0, opacity: 0.7 }}
        >
          <path d="M13.85 4.44L10.57 1.14L10.22 1H2.5L2 1.5V14.5L2.5 15H13.5L14 14.5V4.8L13.85 4.44ZM13 5H10V2L13 5ZM3 14V2H9V5.5L9.5 6H13V14H3Z" />
        </svg>
      )}
      <span style={{ opacity: 0.9 }}>{file.name}</span>
      <CircleX size={12} className="absolute -top-2 -right-2 cursor-pointer" onClick={onDelete} />
    </div>
  );
};

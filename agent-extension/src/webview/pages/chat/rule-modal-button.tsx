import { FilePlus2 } from 'lucide-react';
import { useState } from 'react';
import { RuleModal, RuleModalMode } from '../../components/rule-modal';
import { TooltipButton } from './tooltip-button';

// rules 配置按钮组件
export const RuleModalButton: React.FC = () => {
  const [modalIsOpen, setIsOpen] = useState(false);

  const handleAddRule = () => {
    setIsOpen(true);
  };

  return (
    <>
      <TooltipButton icon={<FilePlus2 size={16} />} tooltip="添加自定义规则" onClick={handleAddRule} />
      <RuleModal isOpen={modalIsOpen} onClose={() => setIsOpen(false)} mode={RuleModalMode.ADD} />
    </>
  );
};

import { Tooltip } from 'radix-ui';

// 输入框底部通用 Tooltip 按钮组件
export const TooltipButton: React.FC<{
  icon: React.ReactNode;
  tooltip: string;
  onClick?: () => void;
  className?: string;
}> = ({ icon, tooltip, onClick, className }) => (
  <Tooltip.Provider delayDuration={300}>
    <Tooltip.Root>
      <Tooltip.Trigger asChild>
        <div className={className} onClick={onClick}>
          {icon}
        </div>
      </Tooltip.Trigger>
      <Tooltip.Portal>
        <Tooltip.Content side="top" sideOffset={6} className="tooltip-content">
          {tooltip}
        </Tooltip.Content>
      </Tooltip.Portal>
    </Tooltip.Root>
  </Tooltip.Provider>
);

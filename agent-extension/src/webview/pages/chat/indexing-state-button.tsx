import { Database, DatabaseBackup, DatabaseZap, CircleAlert, Loader2 } from 'lucide-react';
import React from 'react';
import { useIndexInfo } from '../../hooks/use-index-info';
import { TooltipButton } from './tooltip-button';

const getIndexText = (state: 'init' | 'loading' | 'success' | 'failed') => {
  if (state === 'init') {
    return '索引更新未知';
  }
  if (state === 'loading') {
    return '索引更新中';
  }
  if (state === 'success') {
    return '索引更新成功';
  }
  if (state === 'failed') {
    return '索引更新失败';
  }
  return '索引更新未知';
};

export const IndexingStateButton: React.FC = () => {
  const { indexState, indexExist } = useIndexInfo();

  // 根据 indexExist 的三种状态确定显示内容
  const getTooltipContent = () => {
    if (indexExist === undefined) {
      return '正在获取索引信息...';
    }
    if (indexExist === true) {
      return getIndexText(indexState);
    }
    return '无索引, 请联系 Codin 创建索引';
  };

  const getIconElement = () => {
    if (indexExist === undefined) {
      return <Loader2 size={16} className="animate-spin" />;
    }
    if (indexExist === true) {
      // 有索引的情况下，根据索引状态使用不同图标
      if (indexState === 'loading') {
        return <DatabaseBackup size={16} />;
      }
      if (indexState === 'success') {
        return <DatabaseZap size={16} />;
      }
      if (indexState === 'failed') {
        return <CircleAlert size={16} />;
      }
      // init 和 failed 状态使用 DatabaseBackup
      return <Database size={16} />;
    }
    // 无索引的情况下使用置灰效果的 Database 图标
    return <Database className="opacity-60" size={16} />;
  };

  const getCursorClass = () => {
    if (indexExist === undefined) {
      return 'cursor-default';
    }
    return indexExist ? 'cursor-pointer' : 'cursor-not-allowed';
  };

  const tooltipContent = getTooltipContent();
  const iconElement = getIconElement();
  const cursorClass = getCursorClass();

  return <TooltipButton icon={iconElement} tooltip={tooltipContent} className={`controls-row ${cursorClass}`} />;
};

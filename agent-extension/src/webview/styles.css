@import "@radix-ui/themes/styles.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
* {
  box-sizing: border-box;
  font-size: 13px;
}

body,
#root {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
  color: var(--vscode-foreground);
  background: var(--vscode-sideBar-background);
}

/* 应用容器 */
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  color: var(--vscode-sideBarSectionHeader-foreground);
  background-color: transparent !important;
  position: relative;
}

.radix-themes:where([data-has-background="true"]) {
  background-color: transparent !important;
}

/* 内容容器 */
.content-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.page-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.page-content.active {
  display: block;
}

/* 聊天界面样式 */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px 16px;
}

/* .messages-container.with-status-bar {
  margin-bottom: 52px;
} */

.messages-container::-webkit-scrollbar {
  width: 0;
}

.messages-container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-background);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* 消息样式 */
.message {
  margin-bottom: 16px;
  padding: 0;
}

.message strong {
  display: inline-block;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 6px;
  color: var(--vscode-textLink-foreground);
}

.message-content {
  background: var(--vscode-editor-background);
  padding: 12px 16px;
  border-radius: 8px;
  border-left: 3px solid var(--vscode-progressBar-background);
  line-height: 1.5;
  word-wrap: break-word;
}

.message.error .message-content {
  background: var(--vscode-inputValidation-errorBackground);
  border-left-color: var(--vscode-inputValidation-errorBorder);
  color: var(--vscode-inputValidation-errorForeground);
}

/* 发送按钮样式 */
.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px;
  border: none;
  border-radius: 4px;
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  cursor: pointer;
  transition: opacity 0.2s;
}

.send-button:hover {
  opacity: 0.8;
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 6px;
  color: var(--vscode-foreground);
}

.empty-state-icon {
  font-size: 40px;
}

.empty-state-title {
  font-size: 24px;
  margin-top: 5px;
  font-weight: 500;
  text-align: center;
  line-height: normal;
  padding: 0 8px;
}

.empty-state-description {
  font-size: 13px;
  line-height: 18px;
  text-align: center;
  max-width: 400px;
}

/* 工具提示样式 */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--vscode-editorHoverWidget-background);
  color: var(--vscode-editorHoverWidget-foreground);
  border: 1px solid var(--vscode-editorHoverWidget-border);
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* 加载状态 */
.loading-dots {
  display: flex;
  gap: 2px;
}

.loading-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: var(--vscode-progressBar-background);
}

/* 控制行样式 - mode-selector和send-button的容器 */
.controls-row {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 3px;
  border-radius: 5px;
  color: var(--vscode-foreground);
  background-color: var(--vscode-toolbar-background);
}

.controls-row:hover {
  background-color: var(--vscode-toolbar-hoverBackground);
}

/* 发送按钮样式 */
.controls-row .lucide-send-horizontal {
  color: var(--vscode-icon-foreground);
}

.input-part {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin: 0 16px;
  padding: 4px 0 12px;
}

.attachments-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  margin: 6px 0;
  flex-wrap: wrap;
  cursor: default;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 4px;
  overflow: hidden;
  font-size: 11px;
  padding: 0 4px;
  border: 1px solid var(--vscode-chat-requestBorder, var(--vscode-input-background, transparent));
  border-radius: 4px;
  height: 18px;
  max-width: 100%;
  width: fit-content;
  box-sizing: content-box;
}

.attached-file {
  position: relative;
  border: 1px solid var(--vscode-chat-requestBorder, var(--vscode-input-background, transparent));
  border-radius: 4px;
  display: flex;
  align-items: center;
  font-size: 11px;
  gap: 4px;
  height: 18px;
  max-width: 100%;
  padding: 0 4px;
  width: fit-content;
  box-sizing: content-box;
}

.additional-container {
  margin: 0 16px;
  z-index: 1;
}

/* 输入包装器样式 */
.input-wrapper {
  z-index: 10;
  box-sizing: border-box;
  cursor: text;
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border, transparent);
  border-radius: 4px;
  padding: 0 6px 6px 6px;
  max-width: 100%;
}

.input-wrapper:focus-within {
  border-color: var(--vscode-focusBorder);
}

.input-wrapper.actmode {
  border-color: var(--vscode-button-background);
}

/* 辅助类 */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 输入增强功能样式 */
.input-enhancement {
  display: flex;
  align-items: center;
  gap: 8px;
}

.character-count {
  font-size: 11px;
  color: var(--vscode-input-foreground);
  opacity: 0.5;
}

.character-count.warning {
  color: var(--vscode-inputValidation-warningForeground);
}

.character-count.over-limit {
  color: var(--vscode-inputValidation-errorForeground);
}

/* 胶囊开关样式 */
.coding-mode-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
}

.mode-button {
  background-color: transparent;
  font-size: 11px;
  height: 16px;
  padding: 0 6px;
  border-radius: 5px;
  color: var(--vscode-foreground);
  outline: none;
  gap: var(--space-1);
  cursor: pointer;
}

.mode-button:hover:not(:disabled) {
  background-color: var(--vscode-toolbar-hoverBackground);
}

.mode-button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.index-exist:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.index-update:disable {
  cursor: not-allowed;
  opacity: 0.6;
}

.mode-option:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.character-count.hidden {
  visibility: hidden;
  display: block !important;
}

.keyboard-hint {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: clamp(8px, 2.5vw, 10px);
  opacity: 0.7;
}

.keyboard-hint kbd {
  background: var(--vscode-keybindingLabel-background);
  color: var(--vscode-keybindingLabel-foreground);
  border: 1px solid var(--vscode-keybindingLabel-border);
  border-radius: 3px;
  padding: 1px 4px;
  font-size: clamp(8px, 2.5vw, 10px);
  font-family: inherit;
  min-width: 16px;
  text-align: center;
}

.button-primary {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  font-weight: 500;
  cursor: pointer;
  border: 0 !important;
  border-radius: 4px;
  height: 28px;
  padding: 0 8px;
}

.button-primary:hover {
  background: var(--vscode-button-hoverBackground);
}

.button-secondary {
  background: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  font-weight: 500;
  cursor: pointer;
  border: 0 !important;
  border-radius: 4px;
  height: 28px;
  padding: 0 8px;
}

.button-secondary:hover {
  background: var(--vscode-button-secondaryHoverBackground);
}

.tab-item {
  border: 0 !important;
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
}

/* 汉堡菜单触发器样式 */
.hamburger-menu-trigger {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border: none;
  background: var(--vscode-input-background);
  color: var(--vscode-descriptionForeground);
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 13px;
}

.hamburger-menu-trigger:hover {
  background: var(--vscode-button-secondaryHoverBackground);
}

/* 汉堡菜单按钮样式 */
.hamburger-menu-button {
  padding: 0;
  border: none;
  background: transparent;
  color: inherit;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
}

.hamburger-menu-content .rt-BaseMenuViewport,
.hamburger-menu-sub-content .rt-BaseMenuViewport {
  padding: 4px;
}

.hamburger-menu-content .hamburger-menu-text,
.hamburger-menu-sub-content .hamburger-menu-text {
  font-size: 13px !important;
  height: var(--space-5) !important;
}

/* 规则列表项样式 */
.settings-rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid var(--vscode-editorWidget-border);
  border-radius: 4px;
  background: var(--vscode-editorWidget-background);
  transition: background-color 0.2s ease;
}

.settings-rule-item:hover {
  background: var(--vscode-list-hoverBackground);
  border-color: var(--vscode-focusBorder);
}

.settings-rule-item:focus {
  outline: 1px solid var(--vscode-focusBorder);
  outline-offset: -1px;
}

.settings-rule-item .rule-name {
  flex-grow: 1;
  margin-right: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--vscode-foreground);
}

.settings-rule-item .rule-type {
  margin-right: 16px;
  color: var(--vscode-textPreformat-foreground);
  font-size: 12px;
  font-weight: 500;
}

.settings-rule-item .rule-actions {
  display: flex;
  gap: 8px;
}

/* 业务和模式标注样式 */
.business-mode-label {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 10px;
  color: var(--vscode-descriptionForeground);
  line-height: 1.2;
}

.business-text,
.mode-text,
.separator {
  font-size: 10px;
  color: var(--vscode-descriptionForeground);
  opacity: 0.7;
  white-space: nowrap;
  padding-right: 2px;
}

.business-text,
.mode-text {
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60px;
}

.settings-close-button {
  border: 0 !important;
  color: var(--vscode-foreground);
  background-color: transparent;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 18px;
}

.settings-close-button:hover {
  background-color: var(--vscode-toolbar-hoverBackground);
}

.tips-text {
  color: var(--vscode-descriptionForeground);
  margin: 8px 0 12px;
  word-break: break-word;
}

/** 消息框输入样式 */
.tiptap-editor {
  width: 100%;
  height: 100%;
  max-height: 120px;
  min-height: 40px;
  border: none;
  border-radius: 0;
  outline: none;
  color: var(--vscode-foreground);
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  padding: 0;
  margin: 0;
  overflow-y: auto;
}

.tiptap-editor::-webkit-scrollbar {
  width: 4px;
}

.tiptap-editor::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.tiptap-editor::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-background);
  border-radius: 3px;
}

.tiptap-editor::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground);
}

.tiptap-editor p {
  margin: 0;
  padding: 0;
}

.tiptap-editor:focus {
  outline: none;
  box-shadow: none;
}

.tiptap-wrapper {
  width: 100%;
  overflow-y: auto;
  background: transparent;
}

/* tippy */
.tippy-box[data-theme~="vscode"] .react-renderer {
  border-radius: 8px;
  max-height: 30vh;
  overflow-y: auto;
  overflow-x: hidden;
  color: var(--vscode-menu-foreground) !important;
  background-color: var(--vscode-sideBar-background) !important;
}

.tippy-char-hidden {
  color: transparent;
  width: 0;
  font-size: 0;
  pointer-events: none; /* 防止交互 */
}

/* endof tippy */

/* Remove default Tiptap styles */
.ProseMirror {
  outline: none !important;
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
}

.ProseMirror p {
  margin: 0 !important;
  padding: 0 !important;
}

.ProseMirror:focus {
  outline: none !important;
  box-shadow: none !important;
}

.mention {
  background-color: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  border-radius: 4px;
  padding: 0.2em 0.4em;
}

.image-node-renderer,
.file-node-renderer {
  display: inline-flex;
  vertical-align: middle;
  margin: 0 4px;
}

.image-node::after,
.file-node::after {
  content: "\200B";
}

.image-node-renderer img {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.file-node-renderer {
  margin: 0 2px;
  border-radius: 4px;
  background: var(--vscode-badge-background);
  align-items: center;
  font-size: 13px;
}

.ProseMirror-selectednode {
  background-color: rgba(187, 214, 251, 0.6) !important;
}

.is-editor-empty::before {
  content: attr(data-placeholder);
  float: left;
  pointer-events: none;
  height: 0;
  font-size: 12px;
  line-height: 1.5;
  user-select: none;
  color: var(--vscode-input-placeholderForeground);
  opacity: 0.5;
}

.sub-title {
  margin: 12px 0;
  font-weight: 500;
}

.text-area {
  padding: 4px;
  border-radius: 4px;
  border: 1px solid var(--divider-background);
  background: var(--background);
}

.text-area-no-resize {
  resize: none;
}

.text-area:focus {
  outline: none;
}

.diff-view-accept-reject-button {
  margin: 0 16px;
  margin-bottom: -13px;
  padding: 8px 8px 18px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 4px;
  border: 1px solid var(--vscode-input-border, transparent);
  background: var(--vscode-editor-background);
}

.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: var(--vscode-sideBar-background);
  color: var(--vscode-sideBar-foreground);
  padding: 20px;
  text-align: center;
}

.logo {
  width: 120px;
  margin-bottom: 20px;
}

.title {
  font-size: 24px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.title span {
  color: #4ade80;
}

.subtitle {
  font-size: 14px;
  color: #888;
  margin-bottom: 30px;
  max-width: 600px;
}

.login-button {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  border-radius: 6px;
  padding: 8px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
  max-width: 320px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
}

.login-button:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.login-button:active {
  background-color: var(--vscode-button-activeBackground);
}

.monaco-select-box {
  display: inline-block;
  width: auto;
  padding: 2px 23px 2px 8px;
  font-family: var(--monaco-monospace-font);
  font-size: 13px;
  border: none;
  background-color: var(--vscode-dropdown-background);
  color: var(--vscode-dropdown-foreground);
  height: 24px;
  line-height: 24px;
  border-radius: 4px; /* 增加圆角 */
  min-width: 120px;
  appearance: none;
  -webkit-appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='16' height='16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.427 6.427a.75.75 0 011.06 0L8 8.94l2.513-2.513a.75.75 0 111.06 1.06l-3.043 3.043a.75.75 0 01-1.06 0L4.427 7.487a.75.75 0 010-1.06z' fill='%23C5C5C5'/%3E%3C/svg%3E");
  background-position: center right 4px;
  background-repeat: no-repeat;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  transition: all 0.2s ease; /* 添加过渡效果 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* 添加轻微阴影 */
}

.monaco-select-box:focus {
  outline: 1px solid var(--vscode-focusBorder);
  outline-offset: -1px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 聚焦时增加阴影 */
}

.monaco-select-box:hover:not(:disabled) {
  background-color: var(--vscode-list-activeSelectionBackground);
  color: var(--vscode-list-activeSelectionForeground);
  outline: 1px solid var(--vscode-list-focusOutline);
  outline-offset: -1px;
  transform: translateY(-1px); /* 添加轻微上浮效果 */
}

.monaco-select-box:disabled {
  opacity: 0.4;
  cursor: default;
  pointer-events: none;
  box-shadow: none; /* 禁用状态移除阴影 */
}

.monaco-select-box option {
  background-color: var(--vscode-dropdown-listBackground);
  color: var(--vscode-dropdown-foreground);
  padding: 8px 12px; /* 增加内边距 */
  font-family: var(--monaco-monospace-font);
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s ease; /* 添加过渡效果 */
}

.monaco-select-box option:hover {
  background-color: var(--vscode-list-hoverBackground); /* 悬停背景色 */
}

.monaco-select-box option:checked {
  background-color: var(--vscode-list-activeSelectionBackground);
  color: var(--vscode-list-activeSelectionForeground);
}

/* 添加自定义滚动条样式 */
.monaco-select-box::-webkit-scrollbar {
  width: 8px;
}

.monaco-select-box::-webkit-scrollbar-track {
  background: var(--vscode-scrollbarSlider-background);
  border-radius: 4px;
}

.monaco-select-box::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-hoverBackground);
  border-radius: 4px;
}

.monaco-select-box::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-activeBackground);
}

select {
  background-color: var(--vscode-dropdown-listBackground);
  border-radius: 5px;
  font-size: 14px;
}

/* Radix UI Select Styles */
.SelectTrigger {
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;
  font-size: 13px;
  line-height: 22px;
  height: 22px;
  gap: 4px;
  outline: none;
  background-color: transparent;
  color: var(--vscode-foreground);
  border: none;
  min-width: 100px;
  cursor: pointer;
  position: relative;
}

.SelectTrigger:hover {
  background-color: var(--vscode-toolbar-hoverBackground);
  border-radius: 3px;
}

.SelectTrigger:focus {
  outline: none;
}

.SelectTrigger[data-disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.SelectContent {
  overflow: hidden;
  background-color: var(--vscode-menu-background);
  border-radius: 3px;
  border: none;
  outline: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.SelectItem {
  font-size: 13px;
  line-height: 1.4;
  color: var(--vscode-foreground);
  display: flex;
  align-items: center;
  height: 22px;
  padding: 0 8px;
  position: relative;
  -webkit-user-select: none;
  user-select: none;
  cursor: pointer;
}

.SelectItem[data-highlighted] {
  outline: none;
  background-color: var(--vscode-list-hoverBackground);
}

.SelectItem[data-disabled] {
  color: var(--vscode-disabledForeground);
  pointer-events: none;
}

/* 添加滚动条样式 */
.SelectViewport {
  padding: 4px;
}

.SelectViewport::-webkit-scrollbar {
  width: 10px;
}

.SelectViewport::-webkit-scrollbar-track {
  background: transparent;
}

.SelectViewport::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-background);
  border-radius: 5px;
}

.SelectViewport::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground);
}

.SelectTrigger {
  min-width: 68px;
}

/* Select styles */
.select-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1;
  gap: 8px;
  color: var(--vscode-foreground);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  outline: none;
}

.select-trigger:hover {
  background-color: #2d2d2d;
}

.select-icon {
  color: #8b8b8b;
}

.select-content {
  overflow: hidden;
  background-color: #1e1e1e;
  border-radius: 4px;
}

.select-viewport {
  padding: 4px;
}

.select-item {
  font-size: 14px;
  line-height: 1;
  color: white;
  padding: 8px 12px;
  position: relative;
  -webkit-user-select: none;
  user-select: none;
  cursor: pointer;
  outline: none;
}

.select-item:hover {
  background-color: #2d2d2d;
}

.select-item[data-highlighted] {
  background-color: #2d2d2d;
  color: var(--vscode-foreground);
}

.send-tooltip {
  background-color: var(--vscode-foreground);
  padding: 6px;
  border-radius: 4px;
  font-size: 12px;
}

.chat-request-border {
  border-color: var(--vscode-chat-requestBorder);
}

.table-item-padding {
  padding: 4px 6px;
}

/* Radix UI Tooltip 完整样式控制 */
.rt-TooltipContent {
  background-color: var(--vscode-foreground) !important;
}

.rt-TooltipArrow {
  fill: var(--vscode-foreground) !important;
  stroke: var(--vscode-foreground) !important;
}

.chat-request-bubble {
  background-color: var(--vscode-chat-requestBubbleBackground, var(--vscode-chat-background, rgba(38, 79, 120, 0.3)));
  max-width: 90%;
  color: var(--vscode-interactive-session-foreground);
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.message-item .cursor-pointer-box {
  /* width: 24px;
  height: 24px; */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  padding: 3px;
}

.message-item .cursor-pointer-box:hover {
  cursor: pointer;
  background-color: var(--vscode-toolbar-hoverBackground);
}

.message-item .cursor-pointer-box.active {
  color: var(--vscode-inputOption-activeForeground) !important;
  border-color: var(--vscode-inputOption-activeBorder);
  background-color: var(--vscode-inputOption-activeBackground);
}

.rt-BaseMenuItem {
  padding: 2px 8px;
}

.rt-BaseMenuItem:hover {
  background-color: var(--vscode-menu-selectionBackground) !important;
}

.rt-BaseMenuItem .menu-item-text {
  font-size: 12px;
}

.rule-form-radio {
  vertical-align: text-bottom;
}

.rule-form-radio:focus {
  outline: none;
}

/* 原生 @radix-ui/react-tooltip 样式 */
.tooltip-content {
  background-color: var(--vscode-editorHoverWidget-background, #2d2d30) !important;
  color: var(--vscode-editorHoverWidget-foreground, #cccccc) !important;
  border: 1px solid var(--vscode-editorHoverWidget-border, #454545);
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 200px;
  z-index: 50;
  animation-duration: 300ms;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  will-change: transform, opacity;
}

.tooltip-content[data-state="delayed-open"][data-side="right"] {
  animation-name: slideInFromLeft;
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-2px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* React Complex Tree 样式覆盖 - VSCode 暗色模式适配 */
:root {
  --rct-color-tree-bg: transparent;
  --rct-color-search-highlight-bg: var(--vscode-textPreformat-foreground);
  --rct-color-tree-focus-outline: transparent;
  --rct-radius: 4px;
  --rct-bar-color: var(--vscode-focusBorder);
  --rct-focus-outline: var(--vscode-focusBorder);
  --rct-color-focustree-item-selected-bg: var(--vscode-list-activeSelectionBackground);
  --rct-color-focustree-item-hover-bg: var(--vscode-list-hoverBackground);
  --rct-color-focustree-item-hover-text: var(--vscode-foreground);
  --rct-color-focustree-item-active-bg: var(--vscode-list-activeSelectionBackground);
  --rct-color-focustree-item-active-text: var(--vscode-list-activeSelectionForeground);
  --rct-cursor: pointer;
  --rct-search-border: var(--vscode-input-border);
  --rct-search-border-bottom: var(--vscode-focusBorder);
  --rct-search-bg: var(--vscode-input-background);
  --rct-search-text: var(--vscode-input-foreground);
}

.rct-dark {
  --rct-color-search-highlight-bg: var(--vscode-textPreformat-foreground);
  --rct-bar-color: var(--vscode-focusBorder);
  --rct-focus-outline: var(--vscode-focusBorder);
  --rct-color-focustree-item-selected-text: var(--vscode-list-activeSelectionForeground);
  --rct-color-focustree-item-selected-bg: var(--vscode-list-activeSelectionBackground);
  --rct-color-focustree-item-hover-bg: var(--vscode-list-hoverBackground);
  --rct-color-focustree-item-hover-text: var(--vscode-foreground);
  --rct-color-focustree-item-active-bg: var(--vscode-list-activeSelectionBackground);
  --rct-color-focustree-item-active-text: var(--vscode-list-activeSelectionForeground);
  --rct-color-focustree-item-draggingover-bg: var(--vscode-list-hoverBackground);
  --rct-color-focustree-item-draggingover-color: var(--vscode-foreground);
  --rct-color-arrow: var(--vscode-foreground);
  --rct-search-border: var(--vscode-input-border);
  --rct-search-border-bottom: var(--vscode-focusBorder);
  --rct-search-bg: var(--vscode-input-background);
  --rct-search-text: var(--vscode-input-foreground);
}

.rct-tree-item-button {
  color: var(--vscode-foreground) !important;
  background-color: transparent !important;
  border: none !important;
  cursor: pointer !important;
  transition: color 100ms ease-out, background-color 100ms ease-out !important;
}

.rct-tree-item-button:hover {
  background-color: var(--vscode-list-hoverBackground) !important;
  color: var(--vscode-foreground) !important;
}

.rct-tree-item-button:active {
  background-color: var(--vscode-list-activeSelectionBackground) !important;
  color: var(--vscode-list-activeSelectionForeground) !important;
}

.rct-tree-item-title-container-selected .rct-tree-item-button {
  background-color: var(--vscode-list-activeSelectionBackground) !important;
  color: var(--vscode-list-activeSelectionForeground) !important;
}

.rct-tree-item-title-container-dragging-over .rct-tree-item-button {
  background-color: var(--vscode-list-hoverBackground) !important;
  color: var(--vscode-foreground) !important;
}

.rct-tree-item-arrow {
  cursor: pointer !important;
  color: var(--vscode-foreground) !important;
  background-color: transparent !important;
  border: none !important;
  border-radius: 2px !important;
  transition: color 100ms ease-out, background-color 100ms ease-out !important;
}

.rct-tree-item-arrow.rct-tree-item-arrow-isFolder:hover {
  background-color: var(--vscode-list-hoverBackground) !important;
}

.rct-tree-item-arrow svg {
  color: var(--vscode-foreground) !important;
}

/* 文件夹图标样式 */
.rct-tree-item-button[data-folder="true"] {
  font-weight: 500 !important;
}

/* 文件图标样式 */
.rct-tree-item-button[data-folder="false"] {
  font-weight: normal !important;
}

/* 滚动条样式 */
.rct-tree::-webkit-scrollbar {
  width: 4px !important;
}

.rct-tree::-webkit-scrollbar-track {
  background: transparent !important;
  border-radius: 3px !important;
}

.rct-tree::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-background) !important;
  border-radius: 3px !important;
}

.rct-tree::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground) !important;
}

/* 树容器样式 */
.rct-tree {
  background-color: transparent !important;
  color: var(--vscode-foreground) !important;
  border: none !important;
  font-family: var(--vscode-font-family) !important;
  font-size: var(--vscode-font-size) !important;
}

/* 树项容器样式 */
.rct-tree-item {
  background-color: transparent !important;
  border: none !important;
}

/* 搜索输入框样式 */
.rct-tree-search-input {
  background-color: var(--vscode-input-background) !important;
  color: var(--vscode-input-foreground) !important;
  border: 1px solid var(--vscode-input-border) !important;
  border-radius: 4px !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  width: 100% !important;
}

.rct-tree-search-input:focus {
  outline: 1px solid var(--vscode-focusBorder) !important;
  border-color: var(--vscode-focusBorder) !important;
}

/* 拖拽线样式 */
.rct-tree-drag-between-line {
  background-color: var(--vscode-focusBorder) !important;
  height: 2px !important;
}

/* 重命名输入框样式 */
.rct-tree-item-renaming-submit-button {
  background-color: var(--vscode-button-background) !important;
  color: var(--vscode-button-foreground) !important;
  border: 1px solid var(--vscode-button-border) !important;
  border-radius: 2px !important;
  padding: 2px 6px !important;
  font-size: 11px !important;
  cursor: pointer !important;
}

.rct-tree-item-renaming-submit-button:hover {
  background-color: var(--vscode-button-hoverBackground) !important;
}

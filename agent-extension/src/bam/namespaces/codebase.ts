// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from "./base";
import * as summary from "./summary";

export type Int64 = string | number;

export interface DiffResult {
  /** 文件/目录路径 */
  path: string;
  /** 差异类型: "modify", "add", "delete", "type_mismatch", "path_mismatch" */
  type: string;
  /** 文件 hash */
  hash: string;
}

export interface GetMerkleDiffResponse {
  /** 差异结果 */
  diffs: Array<DiffResult>;
  /** 仓库 */
  server_merkle_id: string;
  /** 索引构建userKnowledgeId id */
  origin_user_knowledge_id: string;
  base_resp?: base.BaseResp;
}

export interface GetSummaryUpdateFilesResponse {
  /** 差异结果 */
  grouped_related_path_info: summary.GroupedRelatedPathInfo;
  /** 差异结果 */
  diffs: Array<DiffResult>;
  /** 索引构建userKnowledgeId id */
  origin_user_knowledge_id: string;
  /** 服务端仓库树id */
  server_merkle_id: string;
  base_resp?: base.BaseResp;
}

export interface QueryBuildRecordResponse {
  /** 根节点id */
  root_merkle_id: string;
  /** 构建状态 0: 构建中 1: 已构建 2: 构建失败, -1: 索引不存在
构建状态 */
  build_status: string;
  base_resp?: base.BaseResp;
}

export interface QuerySummaryBuildRecordResponse {
  /** 根节点id */
  root_merkle_id: string;
  /** 构建状态 */
  build_status: string;
  base_resp?: base.BaseResp;
}

export interface UpdateSummaryResponse {
  /** 索引构建userKnowledgeId */
  user_knowledge_id: string;
  base_resp?: base.BaseResp;
}

export interface UploadMerkleTreeResponse {
  /** 索引构建userKnowledgeId */
  id: string;
  base_resp?: base.BaseResp;
}
/* eslint-enable */

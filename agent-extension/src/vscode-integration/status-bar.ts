import { StatusBarAlignment, type StatusBarItem, window } from 'vscode';

export class VscodeStatusBar {
  /**
   * The status bar item
   */
  private statusBarItem: StatusBarItem;

  /**
   * Create a new status bar
   */
  constructor() {
    this.statusBarItem = window.createStatusBarItem('codin', StatusBarAlignment.Right, 1000);
    this.statusBarItem.text = '$(codin-icon) Codin';
    this.statusBarItem.tooltip = "打开 Codin (⌘+⇧+')";
    this.statusBarItem.command = 'codin-agent.openInEditor';
  }

  /**
   * Hide the status bar
   */
  public hide() {
    this.statusBarItem.hide();
    this.statusBarItem.dispose();
  }

  /**
   * Show the status bar
   */
  public show() {
    this.statusBarItem.show();
  }
}

import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { exec } from 'node:child_process';
import { promisify } from 'node:util';
import { createPrettyPatch } from '@utils/create-pretty-patch';
import { detectEncoding } from '@utils/detect-encoding';
import { createDirectoriesForFile, getCwd } from '@utils/fs';
import { arePathsEqual } from '@/utils/path';
import * as diff from 'diff';
import * as iconv from 'iconv-lite';
import * as vscode from 'vscode';
import type { IDiffViewService } from '@/services/diff-view/diff-view-service.interface';
import { diagnosticsToProblemsString, getNewDiagnostics } from '@/vscode-integration/diagnostics';
import { DecorationController } from './decoration-controller';
import { Emitter } from '@byted-image/lv-bedrock/event';
import { getAllDiagnostics } from '@/utils/diagnostics';
import type { DiffFileInfo, UpdateConversationDiffInfoParams, LineAction } from '@/types/conversation';
import { IGitService } from '@/services/git/git-service.interface';
import { debounce } from 'lodash';
import { Disposable } from '@byted-image/lv-bedrock/dispose';

/**
 * 增减统计信息
 */
interface DiffStats {
  addedLines: number;
  deletedLines: number;
}

export const DIFF_VIEW_URI_SCHEME = 'codin-diff';

const execAsync = promisify(exec);

export class DiffViewService extends Disposable implements IDiffViewService {
  public readonly _serviceBrand: undefined;
  public editType?: 'create' | 'modify';
  public isEditing = false;
  public originalContent: string | undefined;

  private _cwd: string;
  private _createdDirs: string[] = [];
  private _documentWasOpen = false;
  private _relPath?: string;
  private _newContent?: string;
  private _activeDiffEditor?: vscode.TextEditor;
  private _fadedOverlayController?: DecorationController;
  private _activeLineController?: DecorationController;
  private _streamedLines: string[] = [];
  private _preDiagnostics: [vscode.Uri, vscode.Diagnostic[]][] = [];
  private _fileEncoding = 'utf8';
  // @ts-expect-error
  private _lastFirstVisibleLine = 0;
  private _shouldAutoScroll = true;
  private _scrollListener?: vscode.Disposable;

  private readonly _onChangeIsEditing = new Emitter<[]>();
  public readonly onChangeIsEditing = this._onChangeIsEditing.event;

  constructor(@IGitService private readonly _gitService: IGitService) {
    super();
    this._cwd = getCwd();
  }

  public async open(_relPath: string): Promise<void> {
    this._relPath = _relPath;
    const fileExists = this.editType === 'modify';
    const absolutePath = path.resolve(this._cwd, _relPath);
    if (!this.isEditing) {
      this.isEditing = true;
      this._onChangeIsEditing.fire();
    }
    this._shouldAutoScroll = true;
    this._lastFirstVisibleLine = 0;
    // if the file is already open, ensure it's not dirty before getting its contents
    if (fileExists) {
      const existingDocument = vscode.workspace.textDocuments.find((doc) =>
        arePathsEqual(doc.uri.fsPath, absolutePath),
      );
      if (existingDocument?.isDirty) {
        await existingDocument.save();
      }
    }

    // get diagnostics before editing the file, we'll compare to diagnostics after editing to see if codin needs to fix anything
    this._preDiagnostics = await getAllDiagnostics(2000);

    if (fileExists) {
      const fileBuffer = await fs.readFile(absolutePath);
      this._fileEncoding = await detectEncoding(fileBuffer);
      this.originalContent = iconv.decode(fileBuffer, this._fileEncoding);
    } else {
      this.originalContent = '';
      this._fileEncoding = 'utf8';
    }
    // for new files, create any necessary directories and keep track of new directories to delete if the user denies the operation
    this._createdDirs = await createDirectoriesForFile(absolutePath);
    // make sure the file exists before we open it
    if (!fileExists) {
      await fs.writeFile(absolutePath, '');
    }
    // if the file was already open, close it (must happen after showing the diff view since if it's the only tab the column will close)
    this._documentWasOpen = false;
    // close the tab if it's open (it's already saved above)
    const tabs = vscode.window.tabGroups.all
      .flatMap((tg) => tg.tabs)
      .filter((tab) => tab.input instanceof vscode.TabInputText && arePathsEqual(tab.input.uri.fsPath, absolutePath));
    for (const tab of tabs) {
      if (!tab.isDirty) {
        await vscode.window.tabGroups.close(tab);
      }
      this._documentWasOpen = true;
    }
    this._activeDiffEditor = await this._openDiffEditor();
    this._fadedOverlayController = new DecorationController('fadedOverlay', this._activeDiffEditor);
    this._activeLineController = new DecorationController('activeLine', this._activeDiffEditor);
    // Apply faded overlay to all lines initially
    this._fadedOverlayController.addLines(0, this._activeDiffEditor.document.lineCount);
    this._scrollEditorToLine(0); // will this crash for new files?
    this._streamedLines = [];

    // Add scroll detection to disable auto-scrolling when user scrolls up
    this._scrollListener = vscode.window.onDidChangeTextEditorVisibleRanges(
      (e: vscode.TextEditorVisibleRangesChangeEvent) => {
        if (e.textEditor === this._activeDiffEditor) {
          const currentFirstVisibleLine = e.visibleRanges[0]?.start.line || 0;

          // If the first visible line moved upward, user scrolled up
          // if (currentFirstVisibleLine < this._lastFirstVisibleLine) {
          // 	this._shouldAutoScroll = false
          // }

          // Always update our tracking variable
          this._lastFirstVisibleLine = currentFirstVisibleLine;
        }
      },
    );
  }

  public async update(accumulatedContent: string, isFinal: boolean) {
    if (!this._relPath || !this._activeLineController || !this._fadedOverlayController) {
      throw new Error('Required values not set');
    }

    // --- Fix to prevent duplicate BOM ---
    // Strip potential BOM from incoming content. VS Code's `applyEdit` might implicitly handle the BOM
    // when replacing from the start (0,0), and we want to avoid duplication.
    // Final BOM is handled in `saveChanges`.
    if (accumulatedContent.startsWith('\ufeff')) {
      // biome-ignore lint/style/noParameterAssign: 参考代码
      accumulatedContent = accumulatedContent.slice(1); // Remove the BOM character
    }

    this._newContent = accumulatedContent;
    const accumulatedLines = accumulatedContent.split('\n');
    if (!isFinal) {
      accumulatedLines.pop(); // remove the last partial line only if it's not the final update
    }
    const diffLines = accumulatedLines.slice(this._streamedLines.length);

    const diffEditor = this._activeDiffEditor;
    const document = diffEditor?.document;
    if (!diffEditor || !document) {
      throw new Error('User closed text editor, unable to edit file...');
    }

    // Place cursor at the beginning of the diff editor to keep it out of the way of the stream animation
    const beginningOfDocument = new vscode.Position(0, 0);
    diffEditor.selection = new vscode.Selection(beginningOfDocument, beginningOfDocument);

    // Instead of animating each line, we'll update in larger chunks
    const currentLine = this._streamedLines.length + diffLines.length - 1;
    if (currentLine >= 0) {
      // Only proceed if we have new lines

      // Replace all content up to the current line with accumulated lines
      // This is necessary (as compared to inserting one line at a time) to handle cases where html tags on previous lines are auto closed for example
      const edit = new vscode.WorkspaceEdit();
      const rangeToReplace = new vscode.Range(0, 0, currentLine + 1, 0);
      const contentToReplace = `${accumulatedLines.slice(0, currentLine + 1).join('\n')}\n`;
      edit.replace(document.uri, rangeToReplace, contentToReplace);
      await vscode.workspace.applyEdit(edit);

      // Update decorations for the entire changed section
      this._activeLineController.setActiveLine(currentLine);
      this._fadedOverlayController.updateOverlayAfterLine(currentLine, document.lineCount);

      // Scroll to the last changed line only if the user hasn't scrolled up
      if (this._shouldAutoScroll) {
        if (diffLines.length <= 5) {
          // For small changes, just jump directly to the line
          this._scrollEditorToLine(currentLine);
        } else {
          // For larger changes, create a quick scrolling animation
          const startLine = this._streamedLines.length;
          const endLine = currentLine;
          const totalLines = endLine - startLine;
          const numSteps = 10; // Adjust this number to control animation speed
          const stepSize = Math.max(1, Math.floor(totalLines / numSteps));

          // Create and await the smooth scrolling animation
          for (let line = startLine; line <= endLine; line += stepSize) {
            this._activeDiffEditor?.revealRange(
              new vscode.Range(line, 0, line, 0),
              vscode.TextEditorRevealType.InCenter,
            );
            await new Promise((resolve) => setTimeout(resolve, 16)); // ~60fps
          }
          // Ensure we end at the final line
          this._scrollEditorToLine(currentLine);
        }
      }
    }

    // Update the _streamedLines with the new accumulated content
    this._streamedLines = accumulatedLines;
    if (isFinal) {
      // Handle any remaining lines if the new content is shorter than the original
      if (this._streamedLines.length < document.lineCount) {
        const edit = new vscode.WorkspaceEdit();
        edit.delete(document.uri, new vscode.Range(this._streamedLines.length, 0, document.lineCount, 0));
        await vscode.workspace.applyEdit(edit);
      }
      // Add empty last line if original content had one
      const hasEmptyLastLine = this.originalContent?.endsWith('\n');
      if (hasEmptyLastLine) {
        const accumulatedLines = accumulatedContent.split('\n');
        if (accumulatedLines[accumulatedLines.length - 1] !== '') {
          // biome-ignore lint/style/noParameterAssign: 参考代码
          accumulatedContent += '\n';
        }
      }
      // Clear all decorations at the end (before applying final edit)
      this._fadedOverlayController.clear();
      this._activeLineController.clear();
    }
  }

  public async saveChanges(autoCloseAllDiffViews: boolean): Promise<{
    newProblemsMessage: string | undefined;
    userEdits: string | undefined;
    autoFormattingEdits: string | undefined;
    finalContent: string | undefined;
  }> {
    if (!this._relPath || !this._newContent || !this._activeDiffEditor) {
      return {
        newProblemsMessage: undefined,
        userEdits: undefined,
        autoFormattingEdits: undefined,
        finalContent: undefined,
      };
    }
    const absolutePath = path.resolve(this._cwd, this._relPath);
    const updatedDocument = this._activeDiffEditor.document;

    // get the contents before save operation which may do auto-formatting
    const preSaveContent = updatedDocument.getText();

    if (updatedDocument.isDirty) {
      await updatedDocument.save();
    }

    // get text after save in case there is any auto-formatting done by the editor
    const postSaveContent = updatedDocument.getText();

    await vscode.window.showTextDocument(vscode.Uri.file(absolutePath), {
      preview: false,
      preserveFocus: true,
    });
    if (autoCloseAllDiffViews) {
      // 延迟2秒后关闭diff views
      const timer = setTimeout(async () => {
        clearTimeout(timer);
        await this.closeAllDiffViews();
      }, 1000);
    }

    /*
		Getting diagnostics before and after the file edit is a better approach than
		automatically tracking problems in real-time. This method ensures we only
		report new problems that are a direct result of this specific edit.
		Since these are new problems resulting from Codin's edit, we know they're
		directly related to the work he's doing. This eliminates the risk of Codin
		going off-task or getting distracted by unrelated issues, which was a problem
		with the previous auto-debug approach. Some users' machines may be slow to
		update diagnostics, so this approach provides a good balance between automation
		and avoiding potential issues where Codin might get stuck in loops due to
		outdated problem information. If no new problems show up by the time the user
		accepts the changes, they can always debug later using the '@problems' mention.
		This way, Codin only becomes aware of new problems resulting from his edits
		and can address them accordingly. If problems don't change immediately after
		applying a fix, Codin won't be notified, which is generally fine since the
		initial fix is usually correct and it may just take time for linters to catch up.
		*/
    const postDiagnostics = await getAllDiagnostics();
    const newProblems = diagnosticsToProblemsString(
      getNewDiagnostics(this._preDiagnostics, postDiagnostics),
      [
        vscode.DiagnosticSeverity.Error, // only including errors since warnings can be distracting (if user wants to fix warnings they can use the @problems mention)
      ],
      this._cwd,
    ); // will be empty string if no errors
    const newProblemsMessage =
      newProblems.length > 0 ? `\n\nNew problems detected after saving the file:\n${newProblems}` : '';

    // If the edited content has different EOL characters, we don't want to show a diff with all the EOL differences.
    const newContentEOL = this._newContent.includes('\r\n') ? '\r\n' : '\n';
    const normalizedPreSaveContent = preSaveContent.replace(/\r\n|\n/g, newContentEOL).trimEnd() + newContentEOL; // trimEnd to fix issue where editor adds in extra new line automatically
    const normalizedPostSaveContent = postSaveContent.replace(/\r\n|\n/g, newContentEOL).trimEnd() + newContentEOL; // this is the final content we return to the model to use as the new baseline for future edits
    // just in case the new content has a mix of varying EOL characters
    const normalizedNewContent = this._newContent.replace(/\r\n|\n/g, newContentEOL).trimEnd() + newContentEOL;

    let userEdits: string | undefined;
    if (normalizedPreSaveContent !== normalizedNewContent) {
      // user made changes before approving edit. let the model know about user made changes (not including post-save auto-formatting changes)
      userEdits = createPrettyPatch(this._relPath.toPosix(), normalizedNewContent, normalizedPreSaveContent);
      // return { newProblemsMessage, userEdits, finalContent: normalizedPostSaveContent }
    } else {
      // no changes to Codin's edits
      // return { newProblemsMessage, userEdits: undefined, finalContent: normalizedPostSaveContent }
    }

    let autoFormattingEdits: string | undefined;
    if (normalizedPreSaveContent !== normalizedPostSaveContent) {
      // auto-formatting was done by the editor
      autoFormattingEdits = createPrettyPatch(
        this._relPath.toPosix(),
        normalizedPreSaveContent,
        normalizedPostSaveContent,
      );
    }

    return {
      newProblemsMessage,
      userEdits,
      autoFormattingEdits,
      finalContent: normalizedPostSaveContent,
    };
  }

  public async openDiffViewAfterSave() {
    if (!this._relPath) {
      throw new Error('No file path set');
    }
    const uri = vscode.Uri.file(path.resolve(this._cwd, this._relPath));
    // TODO: chezongshao If this diff editor is already open (ie if a previous write file was interrupted) then we should activate that instead of opening a new diff
    // const diffTab = vscode.window.tabGroups.all
    //   .flatMap((group) => group.tabs)
    //   .find(
    //     (tab) =>
    //       tab.input instanceof vscode.TabInputTextDiff &&
    //       tab.input?.original?.scheme === DIFF_VIEW_URI_SCHEME &&
    //       arePathsEqual(tab.input.modified.fsPath, uri.fsPath),
    //   );
    // if (diffTab && diffTab.input instanceof vscode.TabInputTextDiff) {
    //   const editor = await vscode.window.showTextDocument(diffTab.input.modified, {
    //     preserveFocus: true,
    //   });
    //   return editor;
    // }
    // Open new diff editor
    await new Promise<vscode.TextEditor>((resolve, reject) => {
      const fileName = path.basename(uri.fsPath);
      const fileExists = this.editType === 'modify';
      const disposable = vscode.window.onDidChangeActiveTextEditor((editor) => {
        if (editor && arePathsEqual(editor.document.uri.fsPath, uri.fsPath)) {
          disposable.dispose();
          resolve(editor);
        }
      });
      const oldUri = vscode.Uri.parse(`${DIFF_VIEW_URI_SCHEME}:${fileName}`).with({
        query: Buffer.from(this.originalContent ?? '').toString('base64'),
      });
      vscode.commands.executeCommand(
        'vscode.diff',
        oldUri,
        uri,
        `${fileName}: ${fileExists ? "Original ↔ Codin's Changes" : 'New File'} (Editable)`,
        {
          preserveFocus: true,
        },
      );
      // This may happen on very slow machines ie project idx
      setTimeout(() => {
        disposable.dispose();
        reject(new Error('Failed to open diff editor, please try again...'));
      }, 10_000);
    });
  }

  public async revertChanges(autoCloseAllDiffViews: boolean): Promise<void> {
    if (!this._relPath || !this._activeDiffEditor) {
      return;
    }
    const fileExists = this.editType === 'modify';
    const updatedDocument = this._activeDiffEditor.document;
    const absolutePath = path.resolve(this._cwd, this._relPath);
    if (!fileExists) {
      if (updatedDocument.isDirty) {
        await updatedDocument.save();
      }
      if (autoCloseAllDiffViews) {
        await this.closeAllDiffViews();
      }
      await fs.unlink(absolutePath);
      // Remove only the directories we created, in reverse order
      for (let i = this._createdDirs.length - 1; i >= 0; i--) {
        await fs.rmdir(this._createdDirs[i]);
        console.log(`Directory ${this._createdDirs[i]} has been deleted.`);
      }
      console.log(`File ${absolutePath} has been deleted.`);
    } else {
      // revert document
      const edit = new vscode.WorkspaceEdit();
      const fullRange = new vscode.Range(
        updatedDocument.positionAt(0),
        updatedDocument.positionAt(updatedDocument.getText().length),
      );
      edit.replace(updatedDocument.uri, fullRange, this.originalContent ?? '');
      // Apply the edit and save, since contents shouldn't have changed this won't show in local history unless of course the user made changes and saved during the edit
      await vscode.workspace.applyEdit(edit);
      await updatedDocument.save();
      console.log(`File ${absolutePath} has been reverted to its original content.`);
      if (this._documentWasOpen) {
        await vscode.window.showTextDocument(vscode.Uri.file(absolutePath), {
          preview: false,
          preserveFocus: true,
        });
      }
      if (autoCloseAllDiffViews) {
        await this.closeAllDiffViews();
      }
    }

    // edit is done
    await this.reset();
  }

  public async closeAllDiffViews() {
    const tabs = vscode.window.tabGroups.all
      .flatMap((tg) => tg.tabs)
      .filter(
        (tab) => tab.input instanceof vscode.TabInputTextDiff && tab.input?.original?.scheme === DIFF_VIEW_URI_SCHEME,
      );
    for (const tab of tabs) {
      // trying to close dirty views results in save popup
      if (!tab.isDirty) {
        await vscode.window.tabGroups.close(tab);
      }
    }
  }

  private async _openDiffEditor(): Promise<vscode.TextEditor> {
    if (!this._relPath) {
      throw new Error('No file path set');
    }
    const uri = vscode.Uri.file(path.resolve(this._cwd, this._relPath));
    // If this diff editor is already open (ie if a previous write file was interrupted) then we should activate that instead of opening a new diff
    const diffTab = vscode.window.tabGroups.all
      .flatMap((group) => group.tabs)
      .find(
        (tab) =>
          tab.input instanceof vscode.TabInputTextDiff &&
          tab.input?.original?.scheme === DIFF_VIEW_URI_SCHEME &&
          arePathsEqual(tab.input.modified.fsPath, uri.fsPath),
      );
    if (diffTab && diffTab.input instanceof vscode.TabInputTextDiff) {
      const editor = await vscode.window.showTextDocument(diffTab.input.modified, {
        preserveFocus: true,
      });
      return editor;
    }
    // Open new diff editor
    return new Promise<vscode.TextEditor>((resolve, reject) => {
      const fileName = path.basename(uri.fsPath);
      const fileExists = this.editType === 'modify';
      const disposable = vscode.window.onDidChangeActiveTextEditor((editor) => {
        if (editor && arePathsEqual(editor.document.uri.fsPath, uri.fsPath)) {
          disposable.dispose();
          resolve(editor);
        }
      });
      vscode.commands.executeCommand(
        'vscode.diff',
        vscode.Uri.parse(`${DIFF_VIEW_URI_SCHEME}:${fileName}`).with({
          query: Buffer.from(this.originalContent ?? '').toString('base64'),
        }),
        uri,
        `${fileName}: ${fileExists ? "Original ↔ Codin's Changes" : 'New File'} (Editable)`,
        {
          preserveFocus: true,
        },
      );
      // This may happen on very slow machines ie project idx
      setTimeout(() => {
        disposable.dispose();
        reject(new Error('Failed to open diff editor, please try again...'));
      }, 10_000);
    });
  }

  private _scrollEditorToLine(line: number) {
    if (this._activeDiffEditor) {
      const scrollLine = line + 4;
      this._activeDiffEditor.revealRange(
        new vscode.Range(scrollLine, 0, scrollLine, 0),
        vscode.TextEditorRevealType.InCenter,
      );
    }
  }

  public scrollToFirstDiff() {
    if (!this._activeDiffEditor) {
      return;
    }
    const currentContent = this._activeDiffEditor.document.getText();
    const diffs = diff.diffLines(this.originalContent || '', currentContent);
    let lineCount = 0;
    for (const part of diffs) {
      if (part.added || part.removed) {
        // Found the first diff, scroll to it
        this._activeDiffEditor.revealRange(
          new vscode.Range(lineCount, 0, lineCount, 0),
          vscode.TextEditorRevealType.InCenter,
        );
        return;
      }
      if (!part.removed) {
        lineCount += part.count || 0;
      }
    }
  }

  // close editor if open?
  public async reset() {
    this.editType = undefined;
    if (this.isEditing) {
      this.isEditing = false;
      this._onChangeIsEditing.fire();
    }
    this.originalContent = undefined;
    this._createdDirs = [];
    this._documentWasOpen = false;
    this._activeDiffEditor = undefined;
    this._fadedOverlayController = undefined;
    this._activeLineController = undefined;
    this._streamedLines = [];
    this._preDiagnostics = [];

    // Clean up the scroll listener
    if (this._scrollListener) {
      this._scrollListener.dispose();
      this._scrollListener = undefined;
    }

    // Reset auto-scroll state
    this._shouldAutoScroll = true;
    this._lastFirstVisibleLine = 0;
  }

  public registerDiffViewContentProvider() {
    const diffContentProvider = new (class implements vscode.TextDocumentContentProvider {
      provideTextDocumentContent(uri: vscode.Uri): string {
        return Buffer.from(uri.query, 'base64').toString('utf-8');
      }
    })();

    return vscode.workspace.registerTextDocumentContentProvider(DIFF_VIEW_URI_SCHEME, diffContentProvider);
  }

  /**
   * 从 git diff 中获取增减统计信息
   */
  public async getDiffStatsFromGit(filePath: string): Promise<DiffStats> {
    try {
      const absolutePath = path.resolve(this._cwd, filePath);
      const relativePath = path.relative(this._cwd, absolutePath);

      let addedLines = 0;
      let deletedLines = 0;

      // 检查文件是否在 staged 和 unstaged 状态
      const { stdout: stagedFiles } = await execAsync(`git diff --cached --name-only -- "${relativePath}"`, {
        cwd: this._cwd,
      });
      const { stdout: unstagedFiles } = await execAsync(`git diff --name-only -- "${relativePath}"`, {
        cwd: this._cwd,
      });

      let diffCommand: string;
      if (unstagedFiles.trim()) {
        // 文件有 unstaged 变更，优先使用工作区的变更（包含所有变更）
        diffCommand = `git diff HEAD -- "${relativePath}"`;
        console.log(`[getDiffStatsFromGit] File has unstaged changes, using working directory diff: ${relativePath}`);
      } else if (stagedFiles.trim()) {
        // 文件只有 staged 变更
        diffCommand = `git diff --cached HEAD -- "${relativePath}"`;
        console.log(`[getDiffStatsFromGit] File is staged only, using --cached diff: ${relativePath}`);
      } else {
        // 文件没有变更
        return { addedLines: 0, deletedLines: 0 };
      }

      let stdout = '';
      try {
        const result = await execAsync(diffCommand, {
          cwd: this._cwd,
        });
        stdout = result.stdout;
      } catch (error) {
        // 如果 diff 命令失败，可能是新文件，检查 git status
        try {
          const statusResult = await execAsync(`git status --porcelain -- "${relativePath}"`, {
            cwd: this._cwd,
          });
          if (statusResult.stdout.trim().startsWith('A ') || statusResult.stdout.trim().startsWith('?? ')) {
            // 新文件，计算总行数
            const content = await fs.readFile(absolutePath, 'utf8');
            const totalLines = content.split('\n').filter((line) => line.trim() !== '').length;
            console.log(`[getDiffStatsFromGit] New file detected: ${relativePath}, total lines: ${totalLines}`);
            return { addedLines: totalLines, deletedLines: 0 };
          }
        } catch (statusError) {
          console.warn('Failed to get git status:', statusError);
        }
        return { addedLines: 0, deletedLines: 0 };
      }

      // 如果没有差异，返回 0
      if (!stdout.trim()) {
        return { addedLines: 0, deletedLines: 0 };
      }

      const lines = stdout.split('\n');

      for (const line of lines) {
        if (line.startsWith('+') && !line.startsWith('+++')) {
          // 新增行
          addedLines++;
        } else if (line.startsWith('-') && !line.startsWith('---')) {
          // 删除行
          deletedLines++;
        }
      }

      console.log(`[getDiffStatsFromGit] File: ${relativePath}, Added: ${addedLines}, Deleted: ${deletedLines}`);
      return { addedLines, deletedLines };
    } catch (error) {
      console.warn('Failed to get diff stats from git:', error);
      return { addedLines: 0, deletedLines: 0 };
    }
  }

  /**
   * 从 git diff 中提取变更的行操作信息
   */
  public async getLineActionsFromGit(filePath: string): Promise<LineAction[]> {
    try {
      const absolutePath = path.resolve(this._cwd, filePath);
      const relativePath = path.relative(this._cwd, absolutePath);

      // 检查文件是否在 staged 和 unstaged 状态
      const { stdout: stagedFiles } = await execAsync(`git diff --cached --name-only -- "${relativePath}"`, {
        cwd: this._cwd,
      });
      const { stdout: unstagedFiles } = await execAsync(`git diff --name-only -- "${relativePath}"`, {
        cwd: this._cwd,
      });

      let diffCommand: string;
      if (unstagedFiles.trim()) {
        // 文件有 unstaged 变更，优先使用工作区的变更（包含所有变更）
        diffCommand = `git diff HEAD -- "${relativePath}"`;
        console.log(`[getLineActionsFromGit] File has unstaged changes, using working directory diff: ${relativePath}`);
      } else if (stagedFiles.trim()) {
        // 文件只有 staged 变更
        diffCommand = `git diff --cached HEAD -- "${relativePath}"`;
        console.log(`[getLineActionsFromGit] File is staged only, using --cached diff: ${relativePath}`);
      } else {
        // 文件没有变更
        return [];
      }

      const { stdout } = await execAsync(diffCommand, {
        cwd: this._cwd,
      });

      // 如果没有差异，返回空数组
      if (!stdout.trim()) {
        return [];
      }

      const lineActions: LineAction[] = [];
      const lines = stdout.split('\n');
      let currentLine = 0;

      for (const line of lines) {
        // 解析 hunk header，格式：@@ -start,count +start,count @@
        const hunkMatch = line.match(/^@@ -\d+(?:,\d+)? \+(\d+)(?:,\d+)? @@/);
        if (hunkMatch) {
          currentLine = Number.parseInt(hunkMatch[1], 10);
          continue;
        }

        // 处理变更行
        if (line.startsWith('+') && !line.startsWith('+++')) {
          // 新增行
          lineActions.push({
            type: 'add',
            startLine: currentLine,
            endLine: currentLine,
          });
          currentLine++;
        } else if (line.startsWith('-') && !line.startsWith('---')) {
          // 删除行
          lineActions.push({
            type: 'delete',
            startLine: currentLine,
            endLine: currentLine,
          });
          // 不增加行号，因为删除的行不存在于新版本中
        } else if (line.startsWith(' ')) {
          // 上下文行
          currentLine++;
        }
      }

      return lineActions;
    } catch (error) {
      console.warn('Failed to get line actions from git:', error);
      return [];
    }
  }

  /**
   * 更新会话信息，记录文件操作信息
   */
  public async updateConversationDiffInfo(params: UpdateConversationDiffInfoParams): Promise<void> {
    const { conversationId, fileName, fullPath, operation, historyService, rpcService, toolName = 'Tool' } = params;

    let diffStats: DiffStats | null = null;
    let lineActions: LineAction[] = [];

    // 获取增减统计信息
    try {
      diffStats = await this.getDiffStatsFromGit(fullPath);
    } catch (error) {
      console.warn(`[${toolName}] Failed to get diff stats:`, error);
    }

    // 获取行操作信息
    try {
      lineActions = await this.getLineActionsFromGit(fullPath);
    } catch (error) {
      console.warn(`[${toolName}] Failed to get line actions:`, error);
    }

    try {
      // 构建文件操作信息
      const operationType = operation || 'modify';

      // 创建 DiffFileInfo 对象
      const diffInfo: DiffFileInfo = {
        fileName,
        fullPath,
        operation: operationType,
        actions: [
          {
            type: operationType,
            lineActions,
          },
        ],
        addedLines: diffStats?.addedLines || 0,
        deletedLines: diffStats?.deletedLines || 0,
      };

      // 调用 updateDiffInfo 方法来更新会话信息
      await historyService.updateDiffInfo(conversationId, diffInfo);

      // 发送 fileDiffInfo 事件通知前端更新
      rpcService.notify('fileDiffInfo', diffInfo);
    } catch (error) {
      console.error(`[${toolName}] Failed to update diff info:`, error);
    }
  }

  /**
   * 撤销文件操作
   * @param filePath 文件路径
   * @param actions 操作记录
   */
  public async undoFileOperation(filePath: string, actions: any[]): Promise<void> {
    try {
      console.log('[DiffViewService] Undoing file operation:', filePath);

      // 根据操作类型执行撤销
      for (const action of actions) {
        if (action.type === 'create') {
          // 撤销创建操作：删除文件
          await fs.unlink(filePath);
          console.log('[DiffViewService] Deleted file:', filePath);
        } else if (action.type === 'modify') {
          // 撤销修改操作：恢复文件内容
          // 这里需要从 Git 历史中恢复文件内容
          const { stdout } = await execAsync(`git show HEAD:${path.relative(this._cwd, filePath)}`, {
            cwd: this._cwd,
          });
          await fs.writeFile(filePath, stdout);
          console.log('[DiffViewService] Restored file content:', filePath);
        } else if (action.type === 'delete') {
          // 撤销删除操作：恢复文件
          // 这里需要从 Git 历史中恢复文件
          const { stdout } = await execAsync(`git show HEAD:${path.relative(this._cwd, filePath)}`, {
            cwd: this._cwd,
          });
          await fs.writeFile(filePath, stdout);
          console.log('[DiffViewService] Restored deleted file:', filePath);
        }
      }
    } catch (error) {
      console.error('[DiffViewService] Failed to undo file operation:', error);
      throw error;
    }
  }

  public async watchRepoState(conversationId: string, historyService: any, rpcService: any): Promise<void> {
    const git = this._gitService.getGitApi();
    git?.repositories.forEach((repo) => {
      this._register(
        repo.state.onDidChange(
          debounce(async () => {
            const stagedFiles = git?.repositories[0].state.indexChanges.map((c) => c.uri.fsPath);
            const workingTreeChanges = git?.repositories[0].state.workingTreeChanges.map((c) => c.uri.fsPath);
            if (!conversationId) {
              return;
            }
            // 获取当前会话的 diffInfo
            const [error, conversation] = (await historyService.getConversation(conversationId)).pair();
            if (error || !conversation || !conversation.diffInfo) {
              return;
            }

            // 检查每个 diffInfo 中的文件
            const filesToRemove: string[] = [];
            for (const diffFile of conversation.diffInfo) {
              const filePath = diffFile.fullPath;

              if (
                // 如果文件在 stagedFiles 中且不在 workingTreeChanges 中，表示全部接受
                (stagedFiles?.includes(filePath) && !workingTreeChanges?.includes(filePath)) ||
                // 如果文件不在 stagedFiles 中且不在 workingTreeChanges 中，表示文件可能被新建后删除
                (!stagedFiles?.includes(filePath) && !workingTreeChanges?.includes(filePath))
              ) {
                filesToRemove.push(filePath);
              }
            }

            // 移除符合条件的文件
            if (filesToRemove.length > 0) {
              const updatedDiffInfo = conversation.diffInfo.filter(
                (file: DiffFileInfo) => !filesToRemove.includes(file.fullPath),
              );

              // 更新会话历史
              await historyService.updateDiffInfo(conversationId, updatedDiffInfo);

              // 通知前端更新
              rpcService.notify('updateDiffInfo', updatedDiffInfo);
            }
          }),
        ),
      );
    });
  }
}

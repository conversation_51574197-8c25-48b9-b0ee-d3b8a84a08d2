import { Controller } from '@/controller';
import { commands, Uri, Webview, WebviewPanel, WebviewView, WebviewViewProvider } from 'vscode';
import { getNonce } from '../utils/get-nonce';
import { getUri } from '../utils/get-uri';
import { WEBVIEW_VIEW_TYPE } from '@/const';
import { TabCommandsRegister } from './commands/tab-commands.register';

export class WebviewProvider implements WebviewViewProvider {
  public static readonly chatView = WEBVIEW_VIEW_TYPE.SIDEBAR;
  public static readonly tabPanelId = WEBVIEW_VIEW_TYPE.PANEL;
  public view?: WebviewView | WebviewPanel;

  constructor(
    private readonly _extensionUri: Uri,
    private readonly _controller: Controller,
    private readonly _placement?: string,
  ) {}

  async deserializeWebviewPanel(webviewPanel: WebviewPanel) {
    if (this._placement === 'left') {
      webviewPanel.dispose();
    } else {
      this.resolveWebviewView(webviewPanel);
      TabCommandsRegister.panel = webviewPanel;
    }
  }

  public resolveWebviewView(webviewView: WebviewView | WebviewPanel) {
    this.view = webviewView;

    // 设置 webview 给 controller 用于 rpc
    this._controller.setWebview({
      webview: webviewView.webview,
      viewType: webviewView.viewType as WEBVIEW_VIEW_TYPE,
    });

    if (webviewView.viewType === WebviewProvider.chatView) {
      (webviewView as WebviewView).onDidChangeVisibility(() => {
        if (webviewView.visible) {
          commands.executeCommand('codin.panelView.disposal');
          this._controller.setWebview({
            webview: webviewView.webview,
            viewType: webviewView.viewType as WEBVIEW_VIEW_TYPE,
          });
        }
      });
    }

    // Allow scripts in the webview
    webviewView.webview.options = {
      // Enable JavaScript in the webview
      enableScripts: true,
      // Restrict the webview to only load resources from the `out` directory
      localResourceRoots: [Uri.joinPath(this._extensionUri, 'out')],
    };

    // Set the HTML content that will fill the webview view
    webviewView.webview.html = this._getWebviewContent(webviewView.webview, this._extensionUri);
  }

  private _getWebviewContent(webview: Webview, extensionUri: Uri) {
    const webviewUri = getUri(webview, extensionUri, ['out', 'webview.js']);
    const stylesUri = getUri(webview, extensionUri, ['out', 'webview.css']);
    const nonce = getNonce();

    return /*html*/ `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}'; img-src 'self' data: https:;">
          <link rel="stylesheet" href="${stylesUri}">
          <title>Codin Agent</title>
        </head>
        <body>
          <div id="root"></div>
          <script type="module" nonce="${nonce}" src="${webviewUri}"></script>
        </body>
      </html>
    `;
  }
}

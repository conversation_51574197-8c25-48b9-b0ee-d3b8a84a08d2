import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext, commands, FileType, Uri, workspace } from 'vscode';
import { CommandRegister } from './command-register';
import { IRpcService } from '@/services/rpc/rpc-service.interface';
import { Barrier } from '@byted-image/lv-bedrock/async';

/**
 * 文件上下文命令注册器
 * 负责将文件上下文相关的命令注册到VSCode扩展系统
 */
export class AddFilesToInputCommandsRegister extends CommandRegister {
  private readonly disposables: Disposable[] = [];
  private readonly _mainWebviewBarrier = new Barrier();

  constructor(
    @IInstantiationService protected readonly _instantiationService: IInstantiationService,
    @IRpcService protected readonly _rpcService: IRpcService,
  ) {
    super(_instantiationService);
  }

  /**
   * 注册所有文件上下文相关命令
   */
  public register(context: ExtensionContext): void {
    this._rpcService.register('codin.webview.chatView.ready', async () => {
      // 往左侧explorer 添加一个菜单项
      // vscode.commands.executeCommand('setContext', 'codin:show-add-files-to-input', true);
      this._mainWebviewBarrier.open();
    });

    const addFileToContextDisposable = commands.registerCommand(
      'codin.webview.addFilesToChat',
      async (_, uris: Uri[]) => {
        if (!uris.length) {
          return;
        }
        await this._rpcService.focusWebview();
        if (!this._mainWebviewBarrier.isOpen()) {
          await this._mainWebviewBarrier.wait();
        }

        // 判断uri是文件还是目录，如果是文件那么选择文件名和文件path，还有文件类型
        const fileInfos = await Promise.all(
          uris.map(async ({ fsPath }) => {
            const uri = Uri.file(fsPath);
            const stat = await workspace.fs.stat(uri);
            return {
              path: fsPath,
              name: uri.path.split('/').pop() || '',
              type: stat.type === FileType.File ? 'file' : 'directory',
            };
          }),
        );

        this._rpcService.notify('addFilesToChat', fileInfos);
      },
    );

    this.disposables.push(addFileToContextDisposable);
    context.subscriptions.push(...this.disposables);
  }
}

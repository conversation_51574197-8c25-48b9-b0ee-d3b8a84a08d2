import { IAccountService } from '@/services/account/account-service.interface';
import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext, commands } from 'vscode';
import { CommandRegister } from './command-register';

/**
 * 会话命令注册器
 * 负责将会话相关的命令注册到VSCode扩展系统
 */
export class AccountCommandsRegister extends CommandRegister {
  private readonly disposables: Disposable[] = [];

  constructor(
    @IInstantiationService protected readonly _instantiationService: IInstantiationService,
    @IAccountService private readonly _accountService: IAccountService,
  ) {
    super(_instantiationService);
  }

  /**
   * 注册所有会话相关命令
   */
  public register(context: ExtensionContext): void {
    // 登录
    const accountLoginDisposable = commands.registerCommand('codin.webview.account.login', async () => {
      await this._accountService.initialize();
      if (this._accountService.hasLogin) {
        return;
      }
      try {
        await this._accountService.login();
      } catch (error) {
        console.error('login error', error);
      }
    });
    this.disposables.push(accountLoginDisposable);

    // 登出
    const accountLogoutDisposable = commands.registerCommand('codin.webview.account.logout', async () => {
      await this._accountService.initialize();
      if (!this._accountService.hasLogin) {
        return;
      }
      try {
        await this._accountService.logout();
      } catch (error) {
        console.error('logout error', error);
      }
    });
    this.disposables.push(accountLogoutDisposable);

    context.subscriptions.push(...this.disposables);
  }
}

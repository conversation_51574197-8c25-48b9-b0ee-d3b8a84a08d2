import { setTimeout as setTimeoutPromise } from 'node:timers/promises';
import { IInstantiationService, InstantiationService, ServiceRegistry } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext, Uri, ViewColumn, type WebviewPanel, commands, window } from 'vscode';
import { WebviewProvider } from '@/vscode-integration/webview-provider';
import { CommandRegister } from './command-register';
import { IWorkspaceFilesService } from '@/services/workspace-files/workspace-files.interface';
import type { WorkspaceFilesService } from '@/services/workspace-files/workspace-files.service';
import { ILocalServerService } from '@/services/local-server/local-server-service.interface';
import { IRpcService } from '@/services/rpc/rpc-service.interface';
import { Controller } from '@/controller';

/**
 * Tab命令注册器
 * 负责将Tab相关的命令注册到VSCode扩展系统
 */
export class TabCommandsRegister extends CommandRegister {
  private readonly disposables: Disposable[] = [];
  public static panel?: WebviewPanel;

  constructor(@IInstantiationService protected readonly _instantiationService: IInstantiationService) {
    super(_instantiationService);
  }

  /**
   * 注册所有Tab相关命令
   */
  public register(context: ExtensionContext, serviceRegistry: ServiceRegistry, controller: Controller): void {
    const openInEditorDisposable = commands.registerCommand('codin-agent.openInEditor', async () => {
      await commands.executeCommand('workbench.view.explorer');
      if (TabCommandsRegister.panel) {
        try {
          TabCommandsRegister.panel.reveal();
        } catch (error) {
          TabCommandsRegister.panel = undefined;
        }
        return;
      }
      const newInstantiationService = new InstantiationService(
        serviceRegistry.makeCollection(),
        this._instantiationService as InstantiationService,
      );
      newInstantiationService.invokeFunction((accessor) => {
        const workspaceFilesService = accessor.get(IWorkspaceFilesService) as WorkspaceFilesService;
        // 目前没有启动流程，在这里启动
        workspaceFilesService.init();
      });

      //const column = vscode.window.activeTextEditor ? vscode.window.activeTextEditor.viewColumn : undefined
      const lastCol = Math.max(...window.visibleTextEditors.map((editor) => editor.viewColumn || 0));

      // Check if there are any visible text editors, otherwise open a new group to the right
      const hasVisibleEditors = window.visibleTextEditors.length > 0;
      if (!hasVisibleEditors) {
        await commands.executeCommand('workbench.action.newGroupRight');
      }
      const targetCol = hasVisibleEditors ? Math.max(lastCol + 1, 1) : ViewColumn.Two;
      const tabWebview = new WebviewProvider(context.extensionUri, controller);
      TabCommandsRegister.panel = window.createWebviewPanel(WebviewProvider.tabPanelId, "Codin (⌘+⇧+')", targetCol, {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [context.extensionUri],
      });

      TabCommandsRegister.panel.iconPath = Uri.joinPath(context.extensionUri, 'assets', 'codin-icon.svg');
      // 设置 panel 的 HTML 内容
      tabWebview.resolveWebviewView(TabCommandsRegister.panel);
      // 注册 codin.panelView.disposal
      // 防止事件被多次注册
      if (!(commands as any)._codinPanelViewDisposalRegistered) {
        (commands as any)._codinPanelViewDisposalRegistered = true;
        const disposalDisposable = commands.registerCommand('codin.panelView.disposal', () => {
          TabCommandsRegister.panel?.dispose();
        });
        context.subscriptions.push(disposalDisposable);
      }
      if (!(commands as any)._codinPanelViewRevealRegistered) {
        (commands as any)._codinPanelViewRevealRegistered = true;
        const revealDisposable = commands.registerCommand('codin.panelView.reveal', () => {
          if (!TabCommandsRegister.panel) {
            commands.executeCommand('codin-agent.openInEditor');
          } else {
            TabCommandsRegister.panel?.reveal();
          }
        });
        context.subscriptions.push(revealDisposable);
      }

      // 监听panel关闭
      TabCommandsRegister.panel.onDidDispose(() => {
        TabCommandsRegister.panel = undefined;
        newInstantiationService.invokeFunction((accessor) => {
          // 关闭server
          const localServerService = accessor.get(ILocalServerService);
          localServerService.dispose();

          // 关闭rpc
          const rpcService = accessor.get(IRpcService);
          rpcService.dispose();
        });
      });

      // Lock the editor group so clicking on files doesn't open them over the panel
      await setTimeoutPromise(100);
      await commands.executeCommand('workbench.action.lockEditorGroup');
    });

    this.disposables.push(openInEditorDisposable);
    context.subscriptions.push(...this.disposables);
  }
}

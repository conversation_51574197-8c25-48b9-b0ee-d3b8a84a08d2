import { IInstantiationService, type ServiceRegistry } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext } from 'vscode';
import { Controller } from '@/controller';

export abstract class CommandRegister {
  protected readonly _disposables: Disposable[] = [];

  constructor(@IInstantiationService protected readonly _instantiationService: IInstantiationService) {}

  public abstract register(context: ExtensionContext, serviceRegistry: ServiceRegistry, controller: Controller): void;

  public dispose(): void {
    for (const disposable of this._disposables) {
      disposable.dispose();
    }
    this._disposables.length = 0;
  }
}

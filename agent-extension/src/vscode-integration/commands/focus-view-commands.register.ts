import { IRpcService } from '@/services/rpc/rpc-service.interface';
import { IFileLoggerService } from '@/services/file-logger/file-logger-service.interface';
import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext, commands } from 'vscode';
import { CommandRegister } from './command-register';

/**
 * 聚焦视图命令注册器
 * 负责将聚焦视图相关的命令注册到VSCode扩展系统
 */
export class FocusViewCommandsRegister extends CommandRegister {
  private readonly disposables: Disposable[] = [];

  constructor(
    @IInstantiationService protected readonly _instantiationService: IInstantiationService,
    @IRpcService private readonly _rpcService: IRpcService,
    @IFileLoggerService private readonly _fileLoggerService: IFileLoggerService,
  ) {
    super(_instantiationService);
  }

  /**
   * 注册所有聚焦视图相关命令
   */
  public register(context: ExtensionContext): void {
    // 聚焦 Codin Webview
    const focusWebviewDisposable = commands.registerCommand('codin.focusWebview', async () => {
      try {
        this._fileLoggerService.info('FocusViewCommandsRegister: 开始执行 codin.focusWebview 命令');
        await this._rpcService.focusWebview();
        this._fileLoggerService.info('FocusViewCommandsRegister: codin.focusWebview 命令执行成功');
      } catch (error) {
        this._fileLoggerService.error(
          'FocusViewCommandsRegister: codin.focusWebview 命令执行失败',
          error instanceof Error ? error : new Error(String(error)),
        );
        console.error('focus webview error', error);
      }
    });
    this.disposables.push(focusWebviewDisposable);

    context.subscriptions.push(...this.disposables);
  }
}

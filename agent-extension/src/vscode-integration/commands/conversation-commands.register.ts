import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext, commands } from 'vscode';
import { IConversationCommandsService } from '@/services/commands/conversation-commands.interface';
import { CommandRegister } from './command-register';

/**
 * 会话命令注册器
 * 负责将会话相关的命令注册到VSCode扩展系统
 */
export class ConversationCommandsRegister extends CommandRegister {
  private readonly disposables: Disposable[] = [];
  private readonly _conversationCommandsService: IConversationCommandsService;

  constructor(@IInstantiationService protected readonly _instantiationService: IInstantiationService) {
    super(_instantiationService);
    this._conversationCommandsService = this._instantiationService.invokeFunction((accessor) =>
      accessor.get(IConversationCommandsService),
    );
  }

  /**
   * 注册所有会话相关命令
   */
  public register(context: ExtensionContext): void {
    const newConversationDisposable = commands.registerCommand('codin-agent.newConversation', () =>
      this._conversationCommandsService.newConversation(),
    );

    const viewHistoryDisposable = commands.registerCommand('codin-agent.viewHistory', () =>
      this._conversationCommandsService.viewHistory(),
    );

    this.disposables.push(newConversationDisposable, viewHistoryDisposable);
    context.subscriptions.push(...this.disposables);
  }
}

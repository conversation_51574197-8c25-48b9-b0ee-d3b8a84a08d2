import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext, Uri, commands, env, window } from 'vscode';
import { CommandRegister } from './command-register';
import { ICodingChatService } from '@/services/coding-chat/coding-chat-service.interface';

/**
 * 反馈命令注册器
 * 负责将反馈相关的命令注册到VSCode扩展系统
 */
export class FeedbackCommandsRegister extends CommandRegister {
  private readonly disposables: Disposable[] = [];

  constructor(
    @IInstantiationService protected readonly _instantiationService: IInstantiationService,
    @ICodingChatService private readonly _codingChatService: ICodingChatService,
  ) {
    super(_instantiationService);
  }

  /**
   * 注册所有反馈相关命令
   */
  public register(context: ExtensionContext): void {
    const onCallDisposable = commands.registerCommand('codin.webview.oncall', async () => {
      try {
        // 收集各个 agent 的调试信息
        const debugInfo = this._collectDebugInfo();

        // 将调试信息复制到剪切板
        await env.clipboard.writeText(debugInfo);

        // 显示成功提示
        window.showInformationMessage('调试信息已复制到剪切板');

        // 打开 oncall 群链接
        env.openExternal(
          Uri.parse(
            'https://applink.larkoffice.com/client/chat/chatter/add_by_link?link_token=2ect4f64-b6af-484f-9d59-11c062cbb5a7',
          ),
        );
      } catch (error) {
        window.showErrorMessage(`复制调试信息失败: ${error}`);
      }
    });

    this.disposables.push(onCallDisposable);
    context.subscriptions.push(...this.disposables);
  }

  /**
   * 收集各个 agent 服务的调试信息
   */
  private _collectDebugInfo(): string {
    const debugData: Record<string, { cid: string }> = {};

    // 收集 coding-chat-service 信息
    if (this._codingChatService.currentCid) {
      debugData['coding-chat-service'] = {
        cid: this._codingChatService.currentCid,
      };
    }

    // 格式化调试信息
    const timestamp = new Date().toISOString();
    let debugInfo = `=== Codin Debug Info (${timestamp}) ===\n\n`;

    for (const [serviceName, info] of Object.entries(debugData)) {
      debugInfo += `${serviceName}:\n`;
      debugInfo += `  cid: ${info.cid}\n`;
      debugInfo += '\n';
    }

    if (Object.keys(debugData).length === 0) {
      debugInfo += '暂无活跃的 agent 会话\n';
    }

    return debugInfo;
  }
}

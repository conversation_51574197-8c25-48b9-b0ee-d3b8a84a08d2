import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext, commands } from 'vscode';
import { CommandRegister } from './command-register';
import { IRpcService } from '@/services/rpc/rpc-service.interface';
import { IDocumentsService } from '@/services/documents/documents-service.interface';

/**
 * Documents commands register
 * Responsible for registering documents-related commands to the VSCode extension system
 */
export class DocumentsCommandsRegister extends CommandRegister {
  private readonly disposables: Disposable[] = [];

  constructor(
    @IInstantiationService protected readonly _instantiationService: IInstantiationService,
    @IRpcService private readonly _rpcService: IRpcService,
    @IDocumentsService private readonly _documentsService: IDocumentsService,
  ) {
    super(_instantiationService);
  }

  /**
   * Register all documents-related commands
   */
  public register(context: ExtensionContext): void {
    // open documents
    const documentsOpenDisposable = commands.registerCommand('codin.webview.documents.open', () => {
      this._rpcService.notify('navigate', {
        path: '/documents',
      });
    });
    this.disposables.push(documentsOpenDisposable);

    // close documents
    const documentsCloseDisposable = commands.registerCommand('codin.webview.documents.close', () => {
      this._rpcService.notify('navigate', {
        path: '/',
      });
    });
    this.disposables.push(documentsCloseDisposable);

    // open document file
    const openDocumentDisposable = commands.registerCommand('codin.openDocument', async (filePath: string) => {
      try {
        await this._documentsService.openDocument(filePath);
      } catch (error) {
        console.error('Failed to open document:', error);
      }
    });
    this.disposables.push(openDocumentDisposable);

    // subscribe to vscode context
    context.subscriptions.push(...this.disposables);
  }
}

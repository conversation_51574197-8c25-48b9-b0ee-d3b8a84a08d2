import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { ExtensionContext } from 'vscode';
import { VSCodeStorageService } from './vscode-storage-service';
import type { IExtensionContextService } from '@/services/commands/extension-context.interface';

// Mock IExtensionContextService
const mockStore = vi.fn();
const mockGet = vi.fn();
const mockDelete = vi.fn();
// 新增 globalState mock
const mockGlobalStateGet = vi.fn();
const mockGlobalStateUpdate = vi.fn();

const mockExtensionContextService: IExtensionContextService = {
  _serviceBrand: undefined,
  context: {
    secrets: {
      store: mockStore,
      get: mockGet,
      delete: mockDelete,
      onDidChange: vi.fn(),
    },
    globalState: {
      get: mockGlobalStateGet,
      update: mockGlobalStateUpdate,
    },
  } as unknown as ExtensionContext,
};

describe('VSCodeStorageService', () => {
  let storageService: VSCodeStorageService;

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
    storageService = new VSCodeStorageService(mockExtensionContextService);
  });

  it('should be defined', () => {
    expect(storageService).toBeDefined();
  });

  describe('secretStore', () => {
    it('should call secrets.store with the correct key and value', async () => {
      const key = 'myKey';
      const value = 'myValue';
      await storageService.secretStore(key, value);
      expect(mockStore).toHaveBeenCalledTimes(1);
      expect(mockStore).toHaveBeenCalledWith(key, value);
    });
  });

  describe('secretGet', () => {
    it('should call secrets.get with the correct key and return the value', async () => {
      const key = 'myKey';
      const expectedValue = 'mySecretValue';
      mockGet.mockResolvedValue(expectedValue);

      const result = await storageService.secretGet(key);

      expect(mockGet).toHaveBeenCalledTimes(1);
      expect(mockGet).toHaveBeenCalledWith(key);
      expect(result).toBe(expectedValue);
    });

    it('should return undefined if the secret does not exist', async () => {
      const key = 'nonExistentKey';
      mockGet.mockResolvedValue(undefined);

      const result = await storageService.secretGet(key);

      expect(mockGet).toHaveBeenCalledTimes(1);
      expect(mockGet).toHaveBeenCalledWith(key);
      expect(result).toBeUndefined();
    });
  });

  describe('secretDelete', () => {
    it('should call secrets.delete with the correct key', async () => {
      const key = 'myKey';
      await storageService.secretDelete(key);
      expect(mockDelete).toHaveBeenCalledTimes(1);
      expect(mockDelete).toHaveBeenCalledWith(key);
    });
  });

  describe('get', () => {
    it('should call globalState.get with the correct key and return the value', async () => {
      const key = 'testKey';
      const expectedValue = 'testValue';
      mockGlobalStateGet.mockReturnValue(expectedValue);

      const result = await storageService.get(key);

      expect(mockGlobalStateGet).toHaveBeenCalledTimes(1);
      expect(mockGlobalStateGet).toHaveBeenCalledWith(key);
      expect(result).toBe(expectedValue);
    });

    it('should return undefined if the key does not exist', async () => {
      const key = 'nonExistentKey';
      mockGlobalStateGet.mockReturnValue(undefined);

      const result = await storageService.get(key);

      expect(mockGlobalStateGet).toHaveBeenCalledTimes(1);
      expect(mockGlobalStateGet).toHaveBeenCalledWith(key);
      expect(result).toBeUndefined();
    });
  });

  describe('set', () => {
    it('should call globalState.update with the correct key and value', async () => {
      const key = 'setKey';
      const value = 'setValue';
      await storageService.set(key, value);
      expect(mockGlobalStateUpdate).toHaveBeenCalledTimes(1);
      expect(mockGlobalStateUpdate).toHaveBeenCalledWith(key, value);
    });
  });

  describe('delete', () => {
    it('should call globalState.update with the correct key and undefined value', async () => {
      const key = 'deleteKey';
      await storageService.delete(key);
      expect(mockGlobalStateUpdate).toHaveBeenCalledTimes(1);
      expect(mockGlobalStateUpdate).toHaveBeenCalledWith(key, undefined);
    });
  });
});

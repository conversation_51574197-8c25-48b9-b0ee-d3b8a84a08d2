import { IExtensionContextService } from '@/services/commands/extension-context.interface';
import type { IStorageService } from '@/services/storage/storage-service.interface';

export class VSCodeStorageService implements IStorageService {
  public readonly _serviceBrand: undefined;

  constructor(@IExtensionContextService private readonly _extensionContextService: IExtensionContextService) {}

  public get(key: string) {
    return Promise.resolve(this._extensionContextService.context.globalState.get(key) as string | undefined);
  }

  public parseGet<T>(key: string, defaultVal: T): T | undefined {
    const str = this._extensionContextService.context.globalState.get(key) as string | undefined;
    if (str && typeof str === 'string') {
      return (JSON.parse(str) as T) ?? defaultVal;
    }
    return defaultVal;
  }

  public set(key: string, value: string) {
    return Promise.resolve(this._extensionContextService.context.globalState.update(key, value));
  }

  public delete(key: string) {
    return Promise.resolve(this._extensionContextService.context.globalState.update(key, undefined));
  }

  /**
   * @warning 这个方法在云真机评测环境有兼容性问题
   */
  public secretStore(key: string, value: string) {
    return this._extensionContextService.context.secrets.store(key, value);
  }

  /**
   * @warning 这个方法在云真机评测环境有兼容性问题
   */
  public secretGet(key: string) {
    return this._extensionContextService.context.secrets.get(key);
  }

  /**
   * @warning 这个方法在云真机评测环境有兼容性问题
   */
  public secretDelete(key: string) {
    return this._extensionContextService.context.secrets.delete(key);
  }
}

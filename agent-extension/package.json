{"name": "codin", "displayName": "剪映Codin", "publisher": "byted-extensions", "icon": "assets/codin-market-icon.png", "description": "Codin 是一款为剪映团队打造的 IDE 插件，提供智能的代码辅助和集成工具，让您的开发工作流程更快捷、更高效。", "repository": "******************:ies/codin.git", "version": "0.2.85-beta.7", "engines": {"vscode": "^1.75.1"}, "scripts": {"vscode:prepublish": "npm run build", "compile": "node ./esbuild.js", "build": "NODE_ENV=production node ./esbuild.js && chmod -R 777 ./out", "package": "vsce package --no-dependencies", "watch": "NODE_ENV=development node ./esbuild.js --watch", "test": "vitest run --coverage", "lint": "biome check", "lint:fix": "biome check --fix", "typecheck": "tsc -b src/webview/"}, "main": "./out/extension.js", "extensionDependencies": ["vscode.git"], "contributes": {"icons": {"codin-icon": {"description": "Codin Icon", "default": {"fontPath": "assets/codin.woff", "fontCharacter": "\\E900"}}}, "viewsContainers": {"activitybar": [{"id": "codin", "title": "Codin", "icon": "assets/codin-icon.svg"}]}, "views": {"codin": [{"type": "webview", "icon": "assets/codin-icon.svg", "id": "codin.chatView", "name": "Codin (⌘+⇧+')", "when": "codin.placement == 'left'"}]}, "configuration": {"title": "Local Server", "properties": {"codin.localServer.port": {"type": "number", "default": 40001, "description": "Port number for the local server"}}}, "commands": [{"command": "codin-agent.newConversation", "title": "新建对话", "category": "codin-agent", "icon": "$(add)"}, {"command": "codin-agent.viewHistory", "title": "查看历史对话", "category": "codin-agent", "icon": "$(history)"}, {"command": "codin-agent.openInEditor", "title": "移动到右侧边栏", "category": "codin-agent", "icon": "$(link-external)"}, {"command": "coding-agent.indexRecord", "title": "索引记录", "category": "codin-agent", "icon": "${history}"}, {"command": "codin.webview.codeReview", "title": "Code Review", "category": "codin-agent", "icon": "$(comment)"}, {"command": "codin.webview.oncall", "title": "在线答疑", "category": "codin-agent"}, {"command": "codin.webview.account.login", "title": "登录", "category": "codin-agent"}, {"command": "codin.webview.documents.open", "title": "Specs", "icon": "$(note)", "category": "codin-agent"}, {"command": "codin.webview.settings.open", "title": "设置", "icon": "$(settings-gear)", "category": "codin-agent"}, {"command": "codin.webview.log.openLogFolder", "title": "打开日志文件夹", "category": "codin-agent"}, {"command": "codin.webview.codediff.acceptAll", "title": "Accept all change", "category": "codin-agent"}, {"command": "codin.webview.codediff.revertAll", "title": "Revert all change", "category": "codin-agent"}, {"command": "codin.webview.addFilesToChat", "title": "Add File To Codin Chat", "category": "codin-agent"}, {"command": "codin.focusWebview", "title": "Focus Codin Webview", "category": "codin-agent"}, {"command": "codin.panelView.reveal", "title": "显示右侧面板", "category": "codin-agent"}, {"command": "codin.panelView.disposal", "title": "关闭右侧对话框", "category": "codin-agent"}], "menus": {"view/title": [{"command": "codin-agent.newConversation", "when": "codin.login == true && view == 'codin.chatView'", "group": "navigation@1"}, {"command": "codin-agent.viewHistory", "when": "codin.login == true && view == 'codin.chatView'", "group": "navigation@2"}, {"command": "codin.webview.documents.open", "when": "codin.login == true && view == 'codin.chatView'", "group": "navigation@3"}, {"command": "codin.webview.settings.open", "when": "codin.login == true && view == 'codin.chatView'", "group": "navigation@4"}, {"submenu": "codin.submenu", "when": "codin.login == true && view == 'codin.chatView'", "group": "navigation@5"}], "editor/title": [{"command": "codin-agent.newConversation", "when": "codin.login == true && (activeWebviewPanelId == 'codin.TabPanelProvider' && focusedView !='workbench.panel.chat.view.copilot')", "group": "navigation@1"}, {"command": "codin-agent.viewHistory", "when": "codin.login == true && (activeWebviewPanelId == 'codin.TabPanelProvider' && focusedView !='workbench.panel.chat.view.copilot')", "group": "navigation@2"}, {"command": "codin.webview.documents.open", "when": "codin.login == true && (activeWebviewPanelId == 'codin.TabPanelProvider' && focusedView !='workbench.panel.chat.view.copilot')", "group": "navigation@3"}, {"command": "codin.webview.settings.open", "when": "codin.login == true && (activeWebviewPanelId == 'codin.TabPanelProvider' && focusedView !='workbench.panel.chat.view.copilot')", "group": "navigation@4"}, {"submenu": "codin.submenu", "when": "codin.login == true && (activeWebviewPanelId == 'codin.TabPanelProvider' && focusedView !='workbench.panel.chat.view.copilot')", "group": "navigation@5"}], "codin.submenu": [{"command": "coding-agent.indexRecord"}, {"command": "codin.webview.log.openLogFolder"}], "commandPalette": [{"command": "codin-agent.newConversation", "title": "Codin Agent: New Conversation"}, {"command": "codin-agent.viewHistory", "title": "Codin Agent: View History"}, {"command": "codin.webview.addFilesToChat", "title": "Add File To Codin Chat", "when": "false"}, {"command": "codin.panelView.reveal", "when": "false"}, {"command": "codin.panelView.disposal", "when": "false"}]}, "keybindings": [{"command": "codin.focusWebview", "key": "cmd+shift+'", "when": "isMac && codin.cmdAvailable"}, {"command": "codin.focusWebview", "key": "ctrl+shift+'", "when": "!isMac && codin.cmdAvailable"}], "submenus": [{"id": "codin.submenu", "label": "More", "icon": "$(more)"}]}, "activationEvents": ["onStartupFinished"], "devDependencies": {"@biomejs/biome": "^1.9.4", "@byted-arch-fe/bam-code-generator": "^1.18.0", "@edenx/app-tools": "1.67.1", "@tailwindcss/typography": "^0.5.15", "@types/diff": "^5.2.1", "@types/glob": "^8.0.0", "@types/lodash": "^4.17.20", "@types/micromatch": "^4.0.9", "@types/node": "^22.8.6", "@types/react": "^18.2.65", "@types/react-dom": "^18.2.22", "@types/react-modal": "^3.16.3", "@types/react-syntax-highlighter": "^15.5.13", "@types/vscode": "^1.74.0", "@types/vscode-webview": "^1.57.0", "@types/ws": "8.18.1", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitest/coverage-v8": "^3.2.4", "@volcengine/tos-sdk": "^2.7.5", "@vscode/vsce": "^3.4.2", "autoprefixer": "^10.4.18", "dotenv": "^16.5.0", "esbuild": "^0.16.10", "esbuild-plugin-copy": "^2.0.1", "esbuild-postcss": "^0.0.4", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "fzf": "^0.5.2", "glob": "^8.0.3", "ignore": "^7.0.3", "jsdom": "^26.1.0", "memfs": "^4.17.2", "postcss": "^8.4.35", "prettier": "^2.8.1", "tailwindcss": "^3.4.1", "typescript": "^5.6.3", "uuid": "^11.1.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4", "vitest-mock-extended": "^3.1.0", "yaml": "^2.8.0"}, "dependencies": {"@byted-image/codin-indexer": "0.2.59", "@byted-image/lv-bedrock": "1.5.4", "@byted-image/merkle": "^1.1.14", "@byted/frontier-web-sdk": "^1.25.9", "@byted/hooks": "^2.61.4", "@bytesso/device": "0.0.5", "@modelcontextprotocol/sdk": "^1.11.1", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/themes": "^3.2.1", "@sasza/react-panzoom": "^1.19.0", "@slardar/base": "^2.1.2", "@slardar/integrations": "^2.13.0", "@slardar/web": "^1.14.5", "@swc/helpers": "^0.5.17", "@testing-library/react": "^16.3.0", "@tiptap/core": "^2.12.0", "@tiptap/extension-character-count": "^2.12.0", "@tiptap/extension-document": "^2.12.0", "@tiptap/extension-hard-break": "^2.12.0", "@tiptap/extension-history": "^2.12.0", "@tiptap/extension-mention": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-text": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "axios": "^1.5.1", "chokidar": "^4.0.1", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "diff": "^5.2.0", "fast-deep-equal": "^3.1.3", "framer-motion": "^11.3.19", "globby": "^14.1.0", "iconv-lite": "^0.6.3", "ignore": "^7.0.5", "isbinaryfile": "^5.0.2", "js-md5": "^0.8.3", "jschardet": "^3.1.4", "lodash": "^4.17.21", "lucide-react": "^0.446.0", "mermaid": "^11.6.0", "micromatch": "^4.0.8", "nanoid": "^5.0.8", "node-fetch-cache": "^5.0.2", "path": "^0.12.7", "pino": "^9.7.0", "pino-roll": "^3.1.0", "radix-ui": "^1.4.2", "react": "^18.2.0", "react-complex-tree": "^2.6.0", "react-dom": "^18.2.0", "react-json-view": "^1.21.3", "react-markdown": "^9.0.1", "react-modal": "^3.16.3", "react-resizable-panels": "^2.1.7", "react-router-dom": "^6.22.3", "react-syntax-highlighter": "^15.6.1", "reflect-metadata": "0.1.13", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0", "sonner": "^1.5.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tippy.js": "^6.3.7", "tree-sitter-wasms": "0.1.11", "usehooks-ts": "^3.1.0", "web-tree-sitter": "^0.22.6", "ws": "8.18.1", "xxhash-wasm": "^1.1.0", "zod": "^3.23.8"}}